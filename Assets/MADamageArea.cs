using System.Collections.Generic;
using UnityEngine;

public class MADamageArea : MonoBehaviour
{
    private Collider m_damageAreaCollider;
    private HashSet<MACharacterBase> allowedCharacters = null;
    
    private void Awake()
    {
        m_damageAreaCollider = InteractTransform.FindDamageArea(gameObject);
        allowedCharacters = new HashSet<MACharacterBase>();
    }
    
    public bool IsAllowed(MACharacterBase _character)
    {
        return allowedCharacters.Contains(_character) || CanEnter(_character);
    }

    private bool CanEnter(MACharacterBase _character)
    {
        float usedArea = CalculateAreaUsed();
        Vector2 bounds = m_damageAreaCollider.bounds.size.GetXZVector2();
        float totalArea = bounds.x * bounds.y;
        float areaAvailable = totalArea - usedArea;
        Vector2 boundsNeeded = _character.m_bodyToBodyCollider.bounds.size.GetXZVector2();
        float areaNeeded = boundsNeeded.x * boundsNeeded.y;
        return areaAvailable > areaNeeded;
    }

    private float CalculateAreaUsed()
    {
        float areaUsed = 0f;
        allowedCharacters.RemoveWhere((c) => { return c == null; });
        foreach (var character in allowedCharacters)
        {
            Vector2 boundsNeeded = character.m_bodyToBodyCollider.bounds.size.GetXZVector2();
            float areaNeeded = boundsNeeded.x * boundsNeeded.y;
            areaUsed += areaNeeded;
            areaUsed = Mathf.Clamp(areaUsed, 0f, areaUsed);
        }

        return areaUsed;
    }
    
    public void AddOverlap(MACharacterBase _character)
    {
        if (CanEnter(_character))
        {
            allowedCharacters.Add(_character);
        }
    }
    
    public void RemovedOverlap(MACharacterBase _character)
    {
        allowedCharacters.Remove(_character);
    }

    private void OnTriggerEnter(Collider _other)
    {
        MACharacterBase chara = _other.gameObject.GetComponent<MACharacterBase>();
        if (chara != null)
        {
            AddOverlap(chara);
        }
    }

    private void OnTriggerStay(Collider other)
    {
        MACharacterBase chara = other.gameObject.GetComponent<MACharacterBase>();
        if (chara != null)
        {
            
        }
    }

    private void OnTriggerExit(Collider _other)
    {
        MACharacterBase chara = _other.gameObject.GetComponent<MACharacterBase>();
        if (chara != null)
        {
            RemovedOverlap(chara);
        }
    }
}
