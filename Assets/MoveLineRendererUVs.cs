using UnityEngine;

[RequireComponent(typeof(LineRenderer))]
public class MoveAlbedoAlongLine : MonoBehaviour
{
    // Speed at which the albedo texture moves along the line
    public float uvSpeed = 0.1f;

    private LineRenderer lineRenderer;
    private Material material;
    private float textureOffset;

    void Start()
    {
        // Get the LineRenderer and material from the object
        lineRenderer = GetComponent<LineRenderer>();
        material = lineRenderer.material;

        // Initialize the texture offset
        textureOffset = 1.49f;
    }

    void Update()
    {
        // Update the texture offset based on the speed
        textureOffset += uvSpeed * Time.deltaTime;

        // Apply the offset to the texture on the LineRenderer
        if (material != null)
        {
            material.mainTextureOffset = new Vector2(textureOffset, 0f);
        }

        // Optionally, you can modify the texture tiling based on the line length
        if (lineRenderer.positionCount > 1)
        {
            float lineLength = 0f;

            // Calculate the total length of the line
            for (int i = 1; i < lineRenderer.positionCount; i++)
            {
                lineLength += Vector3.Distance(lineRenderer.GetPosition(i - 1), lineRenderer.GetPosition(i));
            }

            // Set the texture tiling based on the line length
            material.mainTextureScale = new Vector2(lineLength, 1f);
        }
    }
}
