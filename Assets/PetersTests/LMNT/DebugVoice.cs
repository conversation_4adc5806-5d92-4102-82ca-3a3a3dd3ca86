using System;
using System.Collections.Generic;
using DG.Tweening;
using UnityEditor;
using UnityEngine;
using UnityEngine.Networking;

public class DebugVoice : MonoBehaviour
{
	private Provider m_chosenprovider;
	public Provider m_provider;
	public enum Provider
	{
		LMNT,
		ElevenLabs
	}
		
	public string m_characterName;
	public bool m_talkNow;
	public bool m_forceGenerateFile;

	public string m_text;

	private void Awake()
	{
		m_chosenprovider = m_provider;
		switch (m_provider)
		{
			case Provider.ElevenLabs:
				var el = ElevenLabsVoice;
				el.m_text = m_text;
				el.m_characterName = m_characterName;
				el.m_forceGenerateFile = m_forceGenerateFile;
				break;
			case Provider.LMNT:
				var lm = LMNTVoice;
				lm.m_text = m_text;
				lm.m_characterName = m_characterName;
				lm.m_forceGenerateFile = m_forceGenerateFile;
				break;
		}
	}
	
	private void OnEnable()
    {
    	switch (m_provider)
    	{
    		case Provider.ElevenLabs:
    			var el = ElevenLabsVoice;
    			el.m_text = m_text;
    			el.m_characterName = m_characterName;
    			el.m_forceGenerateFile = m_forceGenerateFile;
    			break;
    		case Provider.LMNT:
    			var lm = LMNTVoice;
    			lm.m_text = m_text;
    			lm.m_characterName = m_characterName;
    			lm.m_forceGenerateFile = m_forceGenerateFile;
    			break;
    	}
    }

	public ElevenLabsVoice ElevenLabsVoice
	{
		get
		{
			var el = gameObject.GetComponent<ElevenLabsVoice>();
			if (el == null)
			{
				el = gameObject.AddComponent<ElevenLabsVoice>();
				el.m_text = m_text;
				el.m_characterName = m_characterName;
				el.m_forceGenerateFile = m_forceGenerateFile;
			}
			return el;
		}
	}
	public LMNTVoice LMNTVoice
	{
		get
		{
			var lm = gameObject.GetComponent<LMNTVoice>();
			if (lm == null)
			{
				lm = gameObject.AddComponent<LMNTVoice>();
				lm.m_text = m_text;
				lm.m_characterName = m_characterName;
				lm.m_forceGenerateFile = m_forceGenerateFile;
			}
			return lm;
		}
	}
	private void Update()
	{
		if (m_chosenprovider != m_provider)
		{
			m_chosenprovider = m_provider;
			switch (m_provider)
			{
				case Provider.ElevenLabs:
					var el = ElevenLabsVoice;
					el.m_text = m_text;
					el.m_characterName = m_characterName;
					el.m_forceGenerateFile = m_forceGenerateFile;
					break;
				case Provider.LMNT:
					var lm = LMNTVoice;
					lm.m_text = m_text;
					lm.m_characterName = m_characterName;
					lm.m_forceGenerateFile = m_forceGenerateFile;
					break;
			}
		}
		else if (m_talkNow)
		{
			m_talkNow = false;
			
			switch (m_provider)
			{
				case Provider.ElevenLabs:
					var el = ElevenLabsVoice;
					el.m_text = m_text;
					el.m_characterName = m_characterName;
					el.m_forceGenerateFile = m_forceGenerateFile;
					el.Play();
					break;
				case Provider.LMNT:
					var lm = LMNTVoice;
					lm.m_text = m_text;
					lm.m_characterName = m_characterName;
					lm.m_forceGenerateFile = m_forceGenerateFile;
					lm.Play();
					break;
			}
		}
	}

	public bool VoiceActive
	{
		get
		{
			switch (m_provider)
			{
				case Provider.ElevenLabs:
					var el = ElevenLabsVoice;
					return el != null && el.VoiceActive;
				case Provider.LMNT:
					var lm = LMNTVoice;
					return lm != null && lm.VoiceActive;
			}
			return false;
		}
	}

	public AudioSource GetAudioSource()
	{
		switch (m_provider)
		{
			case Provider.ElevenLabs:
				return ElevenLabsVoice?.GetAudioSource();
			case Provider.LMNT:
				return LMNTVoice?.GetAudioSource();
		}
		return null;
	}

	public void SetSpeech(string _text)
	{
		m_text = _text.Replace(@"\n", " ");
		m_talkNow = true;
		
		switch (m_provider)
		{
			case Provider.ElevenLabs:
				var el = ElevenLabsVoice;
				el.m_forceGenerateFile = m_forceGenerateFile;
				el.SetSpeech(_text);
				break;
			case Provider.LMNT:
				var lm = LMNTVoice;
				lm.m_forceGenerateFile = m_forceGenerateFile;
				lm.SetSpeech(_text);
				break;
		}
	}
}
#if UNITY_EDITOR
[CustomEditor(typeof(DebugVoice))]
public class DebugVoiceEditor : Editor
{
	public override void OnInspectorGUI()
	{
		base.OnInspectorGUI();
		
		DebugVoice e = target as DebugVoice;
		if (GUILayout.Button("Add Provider's Components"))
		{
			switch (e.m_provider)
			{
				case DebugVoice.Provider.ElevenLabs:
					var el = e.ElevenLabsVoice;
					break;
				case DebugVoice.Provider.LMNT:
					var l = e.LMNTVoice;
					break;
			}
		}
	}
}
#endif