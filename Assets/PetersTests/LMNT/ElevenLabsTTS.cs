
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEngine;
using UnityEngine.Networking;
#if UNITY_EDITOR
using UnityEditor;
#endif

public class ElevenLabsTTS : MonoBehaviour
{
    [SerializeField] private string m_apiKey = "***************************************************";//put in file
    [SerializeField] private string fallbackVoiceId = "zZLmKvCp1i04X8E0FJ8B"; //vince default
    
    private string m_voicesUrl = "https://api.elevenlabs.io/v1/voices";
    
    public string m_voice;
    public VoicesResponse m_voices = null;//includes both premade and self-made voices

    //[TextArea] public string textToSpeak = "Hello! This is Vince speaking from ElevenLabs.";

    public AudioFormat m_audioFormat = AudioFormat.mp3_44100_128; //default, don't change
    public RequestData.VoiceSettings m_customVoiceSettings = null;
    
    private DownloadHandlerAudioClip m_audioHandlerClip = null;

    private void Awake()
    {
	    GetVoices(null);
    }

    public enum AudioFormat
    {
	    mp3_22050_32,
	    mp3_44100_32,
	    mp3_44100_64,
	    mp3_44100_96,
	    mp3_44100_128,
	    mp3_44100_192,
	    pcm_8000,
	    pcm_16000,
	    pcm_22050,
	    pcm_24000,
	    pcm_44100,
	    pcm_48000,
	    ulaw_8000,
	    alaw_8000,
	    opus_48000_32,
	    opus_48000_64,
	    opus_48000_96,
	    opus_48000_128,
	    opus_48000_192,
    }

    public IEnumerator Speak(Action<AudioClip> _newClip, string inputText, string _voiceName = null)
    {
	    var voiceFound = m_voices.voices.Find(x => x.name == _voiceName);
        string url = $"https://api.elevenlabs.io/v1/text-to-speech/{(voiceFound == null || voiceFound.voice_id.IsNullOrWhiteSpace() ? fallbackVoiceId : voiceFound.voice_id)}";

        string jsonData = JsonUtility.ToJson(new RequestData
        {
            text = inputText,
            model_id = "eleven_multilingual_v2",
            output_format = m_audioFormat.ToString(),
            voice_settings = m_customVoiceSettings,
        });
        
        using (UnityWebRequest www = new UnityWebRequest(url, "POST"))
        {
            byte[] bodyRaw = System.Text.Encoding.UTF8.GetBytes(jsonData);
            www.uploadHandler = new UploadHandlerRaw(bodyRaw);
            m_audioHandlerClip = new DownloadHandlerAudioClip(url, AudioType.MPEG);
            www.downloadHandler = m_audioHandlerClip;

            string formatParam = UnityWebRequest.EscapeURL(m_audioFormat.ToString());
            www.SetRequestHeader("Content-Type", "application/json");
            www.SetRequestHeader("xi-api-key", m_apiKey);
            
            yield return www.SendWebRequest();
            
            if (www.result != UnityWebRequest.Result.Success)
            {
                Debug.LogError($"TTS Error: {www.error.ToString()}\n{www.downloadHandler?.text ?? ""}");
            }
            else
            {
	            if (m_audioHandlerClip.audioClip != null)
	            {
		            _newClip?.Invoke(m_audioHandlerClip.audioClip);
	            }
	            else
	            {
					StartCoroutine(GetClipFromBytes(_newClip, www.downloadHandler.data));
	            }
            }
        }
    }
    
    public IEnumerator GetClipFromBytes(Action<AudioClip> _newClip, byte[] mp3Data)
    {
	    string tempPath = Path.Combine(Application.persistentDataPath, "tts.mp3");
	    File.WriteAllBytes(tempPath, mp3Data);

	    using (UnityWebRequest www = UnityWebRequestMultimedia.GetAudioClip("file://" + tempPath, AudioType.MPEG))
	    {
		    yield return www.SendWebRequest();

		    if (www.result == UnityWebRequest.Result.Success)
		    {
			    AudioClip clip = DownloadHandlerAudioClip.GetContent(www);
			    _newClip.Invoke(clip);
		    }
		    else
		    {
			    Debug.LogError(www.error);
		    }
	    }
    }
    
    //please see this for parameters:
//https://elevenlabs.io/docs/api-reference/text-to-speech/convert
    [System.Serializable]
    public class RequestData
    {
        public string text;
        public string model_id;
        public string output_format;
        public VoiceSettings voice_settings;
        [Serializable]
        public class VoiceSettings : object
        {
	        public double stability = 0.5;
	        public bool use_speaker_boost = true;
	        public double similarity_boost = 0.75;
	        public double style = 0;
	        public double speed = 1;
        }
    }
    
    [System.Serializable]
    public class Voice {
	    public string name;
	    public string voice_id;
	    public string category;
	    public List<string> labels;
    }

    [System.Serializable]
    public class VoicesResponse {
	    public List<Voice> voices;
    }

    public static List<ElevenLabsTTS.Voice> LoadVoices()
    {
	    List<Voice> voiceList = new List<Voice>();
	    var voicesFile = Resources.Load<TextAsset>("ElevenLabsTTS_Voices");
	    if (voicesFile == null) {
		    return voiceList;
	    }

	    string[] lines = voicesFile.ToString().Split('\n');
	    foreach (string line in lines) {
		    if (line.Length == 0) {
			    continue;
		    }
		    string[] fields = line.Split('|');
		    voiceList.Add(new Voice() { voice_id = fields[0], name = fields[1] });
	    }
	    return voiceList;
    }
    
    public void GetVoices(Action<List<ElevenLabsTTS.Voice>> _voicesReturned)
    {
	    StartCoroutine(GetVoicesCoroutine(_voicesReturned));
    }

    private IEnumerator GetVoicesCoroutine(Action<List<ElevenLabsTTS.Voice>> _voicesReturned)
    {
	    string url = "https://api.elevenlabs.io/v1/voices";
	    using (UnityWebRequest www = UnityWebRequest.Get(url))
	    {
		    www.SetRequestHeader("xi-api-key", m_apiKey);

		    yield return www.SendWebRequest();

		    if (www.result == UnityWebRequest.Result.ConnectionError || 
		        www.result == UnityWebRequest.Result.ProtocolError)
		    {
			    Debug.LogError("Error: " + www.error);
		    }
		    else
		    {
			    string json = www.downloadHandler.text;
			    Debug.Log("Voices response: " + json);

			    VoicesResponse voicesResponse = JsonUtility.FromJson<VoicesResponse>(json);

			    if (voicesResponse != null && voicesResponse.voices != null)
			    {
				    m_voices = voicesResponse;
				    _voicesReturned?.Invoke(m_voices.voices);
				    foreach (var voice in voicesResponse.voices)
				    {
					    Debug.Log($"Voice: {voice.name} (ID: {voice.voice_id}, Category: {voice.category})");
				    }
			    }
			    else
			    {
				    Debug.LogWarning("Failed to parse voices.");
			    }
		    }
	    }
    }
    public int selectedIndex
    {
	    get
	    {
		    return m_voices.voices.FindIndex(x => x.name == m_voice);
	    }
	    set
	    {
		    if (value < 0 || value >= m_voices.voices.Count) return;
		    m_voice = m_voices.voices[value].name;
	    }
    }
}

#if UNITY_EDITOR
[CustomEditor(typeof(ElevenLabsTTS))]
public class ElevenLabsTTSEditor : Editor
{
	private void OnEnable()
	{
		ElevenLabsTTS e = target as ElevenLabsTTS;
		e.GetVoices((voices) =>
		{
			CreateInspectorGUI();
		});
	}

	public override void OnInspectorGUI()
	{
		base.OnInspectorGUI();
		
		ElevenLabsTTS e = target as ElevenLabsTTS;
		if (e.m_voices != null && e.m_voices.voices != null && e.m_voices.voices.Count > 0)
		{
				// Create a dropdown
			e.selectedIndex = EditorGUILayout.Popup(
				"Choose Voice",
				e.selectedIndex,
				e.m_voices.voices.ConvertToStringList(x => x.name).ToArray()
			);
			
			// Save changes to scene
			if (GUI.changed)
			{
				EditorUtility.SetDirty(e);
			}
		}
		else
		{
			EditorGUILayout.HelpBox("Options list is empty!", MessageType.Warning);
		}
		
		if (GUILayout.Button("Download Voices"))
		{
			e.GetVoices((voices) =>
			{
				CreateInspectorGUI();
			});
		}
		if (GUILayout.Button("Reset Voice Settings To default Voices"))
		{
			e.m_customVoiceSettings = new ElevenLabsTTS.RequestData.VoiceSettings();
		}
		// if (GUILayout.Button("Speak"))
		// {
		// 	e.StartCoroutine(e.Speak((newClip) =>
		// 	{
		// 		AudioSource a = e.gameObject.AddComponent<AudioSource>();
		// 		a.clip = newClip;
		// 		a.Play();
		// 	}, e.textToSpeak));
		// }
	}
}
#endif