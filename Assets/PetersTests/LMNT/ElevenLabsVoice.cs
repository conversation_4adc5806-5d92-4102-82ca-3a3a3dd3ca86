using System;
using System.Collections;
using UnityEngine;
using UnityEngine.Networking;

[RequireComponent(typeof(ElevenLabsTTS))]
public class ElevenLabsVoice : MonoBehaviour
{
    [NonSerialized]
    public string m_characterName;
    [NonSerialized]
    public bool m_talkNow;
    [NonSerialized]
    public bool m_forceGenerateFile;
    [NonSerialized]
    public string m_text;
    private float m_lastVolume = -1;
    private bool m_voiceActive = false; public bool VoiceActive => m_voiceActive;
    
    private AudioSource m_audioSource;
    private string m_apiKey;
    private System.Collections.Generic.List<ElevenLabsTTS.Voice> m_voiceList;
    private DownloadHandlerAudioClip m_handler;
    private string m_voice;

    public AudioSource GetAudioSource() { return m_audioSource; }
    
    private string LookupByName(string name)
    {
        const string c_fallbackVoiceID = "Vince";
        if (m_voiceList == null || m_voiceList.Count == 0) return c_fallbackVoiceID;
        var voice = m_voiceList.Find(v => v.name == name);
        if (voice == null)
        {
            Debug.LogError($"Couldn't find voice {name} in list of {m_voiceList.Count} voices");
            return c_fallbackVoiceID;
        }
        return voice.voice_id;
    }

    private ElevenLabsTTS m_tts = null;
    private void Awake()
    {
        m_tts = GetComponent<ElevenLabsTTS>();
    }

    void Start()
    {
        m_audioSource = gameObject.GetComponent<AudioSource>();
        if (m_audioSource == null) m_audioSource = gameObject.AddComponent<AudioSource>();
        m_voiceList = m_tts?.m_voices?.voices??new();
        if (m_voiceList == null || m_voiceList.Count == 0)
        {
            //Debug.LogError($"Error reading list of TTL voices");
        }
    }

    public void Play()
    {
        StartCoroutine(GetAndPlaySpeech());
    }
	
    void Update()
    {
        if (m_talkNow)
        {
            m_talkNow = false;
            Play();
        }
        var volume = AudioClipManager.Me.GetOverallVOVolume();
        if (volume.Nearly(m_lastVolume) == false)
        {
            m_lastVolume = volume;
            m_audioSource.volume = volume;
        }
    }
    
    public void SetSpeech(string _text)
    {
        m_text = _text.Replace(@"\n", " ");
        m_talkNow = true;
    }

    private static string s_accountInfo = null;
    
    private IEnumerator GetAndPlaySpeech()
    {
        if (m_text.IsNullOrWhiteSpace()) yield break;
        
        m_voiceActive = true;
        
        var cacheName = $"{m_characterName}_{m_text}".SanitiseFilename();
        if (cacheName.Length > 32) cacheName = cacheName[..32];
        cacheName = $"DebugAudioCache/{cacheName}_{m_text.GetHashCode():x}";
        var clip = Resources.Load<AudioClip>(cacheName);
        if (m_forceGenerateFile == false && clip != null)
        {
            m_forceGenerateFile = false;
            m_audioSource.clip = clip;
            m_audioSource.Play();
        }
        else
        {
             m_forceGenerateFile = false;
             m_voice = m_tts.m_voice;
            
             ElevenLabsTTS elevenLabsTts = GetComponent<ElevenLabsTTS>();
             if(elevenLabsTts == null)
             {
                elevenLabsTts = gameObject.AddComponent<ElevenLabsTTS>();
             }
             
            StartCoroutine(elevenLabsTts.Speak((newclip) =>
            {
                m_audioSource.clip = newclip;
                m_audioSource.Play();
#if UNITY_EDITOR
                AudioSaver.SaveWav($"Assets/Resources/{cacheName}.wav", m_audioSource.clip);
                Debug.LogError($"Audio clip {cacheName} generated, this should be pushed to Git as soon as possible.");
#endif
            }, m_text, m_voice));
            
            if (m_audioSource.clip == null)
            {
                var timeEstimate = MAMessage.GetTimeToRead(m_text);
                yield return WaitWithSkip(timeEstimate);
                m_voiceActive = false;
                yield break;
            }
        }
        bool abandon = false;
        while (m_audioSource.isPlaying)
        {
            if (EKeyboardFunction.SkipDialogue.AnyClicked())
            {
                m_audioSource.Stop();
                abandon = true;
                break;
            }
            yield return null;
        }
        if (abandon == false)
            yield return WaitWithSkip(.5f);
        m_voiceActive = false;
    }

    IEnumerator WaitWithSkip(float _time)
    {
        for (float t = 0; t < _time; t += Time.deltaTime)
        {
            if (EKeyboardFunction.SkipDialogue.AnyClicked()) break;
            yield return null;
        }
    }
}
