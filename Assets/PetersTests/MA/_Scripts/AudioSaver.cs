using System.IO;
using UnityEngine;

public static class AudioSaver
{
    public static void SaveWav(string filename, AudioClip clip)
    {
        if (clip == null)
        {
            Debug.LogError("Cannot save null AudioClip.");
            return;
        }

        byte[] wavData = ConvertAudioClipToWav(clip);
        string path = filename;
        File.WriteAllBytes(path, wavData);
        Debug.Log("Audio saved at: " + path);
    }

    private static byte[] ConvertAudioClipToWav(AudioClip clip)
    {
        int sampleCount = clip.samples * clip.channels;
        int frequency = clip.frequency;

        float[] samples = new float[sampleCount];
        clip.GetData(samples, 0);

        byte[] wavData = EncodeWAV(samples, clip.channels, frequency);
        return wavData;
    }

    private static byte[] EncodeWAV(float[] samples, int channels, int sampleRate)
    {
        int headerSize = 44;
        int dataSize = samples.Length * 2;
        byte[] wavFile = new byte[headerSize + dataSize];

        using (MemoryStream stream = new MemoryStream(wavFile))
        using (BinaryWriter writer = new BinaryWriter(stream))
        {
            writer.Write(System.Text.Encoding.UTF8.GetBytes("RIFF"));
            writer.Write(36 + dataSize);
            writer.Write(System.Text.Encoding.UTF8.GetBytes("WAVE"));
            writer.Write(System.Text.Encoding.UTF8.GetBytes("fmt "));
            writer.Write(16);
            writer.Write((short)1);
            writer.Write((short)channels);
            writer.Write(sampleRate);
            writer.Write(sampleRate * channels * 2);
            writer.Write((short)(channels * 2));
            writer.Write((short)16);
            writer.Write(System.Text.Encoding.UTF8.GetBytes("data"));
            writer.Write(dataSize);

            foreach (float sample in samples)
            {
                short intSample = (short)(sample * short.MaxValue);
                writer.Write(intSample);
            }
        }
        return wavFile;
    }
}

namespace Test
{
    public class AudioSaverTest : MonoBehaviour
    {
        public AudioClip clip;

        public void Start()
        {
            AudioSaver.SaveWav("test", clip);
            VUT.Test();
        }
    }
}

public static class VUT
{
    static public void  Test()
    {
        
    }
}
