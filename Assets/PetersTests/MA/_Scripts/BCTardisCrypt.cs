using UnityEngine;

public class BCTardisCrypt : BCActionBase
{
	public override bool Arrive(MACharacterBase _character)
	{
		bool arrived = base.Arrive(_character);
		if (_character.m_destinationMABuilding != m_building) return false;
		bool canTargetThis = _character.CreatureInfo.ComponentTargets.Contains(MAComponentInfo.GetInfoByClass(typeof(BCTardisCrypt)));
		return arrived || canTargetThis;
	}

	public void ShowHealthBar()
	{
		if (m_building.Health < DefenseValueMax)
		{
			
		}
	}
}
