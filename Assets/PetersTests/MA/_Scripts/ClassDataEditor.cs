#if UNITY_EDITOR
using System;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using UnityEditor;
using UnityEngine;

public class ClassDataEditor : EditorWindow
{
    private Type[] availableClasses; // Stores all detected classes
    private Type selectedClass;      // Currently selected class
    private IList dataList;          // List of data items for the selected class
    private Vector2 scrollPos;       // For scrolling the UI
    private Dictionary<object, bool> foldoutStates = new Dictionary<object, bool>(); // Track foldout states

    private string searchString = ""; // User-entered search string
    private List<object> filteredItems = new List<object>(); // Filtered list of items
    [MenuItem("22Cans/Debug Windows/Knack Class Data Editor")]
    public static void ShowWindow()
    {
        GetWindow<ClassDataEditor>("Class Data Editor");
    }

    private void OnEnable()
    {
        // Find all classes with the required static List property
        NGKnack.SetEditorMe();
        List<Type> validClasses = new List<Type>();
        for (var i = 0; i < NGKnack.Me.m_importKnacks.Count; i++)
        {
            var k = NGKnack.Me.m_importKnacks[i];
            var tClass = Type.GetType(k.m_class);
            if (tClass == null)
            {
                Debug.LogError($"Class {k.m_class}[{i}] not found.");
                continue;
            }

            var gl = tClass.GetProperty("GetList", BindingFlags.Public | BindingFlags.Static);
            if (gl == null)
            {
                Debug.LogError($"Class {k.m_class}[{i}] does not have a static 'GetList' property.");
                continue;
            }

            var dl = tClass.GetProperty("DebugDisplayName", BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
            if (dl == null)
            {
                Debug.LogError($"Class {k.m_class}[{i}] does not have a static 'DebugDisplayName' property.");
            }
            var id = tClass.GetField("id", BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
            if (id == null)
            {
                Debug.LogError($"Class {k.m_class}[{i}] does not have a field 'id' field.");
            }
            var changes = tClass.GetField("m_debugChanged", BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
            if(changes == null)
            {
                Debug.LogError($"Class {k.m_class}[{i}] does not have a field 'm_debugChanged' field.");
            }
            validClasses.Add(tClass);
        }
        availableClasses = validClasses.OrderBy(t =>
        {
            return t.Name; // Default to class name if DebugDisplayName is missing
        }).ToArray();

    }
    private void OnGUI()
{
    EditorGUILayout.LabelField("Class Data Editor", EditorStyles.boldLabel);

    // Dropdown to select a class
    if (availableClasses != null && availableClasses.Length > 0)
    {
        string[] classNames = availableClasses.Select(t => t.Name).ToArray();
        int selectedIndex = Array.IndexOf(availableClasses, selectedClass);
        int newIndex = EditorGUILayout.Popup("Select Class", selectedIndex, classNames);

        if (newIndex >= 0 && newIndex < availableClasses.Length)
        {
            if (selectedClass != availableClasses[newIndex])
            {
                selectedClass = availableClasses[newIndex];
                LoadDataList();
            }
        }
    }
    else
    {
        EditorGUILayout.LabelField("No classes found with a static 'GetList' property.");
        return;
    }

    if (dataList == null) return;

    // Search Bar
    string newSearchString = EditorGUILayout.TextField("Search:", searchString);
    if (newSearchString != searchString)
    {
        searchString = newSearchString;
        ApplySearchFilter();
    }

    int modifiedCount = 0; // Count of modified items

    // Button to add a blank entry
    if (GUILayout.Button("Add New Entry"))
    {
        AddNewEntry();
    }

    // Ensure UI only shows filtered items
    //List<object> itemsToDisplay = filteredItems.Count > 0 || !string.IsNullOrWhiteSpace(searchString) 
    //    ? filteredItems 
    //    : new List<object>(dataList.Cast<object>());
    List<object> itemsToDisplay = filteredItems;
    // Scrollable area for editing data
    scrollPos = EditorGUILayout.BeginScrollView(scrollPos);

    List<object> itemsToDelete = new List<object>(); // Track items marked for deletion

    foreach (var item in itemsToDisplay)
    {
        string displayName = GetItemDisplayName(item, dataList.IndexOf(item)); // Get name for display

        // Check if the item has m_debugChanged field and its value is true
        bool isModified = false;
        var changesFlag = item.GetType().GetField("m_debugChanged", BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
        if (changesFlag != null)
        {
            object value = changesFlag.GetValue(item);
            if (value is bool changed && changed)
            {
                isModified = true;
                modifiedCount++;
            }
        }

        // Style for bold text if modified
        GUIStyle foldoutStyle = new GUIStyle(EditorStyles.foldout);
        if (isModified)
        {
            foldoutStyle.fontStyle = FontStyle.Bold;
        }

        // Ensure foldoutStates entry exists using item ID instead of reference (fixing persistent UI issue)
        string itemID = GetSortableName(item);
        if (!foldoutStates.ContainsKey(itemID))
        {
            foldoutStates[itemID] = false; // Default collapsed
        }

        // Display foldout with custom style
        foldoutStates[itemID] = EditorGUILayout.Foldout(foldoutStates[itemID], displayName, true, foldoutStyle);

        if (foldoutStates[itemID])
        {
            EditorGUI.indentLevel++;

            // Show Delete button
            EditorGUILayout.BeginHorizontal();
            GUILayout.Space(20); // Indent button for alignment
            if (GUILayout.Button("Delete", GUILayout.Width(80)))
            {
                itemsToDelete.Add(item);
            }
            EditorGUILayout.EndHorizontal();

            DisplayAndEditFields(item);
            EditorGUI.indentLevel--;
        }

        EditorGUILayout.Space();
    }

    EditorGUILayout.EndScrollView();

    // Remove marked items
    if (itemsToDelete.Count > 0)
    {
        foreach (var item in itemsToDelete)
        {
            DeleteEntry(item);
        }
    }

    // Display Save Changes button with modified count
    if (GUILayout.Button($"Save Changes ({modifiedCount})"))
    {
        SaveChanges();
    }
}
    
    private void AddNewEntry()
    {
        if (selectedClass == null)
        {
            Debug.LogError("No class selected.");
            return;
        }

        object newEntry = Activator.CreateInstance(selectedClass);
        if (newEntry == null)
        {
            Debug.LogError($"Failed to create instance of {selectedClass.Name}");
            return;
        }

        var changesFlag = selectedClass.GetField("m_debugChanged", BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
        if (changesFlag != null)
        {
            changesFlag.SetValue(newEntry, true);
        }
        else
        {
            Debug.LogWarning($"Field 'm_debugChanged' not found in {selectedClass.Name}");
        }

        // Add the new entry to the data list
        dataList.Add(newEntry);
        filteredItems.Add(newEntry);

        SortItems(); // Sort after adding a new item

        foldoutStates[newEntry] = false;

        Debug.Log($"Added new entry to {selectedClass.Name}");
    }


    private void LoadDataList()
    {
        if (selectedClass == null)
        {
            Debug.LogError("No class selected.");
            return;
        }

        var getListProperty = selectedClass.GetProperty("GetList", BindingFlags.Public | BindingFlags.Static);
        if (getListProperty == null)
        {
            Debug.LogError($"Class {selectedClass.Name} does not have a static 'GetList' property.");
            return;
        }

        dataList = getListProperty.GetValue(null) as IList;
        if (dataList == null)
        {
            Debug.LogError($"Property 'GetList' in class {selectedClass.Name} is null.");
            return;
        }

        if (dataList.Count == 0)
        {
            var LoadInfoMethod = selectedClass.GetMethod("LoadInfo", BindingFlags.Public | BindingFlags.Static);
            if (LoadInfoMethod != null)
            {
                LoadInfoMethod.Invoke(null, null);
                dataList = getListProperty.GetValue(null) as IList;
            }
        }

        // Convert to list for sorting
        filteredItems = new List<object>(dataList.Cast<object>());
        SortItems(); // Sort after loading data
    }
    private void SortItems()
    {
        if (filteredItems == null || filteredItems.Count == 0)
            return;

        filteredItems.Sort((a, b) =>
        {
            string nameA = GetSortableName(a);
            string nameB = GetSortableName(b);

            return string.Compare(nameA, nameB, StringComparison.OrdinalIgnoreCase);
        });
    }
    private string GetSortableName(object item)
    {
        if (item == null) return "";

        var idField = item.GetType().GetField("m_id", BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
        if (idField != null)
        {
            object idValue = idField.GetValue(item);
            if (idValue != null)
            {
                return idValue.ToString();
            }
        }

        var displayNameProperty = item.GetType().GetProperty("DebugDisplayName", BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
        if (displayNameProperty != null)
        {
            object displayNameValue = displayNameProperty.GetValue(item);
            if (displayNameValue != null)
            {
                return displayNameValue.ToString();
            }
        }

        return "Unknown";
    }
    private void ApplySearchFilter()
    {
        if (dataList == null) return;

        if (string.IsNullOrWhiteSpace(searchString))
        {
            // Reset to all items if search is cleared
            filteredItems = new List<object>(dataList.Cast<object>());
            return;
        }

        string searchLower = searchString.ToLower();

        filteredItems = dataList.Cast<object>().Where(item =>
        {
            if (item == null) return false;

            // Get the DebugDisplayName property
            var debugDisplayNameProp = item.GetType().GetProperty("DebugDisplayName", BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
            if (debugDisplayNameProp != null)
            {
                string displayName = debugDisplayNameProp.GetValue(item)?.ToString();
                if (!string.IsNullOrWhiteSpace(displayName) && displayName.ToLower().Contains(searchLower))
                {
                    return true;
                }
            }

            return false;
        }).ToList();
    }
    
    private string GetItemDisplayName(object dataItem, int index)
    {
        if (dataItem == null) return $"Item {index + 1}";

        // Check for a property named DebugDisplayName
        var displayNameProperty = dataItem.GetType().GetProperty("DebugDisplayName", BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
        if (displayNameProperty != null)
        {
            object displayNameValue = displayNameProperty.GetValue(dataItem);
            if (displayNameValue != null && !string.IsNullOrWhiteSpace(displayNameValue.ToString()))
            {
                return displayNameValue.ToString();
            }
        }

        // Fallback to default naming
        return $"Item {index + 1}";
    }

    private void DisplayAndEditFields(object dataItem)
    {
        if (dataItem == null) return;

        var fields = dataItem.GetType().GetFields(BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
        var changesFlag = dataItem.GetType().GetField("m_debugChanged", BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
        foreach (var field in fields)
        {
            object fieldValue = field.GetValue(dataItem);
            object newValue = DrawFieldEditor(field.Name, fieldValue, field.FieldType, dataItem);

            if (!Equals(fieldValue, newValue))
            {
                Undo.RecordObject(this, "Edit Field");
                field.SetValue(dataItem, newValue);
                if(changesFlag != null)
                    changesFlag.SetValue(dataItem, true);
                if(dataItem is MAComponentInfo && field.Name == "m_field" && GameManager.Me != null && GameManager.Me.LoadComplete)
                {
                    var ci = dataItem as MAComponentInfo;
                    MAComponentInfo.SetupField(ci.m_name, newValue as string);
                    foreach (var building in NGManager.Me.m_maBuildings)
                    {
                        var ff = building.BuildingComponents<BCBase>();
                        foreach (var f in building.m_components)
                        {
                            if(f.m_info == ci)
                                f.Setup(ci);
                        }
                    }
                }
            }
        }
    }
    private Dictionary<string, string> textFieldBuffers = new Dictionary<string, string>(); // Store temporary values
    private object DrawFieldEditor(string fieldName, object fieldValue, Type fieldType, object dataItem)
    {
        object newValue = fieldValue;

        if (fieldType == typeof(string))
        {
            string currentValue = fieldValue as string ?? "";

            bool useTextArea = currentValue.Length > 50 || currentValue.Contains("\n");

            if (useTextArea)
            {
                // Calculate height: ~12px per 50 chars, min 60, max 200
                int approxLines = Mathf.CeilToInt(currentValue.Length / 50f);
                int height = Mathf.Clamp(approxLines * 12, 60, 600);

                EditorGUILayout.LabelField(fieldName);
                newValue = EditorGUILayout.TextArea(currentValue, GUILayout.MinHeight(height));
            }
            else
            {
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField(fieldName, GUILayout.MaxWidth(150));
                newValue = EditorGUILayout.TextField(currentValue);
                EditorGUILayout.EndHorizontal();
            }
        }
        else
        {
            newValue = DrawFieldEditorOld1(fieldName, fieldValue, fieldType);
        }

        return newValue;
    }
    private object DrawFieldEditorOld(string fieldName, object fieldValue, Type fieldType, object dataItem)
    {
        object newValue = fieldValue;
        string itemID = $"{GetSortableName(dataItem)}_{fieldName}"; // Unique key per field

        if (fieldType == typeof(string))
        {
            // Ensure the field value is treated as a string
            string currentValue = fieldValue as string ?? "";

            // Initialize buffer if missing
            if (!textFieldBuffers.ContainsKey(itemID))
            {
                textFieldBuffers[itemID] = currentValue;
            }

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField(fieldName, GUILayout.MaxWidth(150));

            // Capture input but don't apply it immediately
            textFieldBuffers[itemID] = EditorGUILayout.TextField(textFieldBuffers[itemID]);

            // Detect Enter key press
            Event e = Event.current;
            if (e.isKey && e.type == EventType.KeyDown && e.keyCode == KeyCode.Return)
            {
                newValue = textFieldBuffers[itemID]; // Apply only on Enter
                Undo.RecordObject(this, "Edit Field");
            }

            EditorGUILayout.EndHorizontal();
        }
        else
        {
            // Use the original logic for other types
            newValue = DrawFieldEditorOld1(fieldName, fieldValue, fieldType);
        }

        return newValue;
    }
    private object DrawFieldEditorOld(string fieldName, object fieldValue, Type fieldType)
{
    object newValue = fieldValue;

    // Make m_id and m_debugChanged read-only
    if (fieldName == "id" || fieldName == "m_debugChanged")
    {
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField(fieldName, GUILayout.MaxWidth(150));
        EditorGUILayout.LabelField(fieldValue?.ToString() ?? "N/A"); // Read-only value
        EditorGUILayout.EndHorizontal();
        return newValue;
    }

    if (fieldType == typeof(int))
    {
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField(fieldName, GUILayout.MaxWidth(150));
        newValue = EditorGUILayout.IntField((int)fieldValue);
        EditorGUILayout.EndHorizontal();
    }
    else if (fieldType == typeof(float))
    {
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField(fieldName, GUILayout.MaxWidth(150));
        newValue = EditorGUILayout.FloatField((float)fieldValue);
        EditorGUILayout.EndHorizontal();
    }
    else if (fieldType == typeof(string))
    {
        string strValue = fieldValue as string ?? "";

        if (strValue.Contains("\n")) // Detect multi-line content
        {
            EditorGUILayout.LabelField(fieldName); // Display label separately for multi-line input
            newValue = EditorGUILayout.TextArea(strValue, GUILayout.Height(60)); // Multi-line input
        }
        else
        {
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField(fieldName, GUILayout.MaxWidth(150));
            newValue = EditorGUILayout.TextField(strValue); // Single-line input
            EditorGUILayout.EndHorizontal();
        }
    }
    else if (fieldType == typeof(bool))
    {
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField(fieldName, GUILayout.MaxWidth(150));
        newValue = EditorGUILayout.Toggle((bool)fieldValue);
        EditorGUILayout.EndHorizontal();
    }
    else if (fieldType.IsEnum)
    {
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField(fieldName, GUILayout.MaxWidth(150));
        newValue = EditorGUILayout.EnumPopup((Enum)fieldValue);
        EditorGUILayout.EndHorizontal();
    }
    else
    {
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField(fieldName, GUILayout.MaxWidth(150));
        EditorGUILayout.LabelField($"Unsupported Type: {fieldType.Name}");
        EditorGUILayout.EndHorizontal();
    }

    return newValue;
}
    private object DrawFieldEditorOld1(string fieldName, object fieldValue, Type fieldType)
    {
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField(fieldName, GUILayout.MaxWidth(150));

        object newValue = fieldValue;

        if (fieldType == typeof(int))
        {
            newValue = EditorGUILayout.IntField((int)fieldValue);
        }
        else if (fieldType == typeof(float))
        {
            newValue = EditorGUILayout.FloatField((float)fieldValue);
        }
        else if (fieldType == typeof(string))
        {
            newValue = EditorGUILayout.TextField((string)fieldValue);
        }
        else if (fieldType == typeof(bool))
        {
            newValue = EditorGUILayout.Toggle((bool)fieldValue);
        }
        else if (fieldType.IsEnum)
        {
            newValue = EditorGUILayout.EnumPopup((Enum)fieldValue);
        }
        else
        {
            EditorGUILayout.LabelField($"Unsupported Type: {fieldType.Name}");
        }

        EditorGUILayout.EndHorizontal();
        return newValue;
    }
    private void DeleteEntry(object item)
    {
        if (dataList == null || item == null)
        {
            Debug.LogError("Cannot delete: Data list is null or item is null.");
            return;
        }

        if (dataList.Contains(item))
        {
            dataList.Remove(item);
        }
    
        if (filteredItems.Contains(item))
        {
            filteredItems.Remove(item);
        }

        if (foldoutStates.ContainsKey(item))
        {
            foldoutStates.Remove(item);
        }
        var dType =item.GetType();
        if (dType != null)
        {
            var id = dType.GetField("id", BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
            if (id != null)
            {
                var idValue = id.GetValue(item);
                if (idValue != null && string.IsNullOrWhiteSpace(idValue.ToString()) == false)
                {
                    NGKnack.KnackDeleteWholeRecord(dType, idValue.ToString());
                    Debug.Log($"Deleted entry from {selectedClass.Name}");
                }
            }            
        }
    }
    private void SaveChanges()
    {
        if(filteredItems == null || filteredItems.Count == 0)
        {
            Debug.Log("No changes to save.");
            return;
        }

        Type changedType = null;
        foreach (var item in filteredItems)
        {
            var changesFlag = item.GetType().GetField("m_debugChanged", BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
            if (changesFlag != null && (bool)changesFlag.GetValue(item))
            {
                NGKnack.GetKnackPutWholeRecord(item);
                changesFlag.SetValue(item, false);
                changedType = item.GetType();
            }
        }
        if(changedType != null)
            NGKnack.CacheKnack(changedType);
    }
}
#endif // UNITY_EDITOR
