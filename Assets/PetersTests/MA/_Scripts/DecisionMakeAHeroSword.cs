using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

public class DecisionMakeAHeroSword
{
    public class DecisionStep
    {
        public string m_text;
        public Steps m_step;
        public bool m_isComplete = false;

        public void Complete()
        {
            m_text = $"<color=grey>{m_text}<sprite=6></color>";
            m_isComplete = true;
        }
    }
    public List<DecisionStep> m_steps = new ();
    public NGBusinessDecision m_decision;
    
    public enum Steps
    {
        DesignMakeAHeroSword,
        ActivateBeacon,
        RebuildWyrmscar,
        BuildMine,
        BuildSmelter,
        BuildFactory,
        BuildDispatch,
        Build3Houses,
        BuildTavern,
        Hire3Workers,
        MakeAndEquipWeapon,
        UnlockWeaponSmith,
        BuildWeaponSmith,
        DesignWeapon,
        Done
    }
        /*
         "Design, Make, Equip A Hero Sword",
        ">Activate Wyrmscar Beacon",
        ">Rebuild Wyrmscar",
        ">>Rebuild Mine",
        ">>Rebuild Smelter",
        ">>Rebuild Factory",
        ">>Rebuild 3 Houses",
        ">>Rebuild Tavern",
        ">Make and Equip Hero Weapon",
        ">>Unlock Weaponsmith in Arcadium",
        ">>Design A Hero Weapon",
        */
    public DecisionMakeAHeroSword(NGBusinessDecision _decision)
    {
        m_decision = _decision;
        var texts = m_decision.m_explainText.Split('\n');
        for (int i = 0; i < texts.Length; i++)
        {
            var step = new DecisionStep() { m_text = texts[i].Replace(">", "  "), m_step = (Steps)i };
            m_steps.Add(step);
        }
    }

    public string GetDisplayText()
    {
        string result = "";
        foreach (var step in m_steps)
            result += step.m_text + "\n";
        result = result.TrimEnd('\n');
        return result;
    }

    public float Update()
    { 
        bool allComplete = true;
        foreach (var step in m_steps)
        {
            if (step.m_isComplete) continue;
            allComplete = false;
            switch (step.m_step)
            {
                case Steps.DesignMakeAHeroSword:
                    step.m_isComplete = true;
                    break;
                case Steps.RebuildWyrmscar:
                    if(m_steps[(int)Steps.ActivateBeacon].m_isComplete &&
                        m_steps[(int)Steps.BuildMine].m_isComplete &&
                        m_steps[(int)Steps.BuildSmelter].m_isComplete &&
                        m_steps[(int)Steps.BuildFactory].m_isComplete &&
                        m_steps[(int)Steps.Build3Houses].m_isComplete &&
                        m_steps[(int)Steps.BuildTavern].m_isComplete)
                    {
                        step.Complete();
                    }
                    break;
                case Steps.ActivateBeacon:
                    if (DistrictManager.Me.IsWithinDistrictBounds(new Vector3(-445.25f, 122.41f, -159.29f), true))
                    {
                        step.Complete();
                    }
                    break;
                case Steps.BuildMine:
                    if (IsMineFinished())
                    {
                        step.Complete();
                    }
                    break;
                case Steps.BuildSmelter:
                    if (IsSmelterFinished())
                    {
                        step.Complete();
                    }
                    break;
                case Steps.BuildFactory:
                    if (IsFactoryFinished())
                    {
                        step.Complete();
                    }
                    break;
                case Steps.BuildDispatch:
                    if(IsDispatchFinished())
                    {
                        step.Complete();
                    }
                    break;
                case Steps.Build3Houses:
                    if (IsHousesFinished())
                    {
                        step.Complete();
                    }
                    break;
                case Steps.BuildTavern:
                    if (IsTavernFinished())
                    {
                        step.Complete();
                    }
                    break;
                case Steps.Hire3Workers:
                    if (IsHired3Workers())
                    {
                        step.Complete();
                    }
                    break;
/*                case Steps.MakeAndEquipWeapon:
                    if (m_steps[(int) Steps.UnlockWeaponSmith].m_isComplete &&
                        m_steps[(int) Steps.BuildWeaponSmith].m_isComplete &&
                        m_steps[(int) Steps.DesignWeapon].m_isComplete)
                    {
                        step.Complete();
                    }
                    break;
                case Steps.UnlockWeaponSmith:
                    if (IsWeaponSmithUnlocked())
                    {
                        step.Complete();
                    }
                    break;
                case Steps.BuildWeaponSmith:
                    if(IsWeaponSmithBuilt())
                    {
                        step.Complete();
                    }
                    break;
                case Steps.DesignWeapon:
                    if (IsWeaponDesigned())
                    {
                        step.Complete();
                    }

                    break;*/
                case Steps.Done:
                    return 1;
                default:
                    step.m_isComplete = true;
                    break;
            }
        }
        return (allComplete) ? 0 : 1;
    }

    bool IsMineFinished()
    {
        MABuilding mineBuilding = null;
        var mineInfo = MAComponentInfo.GetInfo("ActionMineMetal");
        foreach (var b in NGManager.Me.m_maBuildings)
        {
            if (b == null) continue;
            if(b.HasComponentWithInfo(mineInfo))
            {
                mineBuilding = b; 
                break; 
            }
        }

        if (mineBuilding == null)
            return false;
        if (mineBuilding.HasValidEntrance() == false)
            return false;
        var smelterInfo = MAComponentInfo.GetInfo("ActionMetalSmelter");

        if (mineBuilding.HasComponentWithInfo(smelterInfo) == false)
        {
            if (mineBuilding.StockOuts.Count == 0)
                return false;
        }

        return true;
    }

    bool IsSmelterFinished()
    {
        MABuilding smelterBuilding = null;
        var smelterInfo = MAComponentInfo.GetInfo("ActionMetalSmelter");
        foreach (var b in NGManager.Me.m_maBuildings)
        {
            if (b == null) continue;
            if(b.HasComponentWithInfo(smelterInfo))
            {
                smelterBuilding = b; 
                break; 
            }
        }

        if (smelterBuilding == null)
            return false;
        if (smelterBuilding.HasValidEntrance() == false)
            return false;
        var factoryInfo = MAComponentInfo.GetInfo("ActionFactory");

        if (smelterBuilding.HasComponentWithInfo(factoryInfo) == false)
        {
            if (smelterBuilding.StockOuts.Count == 0)
                return false;
        }
        var mineInfo = MAComponentInfo.GetInfo("ActionMineMetal");
        if (smelterBuilding.HasComponentWithInfo(mineInfo) == false)
        {
            if (smelterBuilding.StockIns.Count == 0)
                return false;            
        }        
        return true;
    }
    bool IsFactoryFinished()
    {
        MABuilding factoryBuilding = null;
        var factoryInfo = MAComponentInfo.GetInfo("ActionFactory");
        foreach (var b in NGManager.Me.m_maBuildings)
        {
            if (b == null) continue;
            if(b.HasComponentWithInfo(factoryInfo) && b.IsInDistrict("district4"))
            {
                factoryBuilding = b; 
                break; 
            }
        }

        if (factoryBuilding == null)
            return false;
        if (factoryBuilding.HasValidEntrance() == false)
            return false;
        var smelterInfo = MAComponentInfo.GetInfo("ActionMetalSmelter");

        if (factoryBuilding.HasComponentWithInfo(smelterInfo) == false)
        {
            if (factoryBuilding.StockIns.Count == 0)
                return false;
        }
        
        var dispatchInfo = MAComponentInfo.GetInfo("ActionMetalDispatch");
        if (factoryBuilding.HasComponentWithInfo(dispatchInfo) == false)
        {
            if (factoryBuilding.StockOuts.Count == 0)
                return false;            
        }        
        return true;
    }
    bool IsDispatchFinished()
    {
        MABuilding dispatchBuilding = null;
        var dispatchInfo = MAComponentInfo.GetInfo("ActionMetalDispatch");
        foreach (var b in NGManager.Me.m_maBuildings)
        {
            if (b == null) continue;
            if(b.HasComponentWithInfo(dispatchInfo) )
            {
                if (b.IsInDistrict("district4"))
                {
                    dispatchBuilding = b; 
                    break; 
                }
            }
        }

        if (dispatchBuilding == null)
            return false;
        if (dispatchBuilding.HasValidEntrance() == false)
            return false;
        return true;
    }


    bool IsHousesFinished()
    {
        var bedroomCount = 0;
        foreach (var b in NGManager.Me.m_maBuildings)
        {
            if (b.IsInDistrict("district4") == false) continue;
            if (b.HasValidEntrance() == false) continue;
            bedroomCount+=b.GetBuildingComponentCount(typeof(BCBedroom), true);
            if (bedroomCount >= 3)
                return true;
        }

        return false;
    }
    bool IsTavernFinished()
    {
        var bedroomCount = 0;
        foreach (var b in NGManager.Me.m_maBuildings)
        {
            if (b.IsInDistrict("district4") == false) continue;
            if (b.HasValidEntrance() == false) return false;
            if (b.HasBuildingComponent(typeof(BCActionTavern))) 
                return true;
        }
        return false;
    }

    bool IsHired3Workers()
    {
        var factoryInfo = MAComponentInfo.GetInfo("ActionFactory");
        var smelterInfo = MAComponentInfo.GetInfo("ActionMetalSmelter");
        var mineInfo = MAComponentInfo.GetInfo("ActionMineMetal");
        bool gotFactory = false;
        bool gotSmelter = false;
        bool gotMine = false;
        foreach (var b in NGManager.Me.m_maBuildings)
        {
            //CheckFactory
            if (b.HasComponentWithInfo(factoryInfo) && b.IsInDistrict("district4"))
            {
                gotFactory = true;
                var factoryActionComponents = b.GetComponentsWithInfo(factoryInfo);
                if (factoryActionComponents.Count() == 0)
                    return false;
                var totalFactoryWorkers = 0;
                foreach (var fc in factoryActionComponents)
                {
                    if (fc == null) continue;
                    totalFactoryWorkers += fc.GetWorkersAllocated().Count();
                }

                if (totalFactoryWorkers < 1)
                    return false;
            }

            //CheckSmelter
            if (b.HasComponentWithInfo(smelterInfo))
            {
                gotSmelter = true;
                var smelterActionComponents = b.GetComponentsWithInfo(smelterInfo);
                if (smelterActionComponents.Count() == 0)
                    return false;
                var totalSmelterWorkers = 0;
                foreach (var sc in smelterActionComponents)
                {
                    if (sc == null) continue;
                    totalSmelterWorkers += sc.GetWorkersAllocated().Count();
                }

                if (totalSmelterWorkers < 1)
                    return false;
            }

            //CheckMine
            if (b.HasComponentWithInfo(mineInfo))
            {
                gotMine = true;
                var mineActionComponents = b.GetComponentsWithInfo(mineInfo);
                if (mineActionComponents.Count() == 0)
                    return false;
                var totalMineWorkers = 0;
                foreach (var sc in mineActionComponents)
                {
                    if (sc == null) continue;
                    totalMineWorkers += sc.GetWorkersAllocated().Count();
                }

                if (totalMineWorkers < 1)
                    return false;
            }
        }
        if(gotFactory && gotSmelter && gotMine)
            return true;
        return false;
    }

    bool IsWeaponSmithUnlocked()
    {
        var researchInfo = MAResearchInfo.GetInfo("BuildWeaponsmith");
        if (researchInfo == null)
        {
            MAParser.ParserError($"No such research item field as BuildWeaponsmith");
            return false;
        }

        return researchInfo.IsAcquired;
    }
    bool IsWeaponSmithBuilt()
    {
        MABuilding weaponSmithBuilding = null;
        var weaponSmithInfo = MAComponentInfo.GetInfo("ActionWeaponsmith");
        foreach (var b in NGManager.Me.m_maBuildings)
        {
            if (b == null) continue;
            if (b.HasBuildingComponent<BCActionWeaponsmith>() == false) continue;
            if (b.HasValidEntrance() == false) continue;
            if (b.HasBuildingComponent<BCStockIn>() == false) continue;
            return true;
        }
        return false;
    }

    bool IsWeaponDesigned()
    {
        var confirms = ProductTestingManager.GetDesignConfirms("weapons");
        var trackedConfirms = MAParserManager.GetOrSetTrackValue(confirms);
        return confirms > trackedConfirms;
    }
}
