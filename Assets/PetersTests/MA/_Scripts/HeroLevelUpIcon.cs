using UnityEngine;

public class HeroLevelUpIcon : MonoBehaviour
{
    private MAHeroBase m_hero;
    public GameObject m_visualHolder;
    
    public static HeroLevelUpIcon Create(MAHeroBase _hero)
    {
        var existing = _hero.GetComponentInChildren<HeroLevelUpIcon>();
        if (existing == null)
        {
            var prefab = Resources.Load<HeroLevelUpIcon>("_Prefabs/UI/HeroLevelUpIcon");
            existing = Instantiate(prefab, _hero.transform);
            existing.m_hero = _hero;
        }
        return existing;
    }
    
    private void Update()
    {
        if(m_hero == null) return;
        bool visible = m_hero.m_state != NGMovingObject.STATE.MA_MOVE_TO_INSIDE_BUILDING && m_hero.m_state != NGMovingObject.STATE.MA_MOVE_TO_OUTSIDE_BUILDING;
        if(m_visualHolder.activeSelf != visible)
        {
            m_visualHolder.SetActive(visible);
        }
    }
}
