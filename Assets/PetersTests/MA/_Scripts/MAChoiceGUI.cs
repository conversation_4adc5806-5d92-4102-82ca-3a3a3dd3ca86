using System;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.Video;

public class MAChoiceGUI : MonoBehaviour
{
    public Image m_advisor;
    public Transform m_videoHolder;
    public VideoPlayer m_videoPlayer;
    public TMP_Text m_title;
    public TMP_Text m_explain;
    public RectTransform m_detailsHolder;
    protected Animator m_anim;
    protected int m_id;
    

   virtual protected void Start()
    {
        m_videoPlayer = GetComponentInChildren<VideoPlayer>();
        m_advisor = m_detailsHolder.Find("AdvisorHolder/AdvisorMask/Advisor").GetComponentInChildren<Image>();
        m_title = m_detailsHolder.Find("AdvisorHolder/Title").GetComponentInChildren<TMP_Text>();
        m_explain = m_detailsHolder.Find("ExplainBox").GetComponentInChildren<TMP_Text>();
        m_anim = GetComponentInChildren<Animator>();
        Showing(true);
    }

    bool m_refeshed = false;
    void Update()
    {
        if(m_refeshed == false)
        {
            m_refeshed = true;
            LayoutRebuilder.ForceRebuildLayoutImmediate(GetComponent<RectTransform>());
        }
    }
    public void Showing(bool _flag)
    {
        m_detailsHolder.gameObject.SetActive(_flag);
    }
    public void ReadyToOpen()
    {
        if (m_anim != null)
        {
            m_anim.SetBool("Activate", true);
        }       
    }
    public void DestroyMe()
    {
        Destroy(gameObject);
    }
    public void ClickedMe()
    {
        MAChoiceGUIManager.Me.ClickChoice(m_id);
    }

    void Activate(int _id, string _advisor, string _explain, string _video)
    {
        m_id = _id;
        LoadAndPlayVideo(_video);
        NGBusinessAdvisor advisor = NGBusinessAdvisor.GetInfo(_advisor);
        m_advisor.sprite = advisor.PortaitSprite;
        m_title.text = advisor.m_name;
        m_explain.text = _explain;
        LayoutRebuilder.ForceRebuildLayoutImmediate(GetComponent<RectTransform>());
    }
    public void LoadAndPlayVideo(string _videoName)
    {
        if (_videoName.IsNullOrWhiteSpace())
        {
            m_videoHolder.gameObject.SetActive(false);
            return;
        }
        var videoName = $"Videos/{_videoName}";
        var textureName = $"Videos/Choice{m_id}Texture";
        // Load the VideoClip from Resources
        
        var targetTexture = Resources.Load<RenderTexture>(textureName);
        var rawImage = m_videoPlayer.GetComponentInChildren<RawImage>();
        m_videoPlayer.targetTexture = targetTexture;
        rawImage.texture = targetTexture;
        /*VideoClip clip = Resources.Load<VideoClip>(videoName);

        if (clip == null)
        {
            Debug.LogError($"VideoClip 'name' not found in Resources folder!");
            return;
        }*/
        m_videoPlayer.Stop(); // Stop any currently playing video
        //m_videoPlayer.clip = clip;
        m_videoPlayer.source = VideoSource.Url;
        m_videoPlayer.url = System.IO.Path.Combine(Application.streamingAssetsPath, $"Video/{_videoName}.mp4");
        m_videoPlayer.SetDirectAudioMute(0, true);// Assign the new clip
        m_videoPlayer.Play(); // Play the new clip
    }
    public static MAChoiceGUI Create(int _id, string _advisor, string _explain, string _video, Transform _holder)
    {
        var prefab = Resources.Load<MAChoiceGUI>("_Prefabs/Dialogs/MAChoiceGUI");
        var instance = Instantiate(prefab, _holder);
        instance.Activate(_id, _advisor, _explain, _video);
        return instance;
    }
}
