using TMPro;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.Video;

public class MAChoiceGUIDialog : MAChoiceGUI
{
    public Image m_sprite;
    public TMP_Text m_question;
    override protected void Start()
    {
        m_sprite = transform.Find("BulletImage").GetComponentInChildren<Image>();
        m_question = transform.Find("Question").GetComponentInChildren<TMP_Text>();
        m_anim = GetComponentInChildren<Animator>();
    }
    
    void Activate(int _id, string _sprite, string _explain)
    {
        m_id = _id;
        if (_sprite.IsNullOrWhiteSpace() == false)
            m_sprite.sprite = Resources.Load<Sprite>(_sprite);
        else
            m_sprite.gameObject.SetActive(false);
        m_question.text = _explain;
    }

    public static MAChoiceGUIDialog Create(int _id, string _sprite, string _explain, Transform _holder)
    {
        var prefab = Resources.Load<MAChoiceGUIDialog>("_Prefabs/Dialogs/MAChoiceGUIDialog");
        var instance = Instantiate(prefab, _holder);
        instance.Activate(_id, _sprite, _explain);
        return instance;
    }
}
