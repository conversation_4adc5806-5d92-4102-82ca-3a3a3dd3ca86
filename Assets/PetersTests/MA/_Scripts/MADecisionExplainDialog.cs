using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class MADecisionExplainDialog : Mono<PERSON><PERSON><PERSON><MADecisionExplainDialog>
{
    public Transform m_advisorHolder;
    public Image m_advisorImage;
    public TMP_Text m_advisorName;
    public TMP_Text m_advisorTitle;
    public TMP_Text m_advisorMessage;
    void Activate(NGBusinessAdvisor _advisor, string _message)
    {
        m_advisorHolder.gameObject.SetActive(false);
        if (_advisor != null)
        {
            m_advisorHolder.gameObject.SetActive(true);
            m_advisorName.text = $"{_advisor.m_firstName} {_advisor.m_givenName}";
            m_advisorTitle.text = _advisor.m_title;
            m_advisorImage.sprite = _advisor.PortaitSprite;
        }
        
        if(_message.IsNullOrWhiteSpace() == false)
            m_advisorMessage.text = _message.ReplaceInputTokens();
        else
        {
            m_advisorMessage.text = "";
        }
    }

    public void ClickedClose()
    {
        Destroy(gameObject);
    }
    public static MADecisionExplainDialog Create(NGBusinessAdvisor _advisor, string _message)
    {
        if(Me != null)
            Destroy(Me.gameObject);
        var prefab = Resources.Load<MADecisionExplainDialog>("_Prefabs/Dialogs/MADecisionExplainDialog");
        var instance = Instantiate(prefab, NGManager.Me.m_centreScreenHolder);
        instance.Activate(_advisor, _message);
        return instance;
    }
}
