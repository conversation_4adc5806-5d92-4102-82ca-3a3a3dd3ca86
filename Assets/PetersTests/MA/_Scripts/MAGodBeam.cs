using UnityEngine;

public class MAGodBeam : MonoBehaviour
{
    public string m_name;
    public string m_type;
    void Activate(string _name, string _type)
    {
        m_name = _name;
        m_type = _type; 
        MAParserManager.Me.m_godBeams.Add(this); 
    }
    public void DestroyMe()
    {
        MAParserManager.Me.m_godBeams.Remove(this);
        Destroy(gameObject);
    }
    public static MAGodBeam Create(string _name, GameObject _overObject, string _type)
    {
        var alreadyHasOne = _overObject.GetComponentInChildren<MAGodBeam>();
        if (alreadyHasOne != null)
        {
            alreadyHasOne.DestroyMe();
        }
        var prefab = Resources.Load<MAGodBeam>($"_Prefabs/Effects/GodBeam{_type}");
        var instance = Instantiate(prefab, _overObject.transform);
        instance.Activate(_name, _type);
        return instance;
    }

    public static MAGodBeam Create(string _name, Vector3 _pos, string _type)
    {
        var prefab = Resources.Load<MAGodBeam>($"_Prefabs/Effects/GodBeam{_type}");
        var instance = Instantiate(prefab, _pos, Quaternion.identity);
        instance.Activate(_name, _type);
        return instance;
    }
}
