using System.Collections.Generic;
using UnityEngine;

[System.Serializable]
public class MAHarvest 
{
    private static UnlockState s_defaultUnlock = new UnlockState(0);
    private static Dictionary<string, UnlockState> m_unlockstates = new();
    
    public static UnlockState GetUnlockState(string _itemName)
    {
        if(m_unlockstates.TryGetValue(_itemName, out var result))
            return result;
        return s_defaultUnlock;
    }
    
    public class UnlockState
    {
        private List<int> m_harvestAmounts = new(); // index==level, value==amount
        private int m_unlockedAtLevel = 999;
        private string m_mAUnlockReference;
        
        public UnlockState(int _unlockedAtLevel)
        {
            m_unlockedAtLevel = _unlockedAtLevel;
        }
        
        public UnlockState() {}
        
        public void AddHarvestInfo(MAHarvest _info)
        {
            m_mAUnlockReference = _info.m_mAUnlockReference;
            m_unlockedAtLevel = Mathf.Min(m_unlockedAtLevel, _info.m_level);
            for(int level = 0; level <= _info.m_level; level++)
            {
                if(m_harvestAmounts.Count <= level)
                    m_harvestAmounts.Add(_info.m_amount);
                else if(_info.m_amount < m_harvestAmounts[level])
                    m_harvestAmounts[level] = _info.m_amount;  
            }
        }
        
        public int GetHarvestableAmount()
        {
            int level = GetCurrentLevel();
            int count = m_harvestAmounts.Count;
            
            if(count == 0) return 1;
            if(level < count) return m_harvestAmounts[level];
            return m_harvestAmounts[count-1];
        }
        
        public bool IsLocked()
        {
            return GetCurrentLevel() < m_unlockedAtLevel;
        }
        
        public int GetCurrentLevel()
        {
            if(m_mAUnlockReference.IsNullOrWhiteSpace()) return 0;
            return (int)MAUnlocks.GetObject(m_mAUnlockReference);
        }
    }
    
    public static List<MAHarvest> s_harvestInfo = new();
    public static List<MAHarvest> GetList=>s_harvestInfo;
    public string DebugDisplayName => $"{m_prefabName} : {m_level:D1}"; 
    public bool m_debugChanged;
    public string id;
    public string m_indexer;
    public string m_prefabName;
    public string m_mAUnlockReference;
    public int m_level;
    public int m_amount;
    
    public static bool PostImport(MAHarvest _what)
    {
        return true;
    }
    
    public static List<MAHarvest> LoadInfo()
    {
        s_harvestInfo = NGKnack.ImportKnackInto<MAHarvest>(PostImport);
        
        foreach(var item in s_harvestInfo)
        {
            if(m_unlockstates.TryGetValue(item.m_prefabName, out var state) == false)
            {
                state = new UnlockState();
                m_unlockstates.Add(item.m_prefabName, state);
            }
            state.AddHarvestInfo(item);
        }
        
        return s_harvestInfo;
    }

}
