using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

public class MAHelperSign : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerClickHandler
{
    public const string DesignTable = "designtable";
    public static List<MAHelperSign> m_helperSigns = new List<MAHelperSign>();
    public string m_executeSection = "";
    public bool m_destroyOnExecute = true;
    public Animator m_animator;
    private string m_type;
    private MAHelperSign m_clonedFrom = null;
    public Transform m_visuals;
    void Awake()
    {
        m_animator = GetComponent<Animator>();
        m_visuals = transform.Find("Visuals");
    }

    void Update()
    {
        switch (m_type)
        {
            case DesignTable:         //SpawnHelperSign(DesignTable, Pos[-0.49;0;0.256],"DesignTable", PPP, Hello)
                //m_visuals.gameObject.SetActive(DesignTableManager.Me.IsInDesignInPlace);
                break;
        }
    }

    void Start()
    {
        if(m_animator != null)
        {
            m_animator.SetBool("Attact", true);
        }
    }
    public void OnPointerClick(PointerEventData eventData)
    {
        if(m_executeSection.IsNullOrWhiteSpace() == false)
        {
            MAParserManager.Me.ExecuteSection(m_executeSection);
        }

        if (m_destroyOnExecute)
        {
            if(m_type == DesignTable)
            {
                var existing = m_helperSigns.Find(x => x.m_type == DesignTable && x.m_executeSection == m_executeSection && x.transform.position == transform.position);
                if (existing)
                {
                    existing.DestroyMe();    
                } 
            }
            DestroyMe();
        }
    }

    void DestroyMe()
    {
        if (m_clonedFrom != null)
        {
            m_helperSigns.Remove(m_clonedFrom);
            m_clonedFrom.DestroyMe();
        }
        m_helperSigns.Remove(this);
        Destroy(gameObject);
    }
    void Activate(string _type, string _section)
    {
        m_type = _type.Trim();
        m_executeSection = _section.Trim();
        if(m_helperSigns.Contains(this) == false)
            m_helperSigns.Add(this);
        switch (m_type)
        {
            case DesignTable:
                m_visuals.gameObject.SetActive(DesignTableManager.Me.IsInDesignInPlace);
                if (DesignTableManager.Me.IsInDesignInPlace)
                {
                    CheckForSigns(DesignTableManager.Me.m_turntableOrigin.transform.parent);
                }
                break;
        }
    }
    public static MAHelperSign Create(string _type, string _section, Vector3 _pos, float _scale = 1f)
    {
        var existing = m_helperSigns.Find(x => x.m_type == _type && x.m_executeSection == _section && x.transform.position == _pos);
        foreach (var hs in m_helperSigns)
        {
            if(hs.m_type == _type && hs.m_executeSection == _section && hs.transform.position == _pos)
            {
                existing = hs;
                break;
            }
        }
        if (existing) return existing;
        var prefab = Resources.Load<MAHelperSign>("Videos/MAHelperSign");
        if(prefab == null)
        {
            MAParser.ParserError($"No such prefab: MAHelperSign");
            return null;
        }
        var instance = Instantiate(prefab, NGManager.Me.m_helperHolder);
        instance.transform.position = _pos; 
        instance.transform.localScale = new Vector3(_scale, _scale, _scale);
        instance.Activate(_type, _section);
        return instance;
    }
    public static void CheckForSigns(Transform _parent)
    {
        var existing = m_helperSigns.FindAll(x => x.m_type == DesignTable);
        if (existing.Count == 0) return;
        foreach (var e in existing)
        {
            var go = Instantiate(e.gameObject, _parent);
            go.SetActive(true);
            var hs = go.GetComponent<MAHelperSign>();
            hs.m_visuals.gameObject.SetActive(true);
            hs.m_type = e.m_type;
            hs.m_clonedFrom = e;
            go.transform.SetParent(_parent);
            go.transform.localScale = new Vector3(0.05f, 0.05f, 0.05f);
        }
    }
}
