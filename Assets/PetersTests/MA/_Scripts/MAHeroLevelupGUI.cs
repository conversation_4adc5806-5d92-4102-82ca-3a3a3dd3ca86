using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class MAHeroLevelupGUI : NGBaseInfoGUI
{
    public TMP_Text m_heroTitle;
    public TMP_Text m_heroDescription;
    public Image m_heroIcon;
    public Transform m_levelupHolder;
    
    public TMP_Text m_experienceText;
    public TMP_Text m_levelName;
    public TMP_Text m_level;
    public Image m_experienceBar; 
    MAHeroBase m_hero;
    MACreatureInfo m_creatureInfo;
    List<MAHeroLevelInfo> m_levelInfos;
    public static bool m_changedLevelup = false;
    
    public override void ClickedClose()
    {
        if (m_changedLevelup)
        {
            Utility.ShowDialog("Confirm Level Up Choice", "Are you sure this choice is correct? Once you click 'Yes', you cannot change your mind.", false, "Yes", "No", ClickedCloseCallback);
        }
        else
        {
            ClickedCloseCallback(0);
        }
    }
    
    public void ClickedCloseCallback(int _mode)
    {
        if(_mode == 0) // Yes
        {
            m_hero.HeroGameState.m_characterExperience.ApplyChosenRewards(m_hero);
            
            AudioClipManager.Me.PlaySound("PlaySound_Arcadium_ITEMINFO_CLOSE", GameManager.Me.gameObject);
            DestroyHeroLevelupIcon(m_hero);
            DestroyMe();
        }
        else if (_mode == 1) // No
        {
        }
    }
    public void Activate(MAHeroBase _hero)
    {
        AudioClipManager.Me.PlaySound("PlaySound_Arcadium_ITEMINFO_OPEN", GameManager.Me.gameObject);
        m_hero = _hero;
        m_creatureInfo = _hero.CreatureInfo;
        if (m_hero.HeroGameState == null)
        {
            Debug.LogError("MAHeroLevelupGUI: GameState_Hero is null");
            return;
        }

        base.Activate("");
        
        m_heroTitle.text = m_creatureInfo.m_displayName;
        m_heroDescription.text = m_creatureInfo.m_description;
        
        var levelInfo = m_hero.GetLevelInfo();
        m_levelName.text = levelInfo.name;
        m_level.text = $"Level: {levelInfo.level}";
        
        string path = Utility.PathSimplify($"_Art/Sprites/BusinessRewards/{m_creatureInfo.m_spritePath}");
        var sprite = ResManager.Load<Sprite>(path);
        if (sprite != null)
            m_heroIcon.sprite = sprite;
        m_levelInfos = MAHeroLevelInfo.GetInfoList(m_creatureInfo.m_name);
        DisplayLevels();
        RefreshView();
        
    }

    private void RefreshView()
    {
        if(m_hero == null) return;
        
        m_experienceBar.fillAmount = m_hero.Experience/ m_hero.GetExperienceRequiredForNextLevel();
        m_experienceText.text = $"{m_hero.Experience:N0}/{m_hero.GetExperienceRequiredForNextLevel():N0}";
    }
    
    void DisplayLevels()
    {
        var level = m_hero.HeroGameState.m_characterExperience.m_level;
        m_levelupHolder.DestroyChildren();
        for(int i = 0; i < m_levelInfos.Count; i++)
        {
            var levelInfo = m_levelInfos[i];
            
            if(levelInfo.m_experienceRequired <= 0) continue;
            
            MAHeroLevelupItemGUI.ItemType itemType;
            if (levelInfo.m_level < level)
                itemType = MAHeroLevelupItemGUI.ItemType.Past;
            else if (levelInfo.m_level == level)
                itemType = MAHeroLevelupItemGUI.ItemType.Current;
            else
                itemType = MAHeroLevelupItemGUI.ItemType.Future;
            MAHeroLevelupItemGUI.Create(m_hero, itemType, levelInfo, i, m_levelupHolder);
        }
    }

    public static void CreateHeroLevelupIcon(MAHeroBase _hero)
    {
        if (_hero.HeroGameState.m_characterExperience.CanLevelup())
        {
            AudioClipManager.Me.SetHumanoidType(_hero, _hero.HumanoidType, _hero.HeroGameState.m_characterExperience.m_level);
            HeroLevelUpIcon.Create(_hero);
        }
    }
    public static void DestroyHeroLevelupIcon(MAHeroBase _hero)
    {
        if (_hero.HeroGameState.m_characterExperience.CanLevelup() == false)
        {
            var hlu = _hero.transform.Find("HeroLevelUpIcon(Clone)");
            if(hlu != null)
                Destroy(hlu.gameObject);    
        }
    }
    public static MAHeroLevelupGUI Create(MAHeroBase _hero)
    {
        if(s_infoShowing != null)
        {
            s_infoShowing.DestroyMe();
            s_infoShowing = null;
        }
        
        m_changedLevelup = false;
        
        var prefab = Resources.Load<MAHeroLevelupGUI>("_Prefabs/Dialogs/MAHeroLevelupGUI");
        var instance = Instantiate(prefab, NGManager.Me.NGInfoGUIHolder);
        instance.Activate(_hero);
        s_infoShowing = instance;
        return instance;
    }
}
