using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.Serialization;
using UnityEngine.UI;

public class MAHeroLevelupItemElementGUI : Mono<PERSON>ehaviour, IPointerEnterHandler, IPointerExitHandler
{
    [System.Serializable]
    public class LevelupIcons
    {
        public string m_name;
        public Sprite m_icon;
    }
    public List<LevelupIcons> m_icons;
    public Image m_icon;
    public Image m_iconSelected;
    public TMP_Text m_title;
    public GameObject m_helperHolder;
    public TMP_Text m_helperText;
    private MAHeroBase m_hero;
    private MAHeroLevelupItemGUI.ItemType m_type;
    private int m_rewardIndex;
    private string m_reward;
    private string m_rewardDescription;
    private int m_levelupLevel;
    private MAHeroLevelupItemGUI m_calledFrom;
    private Coroutine showPromptCoroutine;

    public void ClickedMe()
    {
        if(m_hero.HeroGameState.m_characterExperience.TryAssignReward(m_levelupLevel, m_rewardIndex, m_reward) == false)
            return;
            
        AudioClipManager.Me.PlaySound("PlaySound_Arcadium_ITEMINFO_SELECT", GameManager.Me.gameObject);
        MAHeroLevelupGUI.m_changedLevelup = true;
        m_calledFrom.ResetAndSetOneShowing(this);
    }
    
    private Sprite GetRewardIcon()
    {
        var rSplit = m_reward.Split(':','=','+','*');
        var icon = m_icons.Find(o=>o.m_name.ToLower() == rSplit[0].ToLower().Trim());
        return icon != null ? icon.m_icon : null;
    }
    
    public string GetRewardTitle()
    {
        var rSplit = m_reward.Split('+','*');
        if(rSplit.Length != 2) return m_reward;
        if(float.TryParse(rSplit[1], out float value) == false) return m_reward;
        
        var item = rSplit[0];
        
        if(m_reward.Contains('*'))
        {
            value -= 1f;
            return $"+{value:P0} {item}";
        }
        if(m_reward.Contains('+'))
        {
            return $"+{value:0} {item}";
        }
        return m_reward;
    }
    
    public void Activate(MAHeroBase _hero, MAHeroLevelupItemGUI.ItemType _type, int _rewardIndex, string _reward, string _rewardDescription, int _levelupLevel, MAHeroLevelupItemGUI _calledFrom)
    {
        var existingReward = _hero.HeroGameState.m_characterExperience.RewardMatches(_levelupLevel, _rewardIndex);
        if(existingReward != null) _reward = existingReward.m_power; 
        
        m_hero = _hero;
        m_type = _type;
        m_levelupLevel = _levelupLevel;
        m_rewardIndex = _rewardIndex;
        m_reward = _reward;
        m_calledFrom = _calledFrom;
        m_rewardDescription = _rewardDescription;
        m_helperText.text = _rewardDescription;
        m_helperHolder.SetActive(false);
        
        m_icon.sprite = GetRewardIcon();
        m_title.text = GetRewardTitle();
        
        var cg = GetComponentInChildren<CanvasGroup>();
        
        ShowHighlighted(existingReward != null);

        switch (m_type)
        {
            case MAHeroLevelupItemGUI.ItemType.Past:
            case MAHeroLevelupItemGUI.ItemType.Current:
                if (m_hero.HeroGameState.m_characterExperience.RewardGiven(m_levelupLevel) == false)
                {
                    cg.alpha = 1f;
                    cg.interactable = true;
                }
                else
                {
                    cg.alpha = .75f;
                    cg.interactable = false;
                    m_calledFrom.SetLocked(true);
                }
                break;
            case MAHeroLevelupItemGUI.ItemType.Future:
                cg.alpha = .25f;
                cg.interactable = false;
                m_calledFrom.SetLocked(true);
                break;
        }
    }
    public void ShowHighlighted(bool _show)
    {
        m_iconSelected.enabled = _show;
    }

    public void OnPointerEnter(PointerEventData eventData)
    {
        if (m_rewardDescription.IsNullOrWhiteSpace())
            return;
        showPromptCoroutine = StartCoroutine(ShowPromptAfterDelay());
    }

    public void OnPointerExit(PointerEventData eventData)
    {
        if (showPromptCoroutine != null)
        {
            StopCoroutine(showPromptCoroutine);
            showPromptCoroutine = null;
        }
        m_helperHolder.SetActive(false);
    }

    IEnumerator ShowPromptAfterDelay()
    {
        yield return new WaitForSeconds(2f);
        m_helperHolder.SetActive(true);
    }
    
    public static MAHeroLevelupItemElementGUI Create(MAHeroBase _hero, MAHeroLevelupItemGUI.ItemType _type, int _rewardIndex, string _reward, int _levelupLevel, Transform _holder, MAHeroLevelupItemGUI _calledFrom)
    {
        var prefab = Resources.Load<MAHeroLevelupItemElementGUI>("_Prefabs/Dialogs/MAHeroLevelupItemElementGUI");
        var instance = Instantiate(prefab, _holder);
        instance.Activate(_hero, _type, _rewardIndex, _reward, CharacterExperience.Reward.GetRewardDescription(_reward), _levelupLevel, _calledFrom);
        return instance;
    }
}
