using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class MAHeroLevelupItemGUI : MonoBehaviour
{
    public enum ItemType
    {
        Past,
        Current,
        Future,
    }
    public TMP_Text m_header;
    public TMP_Text m_experience;
    public Transform m_iconHolder;

    private MAHeroBase m_hero;
    private MAHeroLevelInfo m_info;
    private ItemType m_type;
    List<MAHeroLevelupItemElementGUI> m_elements = new ();
    public CanvasGroup m_canvasGroup;
    public Image m_background;
    
    public Color m_currentLevelColor;
    
    public void SetLocked(bool value)
    {
        m_canvasGroup.alpha = value ? 0.75f : 1f;
    }
    
    public void ResetAndSetOneShowing(MAHeroLevelupItemElementGUI _setOne)
    {
        foreach(var r in m_elements)
        {
            if (r != null)
                r.ShowHighlighted(_setOne == r);
        }
    }

    public MAHeroLevelupItemElementGUI GetSelectedElement(MAHeroLevelupItemElementGUI _current)
    {
        foreach (var r in m_elements)
        {
            if(r.m_iconSelected.enabled && r != _current)
            {
                return r;
            }
        }

        return null;
    }
    public void Activate(MAHeroBase _hero, ItemType _type, MAHeroLevelInfo _info, int _levelupLevel)
    {
        m_hero = _hero;
        m_info = _info;
        m_type = _type;
        m_elements.Clear();
        
        m_header.text = _info.m_levelDescription;
        m_experience.text = $"{_info.m_experienceRequired}xp";
        
        if(_type == ItemType.Current)
            m_background.color = m_currentLevelColor;
        
        m_iconHolder.DestroyChildren();
        if (m_info.m_reward1.IsNullOrWhiteSpace() == false)
            m_elements.Add(MAHeroLevelupItemElementGUI.Create(m_hero, m_type, 1, m_info.m_reward1, _levelupLevel, m_iconHolder, this));
        if (m_info.m_reward2.IsNullOrWhiteSpace() == false)
            m_elements.Add(MAHeroLevelupItemElementGUI.Create(m_hero, m_type, 2, m_info.m_reward2, _levelupLevel, m_iconHolder, this));
        if (m_info.m_reward3.IsNullOrWhiteSpace() == false)
            m_elements.Add(MAHeroLevelupItemElementGUI.Create(m_hero, m_type, 3, m_info.m_reward3, _levelupLevel, m_iconHolder, this));
    }
    public static MAHeroLevelupItemGUI Create(MAHeroBase _hero, ItemType _type, MAHeroLevelInfo _info, int _levelupLevel, Transform _holder)
    {
        var prefab = Resources.Load<MAHeroLevelupItemGUI>("_Prefabs/Dialogs/MAHeroLevelupItemGUI");
        var instance = Instantiate(prefab,  _holder);
        instance.Activate(_hero, _type, _info, _levelupLevel);
        return instance;
    }
}
