using UnityEngine;

public class MAMoneyDialog : NGBaseInfoGUI
{
    public Transform m_graphHolder;
    void Activate()
    {
        base.Activate("");
        
        m_graphHolder.DestroyChildren();
        MAMoneyDialogPanel showing = null;
        if(NGPlayer.Me.m_cash.m_currencyByDay.Count > 0)
            showing = MAMoneyDialogPanel.Create(NGPlayer.Me.m_cash, m_graphHolder);
        if(NGPlayer.Me.m_commonersFavors.m_currencyByDay.Count > 0)
            showing = MAMoneyDialogPanel.Create(NGPlayer.Me.m_commonersFavors, m_graphHolder);
        if(NGPlayer.Me.m_royalFavors.m_currencyByDay.Count > 0)
            showing = MAMoneyDialogPanel.Create(NGPlayer.Me.m_royalFavors, m_graphHolder);
        if(NGPlayer.Me.m_lordsFavors.m_currencyByDay.Count > 0)
            showing = MAMoneyDialogPanel.Create(NGPlayer.Me.m_lordsFavors, m_graphHolder);
        if(NGPlayer.Me.m_mysticFavors.m_currencyByDay.Count > 0)
            showing = MAMoneyDialogPanel.Create(NGPlayer.Me.m_mysticFavors, m_graphHolder);
        if(showing == null)
            DestroyMe();
    }

    public override void ClickedClose()
    {
        base.ClickedClose();
    }
    
    public static MAMoneyDialog Create()
    {
        if(s_infoShowing != null)
        {
            s_infoShowing.DestroyMe();
            s_infoShowing = null;
        }
        
        var prefab = Resources.Load<MAMoneyDialog>("_Prefabs/Dialogs/MAMoneyDialog");
        var instance = Instantiate(prefab, NGManager.Me.m_centreScreenHolder);
        instance.Activate(); 
        s_infoShowing = instance; 
        return instance;
    } 
}
