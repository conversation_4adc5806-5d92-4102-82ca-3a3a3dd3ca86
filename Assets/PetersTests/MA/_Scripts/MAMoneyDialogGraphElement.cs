using System.Collections;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class MAMoneyDialogGraphElement : Mono<PERSON><PERSON><PERSON>our, IPointerEnterHandler, IPointerExitHandler
{
    public TMP_Text m_xTitle;
    public Image m_incomeBar;
    public Image m_idealIncomeBar;
    public Image m_ExpenseBar;
    public Image m_idealExpenceBar;
    public TMP_Text m_legendText;
    private CurrencyContainer.CurrencyByDay m_day;
    private MAPopup m_popup;
    private Coroutine showPromptCoroutine;
    
    public void Activate(CurrencyContainer.CurrencyByDay _day, string _xTitle, float _income, float _expence, float _idealIncome, float _idealExpence)
    {
        m_day = _day;
        m_xTitle.text = $"{m_day.m_day}";
        m_incomeBar.fillAmount = _income;
        m_ExpenseBar.fillAmount = _expence;
        SetIdeal(m_incomeBar.rectTransform, m_idealIncomeBar, _idealIncome);
        SetIdeal(m_ExpenseBar.rectTransform, m_idealExpenceBar, _idealExpence);
    }
    void SetIdeal(RectTransform _parent, Image _child, float _ideal)
    {

    // Get parent height
        float parentHeight = _parent.rect.height;
        float childHeight = _child.rectTransform.rect.height;


        var bottomY = 0;
        var topY = parentHeight - childHeight/2;
        float newY = Mathf.Lerp(bottomY, topY, _ideal);
        var ap = _child.rectTransform.anchoredPosition;
        ap.y = newY;
        _child.rectTransform.anchoredPosition = ap;
        // Calculate bottom and top local Y positions
//        float bottomY = -parentHeight * 0.5f + childHeight * 0.5f;
  //      float topY    =  parentHeight * 0.5f - childHeight * 0.5f;

        // Interpolate between bottom and top
    //    float newY = Mathf.Lerp(bottomY, topY, _idealExpence);

        // Apply position (keep x and z unchanged)
   //     Vector3 localPos = _child.transform.localPosition;
     //   localPos.y = newY;
       // m_idealIncomeBar.transform.localPosition = localPos;
    }
    IEnumerator ShowPromptAfterDelay()
    {
        yield return new WaitForSeconds(1f);
        var message = $"Income: {(m_day.Income/100f):N0}\n Spent: {(m_day.Expense/100f):N0}";
        m_popup = MAPopup.Create(message, transform);
    }
    public void OnPointerEnter(PointerEventData eventData)
    {
        showPromptCoroutine = StartCoroutine(ShowPromptAfterDelay());
    }

    public void OnPointerExit(PointerEventData eventData)
    {
        if (showPromptCoroutine != null)
        {
            StopCoroutine(showPromptCoroutine);
            showPromptCoroutine = null;
            if(m_popup != null)
                m_popup.DestroyMe();
        }
    }
 
    public static MAMoneyDialogGraphElement Create(CurrencyContainer.CurrencyByDay _day, string _xTitle, float _income, float _expence, float _idealIncome, float _idealExpence, Transform _parent)
    {
        var prefab = Resources.Load<MAMoneyDialogGraphElement>("_Prefabs/Dialogs/MAMoneyDialogGraphElement");
        var instance = Instantiate(prefab, _parent);
        instance.Activate(_day, _xTitle, _income, _expence, _idealIncome, _idealExpence);  
        return instance;
    }
}
