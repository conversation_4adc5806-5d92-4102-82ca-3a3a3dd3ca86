using TMPro;
using UnityEngine;

public class MAMoneyDialogPanel : MonoBehaviour
{
    public TMP_Text m_titleText;
    public TMP_Text m_titleIcon;
    public Transform m_graphHolder;
    private CurrencyContainer m_currency;

    public void Activate(CurrencyContainer _currency)
    {
        m_currency = _currency;
        SetTitle();
        m_currency.m_currencyByDay.Sort((x,y)=> x.m_day.CompareTo(y.m_day));
        int maximum = int.MinValue;
        float idealIncomeMulti = 1.2f;
        float idealExpenceMulti = 0.8f;
        foreach (var c in m_currency.m_currencyByDay)
        {
            if(c.Income > maximum)
                maximum = c.Income;
            if(c.Expense > maximum)
                maximum = c.Expense;
            if(c.Income*idealIncomeMulti > maximum)
                maximum = (int)(c.Income*idealIncomeMulti);
            if(c.Expense*idealExpenceMulti > maximum)
                maximum = (int)(c.Expense*idealExpenceMulti);
        }
        m_graphHolder.DestroyChildren();
        foreach (var c in m_currency.m_currencyByDay)
        {
            float income = (float)c.Income / maximum;
            float expence = (float)c.Expense / maximum;
            float idealIncome= ((float)c.Income*idealIncomeMulti)/maximum;
            float idealExpence= ((float)c.Expense*idealExpenceMulti)/maximum;
            MAMoneyDialogGraphElement.Create(c, c.m_day.ToString(), income, expence, idealIncome, idealExpence, m_graphHolder);
        }
    }

    void SetTitle()
    {
        switch (m_currency.Type)
        {
            case CurrencyContainer.CurrencyType.Cash:
                m_titleText.text = "Cash";
                m_titleIcon.text = "<sprite=0>";
                break;
            case CurrencyContainer.CurrencyType.PeoplesFavours:
                m_titleText.text = "Peoples";
                m_titleIcon.text = "<sprite=3>";
                break;
            case CurrencyContainer.CurrencyType.LordsFavours:
                m_titleText.text = "Lords";
                m_titleIcon.text = "<sprite=2>";
                break;
            case CurrencyContainer.CurrencyType.RoyalFavours:
                m_titleText.text = "Royal";
                m_titleIcon.text = "<sprite=1>";
                break;
            case CurrencyContainer.CurrencyType.MysticFavours:
                m_titleText.text = "Mystic";
                m_titleIcon.text = "<sprite=4>";
                break;
        }
    }
    
    public static MAMoneyDialogPanel Create(CurrencyContainer _currency, Transform _parent)
    {
        var prefab = Resources.Load<MAMoneyDialogPanel>("_Prefabs/Dialogs/MAMoneyDialogPanel");
        var instance = Instantiate(prefab, _parent);
        instance.Activate(_currency);  
        return instance;
    }
}
