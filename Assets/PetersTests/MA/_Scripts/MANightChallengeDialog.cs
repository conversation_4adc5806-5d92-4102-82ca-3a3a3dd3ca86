using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class MANightChallengeDialog : Mono<PERSON>ingleton<MANightChallengeDialog>
{
    public TMP_Text m_titleText;
    public Image m_mapVisuals;
    public TMP_Text m_descriptionText;
    public TMP_Text m_hintText;
    public GameObject m_nightRewards;
    public Transform m_rewardHolder;
    public GameObject m_rewardLinePrefab;
    public Button m_newCreatureButton;
    
    private MACalenderInfo m_info;
    private float m_nightTypeValue;
    
    private const string c_escapeBackLabel = "NightChallenge";
    
    public void ClickedClose()
    {
        GameManager.Me.PopEscapeBackFunction(c_escapeBackLabel);
       // AudioClipManager.Me.PlaySound("PlaySound_Arcadium_ITEMINFO_CLOSE", GameManager.Me.gameObject);
        Destroy(gameObject);
    }

    public void ClickedNewCreature()
    {
       // AudioClipManager.Me.PlaySound("PlaySound_Arcadium_UI_BUTTON_CLICK", GameManager.Me.gameObject);
        MANightNewCreature.Create(MANightChallengeManager.Me.m_newCreatures);
    }

    void Activate(MACalenderInfo _info)
    {
        GameManager.Me.PushEscapeBackFunction(c_escapeBackLabel, ClickedClose);
        m_info = _info;
        m_titleText.text = m_info.m_nightTitle;
        float.TryParse(m_info.m_nightTypeData, out m_nightTypeValue);
        m_descriptionText.text = m_info.m_nightDescription.Replace("[Value]", m_info.m_nightTypeData);
        if(m_info.m_nightHint.IsNullOrWhiteSpace() == false)
            m_hintText.text = $"Hint: {m_info.m_nightHint}";
        else 
            m_hintText.transform.parent.gameObject.SetActive(false);
        if (m_info.m_nightReward.IsNullOrWhiteSpace() == false)
        {
            PopulateNightRewards();
        }
        else
        {
            m_nightRewards.gameObject.SetActive(false);
        }
        
        int w = 1024 * 2, h = 575 * 2;
        var tex = TowerDefenseVisualsManager.Me.Capture(w, h);
        m_mapVisuals.sprite = Sprite.Create(tex, new Rect(0, 0, w, h), Vector2.one * .5f); 
        m_newCreatureButton.gameObject.SetActive(MANightChallengeManager.Me.m_newCreatures.Count > 0);
    }
    void PopulateNightRewards()
    {
        m_rewardHolder.DestroyChildren();
        var rewards = m_info.m_nightReward.Split('\n');
        foreach (var reward in rewards) //TrackNightReward(Survive, “Survive the Night”, 0, Cash=500)
        {
            if (reward.IsNullOrWhiteSpace()) continue;
            
            var parseString = MAParserSupport.ParseString(reward);
            if(parseString == null || parseString.Count == 0) continue;
            var challenge= parseString[0].m_args[1].Trim(' ', '"', '\u201C', '\u201D');; 
            var data =  parseString[0].m_args[2].Trim(' ', '"', '\u201C', '\u201D');  
            var rewardParse = parseString[0].m_args[3].Trim(' ', '"', '\u201C', '\u201D'); // Cash=500
            
            var line = Instantiate(m_rewardLinePrefab, m_rewardHolder);
            line.gameObject.SetActive(true);
            var challengeText = line.transform.Find("Challenge").GetComponent<TMP_Text>();
            var rewardText = line.transform.Find("Reward").GetComponent<TMP_Text>();
            switch (parseString[0].m_args[0].ToLower())
            {
                case "time":
                    float.TryParse(data.Trim(), out var time);
                    challenge = challenge.Replace("[Value]", time.ToHMS().ToString());
                    break;
                default:
                    challenge = challenge.Replace("[Value]", data.ToString());
                    break;
            }
            challengeText.text = challenge.Trim('"');
            
            var rewardSplit = rewardParse.Split('=');
            switch (rewardSplit[0].ToLower().Trim())
            {
                case "cash":
                    rewardText.text = $"Cash: {rewardSplit[1]}";
                    break;
                case "gift":
                    var gift = NGBusinessGift.GetInfo(rewardSplit[1].Trim());
                    if (gift == null)
                    {
                        Debug.LogError($"<color=red>Gift: {rewardSplit[1]}</color> Not found in NGBusinessGift");
                        continue;
                    }

                    rewardText.text = gift.m_giftTitle;
                    break;
                case "best":
                    var bGift = MANightChallengeManager.GetIdealReward(true);
                    var bestGift = NGBusinessGift.GetInfo(bGift.giftName);

                    rewardText.text = $"{bestGift.m_giftTitle}";
                    break;
                case "worst":
                    var wGift = MANightChallengeManager.GetIdealReward(false);
                    var worstGift = NGBusinessGift.GetInfo(wGift.giftName);

                    rewardText.text = $"{worstGift.m_giftTitle}";
                    break;
            }
        }
    }
    public static MANightChallengeDialog Create(MACalenderInfo _info)
    {
        if(Me != null)
            Destroy(Me.gameObject);
        var prefab = Resources.Load<MANightChallengeDialog>("_Prefabs/Dialogs/MANightChallengeDialog");
        var instance = Instantiate(prefab, NGManager.Me.m_centreScreenHolder);
        instance.Activate(_info);
        return instance;
    }
}
