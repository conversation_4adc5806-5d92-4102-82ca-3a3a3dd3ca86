using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class MANightNewCreature : MonoBehaviour
{
    public TMP_Text m_titleText;
    public TMP_Text m_descriptionText;
    public TMP_Text m_descriptionTitleText;
    public Image m_icon;
    public AkEventHolder m_clickAudio;
    public bool IsFinished { get; private set; } = false;
    private Animator m_anim;
    private MACreatureInfo m_info;
    private MADisplayCreature m_displayCreature;
    private List<string> m_newCreatures = new List<string>();
    private int m_curentNewCreatureIndex = 0;
    void Update()
    {
        if(Input.GetMouseButtonUp(0))
        {
            Cleanup();
            m_clickAudio?.Play(gameObject);
            m_curentNewCreatureIndex++;
            if(m_curentNewCreatureIndex >= m_newCreatures.Count)
                Finished();
            else
                ShowCreature();
        }
    }

    void ShowCreature()
    {
        m_info = MACreatureInfo.GetInfo(m_newCreatures[m_curentNewCreatureIndex]);
        m_anim = GetComponent<Animator>();
        m_titleText.text = m_info.m_introTitle;
        m_descriptionTitleText.text =m_info.m_introTitle;
        m_descriptionText.text = m_info.m_introDescription;
        m_icon.sprite = Resources.Load<Sprite>($"_Art/Icons/Creatures/{m_info.m_nightSprite}");
        CreateDummyCreature(m_info, NGManager.Me.m_nightNewCreatureSpawnPoint);
        if (m_anim) m_anim.Play("MANightNewCreature_Anim");
        // var found = GameManager.Me.m_state.m_uniqueCreaturesSpawned.Find(o=> o.name ==m_info.m_name);
        // if(found.name.IsNullOrWhiteSpace() == false)
        //     found.seen = true;
        if(GameManager.Me.m_state.m_nightNewCreaturesSeen.Contains(m_info.m_name) == false)
            GameManager.Me.m_state.m_nightNewCreaturesSeen.Add(m_info.m_name);
    }
    void Cleanup()
    {
        m_displayCreature?.DestoyMe();
    }
    void Finished()
    {
        IsFinished = true;
        m_displayCreature?.DestoyMe();
        GameManager.Me.EndCameraPanSequence();    
        ToggleGUI(true);
        DestroyMe();
    }
    void ToggleGUI(bool _state)
    {
        NGManager.Me.m_screenLocationsHolder.gameObject.SetActive(_state);
        NGManager.Me.m_NGBusinessGiftsPanelHolder.gameObject.SetActive(_state);
    }

    private const string c_escapeBackLabel = "NewCreature";
    public void DestroyMe()
    {
        GameManager.Me.PopEscapeBackFunction(c_escapeBackLabel);
        Destroy(gameObject);
    }
    bool Activate(List<string> _newCreatures)  
    {
        GameManager.Me.PushEscapeBackFunction(c_escapeBackLabel, Finished);
        m_newCreatures = _newCreatures;
        if (m_newCreatures.Count == 0)
            return false;
        m_curentNewCreatureIndex = 0;
        ToggleGUI(false);
        ShowCreature();
        MoveCamersaToCreature();
        return true;
    }
    
    void MoveCamersaToCreature()
    {
        var pos = m_displayCreature.transform.position - Vector3.forward * 10f;
        pos.y += 1f;
        GameManager.Me.StartCameraPanSequence();
        GameManager.Me.UpdateCameraPanSequence(pos, Vector3.forward, "");
    }

    void CreateDummyCreature(MACreatureInfo _creatureInfo, Transform _holder)
    {
        m_displayCreature=MADisplayCreature.SpawnDisplayCreature(_creatureInfo.m_name);
        if (m_displayCreature == null) return;
        m_displayCreature.transform.SetParent(_holder, false);
        m_displayCreature.transform.position = m_displayCreature.transform.position.GroundPosition();
        m_displayCreature.transform.rotation = Quaternion.Euler(0f,180f,0f);
    }

    public static MANightNewCreature Create(List<string> _newCreatures)
    {
        if(_newCreatures.Count == 0) return null;
        var prefab = Resources.Load<MANightNewCreature>("_Prefabs/Dialogs/MANightNewCreature");
        var instance = Instantiate(prefab, NGManager.Me.m_HUDHolder);
        instance.Activate(_newCreatures);
        return instance;
    }
  
}
