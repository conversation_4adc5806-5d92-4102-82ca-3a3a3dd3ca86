using System;
using System.Collections.Generic;
using UnityEngine;

[System.Serializable] public class MAParserSection
    {
        [System.Serializable]
        public class SectionLine
        {
            public SectionLine(int _lineNumber, string _line)
            {
                m_lineNumber = _lineNumber;
                m_line = _line;
            }

            public string m_line;
            public int m_lineNumber;
            private bool m_debugSelected = false;

            public bool DebugSelected
            {
                get => m_debugSelected;
                set => m_debugSelected = value;
            }
        }
        [System.Serializable]
        public class SectionCheck
        {
            public SectionCheck(string _check)
            {
                m_check = _check;
            }
            public string m_check;
            public bool m_triggered = false;
        }

        public MAParserSection()
        {
        }

        [System.Serializable] public class DecisionState { public int m_line; public float m_initialValue; }
        public string m_name;
        public string m_fileName;
        public int m_currentLine = 0;
        public List<SectionLine> m_lines = new();
        public bool m_isStepping = false;
        public bool m_nextStep = false;
        public List<SectionLine> Lines => m_lines;
        [SerializeReference] public DecisionState m_decisionState = null;
        [NonSerialized] public MAFeedbackConditions m_currentFeedbackConditions = null;
        [NonSerialized] public NGBusinessDecision m_currentDecision = null;
        [NonSerialized] public MAGameFlowDialog m_currentDialog = null;
        [NonSerialized] public MAGameFlowDialog m_parsedDialog = null;
        [NonSerialized] public MAMessage m_currentMessage = null;
        [NonSerialized] public MAQuestBase m_currentQuest;
        [NonSerialized] public bool m_currentDisplayingMessage;
        [NonSerialized] public MAParserSection m_currentCall;
        [NonSerialized] public MAQuestCreateScroll m_currentQuestScroll;
        [NonSerialized] public float m_currentParserTimer = -1f;
        [NonSerialized] public MAQuestTrigger m_currentQuestTrigger;
        [NonSerialized] public MAInspection m_currentInspection;
        public List<(string id, float value)> m_currentTrackFloat = new();
        public List<SectionCheck> m_checks = new();
        public string m_currentData;
        //public List<string> m_currentGifts = new();
        public List<string> m_currentSavedTags = new();
        public bool m_debugSelected = false;
        public string m_questName;

        public void ClearChecks()
        {
            m_checks.Clear();
        }
        public void TryLoadDesignConditions(string _path)
        {
            if(m_currentFeedbackConditions != null && m_currentFeedbackConditions.m_id == _path)
                return;
            m_currentFeedbackConditions = new MAFeedbackConditions(_path);
            
            var section = MAParserManager.Me.ExecuteSection(_path, 0, null, false);
            if(section != null)
            {
                while(section.m_currentLine < section.Lines.Count)
                    MAParserManager.Me.UpdateSection(section);
            }
        }
        
        public void UpdateDescisionState()
        {
            if(m_currentDecision == null) // Clear state
            {
                m_decisionState = null;
            }
            else if(m_decisionState == null) // Create state
            {
                m_decisionState = new DecisionState() { m_line = m_currentLine, m_initialValue = m_currentDecision.m_initialValue };
            }
            else if(m_decisionState.m_line == m_currentLine) // Load state
            {
                m_currentDecision.m_initialValue = m_decisionState.m_initialValue; 
            }
            else // Clear state
            {
                m_decisionState = null;
            }
        }
        
        public void ChangeLine(SectionLine _line, string _changedString)
        {
            _line.m_line = _changedString;
            MAParserManager.ChangeLine(this, _line, _changedString);
        }

        public void SkipToLine(int _line)
        {
            ClearCurrents();
            m_currentLine = _line;
        }

        public bool SkipToCloseBracket()
        {
            var newLine = m_currentLine + 1;
            if (newLine >= Lines.Count)
            {
                Debug.LogError(MAParserSupport.DebugColor($"MAParserFile[{m_name}]:{m_currentLine} failed to find opening brace"));
                return false;
            }

            var line = Lines[newLine].m_line;
            if (line.Contains("{") == false)
            {
                Debug.LogError(MAParserSupport.DebugColor($"MAParserFile[{m_name}]:{m_currentLine} failed to find opening brace"));
                return false;
            }

            var braceCount = 0;
            for (int i = newLine + 1; i < Lines.Count; i++)
            {
                if (Lines[i].m_line.Contains("}"))
                {
                    if (braceCount == 0)
                    {
                        m_currentLine = i;
                        MAParserManager.Me.NextLine(this);
                        return true;
                    }
                    braceCount--;
                }
                if (Lines[i].m_line.Contains("{"))
                    braceCount++;
            }

            Debug.LogError(MAParserSupport.DebugColor($"MAParserFile[{m_name}]:{m_currentLine} failed to find closing brace"));
            return false;
        }
        public bool SkipToCloseBracketOrElse()
        {
            if (SkipToCloseBracket())
            {
                if(Lines[m_currentLine].m_line.ToLower().Contains("else"))
                {
                    MAParserManager.Me.NextLine(this);
                    return true;
                }
            }

            return false;
        }
        public void SetStep()
        {
            m_isStepping = true;
            m_nextStep = false;
        }

        public bool SkipElseOrContinue()
        {
            int openBraces = 0;
            for (int i = m_currentLine + 1; i < Lines.Count; i++)
            {
                if (Lines[i].m_line.Contains("}"))
                {
                    openBraces--;
                    if (openBraces == 0)
                    {
                        m_currentLine = i;
                        return true;
                    }
                }
                else if (Lines[i].m_line.Contains("{")) openBraces++;
            }
            MAParser.ParserError($"Unmatcherd {{ }} on Line {m_currentLine}{Lines[m_currentLine].m_line}");
            return false;
        }
        public void ClearCurrents()
        {
            m_currentDecision = null;
            if (m_currentDialog != null)
                m_currentDialog.DestroyMe();
            m_currentDialog = null;
            if(m_currentMessage != null) m_currentMessage.Destroy();
            m_currentMessage = null;
            //m_currentQuest = null;
            m_currentDisplayingMessage = false;
            m_currentCall = null;
            m_currentQuestScroll = null;
            m_currentParserTimer = -1f;
            //m_currentGifts.Clear();
            m_currentData = null;

            for (int i = m_currentSavedTags.Count - 1; i >= 0; i--)
            {
                MAParserManager.Me.RemoveFlowStep(m_currentSavedTags[i]);
            }

            m_currentSavedTags.Clear();

        }

        /*public void GiftRecieved(NGBusinessGift _gift, bool _isChooseCard)
        {
            if(m_currentGifts.Contains(_gift.m_name))
                m_currentGifts.Remove(_gift.m_name);
        }*/

        public void UpdateChecks()
        {
            for (var i = m_checks.Count - 1; i >= 0; i--)
            {
                var c = m_checks[i];
                if (c == null || c.m_check.IsNullOrWhiteSpace()) continue;
                var cSplit = c.m_check.Split('|');
                if (cSplit.Length < 3) continue;
                var checkCall = cSplit[0];
                var checkValue = cSplit[1];
                var checkLineNum = int.Parse(cSplit[2]);
                var oldCurrentLine = m_currentLine;
                m_currentLine = checkLineNum;
                if (c.m_triggered == false)
                {

                    if (MAParserSupport.TryParse(checkCall, out var result) == false) goto ContinueLabel;
                    if (result == false) goto ContinueLabel;
                    if (GoToLabel(checkValue))
                    {
                        ClearCurrents();
                        m_checks.RemoveAt(i);
                        goto BreakLabel;
                    }
                }

                bool result2 = false;
                if (MAParserSupport.TryParse(checkValue, out result2) == false) goto ContinueLabel;
                c.m_triggered = true;
                if (result2 == false) goto ContinueLabel;
                m_checks.RemoveAt(i); 
                ContinueLabel: 
                m_currentLine = oldCurrentLine;
                BreakLabel:
                continue;
            }
        }

        public bool GoToLabel(string _label)
        {
            for (var i = 0; i < m_lines.Count; i++)
            {
                var l = m_lines[i];
                var line = l.m_line.ToLower();
                if (line.StartsWith("label") && l.m_line.Contains(_label) && line.Contains("check") == false)
                {
                    m_currentLine = i;
                    return true;
                }
            }
            return false;
        }

        public void Abort()
        {
            m_currentLine = Lines.Count-1;
        }
        
    }