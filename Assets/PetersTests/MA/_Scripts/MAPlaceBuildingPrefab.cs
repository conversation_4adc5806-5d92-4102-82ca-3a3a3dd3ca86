using UnityEngine;

public class MAPlaceBuildingPrefab : MonoBehaviour
{
    public MABuilding m_building;
    void Start()
    {
        m_building = GetComponentInChildren<MABuilding>();
        m_building.PlaceInWorld(transform.position);
        BuildingPlacementManager.ManageBuildingId(m_building);
        if(GameManager.Me.m_state.m_buildings.Contains(m_building.m_stateData) == false)
            GameManager.Me.m_state.m_buildings.Add(m_building.m_stateData);

    }

  
    void Update()
    {
        
    }
}
