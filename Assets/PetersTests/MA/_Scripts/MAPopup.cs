using TMPro;
using UnityEngine;
using UnityEngine.UI;          // for LayoutRebuilder

public class MAPopup : MonoBehaviour
{
    public TMP_Text m_text;
    [SerializeField] float m_screenPadding = 8f;
    [SerializeField] bool m_clampEveryFrame = false; // set true if the popup moves
    bool _pendingClamp;

    public void Activate(string _message)
    {
        if (m_text != null)
        {
            m_text.text = MAHelper.ReplaceInputTokens(_message);
            transform.SetParent(NGManager.Me.m_HUDHolder, worldPositionStays: true);
            transform.SetAsLastSibling();
            // Ensure TMP + layout know the new size
            m_text.ForceMeshUpdate();
            var rt = GetComponent<RectTransform>();
            LayoutRebuilder.ForceRebuildLayoutImmediate(m_text.rectTransform);
            LayoutRebuilder.ForceRebuildLayoutImmediate(rt);
            

            _pendingClamp = true; // clamp once in LateUpdate so sizes are final
        }
    }

    void LateUpdate()
    {
        if (_pendingClamp || m_clampEveryFrame)
        {
            ClampToCanvas();
            if (!m_clampEveryFrame) _pendingClamp = false;
        }
    }

    void ClampToCanvas()
    {
        var canvas = GetComponentInParent<Canvas>(true);
        if (canvas == null) return;
        var rootCanvas = canvas.rootCanvas;
        var canvasRect = rootCanvas.GetComponent<RectTransform>();
        var popupRect  = GetComponent<RectTransform>();
        if (canvasRect == null || popupRect == null) return;

        // Ensure latest geometry
        LayoutRebuilder.ForceRebuildLayoutImmediate(popupRect);

        // Compute popup bounds relative to the canvas
        var bounds = RectTransformUtility.CalculateRelativeRectTransformBounds(canvasRect, popupRect);
        Rect cr = canvasRect.rect;

        float leftBound   = cr.xMin + m_screenPadding;
        float rightBound  = cr.xMax - m_screenPadding;
        float bottomBound = cr.yMin + m_screenPadding;
        float topBound    = cr.yMax - m_screenPadding;

        Vector3 offset = Vector3.zero;

        if (bounds.min.x < leftBound)  offset.x += leftBound  - bounds.min.x;
        if (bounds.max.x > rightBound) offset.x += rightBound - bounds.max.x;
        if (bounds.min.y < bottomBound)offset.y += bottomBound- bounds.min.y;
        if (bounds.max.y > topBound)   offset.y += topBound   - bounds.max.y;

        if (offset.sqrMagnitude > 0f)
        {
            // Convert canvas-local offset to world movement and apply
            popupRect.position += canvasRect.transform.TransformVector(offset);
        }
    }

    public void DestroyMe() => Destroy(gameObject);
    public static MAPopup Create(string _message, Transform _holder)
    {
        var prefab = MAPrefabLoader.Me.LoadPrefab<MAPopup>("_Prefabs/Dialogs/MAPopup");
        if (prefab == null)
        {
            MAParser.ParserError("No such prefab: MAPopup");
            return null;
        }

        var instance = Instantiate(prefab, _holder);
        instance.Activate(_message);
        return instance;
    }
}