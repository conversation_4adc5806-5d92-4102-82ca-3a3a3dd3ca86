using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;


public class MAPopupHandler : <PERSON>o<PERSON><PERSON><PERSON><PERSON>, IPointerEnterHandler, IPointerExitHandler
{
    public enum PopupType
    {
        Image,
        Text,
        Info
    }
    public PopupType m_popupType;
    [Tooltip("Use a specific popup reference from Knack. If blank, will try to infer from any child Image sprite name.")]
    public string m_popupRefeence;
    [TextArea] public string m_popupMessage;
    public float m_delayBeforePopup = 0.5f;

    private MAPopupHelperInfo m_info;
    private MAPopup m_popup;
    private bool m_isPointerOver;
    private float m_triggerTime; // when to show popup
    public Transform m_spawnLocation;
    
    protected void Start()
    {
        StartCoroutine(WaitForActivate());
        
    }
    private System.Collections.IEnumerator WaitForActivate()
    {
        yield return new WaitUntil(() => GameManager.Me != null && GameManager.Me.LoadComplete);
        if (m_popupRefeence.IsNullOrWhiteSpace() == false)
        {
            m_info = MAPopupHelperInfo.GetInfo(m_popupRefeence);
            if (m_info == null)
            {
                MAParser.ParserError("No such popup reference: " + m_popupRefeence);
            } 
            m_popupMessage = m_info.m_popupText;
        }
        else
        {
            if (m_popupType == PopupType.Image)
            {
                // Try to infer info from any child Image sprite name
                var images = GetComponentsInChildren<Image>(includeInactive: true);
                foreach (var i in images)
                {
                    var sprite = i != null ? i.sprite : null;
                    if (sprite == null) continue;

                    var name = sprite.name;
                    m_info = MAPopupHelperInfo.GetInfo(name);
                    if (m_info != null) break;
                }
                if (m_info == null)
                {
                    if(m_popupMessage.IsNullOrWhiteSpace())
                        Destroy(this);
                }
                else
                {
                    m_popupMessage = m_info.m_popupText;
                }
            }
            else if (m_popupType == PopupType.Text)
            {
                
            }
        }

       
    }

    public void Update()
    {
        if (m_isPointerOver && Time.time >= m_triggerTime)
        {
            ShowPopup(true);
        }
    }

    public void OnPointerEnter(PointerEventData eventData)
    {
        var triggerTime = m_delayBeforePopup;
        if (m_info != null)
            triggerTime = m_info.m_delayBeforePopup;
        m_isPointerOver = true;
        m_triggerTime = Time.time + triggerTime;
    }

    public void OnPointerExit(PointerEventData eventData)
    {
        m_isPointerOver = false;
        ShowPopup(false);
    }

    public void ShowPopup(bool show)
    {
        if (show)
        {
            if (m_popup != null) return;

            if (m_popupType == PopupType.Text)
            {
                var text = GetComponentInChildren<TMP_Text>();
                if (text == null) return;
                var message = text.text;
                bool found = false;
                foreach(var i in MAPopupHelperInfo.s_popupHelperInfoList)
                {
                    if (message.Contains(i.m_name))
                    {
                        m_popupMessage = i.m_popupText;
                        found = true;
                        break;
                    }
                }

                if (found == false) return;
            }
            
            m_popup = MAPopup.Create(m_popupMessage, m_spawnLocation ?? transform); 
            // var prefab = MAPrefabLoader.Me.LoadPrefab<MAPopup>("_Prefabs/Dialogs/MAPopup");
            // if (prefab == null)
            // {
            //     MAParser.ParserError("No such prefab: MAPopup");
            //     return;
            // }
            //
            // m_popup = Instantiate(prefab, transform);
            // m_popup.Activate(m_popupMessage);
        }
        else
        {
            if (m_popup != null)
            {
                m_popup.DestroyMe();
                m_popup = null;
            }
        }
    }

    void OnDisable()
    {
        m_isPointerOver = false;
        ShowPopup(false);
    }

    void OnDestroy()
    {
        if (m_popup != null)
        {
            m_popup.DestroyMe();
            m_popup = null;
        }
    }
}
