using System;
using System.Collections.Generic;
using UnityEngine;
[Serializable]
public class MAPopupHelperInfo 
{
    public static List<MAPopupHelperInfo> s_popupHelperInfoList = new();
    public static List<MAPopupHelperInfo> GetList=>s_popupHelperInfoList;
    public string DebugDisplayName => $"{m_name}";
    public bool m_debugChanged;
    public string id;

    public string m_name;
    public string m_popupText;
    public string m_popupType;
    public int m_count;
    public float m_delayBeforePopup;
    public static MAPopupHelperInfo GetInfo(string _name) => s_popupHelperInfoList.Find(o=> o.m_name == _name);
    public static bool PostImport(MAPopupHelperInfo _what)
    {
        return true;
    }

    public static List<MAPopupHelperInfo> LoadInfo()
    { 
        s_popupHelperInfoList = NGKnack.ImportKnackInto<MAPopupHelperInfo>(PostImport);
        return s_popupHelperInfoList;
    }
}
