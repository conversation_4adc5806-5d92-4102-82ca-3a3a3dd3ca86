using UnityEngine;

public class MAPublisherCheats : Mono<PERSON><PERSON><PERSON><MAPublisherCheats>
{
    public void ClickedMe()
    {
        if(MADemoDialog.Me)
            MADemoDialog.Me.ClickedClose();
        else
            MADemoDialog.Create();
    }

    public void DestroyMe()
    {
        Destroy(gameObject);
    }
    public static MAPublisherCheats Create()
    {
        if (Me)
        {
            Me.DestroyMe();
            return null;
        }
        var prefab = Resources.Load<MAPublisherCheats>("_Prefabs/Dialogs/MAPublisherCheats");
        var holder = NGManager.Me.m_screenLocationsHolder.Find("MiddleLeft");
        var instance = Instantiate(prefab, holder);
        return instance;
    }
}
