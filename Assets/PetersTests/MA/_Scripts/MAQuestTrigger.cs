using System;
using System.Collections.Generic;
using MeshEffects2;
using UnityEngine;

public class MAQuestTrigger : MonoBehaviour
{
    public static List<MAQuestTrigger> m_triggers = new ();
    public enum TriggerType
    {
        OneShot,
        Repeat,
        Last
    }

    public string m_name;
    public float m_radius;
    public TriggerType m_triggerType;
    public string m_questName;
    public List<string> m_lookFor;
    public bool m_triggered;
    public void DestroyMe()
    {
        var name = m_name;
        m_triggers.RemoveAll(o=>o.m_name == name);
        Destroy(gameObject);
    }

    private void OnDestroy()
    {
        DestroyMe();
    }

    void Update()
    {
        var wList = NGManager.Me.m_MAWorkerList;
        var cList = NGManager.Me.m_MACreatureList;
        var hList = NGManager.Me.m_MAHeroList;
        var huList = NGManager.Me.m_MAHumanList;
        var aList = NGManager.Me.m_MAAnimalList;
        var chList = NGManager.Me.m_MACharacterList;
        var movingObjects = new List<NGMovingObject>();
        movingObjects.AddRange(wList);
        movingObjects.AddRange(cList);
        movingObjects.AddRange(hList);
        movingObjects.AddRange(huList);
        movingObjects.AddRange(aList);
        movingObjects.AddRange(chList);
        foreach(var mo in movingObjects)
        {
            if (m_lookFor.Contains(mo.GetTypeInfo()))
            {
                var distance = Vector3.Distance(mo.transform.position, transform.position);
                if (distance < m_radius)
                {
                    if (m_questName.IsNullOrWhiteSpace() == false)
                    {

                        if (MAParserManager.Me.IsSectionActive(m_questName) == false)
                        {
                            var section = MAParserManager.Me.ExecuteSection(m_questName);
                            if (m_triggerType == TriggerType.OneShot)
                            {
                                DestroyMe();
                            }
                        }
                        else
                        {
                            m_triggered = true;
                        }
                    }
                    else
                    {
                        m_triggered = true;
                    }
                }
            }
        }
    }
    void DebugSphere()
    {
        // Create a sphere GameObject
        GameObject sphere = GameObject.CreatePrimitive(PrimitiveType.Sphere);

        // Set the sphere's position to the center of this object
        sphere.transform.position = transform.position;
        sphere.transform.SetParent(transform);
        // Set the radius by scaling the sphere
        sphere.transform.localScale = Vector3.one * (m_radius * 2); // Scale is diameter
        Renderer sphereRenderer = sphere.GetComponent<Renderer>();
        //sphereRenderer.material = hdrpMaterial;
        // Create a new HDRP material
        Material hdrpMaterial = sphereRenderer.material;

        // Configure the material for transparency
        hdrpMaterial.SetColor("_BaseColor", new Color(1f, 1f, 1f, 0.5f));
        hdrpMaterial.SetFloat("_SurfaceType", 1); // Surface Type = Transparent
        hdrpMaterial.SetFloat("_BlendMode", 0);   // Alpha blending mode
        hdrpMaterial.SetFloat("_TransparencyFog", 1); // Enable fog interaction if needed
        hdrpMaterial.SetFloat("_AlphaCutoffEnable", 0); // Ensure alpha cutoff is disabled
        hdrpMaterial.SetFloat("_ZWrite", 0); // Disable ZWrite to allow proper blending
        hdrpMaterial.SetFloat("_EnableBlendModePreserveSpecularLighting", 1); 

        hdrpMaterial.renderQueue = (int)UnityEngine.Rendering.RenderQueue.Transparent; // Ensure proper rendering order

        // Assign the material to the sphere
        //Renderer sphereRenderer = sphere.GetComponent<Renderer>();
        //sphereRenderer.material = hdrpMaterial;

        // Remove collider (optional, if you don't need physics for this sphere)
        Destroy(sphere.GetComponent<Collider>());
    }
    void Activate(string _name, float _radius, string _triggerType, List<string> _lookFor, string _questName, bool _debug)
    {
        m_name = name = _name;
        m_radius = _radius;
        Enum.TryParse(_triggerType, out m_triggerType);
        m_lookFor = _lookFor;
        m_questName = _questName;
        m_triggers.Add(this);
        if(_debug)
            DebugSphere();
    }
    
    void Activate(string _name, float _radius, List<string> _lookFor, bool _debug)
    {
        m_name = name = _name;
        m_radius = _radius;
        m_triggerType  = TriggerType.OneShot;
        m_lookFor = _lookFor;
        m_questName = null;
        m_triggers.Add(this);
        if(_debug)
            DebugSphere();
        
    }
    public static MAQuestTrigger Create(string _name, Vector3 _pos, float _radius, string _triggerType, List<string> _lookFor, string _questName, bool _debug)
    {
        var go = new GameObject("QuestTrigger");
        go.transform.SetParent(NGManager.Me.m_questTriggerHolder);
        go.transform.position = _pos;
        var trigger = go.AddComponent<MAQuestTrigger>();
        trigger.Activate(_name, _radius, _triggerType, _lookFor, _questName, _debug);
        return trigger;
    }
    public static MAQuestTrigger Create(string _name, Vector3 _pos, float _radius, List<string> _lookFor, bool _debug)
    {
        var go = new GameObject("QuestTrigger");
        go.transform.SetParent(NGManager.Me.m_questTriggerHolder);
        go.transform.position = _pos;
        var trigger = go.AddComponent<MAQuestTrigger>();
        trigger.Activate(_name, _radius, _lookFor, _debug);
        return trigger;
    }
    public static void KillTrigger(string _name)
    {
        var trigger = m_triggers.Find(o=>o.m_name == _name);
        if(trigger != null)
        {
            trigger.DestroyMe();
        }
    }
}
