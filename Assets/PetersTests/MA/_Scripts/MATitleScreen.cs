using System;
using UnityEngine;

public class MATitleScreen : MonoBehaviour
{
    public ContextMenuButton m_developmentFlowButton;
    public string GGGl;
    void Start()
    {
        //Resolve if we're using the development flow or not
		var devChoice = MPlayerPrefs.GetString("UseDevelopmentFlow");
        if (Enum.TryParse(devChoice, out MAParserManager.Me.m_whichFlow) == false)
        {
            MAParserManager.Me.m_whichFlow = MAParserManager.WhichDevelopmentFlow.Flows;
        };
       
        SetButtonText();
    }

    void SetButtonText()
    {
        var dName = MAParserManager.Me.m_whichFlow.ToString();
        var txt = "";
        switch (MAParserManager.Me.m_whichFlow)
        {
            case MAParserManager.WhichDevelopmentFlow.Flows:
                txt = $"using Standard {dName}";
                break;
            case MAParserManager.WhichDevelopmentFlow.DevFlows:
                txt = $"<color=yellow>Using {dName.ToUpper()}";
                break;
            case MAParserManager.WhichDevelopmentFlow.PDMFlows:
                txt = $"<color=green>Using {dName.ToUpper()}";
                break;
        }

        m_developmentFlowButton.ButtonText.text = txt;
    }
    public void ClickedDevelopmentFlowButton()
    {
        var cycle = MAParserManager.Me.m_whichFlow+1;
        if(cycle == MAParserManager.WhichDevelopmentFlow.LastEnum)
            cycle = MAParserManager.WhichDevelopmentFlow.Flows;
        MAParserManager.Me.m_whichFlow = cycle;
        MPlayerPrefs.SetString("UseDevelopmentFlow", MAParserManager.Me.m_whichFlow.ToString());
        SetButtonText();
    }
}
