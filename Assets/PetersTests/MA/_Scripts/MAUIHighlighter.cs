using UnityEngine;
using UnityEngine.EventSystems;

public class MA<PERSON>Highlighter : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerEnterHandler, IPointerExitHandler
{
    public float m_sizeMultiplier = 1.1f;


    public void OnPointerEnter(PointerEventData eventData)
    {
        transform.localScale *= m_sizeMultiplier;
    }
    
    public void OnPointerExit(PointerEventData eventData)
    {
        transform.localScale /= m_sizeMultiplier;
    }
}
