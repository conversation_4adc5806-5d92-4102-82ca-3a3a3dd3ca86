using TMPro;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.Video;

public class MAVideoPlayer : NGBaseInfoGUI
{
    public VideoPlayer videoPlayer; // Assign in Inspector
    public TMP_Text m_message;
    public GameObject m_playImage;
    public TMP_Text m_playButtonText;

    void Start()
    {
    }

    public void PlayVideo()
    {
        if (videoPlayer != null && videoPlayer.isPlaying)
        {
            videoPlayer.Pause();
        }
        else if (videoPlayer != null && !videoPlayer.isPlaying)
        {
            videoPlayer.Play();
        }
    }

    public void PauseVideo()
    {
        if (videoPlayer != null && videoPlayer.isPlaying)
        {
            videoPlayer.Pause();
        }
    }
    
    public void Update()
    {
        if(videoPlayer != null)
        {
            if(videoPlayer.isPlaying)
            {
                m_playButtonText.text = "Pause";
                if(m_playImage.activeSelf) m_playImage.SetActive(false);
            }
            else
            {
                m_playButtonText.text = "Play";
                if(m_playImage.activeSelf == false) m_playImage.SetActive(true);
            }
        }
        
    }
 
    public void CloseButton()
    {
        DestroyMe();
    }

    public void StopVideo()
    {
        if (videoPlayer != null)
        {
            videoPlayer.Stop();
        }
    }
    
    public void LoadAndPlayVideo(string videoName)
    {
        /*var name = $"Videos/{videoName}";
        // Load the VideoClip from Resources
        VideoClip clip = Resources.Load<VideoClip>(name);

        if (clip == null)
        {
            Debug.LogError($"VideoClip 'name' not found in Resources folder!");
            return;
        }*/
        videoPlayer.Stop(); // Stop any currently playing video
        videoPlayer.source = VideoSource.Url;
        videoPlayer.url = System.IO.Path.Combine(Application.streamingAssetsPath, $"Video/{videoName}.mp4");
        //videoPlayer.clip = clip; // Assign the new clip
        videoPlayer.Play(); // Play the new clip
    }
    void Activate(string _video, string _message)
    {
        base.Activate("Video Helper");
        
        LoadAndPlayVideo(_video);
        if(_message.IsNullOrWhiteSpace())
            m_message.gameObject.SetActive(false);
        else
            m_message.text = _message;
    }
    
    public static MAVideoPlayer Create(string _video, string _message)
    {
        if(s_infoShowing) s_infoShowing.DestroyMe();
        
        var prefab = Resources.Load<MAVideoPlayer>("Videos/MAVideoPlayer");
        if(prefab == null)
        {
            MAParser.ParserError($"No such prefab: MAVideoPlayer");
            return null;
        }
        var inst = Instantiate(prefab, NGManager.Me.m_orderBoardUI);
        inst.Activate(_video, _message);
        s_infoShowing = inst;
        return inst;
    }
}