using UnityEngine;
using UnityEngine.UI;

public class MAZoomHUDLine : MonoBehaviour
{
    public TMPro.TextMeshProUGUI m_name;
    public Image m_image;

    public void Activate(string _name)
    {
        m_name.text = _name;
    }
    public void HighLight(bool _highlight)
    {
        if (_highlight)
        {
            m_name.fontStyle = TMPro.FontStyles.Bold;
            m_name.fontSize *= 1.2f;
            m_image.transform.localScale = new Vector3(1.5f, 1.5f, 1.5f);
        }
        else
        {
            m_name.fontStyle = TMPro.FontStyles.Normal;
            m_name.fontSize /= 1.2f;
            m_image.transform.localScale = new Vector3(1f, 1f, 1f);
        }
    }
}
