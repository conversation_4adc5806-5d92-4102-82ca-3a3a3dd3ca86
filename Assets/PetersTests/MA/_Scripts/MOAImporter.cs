#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using UnityEditor.AssetImporters;

[ScriptedImporter(1, "moa")]
public class MOAImporter : ScriptedImporter
{
    public override void OnImportAsset(AssetImportContext ctx)
    {
        // Load the file's content
        string fileContent = System.IO.File.ReadAllText(ctx.assetPath);

        // Create a TextAsset from the file's content
        TextAsset textAsset = new TextAsset(fileContent);

        // Add the TextAsset to the asset database
        ctx.AddObjectToAsset("text", textAsset);
        ctx.SetMainObject(textAsset);
    }
}
#endif
