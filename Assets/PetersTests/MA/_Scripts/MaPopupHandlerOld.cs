using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.Rendering.HighDefinition;
using UnityEngine.UI;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class MaPopupHandlerOld : MonoBeh<PERSON>our, IPointerEnterHandler, IPointerExitHandler
{
    [TextArea] public string m_popupMessage;
    public float m_delayBeforePopup = 0.5f;

    private MAPopupHelperInfo m_info;
    private MAPopup m_popup;
    private bool m_isPointerOver;
    private float m_triggerTime; // when to show popup

    protected void Start()
    {
        StartCoroutine(WaitForActivate());
        
    }
    private System.Collections.IEnumerator WaitForActivate()
    {
        yield return new WaitUntil(() => GameManager.Me != null && GameManager.Me.LoadComplete);
        // Try to infer info from any child Image sprite name
        var images = GetComponentsInChildren<Image>(includeInactive: true);
        foreach (var i in images)
        {
            var sprite = i != null ? i.sprite : null;
            if (sprite == null) continue;

            var name = sprite.name;
            m_info = MAPopupHelperInfo.GetInfo(name);
            if (m_info != null) break;
        }

        // If no info or message is available, remove this behaviour
        if (m_info == null)
        {
            if(m_popupMessage.IsNullOrWhiteSpace())
                Destroy(this);
        }
        else
        {
            m_popupMessage = m_info.m_popupText;
        }
    }

    public void Update()
    {
        if (m_isPointerOver && Time.time >= m_triggerTime)
        {
            ShowPopup(true);
        }
    }

    public void OnPointerEnter(PointerEventData eventData)
    {
        m_isPointerOver = true;
        m_triggerTime = Time.time + m_delayBeforePopup;
    }

    public void OnPointerExit(PointerEventData eventData)
    {
        m_isPointerOver = false;
        ShowPopup(false);
    }

    public void ShowPopup(bool show)
    {
        if (show)
        {
            if (m_popup != null) return;

            var prefab = MAPrefabLoader.Me.LoadPrefab<MAPopup>("_Prefabs/Dialogs/MAPopup");
            if (prefab == null)
            {
                MAParser.ParserError("No such prefab: MAPopup");
                return;
            }

            m_popup = Instantiate(prefab, transform);
            m_popup.Activate(m_popupMessage);
        }
        else
        {
            if (m_popup != null)
            {
                m_popup.DestroyMe();
                m_popup = null;
            }
        }
    }

    void OnDisable()
    {
        m_isPointerOver = false;
        ShowPopup(false);
    }

    void OnDestroy()
    {
        if (m_popup != null)
        {
            m_popup.DestroyMe();
            m_popup = null;
        }
    }
}
#if WithoutAI

public class MAPopupHandler : MonoBehaviour, IPointerEnterHandler, IPointerExitHandler
{
    public string m_popupMessage;  
    public int m_popupCount = 1;
    public float m_delayBeforePopup = 0.5f;
    private MAPopupHelperInfo m_info;
    private float m_delayTime;
    private bool m_isPointerOver = false;
    private float pointerEnterTime;
    private GameObject m_popup;
    void Start()
    {
        var images = GetComponentsInChildren<Image>();
        foreach(var i in images)
        {
            var name = i.sprite.name;
            m_info = MAPopupHelperInfo.GetInfo(name);
            if(m_info != null)
            {
                break;
            }
        }

        if (m_info == null)
        {
            if(m_popupMessage.IsNullOrWhiteSpace())
                DestroyMe();
        }
        
    }

    void DestroyMe()
    {
        Destroy(this);
    }
    // Update is called once per frame
    void Update()
    {
        if (m_isPointerOver)
        {
            if(Time.time > pointerEnterTime)
            {
                ShowPopup(true);
            }
        }
    }

    public void ShowPopup(bool show)
    {
        if (show)
        {
            if(m_popup != null)
                return;
            var prefab = MAPrefabLoader.Me.LoadPrefab<GameObject>("_Prefabs/Dialogs/MAPopup");
            if(prefab == null)
            {
                MAParser.ParserError($"No such prefab: MAPopup");
                return;
            }
            m_popup = Instantiate(prefab, transform);
            m_popup.GetComponentInChildren<TMP_Text>().text = MAHelper.ReplaceInputTokens(m_popupMessage);
        }
        else
        {
            if(m_popup != null)
            {
                Destroy(m_popup);
                m_popup = null;
            }
        }
    }
    public void OnPointerEnter(PointerEventData eventData)
    {
        m_isPointerOver = true;
        pointerEnterTime = Time.time + m_delayBeforePopup;
    }

    public void OnPointerExit(PointerEventData eventData)
    {
        m_isPointerOver = false;
        ShowPopup(false);
    }
}
#endif
