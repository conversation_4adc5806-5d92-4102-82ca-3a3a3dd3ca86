using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
#if false
public class NGBusinessGiftsPopup : MonoBehaviour, INGDecisionCardHolder
{
    public INGDecisionCardHolder.ECardView CardView { get { return INGDecisionCardHolder.ECardView.Standard; } }
    public bool DragIn3D { get { return true; } }
    public float m_cardWidthAndPadding = 200;
    public int m_maxCardCountBeforeScrolling = 6;
    public float m_fractionOfHiddenCardToShow = 0.3f;
    public bool ShowOrderBoardGUIOnCardClick { get { return true; } }
    
    public RectTransform m_takeScrollView;
    public RectTransform m_chooseScrollView;
    public TMP_Text m_takeAllHeaderText;
    public TMP_Text m_chooseHeaderText;
    public TMP_Text m_playerLevelText;
    public TMP_Text m_panelTitle;

    public Transform m_takeAllHolder;
    public Transform m_chooseHolder;

    public Transform m_takeAllContent;
    public Transform m_chooseContent;
    public RectTransform m_cardHolderPrefab;

    private MAGameFlow m_flow;
    private List<NGBusinessGift> m_takeAllGifts;
    private List<NGBusinessGift> m_chooseGifts;
    private List<NGDirectionCardBase> m_createdCards;
    public List<NGDirectionCardBase> CreatedCards => m_createdCards;
    public int m_maxChooseGifts;
    public GameObject m_objectToEnableWhenDraggingCard;
    
    public Transform GiftsHolder() { return transform; }
    public Transform Root { get { return transform; } }
    
    private bool m_isHidden = true;
    [HideInInspector] public RectTransform m_rt;
    float m_jiggleTimer;
    public Canvas m_panelCanvas;
    public Canvas m_canvas2D = null;

    public Action m_onCardHolderActivated = null;

    public virtual void Start()
    {
        m_panelCanvas.worldCamera = Camera.main;
    }

    protected virtual void OnDestroy()
    {
        m_onCardHolderActivated = null;
        Debug.Log($"Destroying {GetType().Name}");
    }

    public bool EnableCardOnUpwardDrag() { return false; }
    
    public Transform ParentForDraggingCard(NGDirectionCardBase _card)
    {
        return m_canvas2D.transform;
    }

    public void OnCardClick() {}
    public void OnStartCardDrag() { if(m_objectToEnableWhenDraggingCard != null) m_objectToEnableWhenDraggingCard.SetActive(true); }
    public void OnEndCardDrag() { if(m_objectToEnableWhenDraggingCard != null) m_objectToEnableWhenDraggingCard.SetActive(false); }

    public void Activate(List<NGBusinessGift> _takeAllGifts, List<NGBusinessGift> _chooseGifts, int _maxChooseGifts, MAGameFlow _flow)
    {
        if(_flow != null) m_flow = _flow;
        m_takeAllGifts = _takeAllGifts ?? new List<NGBusinessGift>();;
        m_chooseGifts = _chooseGifts ?? new List<NGBusinessGift>();
        m_maxChooseGifts = _maxChooseGifts;
        m_rt = GetComponent<RectTransform>();
        m_createdCards = new List<NGDirectionCardBase>();
        m_takeAllContent.DestroyChildren();
        m_chooseContent.DestroyChildren();
        if(m_playerLevelText != null)
            m_playerLevelText.text = NGBusinessDecisionManager.Me.PlayerLevel.ToString();
        
        CreateCards(m_takeAllGifts, m_takeAllContent, m_takeAllHolder);
        CreateCards(m_chooseGifts, m_chooseContent, m_chooseHolder);
        
        if(m_flow != null)
        {
            m_panelTitle.text = $"{m_flow.m_blockName}[{MAGameFlow.m_activeFlows.IndexOf(m_flow)}]";
        }
        else
        {
            m_panelTitle.text = "Not Flow";
            m_panelTitle.gameObject.SetActive(false);
        }

        m_isHidden = false;
        ToggleHiding(true);
        
        UpdateCardHolderSizes();
        
        m_onCardHolderActivated?.Invoke();
    }

    private void UpdateCardHolderSizes()
    {
        int totalSlots = (int)Math.Round(m_maxCardCountBeforeScrolling / 2f) * 2;
        int halfSlots = totalSlots/2;
        int totalCards = m_takeAllGifts.Count + m_chooseGifts.Count;
        int takeCards = m_takeAllGifts.Count;
        int chooseCards = m_chooseGifts.Count;
        
        float chooseSlots = halfSlots;
        float takeSlots = halfSlots;
        if(totalCards <= totalSlots)
        {
            takeSlots = takeCards;
            chooseSlots = chooseCards; 
        }
        else if(takeCards < halfSlots)
        {
            takeSlots = takeCards;
            chooseSlots = totalSlots - takeSlots;
        }
        else if(chooseCards < halfSlots)
        {
            chooseSlots = chooseCards;
            takeSlots = totalSlots - chooseSlots;
        }

        // Add an extra section to show the hidden cards
        if(chooseSlots < chooseCards) chooseSlots += m_fractionOfHiddenCardToShow;
        if(takeSlots < takeCards) takeSlots += m_fractionOfHiddenCardToShow;

        m_takeScrollView.SetWidth(m_cardWidthAndPadding * takeSlots);
        m_chooseScrollView.SetWidth(m_cardWidthAndPadding * chooseSlots);
    }

    
    //we either need this method or we need to be able top return 'createdCards' because our card will be a subclass of NGDirectionCardBase
    public NGDirectionCardBase CreateCard(NGBusinessGift _gift, Transform _content)
    {
        //NGTutorialManager.Me.FireRewardCardHereTrigger(_gift.m_name);
        var flow = _gift.m_fromFlow ? m_flow : null;
        var res = NGBusinessGiftsPanel.CreateGiftCard(this, m_cardHolderPrefab.gameObject, _gift, flow, _content);
        _gift.SetupContent(res);
        if(res != null) m_createdCards.Add(res);
        return res;
    }

    private void CreateCards(List<NGBusinessGift> _gifts, Transform _content, Transform _holder)
    {
        if(_gifts == null || _gifts.Count == 0)
        {
            _holder.gameObject.SetActive(false);
            return; 
        }
        
        _holder.gameObject.SetActive(true);
        foreach(var g in _gifts)
        {
            CreateCard(g, _content);
        }

        m_onCardHolderActivated?.Invoke();
    }

    void Update()
    {
        if (m_chooseHolder.gameObject.activeSelf)
        {
            m_chooseHeaderText.text = $"Choose {m_maxChooseGifts}";
        }
        if(m_takeAllHolder.gameObject.activeSelf == false && m_chooseHolder.gameObject.activeSelf == false)
        {
            //DestroyMe();
        }
    }

    public void GiftReceived(IBusinessCard _card)
    {
        var _fromCard = _card as NGDirectionCardBase;
        var gift = _fromCard.Gift;
        bool isChooseCard = RemoveCard(_fromCard);

        // Only if the card is a flow card
        var gameFlow = _card.GameFlow;
        if(gameFlow != null)
        {
            gameFlow.GiftRecieved(gift, isChooseCard);
            gameFlow.SendBusinessGiftInteractionAnalyticsEvent(gift, "Used");
        }
#if OldBusinessFlow
        else if(flow != null)
        {
            flow.GiftRecieved();
            flow.RemoveGift(gift, isChooseCard);
            flow.SendBusinessGiftInteractionAnalyticsEvent(gift, "Used");
        }
#endif
        else
        {
            Debug.LogError($"No flow found for card {_card}");
        }
    }

    public int WorkerCards()
    {
        int count = 0;
        foreach(var card in m_chooseGifts)
        {
            if(card.Type == NGBusinessGift.GiftType.Worker) count++;
            if(card.Type == NGBusinessGift.GiftType.Hero) count++;
        }
        foreach(var card in m_takeAllGifts)
        {
            if(card.Type == NGBusinessGift.GiftType.Worker) count++;
            if(card.Type == NGBusinessGift.GiftType.Hero) count++;
        }
        return count;
    }
    
    public bool RemoveCard(NGDirectionCardBase _card)
    {
        m_createdCards?.Remove(_card);
        m_chooseGifts?.Remove(_card.Gift);
        m_takeAllGifts?.Remove(_card.Gift);
        
        bool isChooseCard = _card.m_origionalParent == m_chooseContent;
        
        if (_card.m_origionalParent != null)
        {
            if (m_takeAllContent.childCount == 1 && isChooseCard == false)
            {
                m_takeAllHolder.gameObject.SetActive(false);
            }

            if (isChooseCard)
            {
                --m_maxChooseGifts;
                if (m_chooseContent.childCount == 1 || m_maxChooseGifts <= 0 )
                {
                    m_maxChooseGifts = 0;
                    m_chooseGifts.Clear();
                    m_chooseHolder.gameObject.SetActive(false);
                }
            }
        }
        else
        {
            Debug.LogError($"Can't find card to remove {_card} TakeCount={m_takeAllContent.childCount} ChooseCount={m_chooseContent.childCount}");
        }

        UpdateCardHolderSizes();
        
        return isChooseCard;
    }
    
    public void Clear()
    {
        m_takeAllContent.DestroyChildren();
        m_chooseContent.DestroyChildren();
        m_takeAllGifts?.Clear();
        m_chooseGifts?.Clear();
        m_createdCards?.Clear();
    }

    public void SetInDrawer(bool _on, float _scaleInDrawer)
    {
        GetComponent<AlwaysFaceCamera>().enabled = !_on;
        GetComponent<Canvas>().overrideSorting = _on;
        transform.localScale = Vector3.one * (_on ? _scaleInDrawer : 1f);
    }
    
    public void RemoveAllExistingOrderCards()
    {
        List<NGOrderTile> cardsToRemove = new ();
        foreach(var card in m_createdCards)
        {
            var orderCard = card as NGOrderTile;
            if(orderCard == null)
                continue;
                
            cardsToRemove.Add(orderCard);
        }
        
        foreach(var cardToRemove in cardsToRemove)
        {
            RemoveCard(cardToRemove);
        }
    }
    
    public Transform GetParentHolder(NGDirectionCardBase _card)
    {
        var takeAllCards = m_takeAllContent.GetComponentsInChildren<NGDirectionCardBase>();
        if(takeAllCards == null) return null;
        if(takeAllCards.Contains(_card)) return m_takeAllContent;

        var chooseCards = m_chooseContent.GetComponentsInChildren<NGDirectionCardBase>();
        if(chooseCards == null) return null;
        if(chooseCards.Contains(_card)) return m_chooseContent;
        
        return null;
    }
    
    virtual protected void OnHidden() { }

    public void ToggleHiding(bool _hide, bool _isDueToClick = false)
    {
        if (_hide == m_isHidden)
            return;
        if(BuildingPlacementManager.Me.IsActive)
            return;
            
        m_isHidden = !m_isHidden;

        if(m_isHidden) OnHidden();

        /*if (GameManager.Me.IsOKToPlayUISound() && this != null)
        {
            if(m_isHidden)
                AudioClipManager.Me.PlaySound("PlaySound_Card_PanelClosed", transform);
            else
                AudioClipManager.Me.PlaySound("PlaySound_Card_PanelOpen", transform);
        }*/
    }

    public bool IsHidden { get { return m_isHidden;  }  }
    
    public virtual void DestroyMe()
    {
        var building = GetComponentInParent<MABuilding>();
        if(building != null && building.CardHolder == this)
        {
            building.CardHolder = null;
        }
        
        // Must use parent as this script is on a child of the prefab gameobject
        Destroy(transform.parent.gameObject); 
    }
    
    public bool HasOutstandingChoice()
    {
        return m_maxChooseGifts > 0;
    }
    
    public void ResetChooseCards(List<NGBusinessGift> _gifts, int _choiceCount)
    {
        Activate(m_takeAllGifts, _gifts, _choiceCount, m_flow);
    }
    
    public void AddCards(List<NGBusinessGift> _takeAllGifts, bool _toFront = true)
    {
        var takeAllGifts = _takeAllGifts ?? new List<NGBusinessGift>();
       // var chooseGifts = _chooseGifts ?? new List<NGBusinessGift>();

       if(_toFront)
       {
           takeAllGifts.AddRange(m_takeAllGifts);
           //chooseGifts.AddRange(Me.m_chooseGifts);
       }
       else
       {
           takeAllGifts.InsertRange(0, m_takeAllGifts);
       }

       var maxChoose = m_maxChooseGifts;
            
        Activate(takeAllGifts, m_chooseGifts, maxChoose, m_flow);
    }
    
    public static Vector3 CalculatePosition(MABuilding _building)
    {
        return _building.GetCentalPosition(true) + new Vector3(0, 1, 0);
    }
    
    public void UpdatePosition(MABuilding _building)
    {
        transform.position = CalculatePosition(_building);
    }
    
    public IEnumerator UpdatePositionDelayed(MABuilding _building)
    {
        yield return null;
        transform.position = CalculatePosition(_building);
    }

    /*public static NGBusinessGiftsPopup Create(MABuilding _building, List<NGBusinessGift> _takeAllGifts, List<NGBusinessGift> _chooseGifts = null, int _maxChooseGifts = 0, MAGameFlow _flow = null)
    {
        return Create(_building.GetCentalPosition(true) + new Vector3(0, 1, 0), _building, _takeAllGifts, _chooseGifts, _maxChooseGifts, _flow);
    }*/
    
    public static NGBusinessGiftsPopup Create(MABuilding _parent, List<NGBusinessGift> _takeAllGifts = null, List<NGBusinessGift> _chooseGifts = null, int _maxChooseGifts = 0, MAGameFlow _flow = null)
    {
        var takeAllGifts = _takeAllGifts ?? new List<NGBusinessGift>();
        var chooseGifts = _chooseGifts ?? new List<NGBusinessGift>();

        var go = Instantiate(NGManager.Me.m_NGBusinessGiftsPopupPrefab, _parent.transform);
        var bgp = go.GetComponentInChildren<NGBusinessGiftsPopup>();
        
        bgp.transform.localScale = new Vector3(0.01f, 0.01f, 0.01f);
        bgp.transform.position = CalculatePosition(_parent);
        bgp.Activate(takeAllGifts, chooseGifts, _maxChooseGifts, _flow);
        
        bgp.StartCoroutine(bgp.UpdatePositionDelayed(_parent));
        return bgp;
    }
}
#endif