using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System;

public class NGCardInfoGUI : NGBaseInfoGUI
{
    [Serializable]
    public class NGCardInfoGUIComponents
    {
        public string m_name;
        public NGCardInfoGUIComponentBase m_prefab;
    }

    public BlockInfoPanelLine m_infoLinePrefab;
    public GameObject m_lineHolder;
    public NGDirectionCardBase m_card;
    public List<NGCardInfoGUIComponents> m_componentInfos;
    public List<NGCardInfoGUIComponentBase> m_components;

    protected override Transform CreateComponent(string _name, Transform _parent)
    {
        var componentInfo =
            m_componentInfos.Find(o => o.m_name.Equals(_name, StringComparison.CurrentCultureIgnoreCase));
        if (componentInfo == null)
        {
            Debug.LogError($"No such component {_name}");
            return null;
        }
        var component = NGCardInfoGUIComponentBase.Create(m_card, componentInfo.m_prefab, _parent);
        m_components.Add(component);
        
        if(m_card as NGHeroTile)
        {
            var info = (m_card as NGHeroTile).GetCreatureInfo();
            if(info != null)
            {
                AddLine("Cost To Hire", $"{GlobalData.CurrencySymbol}{m_card.Cost:F0}");
                AddLine("Persona", info.m_persona);
                AddLine("Speed", GetSpeed(info.m_lowWalkSpeed, info.m_highWalkSpeed));
                AddLine("Likes", info.m_likes);
                AddLine("Dislikes", info.m_dislikes);
            }
        }
        else if(m_card as NGWorkerTile)
        {
            var info = (m_card as NGWorkerTile).GetWorkerInfo();
            if(info != null)
            {
                AddLine("Cost To Hire", $"{GlobalData.CurrencySymbol}{m_card.Cost:F0}");
                AddLine("Persona", info.m_persona);
                AddLine("Speed", GetSpeed(info.m_lowWalkSpeed, info.m_highWalkSpeed));
                AddLine("Likes", info.m_likes);
                AddLine("Dislikes", info.m_dislikes);
            }
        }
        
        return component.transform;
    }
    
    public static string GetSpeed(float _lowSpeed, float _highSpeed)
    {
        string speed = $"{(_lowSpeed/MACharacterBase.c_speedCalculationDivisor):0.0}";
        if(Mathf.Approximately(_lowSpeed, _highSpeed) == false)
        {
            speed += $" - {(_highSpeed/MACharacterBase.c_speedCalculationDivisor):0.0}";
        }
        return speed + " m/s";
        /*string[] speed = { "Plodding", "Slow", "Normal", "Speedy", "Fast" };
        var index = (int)(Mathf.Clamp01(_speed / 20f) * (speed.Length-1));
        return speed[index];*/
    }
    
    private void AddLine(string _name, string _value)
    {
        var line = Instantiate(m_infoLinePrefab, m_lineHolder.transform);
        line.m_key.text = _name;
        line.m_value.text = _value;
    }
    
    void Activate(NGDirectionCardBase _card)
    {
        m_card = _card;
        var info = _card.GetDescriptionText();
        base.Activate(info.m_title);
        AddComponent("Info");
        // TODO - Add other sections
    }

    public static NGCardInfoGUI Create(NGDirectionCardBase _card)
    {
        var ciGUI = Instantiate(NGManager.Me.m_NGCardInfoGUIPrefab, NGManager.Me.NGInfoGUIHolder);
        ciGUI.Activate(_card);
        return ciGUI;
    }
}
