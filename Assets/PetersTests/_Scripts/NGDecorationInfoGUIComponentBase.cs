using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
public class NGDecorationInfoGUIComponentBase : MonoBehaviour
{
    public TMP_Text m_title;
    [HideInInspector] public NGDecoration m_decoration;
    virtual public void Activate(NGDecoration _decoration)
    {
        m_decoration = _decoration;
    }
    public static NGDecorationInfoGUIComponentBase Create(NGDecoration _decoration, NGDecorationInfoGUIComponentBase _prefab, Transform _holder)
    {
        var go = Instantiate(_prefab.gameObject, _holder);
        var bigcb = go.GetComponent<NGDecorationInfoGUIComponentBase>();
        bigcb.Activate(_decoration);
        return bigcb;
    }
}
