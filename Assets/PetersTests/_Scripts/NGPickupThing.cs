using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

public class NGPickupThing : NGPickupBase
{
    public TMP_Text m_title;
    public float m_origionalScale = 0.02f;
    public float m_desiredScale = 0.05f;
    private GameObject m_object;

    public void Update()
    {
        if(m_object == null) return;
        
        m_title.text = GetText();
    }
    
    bool Activate(GameObject _object)
    {
        m_object = _object;
        var parent = transform.parent;
        transform.localScale = new Vector3 (m_origionalScale/parent.lossyScale.x, m_origionalScale/parent.lossyScale.y, m_origionalScale/parent.lossyScale.z) * m_desiredScale;

        var text = GetText();
        m_title.text = GetText();
        
        return !string.IsNullOrEmpty(text);
    }
    
    private string GetText()
    {
        var material = m_object.GetComponent<ReactPickupRawMaterialCrate>();
        if (material != null)
        {
            return $"{material.m_contents.m_title}\nx{material.Quantity}";
        }
        
        var worker = m_object.GetComponent<MAWorker>();
        
        if(worker != null)
        {
            var text = $"{worker.Name}";
            return text;
        }
        
        var product = m_object.GetComponent<NGReactPickupAny>(); 
        if (product != null)
        { 
            return $"Product\n${product.m_product.m_pricePerProduct:F0}";
        }
        var multi = m_object.GetComponent<ReactPickupMulti>();
        if (multi != null)
        {
            return multi.GetProductStock();
        }
        var pickupCard = m_object.GetComponent<NGPickupUpgradeCard>();
        if (pickupCard != null)
        {
            return pickupCard.m_gift.m_giftTitle;
        }
        return null;
    }
    
    public static NGPickupThing Create(GameObject _object)
    {
        if (NGManager.Me.m_NGPickupThingPrefab == null) return null;
        var go = Instantiate(NGManager.Me.m_NGPickupThingPrefab.gameObject, _object.transform);
        var pt = go.GetComponent<NGPickupThing>();
        if (pt.Activate(_object) == false)
        {
            DestroyImmediate(go);
            return null;
        }
        return pt;
    }
}
