using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class NGSpendDialog : MonoBehaviour
{
    public TMP_Text m_title;
    public TMP_Text m_message;
    public TMP_Text m_buttonText;
    public Button m_button;
    private Func<int> m_costCallback;
    private Action m_spendGemsCallback;
    private Action m_closeDialog;

    public void ClickedMe()
    {
//            NGPlayer.Me.m_legacyGems.SpendIdeas(cost, "Replenish Mine", "Fill Mine", "Replenish Mine");
//            GameManager.Transactions.AddTransaction_PayToGoFaster("Replenish Mine", cost, false);
        if (m_spendGemsCallback != null)
            m_spendGemsCallback();
        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
    }

    public void ClickedClose()
    {
        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
        DestroyMe();
    }

    private void Update()
    {
        if (m_costCallback != null)
        {
            var cost = m_costCallback();
            if(cost <= 0)
                DestroyMe();
            m_buttonText.text = $"<sprite=3> {cost}";
        }
    }

    public void DestroyMe()
    {
        if (m_closeDialog != null)
            m_closeDialog();
        Destroy(gameObject);
    }
    void Activate(string _title, string _message, Func<int> _costCallback, Action _spentGemsCallback, Action _closeDialog)
    {
        m_costCallback = _costCallback;
        m_spendGemsCallback = _spentGemsCallback;
        m_closeDialog = _closeDialog;
        
        UpdateText(_title, _message);
    }

    public void UpdateText(string _title, string _message)
    {
        m_title.text = _title;
        m_message.text = _message;
    }

    public static NGSpendDialog Create(string _title, string _message, Func<int> _costCallback, Action _spentGemsCallback, Action _closeDialog)
    {
        var go = Instantiate(NGManager.Me.m_NGSpendDialogPrefab, NGManager.Me.NGInfoGUIHolder);
        var sd = go.GetComponent<NGSpendDialog>();
        sd.Activate(_title, _message, _costCallback, _spentGemsCallback, _closeDialog);
        return go;
    }
}
