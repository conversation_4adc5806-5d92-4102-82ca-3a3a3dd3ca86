using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class NGVisualiseDeliveryVan : MonoBehaviour
{
    [SerializeField] private Sprite m_toDestSprite;
    [SerializeField] private Sprite m_toHomeSprite;

    private Image m_vehicleImage = null;
    
    private RectTransform m_parentRectTransform = null;
    private RectTransform m_rectTransform = null;

    private float m_toDestTime;
    private float m_pauseTime;
    private float m_toHomeTime;
    
    private int m_count;
    private static int s_count;
    
    private void Awake()
    {
        m_parentRectTransform = transform.parent.GetComponent<RectTransform>();
        m_rectTransform = transform.GetComponent<RectTransform>();
        m_vehicleImage = GetComponent<Image>();
    }

    public void DestroyMe()
    {
        Destroy(gameObject);
    }
    
    private void Activate(float _toDestTime, float _pauseTime, float _toHomeTime)
    {
        m_vehicleImage.sprite = m_toDestSprite;
        
        m_toDestTime = _toDestTime;
        m_pauseTime = _pauseTime;
        m_toHomeTime = _toHomeTime;
        m_count = s_count++;
        
        StartCoroutine(Travel());
    }

    private void Move(float _normTravelled, bool toDest)
    {
        var size = m_parentRectTransform.rect.width;
        var l = Mathf.Lerp(m_parentRectTransform.anchoredPosition.x, m_parentRectTransform.anchoredPosition.x + size, toDest ? (1f - _normTravelled) : _normTravelled);
        m_rectTransform.anchoredPosition = new Vector2(l, m_rectTransform.anchoredPosition.y);
    }
    
    private IEnumerator Travel()
    {
        float totalTravelTime = m_toDestTime;
        float travelTime = m_toDestTime;
        while(travelTime >= 0)
        {
            Move(travelTime / totalTravelTime, true);
            travelTime -= Time.deltaTime;
            yield return new WaitForEndOfFrame();
        }
        
        m_vehicleImage.sprite = m_toHomeSprite;
        
        if(m_pauseTime > 0) yield return new WaitForSeconds(m_pauseTime);
        
        totalTravelTime = m_toHomeTime;
        travelTime = m_toHomeTime;
        while(travelTime >= 0)
        {
            Move(travelTime / totalTravelTime, false);
            travelTime -= Time.deltaTime;
            yield return new WaitForEndOfFrame();
        }

        DestroyMe();
    }

    public static NGVisualiseDeliveryVan Create(NGVisualiseDeliveryVan _prefab, Transform _holder, float _toDestTime, float _pauseTime, float _toHomeTime)
    {
        var go = Instantiate(_prefab.gameObject, _holder);
        var vdv = go.GetComponent<NGVisualiseDeliveryVan>();
        vdv.Activate(_toDestTime, _pauseTime, _toHomeTime);
        return vdv;
    }
}
