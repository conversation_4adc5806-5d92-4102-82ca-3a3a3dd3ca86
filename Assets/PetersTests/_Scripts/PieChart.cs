using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using TMPro;
using UnityEngine;

public class PieChart : MonoBehaviour
{
    [System.Serializable]
    public class PieChartData
    {
        public PieChartData(string _name, float _value)
        {
            m_name = _name;
            m_value = _value;
        }
        public string m_name;
        public float m_value;
    }

    [System.Serializable]
    public class PieChartDataContainer
    {
        public string m_title;
        public string m_body;
        public List<PieChartData> m_data;
    }

    public List<PieChartWedge> m_wedges;

    public PieChartWedge m_pieChartWedgePrefab;
    public Transform m_pieChartHolder;
    public TMP_Text m_title;
    public TMP_Text m_body;

    public List<Color> m_colors = new List<Color>
    {
        Color.blue,
        Color.green,
        Color.grey,
        Color.yellow,
        Color.red,
        Color.magenta,
    };

    public List<PieChartData> m_data;

    private bool m_activated = false;
    void Start()
    {
        if(m_activated == false)
            Activate("Sample", "Body sample", m_data);
    }

    public void Activate(string _title, string _body, List<PieChartData> _data)
    {
        m_title.text = _title;
        m_body.text = _body;
        m_activated = true;
        m_data = _data;
        var total = 0f;
        var wedgeAngle = 0f;
        foreach (var d in m_data) total += d.m_value;
        m_pieChartHolder.DestroyChildren();
        m_wedges.Clear();

        m_data.Sort((x, y) => y.m_value.CompareTo(x.m_value));
        for (int i = 0; i < m_data.Count; i++)
        {
            var percent = m_data[i].m_value / total;
            var angle = wedgeAngle;
            var color = m_colors[i % m_colors.Count];
            m_wedges.Add(PieChartWedge.Create(m_data[i].m_name, percent, wedgeAngle, color, m_pieChartWedgePrefab, m_pieChartHolder));
            wedgeAngle += 360-percent * 360;
        }
    }

    public static PieChart Create(string _title, string _body, List<PieChartData> _data, Transform _holder)
    {
        var go = Instantiate(NGManager.Me.m_pieChartPrefab, _holder);
        var pc = go.GetComponent<PieChart>();
        pc.Activate(_title, _body, _data);
        return pc;
    }

}
