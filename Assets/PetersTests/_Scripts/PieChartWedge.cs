using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class PieChartWedge : MonoBehaviour
{
    public Image m_wedge;
    public TMP_Text m_text;
    void Activate(string _name, float _percent, float _angle, Color _color)
    {
        if (_name.IsNullOrWhiteSpace() == false && _percent >= 0.035f)
        {
            m_text.gameObject.SetActive(true);
            Vector3 p = m_text.transform.localPosition;

            if (_percent >= 0.085f)
            {
                m_text.text = $"{_name}\n{_percent * 100:F0}%";
            }
            else
            {
                m_text.text = $"{_percent * 100:F0}%";
                p.x = 17.5f;
                p.y = 150.0f;
            }
            m_text.transform.localPosition = p;
            m_text.transform.eulerAngles = new Vector3(m_text.transform.eulerAngles.x, m_text.transform.eulerAngles.y, 360f-_angle);
        }
        else
        {
            m_text.gameObject.SetActive(false);
        }
        m_wedge.fillAmount = _percent;
        transform.eulerAngles = new Vector3(transform.eulerAngles.x, transform.eulerAngles.y, _angle);
        m_wedge.color = _color;
    }
    public static PieChartWedge Create(string _name, float _percent, float _angle, Color _color, PieChartWedge _prefab, Transform _holder)
    {
        var go = Instantiate(_prefab, _holder);
        var pcw = go.GetComponent<PieChartWedge>();
        pcw.Activate(_name, _percent, _angle, _color);
        return pcw;
    }
}
