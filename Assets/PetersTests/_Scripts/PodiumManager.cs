using UnityEngine;
using UnityEngine.EventSystems;

public class PodiumManager : MonoBeh<PERSON>our
{
    Ray ray;
    RaycastHit hit;

    private string m_userID;
    private string m_name;
    private string m_avatar;
    private string m_awards;
    private int m_activity;
    private float m_satisfaction;

    public void SetUserID(string id, string n, string av, string aw, int ac, float s) 
    {
        m_userID = id;
        m_name = n;
        m_avatar = av;
        m_awards = aw;
        m_activity = ac;
        m_satisfaction = s;
    }

    void Update()
    {
        if (string.IsNullOrEmpty(m_userID)) return;
        ray = Camera.main.ScreenPointToRay(Input.mousePosition);
        if (Physics.Raycast(ray, out hit))
        {
            if (Input.GetMouseButtonDown(0) && hit.transform == transform)
                OtherPlayerDetailGUI.Create(m_name, m_userID, m_avatar, m_awards, m_activity, m_satisfaction);
        }
    }
}
