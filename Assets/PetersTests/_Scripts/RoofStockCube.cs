using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class RoofStockCube : MonoBehaviour
{
    public MeshRenderer m_mesh;
    public int m_stockItemIndex;

    private Vector3 m_originalScale = new Vector3(1,1,1);
    private void Awake()
    {
        m_originalScale = m_mesh.transform.localScale;
    }

    public void SetTransparent(bool _transparent)
    {
        if (_transparent)
            m_mesh.transform.localScale = new Vector3(.5f, .5f, 0.1f);
        else
            m_mesh.transform.localScale = new Vector3(1f, 1f, 2.8f);
    }
    public float GetHeightPos()
    {
        return transform.localPosition.y + m_mesh.bounds.extents.y;
    }
    public void Activate(float _blockPad, int _stockItemIndex, float _sectionY, int _createPos, int _maxWidth, bool _transparent, NGCarriableResource _resource)
    {
        m_stockItemIndex = _stockItemIndex;
        var width = m_mesh.bounds.size.x+_blockPad;
        var height = m_mesh.bounds.size.y+_blockPad;
        int horizontal = _createPos % _maxWidth;
        int vertical = _createPos / _maxWidth;
        var pos = new Vector3((float) horizontal * width, (float) vertical * height+_sectionY, 0f);
        transform.localPosition = pos;
        
        SetTransparent(_transparent);
        
        //var mat = _resource.m_roofStockMaterial;
        //m_mesh.material = mat;
    }

    public static RoofStockCube Create(RoofStockCube _prefab, Transform _holder, bool _inDesignTable, float _blockPad, int _stockItemIndex, float _sectionY, int _createPos, int _maxWidth, bool _transparent, NGCarriableResource _resource)
    {
        var go = Instantiate(_prefab.gameObject, _holder);
        if(_inDesignTable)
            go.SetLayerRecursively(31);
        var rsc = go.GetComponent<RoofStockCube>();
        rsc.Activate( _blockPad, _stockItemIndex, _sectionY, _createPos, _maxWidth, _transparent, _resource);
        return rsc;
    }
}
