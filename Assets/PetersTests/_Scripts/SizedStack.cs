using System.Collections.Generic;

public class SizedStack<T> : LinkedList<T>
{
    private readonly int maxSize;
    public SizedStack(int maxSize)
    {
        this.maxSize = maxSize;
    }

    public T Pop()
    {
        T first = First.Value;
        RemoveFirst();
        return first;
    }

    public void Push(T o)
    {
        if (Count >= maxSize)
            RemoveLast();
        AddFirst(o);
    }
}
