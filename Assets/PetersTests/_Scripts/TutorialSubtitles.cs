using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
public class TutorialSubtitles : Mono<PERSON><PERSON><PERSON><TutorialSubtitles>
{
    public TMP_Text m_message;

    public void DestroyMe()
    {
        Destroy(gameObject);
    }
    void Activate(string _message)
    {
        m_message.text = _message;
    }

    public static TutorialSubtitles Create(string _message)
    {
        //        var go = Instantiate(NGManager.Me.m_tutorialSubtitlesPrefab, NGManager.Me.m_tutorialSubtitlesHolder);
        //        var ts = go.GetComponent<TutorialSubtitles>();
        //        ts.Activate(_message);
        return null;// ts;
    }
}
