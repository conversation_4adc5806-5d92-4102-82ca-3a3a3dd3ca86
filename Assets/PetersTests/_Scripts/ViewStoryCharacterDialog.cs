#if CHOICES
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using UnityEngine.UI;

public class ViewStoryCharacterDialog : MonoBehaviour
{
    public TMP_Text m_text;
    public c_character m_who;

    public void ClickedMe()
    {
        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
        PDMViewStoriesManager.Me.ClickedCharacter(m_who.m_givenName);
    }

    public void Activate(c_character _who, Transform _holder)
    {
        m_who = _who;
        var tg = PDMViewStoriesManager.Me.m_characterHolder.GetComponent<ToggleGroup>();
        if (tg)
        {
            var toggle = gameObject.GetComponent<Toggle>();
            if(toggle)
                toggle.group = tg;
        }

        var count = 0;
        foreach (var s in c_story.s_stories)
        {
            if (s.m_characterName.Equals(m_who.m_givenName))
                count++;
        }
        m_text.text = $"{m_who.m_givenName}\n{count}";
    }
    public static ViewStoryCharacterDialog Create(c_character _who)
    {
        var holder = PDMViewStoriesManager.Me.m_characterHolder;
        var go = Instantiate(PDMViewStoriesManager.Me.m_storyCharactersDialog, holder);
        var s = go.GetComponent<ViewStoryCharacterDialog>();
        s.Activate(_who, holder);
        return s;
    }
}
#endif