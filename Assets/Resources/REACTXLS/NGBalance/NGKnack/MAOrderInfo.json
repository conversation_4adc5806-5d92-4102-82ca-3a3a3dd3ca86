[{"id": "68b9881004ec85029f0678b1", "m_indexer": "armourorder:0010", "m_name": "Male Armour", "m_useName": "True", "m_index": "0010", "m_mAOrderGiver": "Player", "m_type": "Repeat", "m_product": "Armour", "m_blockName": "armourorder", "m_collectType": "MaleHero", "m_positiveTags": "", "m_negativeTags": "", "m_mustHaveTags": "", "m_tagHint": "The best form of defence is good armour.", "m_cardDescription": "Male Armour", "m_lowQuantity": 1, "m_highQuantity": 1, "m_lowDesignScore": "0.001", "m_highDesignScore": "0.001", "m_rewards": "", "m_design": "1|<PERSON>_<PERSON><PERSON>_Mannequin@|0|"}, {"id": "68b9881004ec85029f0678b5", "m_indexer": "armourorder:0011", "m_name": "Female Armour", "m_useName": "True", "m_index": "0011", "m_mAOrderGiver": "Player", "m_type": "Repeat", "m_product": "Armour Female", "m_blockName": "armourorder", "m_collectType": "FemaleHero", "m_positiveTags": "", "m_negativeTags": "", "m_mustHaveTags": "", "m_tagHint": "The best form of defence is good armour.", "m_cardDescription": "Female Armour", "m_lowQuantity": 1, "m_highQuantity": 1, "m_lowDesignScore": "0.001", "m_highDesignScore": "0.001", "m_rewards": "", "m_design": "1|<PERSON>_<PERSON><PERSON>_Mannequin_Female@|0|"}, {"id": "68b9881004ec85029f0678be", "m_indexer": "BriarLakeQuestTable:0010", "m_name": "Bandit King's New clothes", "m_useName": "False", "m_index": "0010", "m_mAOrderGiver": "bandit_king", "m_type": "OneShot", "m_product": "Clothing", "m_blockName": "BriarLakeQuestTable", "m_collectType": "", "m_positiveTags": "Coat|Hat|Shoes|Stylish|Trousers", "m_negativeTags": "", "m_mustHaveTags": "", "m_tagHint": "Stylish is rare", "m_cardDescription": "<PERSON><PERSON><PERSON> will only wear the finest of clothes fit for a king. Impress him with his dress.", "m_lowQuantity": 1, "m_highQuantity": 1, "m_lowDesignScore": "0.400", "m_highDesignScore": "0.400", "m_rewards": "", "m_design": "1|<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_Mannequin@|0|"}, {"id": "68bab92ffe63a402cbf13531", "m_indexer": "BriarLakeQuestTable:0020", "m_name": "A Dress For Valmey", "m_useName": "False", "m_index": "0020", "m_mAOrderGiver": "valmey", "m_type": "OneShot", "m_product": "Clothing Female", "m_blockName": "BriarLakeQuestTable", "m_collectType": "", "m_positiveTags": "", "m_negativeTags": "", "m_mustHaveTags": "", "m_tagHint": "A step up from dowdy will make her feel proudy.", "m_cardDescription": "Give <PERSON><PERSON><PERSON> a lift, design her a wearable gift.", "m_lowQuantity": 1, "m_highQuantity": 1, "m_lowDesignScore": "0.100", "m_highDesignScore": "0.100", "m_rewards": "PeopleFavour5", "m_design": "1|<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_Mannequin@|0|"}, {"id": "68b9881004ec85029f0678c4", "m_indexer": "lady_frencham_pound:0010", "m_name": "WormscarBasicRoyalOrders", "m_useName": "False", "m_index": "0010", "m_mAOrderGiver": "lady_frencham_pound", "m_type": "Repeat", "m_product": "Weapons", "m_blockName": "lady_frencham_pound", "m_collectType": "", "m_positiveTags": "Blade|Crossguard|Grip|Pommel", "m_negativeTags": "", "m_mustHaveTags": "", "m_tagHint": "Never left a hand slip on the grip, lest you cut off your own tip.", "m_cardDescription": "Lady <PERSON> needs some dress swords for a parade. If the King sees how well armed her troops are, then everyone's happy.", "m_lowQuantity": 13, "m_highQuantity": 13, "m_lowDesignScore": "0.001", "m_highDesignScore": "0.001", "m_rewards": "RoyalFavour1", "m_design": ""}, {"id": "68b9881104ec85029f067947", "m_indexer": "lady_frencham_pound:0020", "m_name": "Flamboyant Weapons", "m_useName": "False", "m_index": "0020", "m_mAOrderGiver": "lady_frencham_pound", "m_type": "Repeat", "m_product": "Weapons", "m_blockName": "lady_frencham_pound", "m_collectType": "", "m_positiveTags": "", "m_negativeTags": "", "m_mustHaveTags": "", "m_tagHint": "Form over function", "m_cardDescription": "The summer social calendar is littered with high profile parties and events. Lady <PERSON> will attend them all, as will her entourage. She requires matching dress weapons to act as her motif.\n<b>Hint:</b> Flamboyant and fantastical wins over pragmatic and lethal.", "m_lowQuantity": 6, "m_highQuantity": 14, "m_lowDesignScore": "0.001", "m_highDesignScore": "0.001", "m_rewards": "RoyalFavour1", "m_design": ""}, {"id": "68b9881104ec85029f06794c", "m_indexer": "lady_frencham_pound:0030", "m_name": "Fancy Weapons", "m_useName": "False", "m_index": "0030", "m_mAOrderGiver": "lady_frencham_pound", "m_type": "Repeat", "m_product": "Weapons", "m_blockName": "lady_frencham_pound", "m_collectType": "", "m_positiveTags": "", "m_negativeTags": "", "m_mustHaveTags": "", "m_tagHint": "", "m_cardDescription": "The jousting tournaments are the real reason <PERSON><PERSON><PERSON><PERSON> attends any social event. It's here that she can exert her dominance over the rest of the aristocracy. She needs to arm her knights with the best weapons money can buy.\n<b>Hint:</b> Style is just as important as substance here.", "m_lowQuantity": 10, "m_highQuantity": 20, "m_lowDesignScore": "0.200", "m_highDesignScore": "0.200", "m_rewards": "RoyalFavour2", "m_design": ""}, {"id": "68b9881004ec85029f0678c9", "m_indexer": "macio_gourd:0001", "m_name": "Fancy Outfits", "m_useName": "False", "m_index": "0001", "m_mAOrderGiver": "macio_gourd", "m_type": "Repeat", "m_product": "Clothing", "m_blockName": "macio_gourd", "m_collectType": "Male<PERSON><PERSON><PERSON>", "m_positiveTags": "", "m_negativeTags": "", "m_mustHaveTags": "", "m_tagHint": "Collars and belts befit the wearers.", "m_cardDescription": "The pomp and the pageantry. A victory parade will take place in the capital soon, for his Majesty's troops. The honourable Hand wants the court musicians redressed in honour of this occasion.", "m_lowQuantity": 10, "m_highQuantity": 10, "m_lowDesignScore": "0.300", "m_highDesignScore": "0.400", "m_rewards": "RoyalFavour2", "m_design": "1|<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_Mannequin@|0|"}, {"id": "68b9881004ec85029f0678ce", "m_indexer": "macio_gourd:0010", "m_name": "Female Outfits", "m_useName": "False", "m_index": "0010", "m_mAOrderGiver": "macio_gourd", "m_type": "Repeat", "m_product": "Clothing Female", "m_blockName": "macio_gourd", "m_collectType": "Female<PERSON><PERSON><PERSON>", "m_positiveTags": "", "m_negativeTags": "", "m_mustHaveTags": "", "m_tagHint": "Rags not riches.", "m_cardDescription": "<PERSON><PERSON><PERSON>'s cousin is getting married and she's got five bridesmaids to dress. She needs simple dresses.", "m_lowQuantity": 5, "m_highQuantity": 5, "m_lowDesignScore": "0.010", "m_highDesignScore": "0.200", "m_rewards": "PeopleFavour3", "m_design": "1|<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_Mannequin@|0|"}, {"id": "68b9881104ec85029f0678d4", "m_indexer": "macio_gourd:0020", "m_name": "Flamboyant Male Outfits", "m_useName": "False", "m_index": "0020", "m_mAOrderGiver": "macio_gourd", "m_type": "Repeat", "m_product": "Clothing", "m_blockName": "macio_gourd", "m_collectType": "Male<PERSON><PERSON><PERSON>", "m_positiveTags": "Stylish", "m_negativeTags": "", "m_mustHaveTags": "", "m_tagHint": "Some thing flamboyant for party goers", "m_cardDescription": "When you're an old money member of the continental aristocracy, as <PERSON><PERSON> is, you dress the part and so does your team. <PERSON><PERSON> needs clothes for his entourage.", "m_lowQuantity": 14, "m_highQuantity": 14, "m_lowDesignScore": "0.200", "m_highDesignScore": "0.300", "m_rewards": "LordsFavour3", "m_design": "1|<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_Mannequin@|0|"}, {"id": "68b9881104ec85029f0678db", "m_indexer": "QuestOrder:0090", "m_name": "Quest Cotters Food", "m_useName": "False", "m_index": "0090", "m_mAOrderGiver": "thomas_cotter", "m_type": "OneShot", "m_product": "Food", "m_blockName": "QuestOrder", "m_collectType": "", "m_positiveTags": "Chicken|Pie|Stock", "m_negativeTags": "", "m_mustHaveTags": "", "m_tagHint": "A pie with chicken.", "m_cardDescription": "<PERSON> loves a paltry pie, especially if it involves poultry. And pie. She's a woman of simple tastes.\n<PERSON> needs two chicken pies.\n<b>Hint:</b> Bk, bk, bk, b-kwark!", "m_lowQuantity": 2, "m_highQuantity": 2, "m_lowDesignScore": "0.200", "m_highDesignScore": "0.200", "m_rewards": "PeopleFavour2", "m_design": ""}, {"id": "68b9881104ec85029f0678e3", "m_indexer": "QuestOrder:0100", "m_name": "<PERSON>'s Sword Order", "m_useName": "False", "m_index": "0100", "m_mAOrderGiver": "<PERSON>", "m_type": "OneShot", "m_product": "Weapons", "m_blockName": "QuestOrder", "m_collectType": "", "m_positiveTags": "Blade|Grip|Short", "m_negativeTags": "", "m_mustHaveTags": "", "m_tagHint": "A sturdy blade built for tight cave skirmishes.", "m_cardDescription": "<PERSON>'s not going in that cave unarmed. \nHe wants a weapon he can kill with.\n<b>Hint:</b> A short blade is great for cave fighting.", "m_lowQuantity": 1, "m_highQuantity": 1, "m_lowDesignScore": "0.200", "m_highDesignScore": "0.200", "m_rewards": "LordsFavour2", "m_design": ""}, {"id": "68b9881104ec85029f0678ed", "m_indexer": "rod_smallwood:0010", "m_name": "Pies a plenty", "m_useName": "False", "m_index": "0010", "m_mAOrderGiver": "rod_smallwood", "m_type": "Repeat", "m_product": "Food", "m_blockName": "rod_smallwood", "m_collectType": "", "m_positiveTags": "Cheap|Meat|Pie|Stock", "m_negativeTags": "Expensive", "m_mustHaveTags": "", "m_tagHint": "Cheap pie with any meat.", "m_cardDescription": "He needs pies for his chain of street vendors, but they need to be cheap. He does not really care about where the ingredients come from. ", "m_lowQuantity": 10, "m_highQuantity": 12, "m_lowDesignScore": "0.001", "m_highDesignScore": "0.001", "m_rewards": "PeopleFavour3", "m_design": ""}, {"id": "68b9881104ec85029f0678f7", "m_indexer": "rod_smallwood:0020", "m_name": "I like veg", "m_useName": "False", "m_index": "0020", "m_mAOrderGiver": "rod_smallwood", "m_type": "Repeat", "m_product": "Food", "m_blockName": "rod_smallwood", "m_collectType": "", "m_positiveTags": "Healthy|Salad|Vegitarian", "m_negativeTags": "Meat|Unhealthy", "m_mustHaveTags": "", "m_tagHint": "A healthy salad.", "m_cardDescription": "<PERSON> and his extended family on on a health drive, supply him with healthy stuff", "m_lowQuantity": 8, "m_highQuantity": 12, "m_lowDesignScore": "0.200", "m_highDesignScore": "0.200", "m_rewards": "PeopleFavour3", "m_design": ""}, {"id": "68b9881104ec85029f0678fb", "m_indexer": "shoporder:0010", "m_name": "Male Clothing", "m_useName": "True", "m_index": "0010", "m_mAOrderGiver": "Player", "m_type": "Repeat", "m_product": "Clothing", "m_blockName": "shoporder", "m_collectType": "Male<PERSON><PERSON><PERSON>", "m_positiveTags": "", "m_negativeTags": "", "m_mustHaveTags": "", "m_tagHint": "", "m_cardDescription": "Make Clothing For A Male", "m_lowQuantity": 6, "m_highQuantity": 6, "m_lowDesignScore": "0.001", "m_highDesignScore": "0.001", "m_rewards": "", "m_design": "1|<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_Mannequin@|0|"}, {"id": "68b9881104ec85029f0678ff", "m_indexer": "shoporder:0020", "m_name": "Female Clothing", "m_useName": "True", "m_index": "0020", "m_mAOrderGiver": "Player", "m_type": "Repeat", "m_product": "Clothing Female", "m_blockName": "shoporder", "m_collectType": "Female<PERSON><PERSON><PERSON>", "m_positiveTags": "", "m_negativeTags": "", "m_mustHaveTags": "", "m_tagHint": "", "m_cardDescription": "Make Clothing For A Female", "m_lowQuantity": 6, "m_highQuantity": 6, "m_lowDesignScore": "0.001", "m_highDesignScore": "0.001", "m_rewards": "", "m_design": "1|<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_Mannequin@|0|"}, {"id": "68b9881104ec85029f067908", "m_indexer": "silvia_plinth:0010", "m_name": "<PERSON><PERSON>p <PERSON>", "m_useName": "False", "m_index": "0010", "m_mAOrderGiver": "silvia_plinth", "m_type": "Repeat", "m_product": "Food", "m_blockName": "silvia_plinth", "m_collectType": "", "m_positiveTags": "Bread|Cheap|cheese|Sandwich|Sliced", "m_negativeTags": "Expensive", "m_mustHaveTags": "", "m_tagHint": "A cheap cheese sandwich.", "m_cardDescription": "<PERSON><PERSON><PERSON> is a woman on a mission. She always needs cheap food for her brood.", "m_lowQuantity": 6, "m_highQuantity": 6, "m_lowDesignScore": "0.001", "m_highDesignScore": "0.001", "m_rewards": "PeopleFavour1", "m_design": ""}, {"id": "68b9881104ec85029f067912", "m_indexer": "silvia_plinth:0040", "m_name": "I Can't Believe It's Not Rat", "m_useName": "False", "m_index": "0040", "m_mAOrderGiver": "silvia_plinth", "m_type": "Repeat", "m_product": "Food", "m_blockName": "silvia_plinth", "m_collectType": "", "m_positiveTags": "Animal_Product|Meat|Protein|Rat", "m_negativeTags": "", "m_mustHaveTags": "", "m_tagHint": "Rat meat tastes like chicken.", "m_cardDescription": "<PERSON><PERSON><PERSON> is mobilising her people, sending union agents across the country to spread the word of unity and people power. She wants food that travels to pack in their bags.", "m_lowQuantity": 6, "m_highQuantity": 6, "m_lowDesignScore": "0.001", "m_highDesignScore": "0.001", "m_rewards": "PeopleFavour2", "m_design": ""}, {"id": "68b9881104ec85029f06791c", "m_indexer": "silvia_plinth:0070", "m_name": "<PERSON><PERSON><PERSON>b", "m_useName": "False", "m_index": "0070", "m_mAOrderGiver": "silvia_plinth", "m_type": "Repeat", "m_product": "Food", "m_blockName": "silvia_plinth", "m_collectType": "", "m_positiveTags": "Animal_Product|Healthy|Meat|Protein", "m_negativeTags": "Bread|Carbohydrate", "m_mustHaveTags": "", "m_tagHint": "Healthy Meat, no Carbs", "m_cardDescription": "<PERSON><PERSON><PERSON>'s team has grown. They will gather in her home to thrash out their manifesto. Success will depend on the fullness of the stomachs. She wants food she can serve to impress.", "m_lowQuantity": 8, "m_highQuantity": 8, "m_lowDesignScore": "0.001", "m_highDesignScore": "0.001", "m_rewards": "PeopleFavour2|UpgradeWorkerCapacityFactory", "m_design": ""}, {"id": "68b9881104ec85029f067926", "m_indexer": "silvia_plinth:0080", "m_name": "Gruel to be Kind", "m_useName": "False", "m_index": "0080", "m_mAOrderGiver": "silvia_plinth", "m_type": "Repeat", "m_product": "Food", "m_blockName": "silvia_plinth", "m_collectType": "", "m_positiveTags": "Healthy|Plant_Based|Stock|Vegan|Vegitarian", "m_negativeTags": "Dairy|Meat", "m_mustHaveTags": "", "m_tagHint": "Soup for vegans", "m_cardDescription": "<PERSON><PERSON><PERSON>'s reputation with the workers of Albion is rising. But she will not let it go to her head. The first meeting of the Union takes place this weekend, she needs to feed everyone who comes.", "m_lowQuantity": 12, "m_highQuantity": 12, "m_lowDesignScore": "0.001", "m_highDesignScore": "0.001", "m_rewards": "PeopleFavour3", "m_design": ""}, {"id": "68bab92ffe63a402cbf1352b", "m_indexer": "Special:0010", "m_name": "First Royal Order", "m_useName": "False", "m_index": "0010", "m_mAOrderGiver": "the_honourable_hand", "m_type": "Repeat", "m_product": "Weapons", "m_blockName": "Special", "m_collectType": "", "m_positiveTags": "", "m_negativeTags": "", "m_mustHaveTags": "", "m_tagHint": "Stabby, stabby.", "m_cardDescription": "His Majesty requires [quantity] [quality] swords. Heads will roll if you fail.", "m_lowQuantity": 20, "m_highQuantity": 20, "m_lowDesignScore": "0.200", "m_highDesignScore": "0.200", "m_rewards": "RoyalFavour3", "m_design": ""}, {"id": "68ca93a5beac710302ede67f", "m_indexer": "Special:0020", "m_name": "No Place For His Mace", "m_useName": "False", "m_index": "0020", "m_mAOrderGiver": "the_honourable_hand", "m_type": "OneShot", "m_product": "Weapons", "m_blockName": "Special", "m_collectType": "", "m_positiveTags": "Mace|Shaft", "m_negativeTags": "", "m_mustHaveTags": "", "m_tagHint": "", "m_cardDescription": "King <PERSON><PERSON> requires the factories of this great nation to produce maces. Let us work together to crush the skulls of our enemies.", "m_lowQuantity": 30, "m_highQuantity": 50, "m_lowDesignScore": "0.001", "m_highDesignScore": "0.002", "m_rewards": "RoyalFavour4", "m_design": ""}, {"id": "68bac307743f0202b1ae2617", "m_indexer": "Special:0100", "m_name": "<PERSON>y <PERSON>'s First Big Order", "m_useName": "False", "m_index": "0100", "m_mAOrderGiver": "Tricky<PERSON><PERSON><PERSON><PERSON>y", "m_type": "Repeat", "m_product": "Food", "m_blockName": "Special", "m_collectType": "", "m_positiveTags": "Expensive|Pie", "m_negativeTags": "Cheap", "m_mustHaveTags": "", "m_tagHint": "Pies filled with anything", "m_cardDescription": "Lord <PERSON><PERSON><PERSON><PERSON><PERSON>'s huge pie order is in. Make sure it's the best quality. Does it want a huge pie making..?", "m_lowQuantity": 25, "m_highQuantity": 30, "m_lowDesignScore": "0.001", "m_highDesignScore": "0.001", "m_rewards": "PeopleFavour4", "m_design": ""}, {"id": "68b9881104ec85029f06792d", "m_indexer": "TrickyOrders:0100", "m_name": "TrickysOrders", "m_useName": "False", "m_index": "0100", "m_mAOrderGiver": "Tricky<PERSON><PERSON><PERSON><PERSON>y", "m_type": "Repeat", "m_product": "Food", "m_blockName": "TrickyOrders", "m_collectType": "", "m_positiveTags": "Pie|Plant_Based|Vegitarian", "m_negativeTags": "Cake|Meat", "m_mustHaveTags": "Pie", "m_tagHint": "Pies for leaf chewers", "m_cardDescription": "Lord <PERSON><PERSON><PERSON><PERSON><PERSON> wants a pie. He wants a pie that tastes of summer and joy and \"the forlorn nature of beauty on the wane\".", "m_lowQuantity": 50, "m_highQuantity": 150, "m_lowDesignScore": "0.200", "m_highDesignScore": "0.400", "m_rewards": "PeopleFavour3", "m_design": ""}, {"id": "68b9881104ec85029f067935", "m_indexer": "TrickyOrders:0110", "m_name": "TrickysSpecialOrder", "m_useName": "False", "m_index": "0110", "m_mAOrderGiver": "Tricky<PERSON><PERSON><PERSON><PERSON>y", "m_type": "Repeat", "m_product": "Food", "m_blockName": "TrickyOrders", "m_collectType": "", "m_positiveTags": "Carbohydrate|Dairy|Expensive|Fruit", "m_negativeTags": "Cheap|salty|Tasteless", "m_mustHaveTags": "", "m_tagHint": "Think happy foods…", "m_cardDescription": "You know when you step out of your house and into the world and it greets you with a smile and a handshake? Yeah, <PERSON><PERSON> wants something that tastes like that.", "m_lowQuantity": 5, "m_highQuantity": 15, "m_lowDesignScore": "0.600", "m_highDesignScore": "0.600", "m_rewards": "PeopleFavour2", "m_design": ""}, {"id": "68b9881104ec85029f06793d", "m_indexer": "weaponorder:0010", "m_name": "Weaponsmith order 1", "m_useName": "False", "m_index": "0010", "m_mAOrderGiver": "Player", "m_type": "Repeat", "m_product": "Weapons", "m_blockName": "weaponorder", "m_collectType": "", "m_positiveTags": "", "m_negativeTags": "", "m_mustHaveTags": "", "m_tagHint": "Something slightly better than a stick will do.", "m_cardDescription": "", "m_lowQuantity": 1, "m_highQuantity": 1, "m_lowDesignScore": "", "m_highDesignScore": "", "m_rewards": "", "m_design": ""}]