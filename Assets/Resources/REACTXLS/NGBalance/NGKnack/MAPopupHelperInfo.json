[{"id": "68bf021573727d02de8fbbf8", "m_name": "CommonersResearchCoin", "m_popupText": "Peoples Favours earned by completing food orders, spent in Arcadium.", "m_popupType": "Always", "m_count": 1, "m_delayBeforePopup": "0.50"}, {"id": "68cd97c91f494d02f9284dad", "m_name": "Decision", "m_popupText": "<LMB> For Details", "m_popupType": "Always", "m_count": 1, "m_delayBeforePopup": "0.50"}, {"id": "68dd2e1ef35b4802e6e50d3e", "m_name": "HandPowerButton", "m_popupText": "<LMB> Toggle Hand Power", "m_popupType": "Always", "m_count": 1, "m_delayBeforePopup": "0.50"}, {"id": "68bf048fd68a6002cc14120d", "m_name": "LordsResearchCoin", "m_popupText": "Lords Favours earned by completing cloth orders, spent in Arcadium.", "m_popupType": "Always", "m_count": 1, "m_delayBeforePopup": "0.50"}, {"id": "68bf015073727d02de8fb9c8", "m_name": "MA_Coin", "m_popupText": "This is your cash; earned by completing orders, quests and finding treasure.", "m_popupType": "Always", "m_count": 1, "m_delayBeforePopup": "0.50"}, {"id": "68bc65b973727d02de8cd5ee", "m_name": "MA_GUI_V2_Icon_QuickResearch", "m_popupText": "<LMB> Open Arcadium", "m_popupType": "Always", "m_count": 1, "m_delayBeforePopup": "0.50"}, {"id": "68bf01da29ff2e02eb575a46", "m_name": "MA_Order_Lords", "m_popupText": "This is an order for cloth", "m_popupType": "Always", "m_count": 1, "m_delayBeforePopup": "0.50"}, {"id": "68bfbbf5d68a6002cc14a649", "m_name": "<PERSON><PERSON>_<PERSON>", "m_popupText": "<LMB> Enter Wall Building Mode", "m_popupType": "Always", "m_count": 1, "m_delayBeforePopup": "0.50"}, {"id": "68bf044ee9af0602fde8b3b5", "m_name": "MysticResearchCoin", "m_popupText": "Mystic Favours earned by killing creatures at night.", "m_popupType": "Always", "m_count": 1, "m_delayBeforePopup": "0.50"}, {"id": "68dd2c4718e38002c479464b", "m_name": "ResearchButton", "m_popupText": "<LMB> Open Arcadium", "m_popupType": "Always", "m_count": 1, "m_delayBeforePopup": "0.50"}, {"id": "68bf01b929ff2e02eb5759ef", "m_name": "RoyalResearchCoin", "m_popupText": "Royal Favours earned by completing metal orders, spent in Arcadium.", "m_popupType": "Always", "m_count": 1, "m_delayBeforePopup": "0.50"}, {"id": "68cd6feb506c0e02d934baf4", "m_name": "Tag:CheckGates", "m_popupText": "<LMB> Zoom to Closed Gate", "m_popupType": "Always", "m_count": 1, "m_delayBeforePopup": "0.50"}, {"id": "68cd6f7c77f4f2031a29918d", "m_name": "Tag:CombatInstance", "m_popupText": "<LMB> Zoom to Creature Attacking", "m_popupType": "Always", "m_count": 1, "m_delayBeforePopup": "0.50"}, {"id": "68cd705c77f4f2031a299a41", "m_name": "Tag:EnemiesInRegion", "m_popupText": "<LMB> Zoom to Enemy in Region", "m_popupType": "Always", "m_count": 1, "m_delayBeforePopup": "0.50"}, {"id": "68cd71ee84f1a402f7db1b6d", "m_name": "Tag:EnemyInRangeOfFinalObjective", "m_popupText": "<LMB> Zoom to Attacking Crypt", "m_popupType": "Always", "m_count": 1, "m_delayBeforePopup": "0.50"}, {"id": "68cd6e6a86789302e7963f97", "m_name": "Tag:Hero", "m_popupText": "<LMB> Zoom to Hero\n<RMB> Hero Info", "m_popupType": "Always", "m_count": 1, "m_delayBeforePopup": "0.50"}, {"id": "68cd71a893221902ee115e3e", "m_name": "Tag:NoOrderInFactory", "m_popupText": "<LMB> Zoom to Factory Without Order", "m_popupType": "Always", "m_count": 1, "m_delayBeforePopup": "0.50"}, {"id": "68de518e7ee28c02c17d7037", "m_name": "TapCalender", "m_popupText": "Shows the progress of time \nfrom Dawn to Dusk.\n<LMB> To Expand", "m_popupType": "Always", "m_count": 1, "m_delayBeforePopup": "1.50"}, {"id": "68de4a627ee28c02c17d3722", "m_name": "TapCurrency", "m_popupText": "<LMB> Show Currency Report", "m_popupType": "Always", "m_count": 1, "m_delayBeforePopup": "0.50"}, {"id": "68dd2c7318e38002c47946ea", "m_name": "WallBuildingButton", "m_popupText": "<LMB> Toggle Wall Building", "m_popupType": "Always", "m_count": 1, "m_delayBeforePopup": "0.50"}]