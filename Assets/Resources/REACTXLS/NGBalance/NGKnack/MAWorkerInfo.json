[{"id": "6797a0f114e23f02f6031df3", "m_name": "AccusedMale", "m_workerType": "QuestGiver", "m_subType": "", "m_level": 0, "m_gender": "Male", "m_prefabName": "AccusedMale", "m_characterPrefabName": "<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>", "m_deathAlignment": "-0.01000", "m_positiveInteractionAlignment": "0.00100", "m_negativeInteractionAlignment": "-0.00100", "m_energy": "10.00", "m_productionPower": "0.250", "m_attack": "0.08", "m_lowWalkSpeed": "10.00000", "m_highWalkSpeed": "14.00000", "m_possessWalkSpeed": "2.00", "m_possessRunSpeed": "3.00", "m_autoFindJob": "False", "m_autoFindHome": "False", "m_lowSkinColor": "1.00", "m_highSkinColor": "0.00", "m_lowBodyColor": "1.00", "m_highBodyColor": "0.00", "m_lowHairColor": "1.00", "m_highHairColor": "0.00", "m_lowOverallScale": "1.00", "m_highOverallScale": "1.00", "m_lowHeightScale": "1.00", "m_highHeightScale": "1.00", "m_lowFatScale": "1.00", "m_highFatScale": "1.00", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "40.00", "m_health": "60.00", "m_tavernCost": 3333, "m_tavernCostPerWorkerMultiplier": "0.10", "m_description": "Stands accused of a crime.", "m_likes": "", "m_dislikes": "", "m_persona": "", "m_spritePath": "male_worker", "m_displayName": "The Accused", "m_deathDropFavour": "PeoplesFavour", "m_deathDropChance": "0.01000", "m_defaultArmour": ""}, {"id": "67daaaf18b9d0202d44c7200", "m_name": "CaveBlockerMetal", "m_workerType": "QuestGiver", "m_subType": "", "m_level": 0, "m_gender": "Male", "m_prefabName": "CaveBlockerMetal", "m_characterPrefabName": "<PERSON>_Ken<PERSON>_The_Cave_Explorer_Ragdoll", "m_deathAlignment": "-0.01000", "m_positiveInteractionAlignment": "0.00100", "m_negativeInteractionAlignment": "-0.00100", "m_energy": "10.00", "m_productionPower": "0.000", "m_attack": "0.10", "m_lowWalkSpeed": "10.00000", "m_highWalkSpeed": "14.00000", "m_possessWalkSpeed": "2.00", "m_possessRunSpeed": "3.00", "m_autoFindJob": "False", "m_autoFindHome": "False", "m_lowSkinColor": "0.40", "m_highSkinColor": "0.40", "m_lowBodyColor": "1.00", "m_highBodyColor": "0.00", "m_lowHairColor": "1.00", "m_highHairColor": "0.00", "m_lowOverallScale": "1.00", "m_highOverallScale": "1.00", "m_lowHeightScale": "1.00", "m_highHeightScale": "1.00", "m_lowFatScale": "1.00", "m_highFatScale": "1.00", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "50.00", "m_health": "60.00", "m_tavernCost": 1000, "m_tavernCostPerWorkerMultiplier": "", "m_description": "Character that blocks the cave in the metal region. Requests a sword.", "m_likes": "", "m_dislikes": "", "m_persona": "", "m_spritePath": "male_worker", "m_displayName": "<PERSON>", "m_deathDropFavour": "", "m_deathDropChance": "0.00000", "m_defaultArmour": ""}, {"id": "68b84d2c9de97a03038b295b", "m_name": "CaveExplorerDeath", "m_workerType": "QuestGiver", "m_subType": "", "m_level": 0, "m_gender": "Male", "m_prefabName": "CaveExplorerDeath", "m_characterPrefabName": "<PERSON>_Ken<PERSON>_The_Cave_Explorer_Ragdoll", "m_deathAlignment": "-0.01000", "m_positiveInteractionAlignment": "0.00100", "m_negativeInteractionAlignment": "-0.00100", "m_energy": "10.00", "m_productionPower": "0.000", "m_attack": "0.10", "m_lowWalkSpeed": "10.00000", "m_highWalkSpeed": "14.00000", "m_possessWalkSpeed": "2.00", "m_possessRunSpeed": "3.00", "m_autoFindJob": "False", "m_autoFindHome": "False", "m_lowSkinColor": "0.40", "m_highSkinColor": "0.40", "m_lowBodyColor": "1.00", "m_highBodyColor": "0.00", "m_lowHairColor": "1.00", "m_highHairColor": "0.00", "m_lowOverallScale": "1.00", "m_highOverallScale": "1.00", "m_lowHeightScale": "1.00", "m_highHeightScale": "1.00", "m_lowFatScale": "1.00", "m_highFatScale": "1.00", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "50.00", "m_health": "60.00", "m_tavernCost": 1000, "m_tavernCostPerWorkerMultiplier": "", "m_description": "Character that blocks the cave in the metal region. Found dying in Crystal Caves.", "m_likes": "", "m_dislikes": "", "m_persona": "", "m_spritePath": "male_worker", "m_displayName": "<PERSON>", "m_deathDropFavour": "", "m_deathDropChance": "0.00000", "m_defaultArmour": ""}, {"id": "67e516c6c5b21f02e5f8f95c", "m_name": "<PERSON><PERSON><PERSON><PERSON>", "m_workerType": "QuestGiver", "m_subType": "", "m_level": 0, "m_gender": "Female", "m_prefabName": "<PERSON><PERSON><PERSON><PERSON>", "m_characterPrefabName": "<PERSON>_<PERSON><PERSON>_Chicken_<PERSON>_<PERSON><PERSON>", "m_deathAlignment": "-0.01000", "m_positiveInteractionAlignment": "0.00100", "m_negativeInteractionAlignment": "-0.00100", "m_energy": "10.00", "m_productionPower": "0.250", "m_attack": "0.10", "m_lowWalkSpeed": "10.00000", "m_highWalkSpeed": "14.00000", "m_possessWalkSpeed": "2.00", "m_possessRunSpeed": "3.00", "m_autoFindJob": "False", "m_autoFindHome": "False", "m_lowSkinColor": "1.00", "m_highSkinColor": "0.00", "m_lowBodyColor": "1.00", "m_highBodyColor": "0.00", "m_lowHairColor": "1.00", "m_highHairColor": "0.00", "m_lowOverallScale": "1.00", "m_highOverallScale": "1.00", "m_lowHeightScale": "1.00", "m_highHeightScale": "1.00", "m_lowFatScale": "1.00", "m_highFatScale": "1.00", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "20.00", "m_health": "20.00", "m_tavernCost": 3333, "m_tavernCostPerWorkerMultiplier": "0.10", "m_description": "Quest giver that asks player to return escaped chickens.", "m_likes": "", "m_dislikes": "", "m_persona": "", "m_spritePath": "female_worker", "m_displayName": "Chicken Farmer", "m_deathDropFavour": "PeoplesFavour", "m_deathDropChance": "0.00100", "m_defaultArmour": ""}, {"id": "67fbbcdb8f292802e95e2f58", "m_name": "Clive<PERSON>ineWorker", "m_workerType": "QuestGiver", "m_subType": "", "m_level": 0, "m_gender": "Male", "m_prefabName": "Clive<PERSON>ineWorker", "m_characterPrefabName": "<PERSON><PERSON><PERSON>_Tourist_Commoner_<PERSON>_<PERSON><PERSON><PERSON><PERSON>", "m_deathAlignment": "-0.01000", "m_positiveInteractionAlignment": "0.00100", "m_negativeInteractionAlignment": "-0.00100", "m_energy": "90.00", "m_productionPower": "0.000", "m_attack": "0.20", "m_lowWalkSpeed": "9.00000", "m_highWalkSpeed": "9.00000", "m_possessWalkSpeed": "3.00", "m_possessRunSpeed": "6.00", "m_autoFindJob": "False", "m_autoFindHome": "False", "m_lowSkinColor": "0.70", "m_highSkinColor": "0.70", "m_lowBodyColor": "0.20", "m_highBodyColor": "0.20", "m_lowHairColor": "0.90", "m_highHairColor": "1.00", "m_lowOverallScale": "1.00", "m_highOverallScale": "1.00", "m_lowHeightScale": "1.00", "m_highHeightScale": "1.00", "m_lowFatScale": "0.50", "m_highFatScale": "0.50", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "50.00", "m_health": "200.00", "m_tavernCost": 200, "m_tavernCostPerWorkerMultiplier": "0.15", "m_description": "<PERSON>, loving brother of <PERSON>", "m_likes": "Metal", "m_dislikes": "Farmers", "m_persona": "Loving", "m_spritePath": "male_worker", "m_displayName": "<PERSON>", "m_deathDropFavour": "RoyalFavour", "m_deathDropChance": "0.50000", "m_defaultArmour": ""}, {"id": "66c47eae3b828c027fca08bb", "m_name": "CommonWorker", "m_workerType": "Worker", "m_subType": "", "m_level": 0, "m_gender": "Male", "m_prefabName": "CommonWorker", "m_characterPrefabName": "<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>", "m_deathAlignment": "-0.01000", "m_positiveInteractionAlignment": "0.00100", "m_negativeInteractionAlignment": "-0.00100", "m_energy": "10.00", "m_productionPower": "0.250", "m_attack": "0.10", "m_lowWalkSpeed": "16.00000", "m_highWalkSpeed": "16.00000", "m_possessWalkSpeed": "2.20", "m_possessRunSpeed": "4.40", "m_autoFindJob": "True", "m_autoFindHome": "True", "m_lowSkinColor": "0.00", "m_highSkinColor": "1.00", "m_lowBodyColor": "0.00", "m_highBodyColor": "0.00", "m_lowHairColor": "0.00", "m_highHairColor": "1.00", "m_lowOverallScale": "0.95", "m_highOverallScale": "1.05", "m_lowHeightScale": "0.95", "m_highHeightScale": "1.05", "m_lowFatScale": "0.95", "m_highFatScale": "1.05", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "25.00", "m_health": "35.00", "m_tavernCost": 40, "m_tavernCostPerWorkerMultiplier": "0.02", "m_description": "The backbone of your operation. Workers need a home to live in and a job to go to. One worker block per job, one bedroom per worker.", "m_likes": "Drinking", "m_dislikes": "Zombies", "m_persona": "Miserable", "m_spritePath": "male_worker", "m_displayName": "Common Worker", "m_deathDropFavour": "PeoplesFavour", "m_deathDropChance": "0.00100", "m_defaultArmour": "6|MA_Male_Clothes_Mannequin@|MA_Clothes_Worker_C_Body@|MA_Clothes_Worker_C_Hat@|MA_Clothes_Worker_C_Pants@|MA_Clothes_Worker_B_Male_Boots@|MA_Clothes_Worker_B_Male_Boots@|5|*******.0|*******.0|*******.0|********.0|********.0|\n\n8|MA_Male_Clothes_Mannequin@|MA_Clothes_Worker_B_Male_Pants@|MA_Clothes_Worker_B_Male_Body@|MA_Clothes_Worker_B_Male_Boots@|MA_Clothes_Worker_B_Male_Hands@|MA_Clothes_Worker_B_Male_Hands@|MA_Clothes_Worker_B_Male_Boots@|MA_Clothes_Worker_B_Male_Hat@|7|*******.0|*******.0|********.0|*******.0|*******.0|********.0|*******.0|\n\n6|MA_Male_Clothes_Mannequin@|MA_Clothes_Worker_A_Male_Body@|MA_Clothes_Worker_A_Male_Pants@|MA_Clothes_Worker_A_Male_Boots@|MA_Clothes_Worker_A_Male_Hat@|MA_Clothes_Worker_A_Male_Boots@|5|*******.0|*******.0|3.0.0.11.0|*******.0|5.0.0.10.0|\n\n6|MA_Male_Clothes_Mannequin@|MA_Clothes_Worker_A_Male_Pants@|MA_Clothes_Worker_C_Shoes@|MA_Clothes_Worker_C_Hat@|MA_Clothes_Worker_B_Male_Body@|MA_Clothes_Worker_C_Shoes@|5|*******.0|********.0|*******.0|*******.0|5.0.0.10.0|\n\n6|MA_Male_Clothes_Mannequin@|MA_Clothes_Worker_B_Male_Pants@|MA_Clothes_Worker_A_Male_Hat@|MA_Clothes_Worker_A_Male_Boots@|MA_Clothes_Worker_C_Body@|MA_Clothes_Worker_A_Male_Boots@|5|*******.0|*******.0|3.0.0.11.0|*******.0|5.0.0.10.0|\n\n6|MA_Male_Clothes_Mannequin@|MA_Clothes_Worker_C_Shoes@|MA_Clothes_Worker_B_Male_Hat@|MA_Clothes_Worker_C_Boots@|MA_Clothes_Worker_C_Body@|MA_Clothes_Worker_B_Male_Pants@|5|********.0|*******.0|3.0.0.11.0|*******.0|5.0.0.7.0|\n\n6|MA_Male_Clothes_Mannequin@|MA_Clothes_Worker_C_Pants@|MA_Clothes_Council_A_Hat@|MA_Clothes_Worker_A_Male_Boots@|MA_Clothes_Worker_A_Male_Boots@|MA_Clothes_Worker_B_Male_Body@|5|*******.0|*******.0|3.0.0.11.0|********.0|5.1.0.1.0|"}, {"id": "68a0bcfe7836c202a61d8581", "m_name": "CommonWorkerBase", "m_workerType": "Worker", "m_subType": "", "m_level": 0, "m_gender": "Male", "m_prefabName": "CommonWorker", "m_characterPrefabName": "<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>", "m_deathAlignment": "-0.01000", "m_positiveInteractionAlignment": "0.00100", "m_negativeInteractionAlignment": "-0.00100", "m_energy": "10.00", "m_productionPower": "0.250", "m_attack": "0.10", "m_lowWalkSpeed": "16.00000", "m_highWalkSpeed": "16.00000", "m_possessWalkSpeed": "2.20", "m_possessRunSpeed": "4.40", "m_autoFindJob": "True", "m_autoFindHome": "True", "m_lowSkinColor": "0.00", "m_highSkinColor": "1.00", "m_lowBodyColor": "0.00", "m_highBodyColor": "0.00", "m_lowHairColor": "0.00", "m_highHairColor": "1.00", "m_lowOverallScale": "0.95", "m_highOverallScale": "1.05", "m_lowHeightScale": "0.95", "m_highHeightScale": "1.05", "m_lowFatScale": "0.95", "m_highFatScale": "1.05", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "25.00", "m_health": "35.00", "m_tavernCost": 40, "m_tavernCostPerWorkerMultiplier": "0.02", "m_description": "The backbone of your operation. Workers need a home to live in and a job to go to. One worker block per job, one bedroom per worker.", "m_likes": "Drinking", "m_dislikes": "Zombies", "m_persona": "Miserable", "m_spritePath": "male_worker", "m_displayName": "Common Worker", "m_deathDropFavour": "PeoplesFavour", "m_deathDropChance": "0.00100", "m_defaultArmour": "6|MA_Male_Clothes_Mannequin@|MA_Clothes_Worker_C_Body@|MA_Clothes_Worker_C_Hat@|MA_Clothes_Worker_C_Pants@|MA_Clothes_Worker_B_Male_Boots@|MA_Clothes_Worker_B_Male_Boots@|5|*******.0|*******.0|*******.0|********.0|********.0|\n\n8|MA_Male_Clothes_Mannequin@|MA_Clothes_Worker_B_Male_Pants@|MA_Clothes_Worker_B_Male_Body@|MA_Clothes_Worker_B_Male_Boots@|MA_Clothes_Worker_B_Male_Hands@|MA_Clothes_Worker_B_Male_Hands@|MA_Clothes_Worker_B_Male_Boots@|MA_Clothes_Worker_B_Male_Hat@|7|*******.0|*******.0|********.0|*******.0|*******.0|********.0|*******.0|\n\n6|MA_Male_Clothes_Mannequin@|MA_Clothes_Worker_A_Male_Body@|MA_Clothes_Worker_A_Male_Pants@|MA_Clothes_Worker_A_Male_Boots@|MA_Clothes_Worker_A_Male_Hat@|MA_Clothes_Worker_A_Male_Boots@|5|*******.0|*******.0|3.0.0.11.0|*******.0|5.0.0.10.0|\n\n6|MA_Male_Clothes_Mannequin@|MA_Clothes_Worker_A_Male_Pants@|MA_Clothes_Worker_C_Shoes@|MA_Clothes_Worker_C_Hat@|MA_Clothes_Worker_B_Male_Body@|MA_Clothes_Worker_C_Shoes@|5|*******.0|********.0|*******.0|*******.0|5.0.0.10.0|\n\n6|MA_Male_Clothes_Mannequin@|MA_Clothes_Worker_B_Male_Pants@|MA_Clothes_Worker_A_Male_Hat@|MA_Clothes_Worker_A_Male_Boots@|MA_Clothes_Worker_C_Body@|MA_Clothes_Worker_A_Male_Boots@|5|*******.0|*******.0|3.0.0.11.0|*******.0|5.0.0.10.0|\n\n6|MA_Male_Clothes_Mannequin@|MA_Clothes_Worker_C_Shoes@|MA_Clothes_Worker_B_Male_Hat@|MA_Clothes_Worker_C_Boots@|MA_Clothes_Worker_C_Body@|MA_Clothes_Worker_B_Male_Pants@|5|********.0|*******.0|3.0.0.11.0|*******.0|5.0.0.7.0|\n\n6|MA_Male_Clothes_Mannequin@|MA_Clothes_Worker_C_Pants@|MA_Clothes_Council_A_Hat@|MA_Clothes_Worker_A_Male_Boots@|MA_Clothes_Worker_A_Male_Boots@|MA_Clothes_Worker_B_Male_Body@|5|*******.0|*******.0|3.0.0.11.0|********.0|5.1.0.1.0|"}, {"id": "68a0bcfd7836c202a61d857c", "m_name": "CommonWorkerBaseFemale", "m_workerType": "Worker", "m_subType": "", "m_level": 0, "m_gender": "Female", "m_prefabName": "CommonWorker", "m_characterPrefabName": "<PERSON>_<PERSON>_Female_Ra<PERSON><PERSON><PERSON>", "m_deathAlignment": "-0.01000", "m_positiveInteractionAlignment": "0.00100", "m_negativeInteractionAlignment": "-0.00100", "m_energy": "10.00", "m_productionPower": "0.250", "m_attack": "0.10", "m_lowWalkSpeed": "16.00000", "m_highWalkSpeed": "16.00000", "m_possessWalkSpeed": "2.20", "m_possessRunSpeed": "4.40", "m_autoFindJob": "True", "m_autoFindHome": "True", "m_lowSkinColor": "0.00", "m_highSkinColor": "1.00", "m_lowBodyColor": "0.00", "m_highBodyColor": "0.00", "m_lowHairColor": "0.00", "m_highHairColor": "1.00", "m_lowOverallScale": "0.95", "m_highOverallScale": "1.05", "m_lowHeightScale": "0.95", "m_highHeightScale": "1.05", "m_lowFatScale": "0.95", "m_highFatScale": "1.05", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "25.00", "m_health": "25.00", "m_tavernCost": 40, "m_tavernCostPerWorkerMultiplier": "0.02", "m_description": "The backbone of your operation. Workers need a home to live in and a job to go to. One worker block per job, one bedroom per worker.", "m_likes": "Drinking", "m_dislikes": "Zombies", "m_persona": "Miserable", "m_spritePath": "male_worker", "m_displayName": "Common Worker", "m_deathDropFavour": "PeoplesFavour", "m_deathDropChance": "0.00100", "m_defaultArmour": "6|MA_Female_Clothes_Mannequin@|MA_Clothes_WorkerA_Female_Body@|MA_Clothes_WorkerA_Female_Legs@|MA_Clothes_WorkerA_Female_Feet@|MA_Clothes_WorkerA_Female_Feet@|MA_Clothes_WorkerA_Female_Head@|5|*******.0|*******.0|********.0|********.0|*******.0|\n6|MA_Female_Clothes_Mannequin@|MA_Clothes_WorkerB_Female_Body@|MA_Clothes_WorkerB_Female_Legs@|MA_Clothes_WorkerA_Female_Feet@|MA_Clothes_WorkerA_Female_Feet@|MA_Clothes_WorkerB_Female_Head@|5|*******.0|*******.0|********.0|********.0|*******.0|\n6|MA_Female_Clothes_Mannequin@|MA_Clothes_WorkerC_Female_Body@|MA_Clothes_WorkerC_Female_Legs@|MA_Clothes_WorkerA_Female_Feet@|MA_Clothes_WorkerA_Female_Feet@|MA_Clothes_WorkerC_Female_Head@|5|*******.0|*******.0|********.0|********.0|*******.0|"}, {"id": "6880b4049390fe0304fc91e0", "m_name": "CommonWorkerFemale", "m_workerType": "Worker", "m_subType": "", "m_level": 0, "m_gender": "Female", "m_prefabName": "CommonWorker", "m_characterPrefabName": "<PERSON>_<PERSON>_Female_Ra<PERSON><PERSON><PERSON>", "m_deathAlignment": "-0.01000", "m_positiveInteractionAlignment": "0.00100", "m_negativeInteractionAlignment": "-0.00100", "m_energy": "10.00", "m_productionPower": "0.250", "m_attack": "0.10", "m_lowWalkSpeed": "16.00000", "m_highWalkSpeed": "16.00000", "m_possessWalkSpeed": "2.20", "m_possessRunSpeed": "4.40", "m_autoFindJob": "True", "m_autoFindHome": "True", "m_lowSkinColor": "0.00", "m_highSkinColor": "1.00", "m_lowBodyColor": "0.00", "m_highBodyColor": "0.00", "m_lowHairColor": "0.00", "m_highHairColor": "1.00", "m_lowOverallScale": "0.95", "m_highOverallScale": "1.05", "m_lowHeightScale": "0.95", "m_highHeightScale": "1.05", "m_lowFatScale": "0.95", "m_highFatScale": "1.05", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "25.00", "m_health": "25.00", "m_tavernCost": 40, "m_tavernCostPerWorkerMultiplier": "0.02", "m_description": "The backbone of your operation. Workers need a home to live in and a job to go to. One worker block per job, one bedroom per worker.", "m_likes": "Drinking", "m_dislikes": "Zombies", "m_persona": "Miserable", "m_spritePath": "male_worker", "m_displayName": "Common Worker", "m_deathDropFavour": "PeoplesFavour", "m_deathDropChance": "0.00100", "m_defaultArmour": "6|MA_Female_Clothes_Mannequin@|MA_Clothes_WorkerA_Female_Body@|MA_Clothes_WorkerA_Female_Legs@|MA_Clothes_WorkerA_Female_Feet@|MA_Clothes_WorkerA_Female_Feet@|MA_Clothes_WorkerA_Female_Head@|5|*******.0|*******.0|********.0|********.0|*******.0|\n6|MA_Female_Clothes_Mannequin@|MA_Clothes_WorkerB_Female_Body@|MA_Clothes_WorkerB_Female_Legs@|MA_Clothes_WorkerA_Female_Feet@|MA_Clothes_WorkerA_Female_Feet@|MA_Clothes_WorkerB_Female_Head@|5|*******.0|*******.0|********.0|********.0|*******.0|\n6|MA_Female_Clothes_Mannequin@|MA_Clothes_WorkerC_Female_Body@|MA_Clothes_WorkerC_Female_Legs@|MA_Clothes_WorkerA_Female_Feet@|MA_Clothes_WorkerA_Female_Feet@|MA_Clothes_WorkerC_Female_Head@|5|*******.0|*******.0|********.0|********.0|*******.0|"}, {"id": "675af098fd8449031e589d99", "m_name": "CottersCousin", "m_workerType": "QuestGiver", "m_subType": "", "m_level": 1, "m_gender": "Male", "m_prefabName": "CommonWorker", "m_characterPrefabName": "<PERSON><PERSON><PERSON>_Worker_Rag<PERSON>", "m_deathAlignment": "-0.01000", "m_positiveInteractionAlignment": "0.00100", "m_negativeInteractionAlignment": "-0.00100", "m_energy": "10.00", "m_productionPower": "0.250", "m_attack": "0.10", "m_lowWalkSpeed": "16.00000", "m_highWalkSpeed": "16.00000", "m_possessWalkSpeed": "2.00", "m_possessRunSpeed": "4.00", "m_autoFindJob": "True", "m_autoFindHome": "True", "m_lowSkinColor": "0.50", "m_highSkinColor": "0.50", "m_lowBodyColor": "0.30", "m_highBodyColor": "0.30", "m_lowHairColor": "0.30", "m_highHairColor": "0.30", "m_lowOverallScale": "0.70", "m_highOverallScale": "0.70", "m_lowHeightScale": "0.60", "m_highHeightScale": "0.70", "m_lowFatScale": "0.90", "m_highFatScale": "0.90", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "10.00", "m_health": "25.00", "m_tavernCost": 35, "m_tavernCostPerWorkerMultiplier": "0.02", "m_description": "<PERSON>, cousin know for their offhand manner and questionable cleanliness regime.", "m_likes": "A good snooze", "m_dislikes": "being woken up", "m_persona": "Lazy", "m_spritePath": "male_worker", "m_displayName": "Thomas's Cousin", "m_deathDropFavour": "PeoplesFavour", "m_deathDropChance": "0.00100", "m_defaultArmour": ""}, {"id": "68dbaca002006a697a51a814", "m_name": "DrunkBandit", "m_workerType": "QuestGiver", "m_subType": "", "m_level": 2, "m_gender": "Male", "m_prefabName": "<PERSON><PERSON><PERSON><PERSON>", "m_characterPrefabName": "<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>g<PERSON><PERSON>", "m_deathAlignment": "-0.01000", "m_positiveInteractionAlignment": "0.00100", "m_negativeInteractionAlignment": "-0.00100", "m_energy": "60.00", "m_productionPower": "0.000", "m_attack": "0.70", "m_lowWalkSpeed": "18.00000", "m_highWalkSpeed": "18.00000", "m_possessWalkSpeed": "3.00", "m_possessRunSpeed": "6.00", "m_autoFindJob": "False", "m_autoFindHome": "False", "m_lowSkinColor": "1.00", "m_highSkinColor": "0.00", "m_lowBodyColor": "1.00", "m_highBodyColor": "0.00", "m_lowHairColor": "1.00", "m_highHairColor": "0.00", "m_lowOverallScale": "1.00", "m_highOverallScale": "1.00", "m_lowHeightScale": "1.00", "m_highHeightScale": "1.00", "m_lowFatScale": "0.90", "m_highFatScale": "0.90", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "50.00", "m_health": "60.00", "m_tavernCost": 9999, "m_tavernCostPerWorkerMultiplier": "0.15", "m_description": "<PERSON><PERSON>, the Suckling. Always thirsty for the tavern's teat.", "m_likes": "<PERSON>", "m_dislikes": "Water", "m_persona": "Sozzled", "m_spritePath": "male_worker", "m_displayName": "<PERSON><PERSON> the Suckling", "m_deathDropFavour": "CommonerFavour", "m_deathDropChance": "0.40000", "m_defaultArmour": ""}, {"id": "67f7a5306bd56c02c171fbb1", "m_name": "<PERSON><PERSON><PERSON><PERSON>", "m_workerType": "QuestGiver", "m_subType": "", "m_level": 0, "m_gender": "Male", "m_prefabName": "<PERSON><PERSON><PERSON><PERSON>", "m_characterPrefabName": "<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON><PERSON><PERSON><PERSON>", "m_deathAlignment": "-0.01000", "m_positiveInteractionAlignment": "0.00100", "m_negativeInteractionAlignment": "-0.00100", "m_energy": "10.00", "m_productionPower": "0.000", "m_attack": "0.80", "m_lowWalkSpeed": "12.00000", "m_highWalkSpeed": "12.00000", "m_possessWalkSpeed": "2.00", "m_possessRunSpeed": "3.00", "m_autoFindJob": "False", "m_autoFindHome": "False", "m_lowSkinColor": "1.00", "m_highSkinColor": "0.00", "m_lowBodyColor": "1.00", "m_highBodyColor": "0.00", "m_lowHairColor": "1.00", "m_highHairColor": "0.00", "m_lowOverallScale": "1.00", "m_highOverallScale": "1.00", "m_lowHeightScale": "1.00", "m_highHeightScale": "1.00", "m_lowFatScale": "1.00", "m_highFatScale": "1.00", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "50.00", "m_health": "60.00", "m_tavernCost": 3333, "m_tavernCostPerWorkerMultiplier": "0.10", "m_description": "Wife of <PERSON>", "m_likes": "", "m_dislikes": "", "m_persona": "", "m_spritePath": "male_worker", "m_displayName": "<PERSON>", "m_deathDropFavour": "PeoplesFavour", "m_deathDropChance": "0.01000", "m_defaultArmour": ""}, {"id": "667e997ebd62640025a883d6", "m_name": "EpicWorker", "m_workerType": "Worker", "m_subType": "", "m_level": 5, "m_gender": "Female", "m_prefabName": "CommonWorker", "m_characterPrefabName": "<PERSON><PERSON><PERSON>_Worker_Rag<PERSON>", "m_deathAlignment": "-0.01000", "m_positiveInteractionAlignment": "0.00100", "m_negativeInteractionAlignment": "-0.00100", "m_energy": "10.00", "m_productionPower": "0.250", "m_attack": "0.10", "m_lowWalkSpeed": "18.00000", "m_highWalkSpeed": "18.00000", "m_possessWalkSpeed": "2.20", "m_possessRunSpeed": "4.40", "m_autoFindJob": "True", "m_autoFindHome": "True", "m_lowSkinColor": "0.00", "m_highSkinColor": "1.00", "m_lowBodyColor": "0.50", "m_highBodyColor": "0.50", "m_lowHairColor": "0.00", "m_highHairColor": "1.00", "m_lowOverallScale": "0.90", "m_highOverallScale": "0.90", "m_lowHeightScale": "0.90", "m_highHeightScale": "1.05", "m_lowFatScale": "0.90", "m_highFatScale": "1.30", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "35.00", "m_health": "100.00", "m_tavernCost": 150, "m_tavernCostPerWorkerMultiplier": "0.08", "m_description": "You keep workers like this in the good houses, know what I mean?", "m_likes": "", "m_dislikes": "", "m_persona": "", "m_spritePath": "population_worker_female", "m_displayName": "Epic Worker", "m_deathDropFavour": "PeoplesFavour", "m_deathDropChance": "0.00100", "m_defaultArmour": ""}, {"id": "678f84d1109195030330035d", "m_name": "Executioner", "m_workerType": "QuestGiver", "m_subType": "", "m_level": 0, "m_gender": "Male", "m_prefabName": "Executioner", "m_characterPrefabName": "SK_Executioner", "m_deathAlignment": "-0.01000", "m_positiveInteractionAlignment": "0.00100", "m_negativeInteractionAlignment": "-0.00100", "m_energy": "10.00", "m_productionPower": "0.250", "m_attack": "0.10", "m_lowWalkSpeed": "16.00000", "m_highWalkSpeed": "16.00000", "m_possessWalkSpeed": "2.00", "m_possessRunSpeed": "4.00", "m_autoFindJob": "False", "m_autoFindHome": "False", "m_lowSkinColor": "1.00", "m_highSkinColor": "0.00", "m_lowBodyColor": "1.00", "m_highBodyColor": "0.00", "m_lowHairColor": "1.00", "m_highHairColor": "0.00", "m_lowOverallScale": "1.10", "m_highOverallScale": "1.10", "m_lowHeightScale": "1.00", "m_highHeightScale": "1.00", "m_lowFatScale": "1.00", "m_highFatScale": "1.00", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "50.00", "m_health": "60.00", "m_tavernCost": 0, "m_tavernCostPerWorkerMultiplier": "0.15", "m_description": "Bringer of justice to the wicked.", "m_likes": "", "m_dislikes": "", "m_persona": "", "m_spritePath": "male_worker", "m_displayName": "Executioner", "m_deathDropFavour": "PeoplesFavour", "m_deathDropChance": "0.00100", "m_defaultArmour": ""}, {"id": "67a4937acce6022dfdb018f1", "m_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "m_workerType": "QuestGiver", "m_subType": "", "m_level": 0, "m_gender": "Female", "m_prefabName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "m_characterPrefabName": "<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_Daughter_<PERSON><PERSON><PERSON><PERSON>", "m_deathAlignment": "-0.01000", "m_positiveInteractionAlignment": "0.00100", "m_negativeInteractionAlignment": "-0.00100", "m_energy": "10.00", "m_productionPower": "0.000", "m_attack": "0.08", "m_lowWalkSpeed": "10.00000", "m_highWalkSpeed": "14.00000", "m_possessWalkSpeed": "2.00", "m_possessRunSpeed": "3.00", "m_autoFindJob": "False", "m_autoFindHome": "False", "m_lowSkinColor": "1.00", "m_highSkinColor": "0.00", "m_lowBodyColor": "1.00", "m_highBodyColor": "0.00", "m_lowHairColor": "1.00", "m_highHairColor": "0.00", "m_lowOverallScale": "0.85", "m_highOverallScale": "0.85", "m_lowHeightScale": "0.85", "m_highHeightScale": "0.85", "m_lowFatScale": "0.70", "m_highFatScale": "0.70", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "0.00", "m_health": "60.00", "m_tavernCost": 3333, "m_tavernCostPerWorkerMultiplier": "0.10", "m_description": "<PERSON> Foreman's daughter", "m_likes": "", "m_dislikes": "", "m_persona": "", "m_spritePath": "female_worker", "m_displayName": "<PERSON><PERSON>'s Daughter", "m_deathDropFavour": "PeoplesFavour", "m_deathDropChance": "0.00100", "m_defaultArmour": ""}, {"id": "6788e72775236702f2409999", "m_name": "ForemanWife", "m_workerType": "QuestGiver", "m_subType": "", "m_level": 0, "m_gender": "Female", "m_prefabName": "ForemanWife", "m_characterPrefabName": "<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_Wife_<PERSON><PERSON><PERSON><PERSON>", "m_deathAlignment": "-0.01000", "m_positiveInteractionAlignment": "0.00100", "m_negativeInteractionAlignment": "-0.00100", "m_energy": "10.00", "m_productionPower": "0.250", "m_attack": "0.08", "m_lowWalkSpeed": "14.00000", "m_highWalkSpeed": "14.00000", "m_possessWalkSpeed": "2.00", "m_possessRunSpeed": "3.00", "m_autoFindJob": "False", "m_autoFindHome": "False", "m_lowSkinColor": "1.00", "m_highSkinColor": "0.00", "m_lowBodyColor": "1.00", "m_highBodyColor": "0.00", "m_lowHairColor": "1.00", "m_highHairColor": "0.00", "m_lowOverallScale": "1.00", "m_highOverallScale": "1.00", "m_lowHeightScale": "1.00", "m_highHeightScale": "1.00", "m_lowFatScale": "1.00", "m_highFatScale": "1.00", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "50.00", "m_health": "60.00", "m_tavernCost": 3333, "m_tavernCostPerWorkerMultiplier": "0.10", "m_description": "The wife of <PERSON> (<PERSON><PERSON>) in the <PERSON><PERSON> quest. She worries about her husband, who bolted out the door and forgot his lunch in the chaos.", "m_likes": "", "m_dislikes": "", "m_persona": "", "m_spritePath": "female_worker", "m_displayName": "<PERSON>'s Wife", "m_deathDropFavour": "PeoplesFavour", "m_deathDropChance": "0.00100", "m_defaultArmour": ""}, {"id": "67f2eafcb2b05a03045bcbba", "m_name": "<PERSON><PERSON><PERSON>", "m_workerType": "QuestGiver", "m_subType": "", "m_level": 0, "m_gender": "Male", "m_prefabName": "<PERSON><PERSON><PERSON>", "m_characterPrefabName": "SK_Kengo_Bandit_<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>", "m_deathAlignment": "0.01000", "m_positiveInteractionAlignment": "-0.00100", "m_negativeInteractionAlignment": "0.00100", "m_energy": "400.00", "m_productionPower": "0.000", "m_attack": "0.90", "m_lowWalkSpeed": "18.00000", "m_highWalkSpeed": "18.00000", "m_possessWalkSpeed": "3.00", "m_possessRunSpeed": "6.00", "m_autoFindJob": "False", "m_autoFindHome": "False", "m_lowSkinColor": "1.00", "m_highSkinColor": "0.00", "m_lowBodyColor": "1.00", "m_highBodyColor": "0.00", "m_lowHairColor": "1.00", "m_highHairColor": "0.00", "m_lowOverallScale": "1.00", "m_highOverallScale": "1.00", "m_lowHeightScale": "1.00", "m_highHeightScale": "1.00", "m_lowFatScale": "0.90", "m_highFatScale": "0.90", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "100.00", "m_health": "600.00", "m_tavernCost": 9999, "m_tavernCostPerWorkerMultiplier": "0.15", "m_description": "<PERSON><PERSON><PERSON>, charming, amoral, sociopathic Bandit King", "m_likes": "Money", "m_dislikes": "Poverty", "m_persona": "<PERSON><PERSON>", "m_spritePath": "male_worker", "m_displayName": "<PERSON><PERSON><PERSON>", "m_deathDropFavour": "LordsFavour", "m_deathDropChance": "0.80000", "m_defaultArmour": ""}, {"id": "67f2ea33b2b05a03045bcb6b", "m_name": "<PERSON><PERSON><PERSON><PERSON>", "m_workerType": "QuestGiver", "m_subType": "", "m_level": 0, "m_gender": "Male", "m_prefabName": "<PERSON><PERSON><PERSON><PERSON>", "m_characterPrefabName": "<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>g<PERSON><PERSON>", "m_deathAlignment": "-0.01000", "m_positiveInteractionAlignment": "0.00100", "m_negativeInteractionAlignment": "-0.00100", "m_energy": "60.00", "m_productionPower": "0.000", "m_attack": "0.70", "m_lowWalkSpeed": "18.00000", "m_highWalkSpeed": "18.00000", "m_possessWalkSpeed": "3.00", "m_possessRunSpeed": "6.00", "m_autoFindJob": "False", "m_autoFindHome": "False", "m_lowSkinColor": "1.00", "m_highSkinColor": "0.00", "m_lowBodyColor": "1.00", "m_highBodyColor": "0.00", "m_lowHairColor": "1.00", "m_highHairColor": "0.00", "m_lowOverallScale": "1.00", "m_highOverallScale": "1.00", "m_lowHeightScale": "1.00", "m_highHeightScale": "1.00", "m_lowFatScale": "0.90", "m_highFatScale": "0.90", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "50.00", "m_health": "60.00", "m_tavernCost": 9999, "m_tavernCostPerWorkerMultiplier": "0.15", "m_description": "<PERSON><PERSON>, a surly lacky of the Bandit King", "m_likes": "Gloves", "m_dislikes": "Common folk", "m_persona": "<PERSON><PERSON>", "m_spritePath": "male_worker", "m_displayName": "<PERSON><PERSON> the Gate Keeper", "m_deathDropFavour": "LordsFavour", "m_deathDropChance": "0.80000", "m_defaultArmour": ""}, {"id": "667f141e7505c500270484cf", "m_name": "GenericQuestGiverMale", "m_workerType": "QuestGiver", "m_subType": "", "m_level": 0, "m_gender": "Male", "m_prefabName": "GenericQuestGiverMale", "m_characterPrefabName": "<PERSON><PERSON><PERSON>_Tourist_Commoner_<PERSON>_<PERSON><PERSON><PERSON><PERSON>", "m_deathAlignment": "-0.01000", "m_positiveInteractionAlignment": "0.00100", "m_negativeInteractionAlignment": "-0.00100", "m_energy": "10.00", "m_productionPower": "0.250", "m_attack": "0.05", "m_lowWalkSpeed": "12.00000", "m_highWalkSpeed": "12.00000", "m_possessWalkSpeed": "2.00", "m_possessRunSpeed": "3.00", "m_autoFindJob": "False", "m_autoFindHome": "False", "m_lowSkinColor": "0.00", "m_highSkinColor": "1.00", "m_lowBodyColor": "0.00", "m_highBodyColor": "0.00", "m_lowHairColor": "0.00", "m_highHairColor": "1.00", "m_lowOverallScale": "1.10", "m_highOverallScale": "1.10", "m_lowHeightScale": "1.10", "m_highHeightScale": "1.10", "m_lowFatScale": "0.90", "m_highFatScale": "1.30", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "50.00", "m_health": "40.00", "m_tavernCost": 0, "m_tavernCostPerWorkerMultiplier": "0.15", "m_description": "You see me hanging around in a weird place, you know I've got the good for you. Come find me, let's talk.", "m_likes": "", "m_dislikes": "", "m_persona": "", "m_spritePath": "male_worker", "m_displayName": "Quest man", "m_deathDropFavour": "", "m_deathDropChance": "", "m_defaultArmour": ""}, {"id": "66c25741bc1db80288c7fa54", "m_name": "GenericQuestGiverMaleV2", "m_workerType": "QuestGiver", "m_subType": "", "m_level": 0, "m_gender": "Male", "m_prefabName": "GenericQuestGiverMaleV2", "m_characterPrefabName": "MA_Hippy_V6", "m_deathAlignment": "-0.01000", "m_positiveInteractionAlignment": "0.00100", "m_negativeInteractionAlignment": "-0.00100", "m_energy": "10.00", "m_productionPower": "0.250", "m_attack": "0.05", "m_lowWalkSpeed": "12.00000", "m_highWalkSpeed": "12.00000", "m_possessWalkSpeed": "2.00", "m_possessRunSpeed": "3.00", "m_autoFindJob": "False", "m_autoFindHome": "False", "m_lowSkinColor": "1.00", "m_highSkinColor": "0.00", "m_lowBodyColor": "1.00", "m_highBodyColor": "0.00", "m_lowHairColor": "1.00", "m_highHairColor": "0.00", "m_lowOverallScale": "1.00", "m_highOverallScale": "1.00", "m_lowHeightScale": "1.00", "m_highHeightScale": "1.00", "m_lowFatScale": "1.00", "m_highFatScale": "1.00", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "50.00", "m_health": "40.00", "m_tavernCost": 0, "m_tavernCostPerWorkerMultiplier": "0.15", "m_description": "Don't call me an NPC, I'm more than that.", "m_likes": "", "m_dislikes": "", "m_persona": "", "m_spritePath": "male_worker", "m_displayName": "Quest Man", "m_deathDropFavour": "", "m_deathDropChance": "", "m_defaultArmour": ""}, {"id": "67f51b76fd0cd502f267de96", "m_name": "<PERSON><PERSON><PERSON>", "m_workerType": "QuestGiver", "m_subType": "", "m_level": 0, "m_gender": "Male", "m_prefabName": "<PERSON><PERSON><PERSON>", "m_characterPrefabName": "SK_Kengo_Housing_Inspector_<PERSON><PERSON><PERSON><PERSON>", "m_deathAlignment": "-0.01000", "m_positiveInteractionAlignment": "0.00100", "m_negativeInteractionAlignment": "-0.00100", "m_energy": "50.00", "m_productionPower": "0.000", "m_attack": "0.10", "m_lowWalkSpeed": "15.00000", "m_highWalkSpeed": "15.00000", "m_possessWalkSpeed": "3.00", "m_possessRunSpeed": "3.00", "m_autoFindJob": "False", "m_autoFindHome": "False", "m_lowSkinColor": "1.00", "m_highSkinColor": "0.00", "m_lowBodyColor": "1.00", "m_highBodyColor": "0.00", "m_lowHairColor": "1.00", "m_highHairColor": "0.00", "m_lowOverallScale": "1.00", "m_highOverallScale": "1.00", "m_lowHeightScale": "1.00", "m_highHeightScale": "1.00", "m_lowFatScale": "1.00", "m_highFatScale": "1.00", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "50.00", "m_health": "60.00", "m_tavernCost": 9999, "m_tavernCostPerWorkerMultiplier": "0.15", "m_description": "<PERSON><PERSON><PERSON>, inspector supreme nothing escapes his beady eye.", "m_likes": "Order", "m_dislikes": "Chaos", "m_persona": "Perfunctory", "m_spritePath": "male_worker", "m_displayName": "<PERSON><PERSON><PERSON> the inspector", "m_deathDropFavour": "RoyalFavour", "m_deathDropChance": "0.70000", "m_defaultArmour": ""}, {"id": "6839b2d8a7f6760307b5e24c", "m_name": "HippySimon", "m_workerType": "QuestGiver", "m_subType": "", "m_level": 0, "m_gender": "Male", "m_prefabName": "GenericQuestGiverMaleV2", "m_characterPrefabName": "MA_Hippy_V6", "m_deathAlignment": "-0.01000", "m_positiveInteractionAlignment": "0.00100", "m_negativeInteractionAlignment": "-0.00100", "m_energy": "10.00", "m_productionPower": "0.250", "m_attack": "0.05", "m_lowWalkSpeed": "12.00000", "m_highWalkSpeed": "12.00000", "m_possessWalkSpeed": "2.00", "m_possessRunSpeed": "3.00", "m_autoFindJob": "False", "m_autoFindHome": "False", "m_lowSkinColor": "1.00", "m_highSkinColor": "0.00", "m_lowBodyColor": "1.00", "m_highBodyColor": "0.00", "m_lowHairColor": "1.00", "m_highHairColor": "0.00", "m_lowOverallScale": "1.00", "m_highOverallScale": "1.00", "m_lowHeightScale": "1.00", "m_highHeightScale": "1.00", "m_lowFatScale": "1.00", "m_highFatScale": "1.00", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "50.00", "m_health": "40.00", "m_tavernCost": 0, "m_tavernCostPerWorkerMultiplier": "0.15", "m_description": "Don't call me an NPC, I'm more than that.", "m_likes": "", "m_dislikes": "", "m_persona": "", "m_spritePath": "male_worker", "m_displayName": "Quest Man", "m_deathDropFavour": "", "m_deathDropChance": "", "m_defaultArmour": ""}, {"id": "6717c68d29b4dd02c0d76b3f", "m_name": "<PERSON><PERSON>Chara<PERSON>", "m_workerType": "Player", "m_subType": "", "m_level": 0, "m_gender": "Male", "m_prefabName": "<PERSON><PERSON>Chara<PERSON>", "m_characterPrefabName": "<PERSON><PERSON><PERSON>_<PERSON><PERSON>_Character_Ra<PERSON><PERSON><PERSON>", "m_deathAlignment": "-0.01000", "m_positiveInteractionAlignment": "0.00100", "m_negativeInteractionAlignment": "-0.00100", "m_energy": "10.00", "m_productionPower": "0.250", "m_attack": "0.05", "m_lowWalkSpeed": "14.00000", "m_highWalkSpeed": "14.00000", "m_possessWalkSpeed": "2.20", "m_possessRunSpeed": "4.40", "m_autoFindJob": "False", "m_autoFindHome": "False", "m_lowSkinColor": "0.25", "m_highSkinColor": "0.50", "m_lowBodyColor": "1.00", "m_highBodyColor": "0.00", "m_lowHairColor": "0.75", "m_highHairColor": "1.00", "m_lowOverallScale": "1.00", "m_highOverallScale": "1.00", "m_lowHeightScale": "1.00", "m_highHeightScale": "1.00", "m_lowFatScale": "1.00", "m_highFatScale": "1.00", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "9999.00", "m_health": "20.00", "m_tavernCost": 0, "m_tavernCostPerWorkerMultiplier": "", "m_description": "", "m_likes": "", "m_dislikes": "", "m_persona": "", "m_spritePath": "", "m_displayName": "Hooded Character", "m_deathDropFavour": "", "m_deathDropChance": "", "m_defaultArmour": ""}, {"id": "667e997bd067330029b03ae1", "m_name": "LegendaryWorker", "m_workerType": "Worker", "m_subType": "", "m_level": 6, "m_gender": "Female", "m_prefabName": "CommonWorker", "m_characterPrefabName": "<PERSON><PERSON><PERSON>_Worker_Rag<PERSON>", "m_deathAlignment": "-0.01000", "m_positiveInteractionAlignment": "0.00100", "m_negativeInteractionAlignment": "-0.00100", "m_energy": "10.00", "m_productionPower": "0.250", "m_attack": "0.10", "m_lowWalkSpeed": "20.00000", "m_highWalkSpeed": "20.00000", "m_possessWalkSpeed": "2.20", "m_possessRunSpeed": "4.40", "m_autoFindJob": "True", "m_autoFindHome": "True", "m_lowSkinColor": "0.00", "m_highSkinColor": "1.00", "m_lowBodyColor": "0.50", "m_highBodyColor": "0.50", "m_lowHairColor": "0.00", "m_highHairColor": "1.00", "m_lowOverallScale": "0.90", "m_highOverallScale": "0.90", "m_lowHeightScale": "0.90", "m_highHeightScale": "1.05", "m_lowFatScale": "0.90", "m_highFatScale": "1.30", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "40.00", "m_health": "200.00", "m_tavernCost": 350, "m_tavernCostPerWorkerMultiplier": "0.10", "m_description": "They'll be talking about this one long after the zombies have eaten all the best bits.", "m_likes": "", "m_dislikes": "", "m_persona": "", "m_spritePath": "population_worker_female", "m_displayName": "Legendary Worker", "m_deathDropFavour": "PeoplesFavour", "m_deathDropChance": "0.00100", "m_defaultArmour": ""}, {"id": "66bb34848d0441158c831084", "m_name": "LostBoy", "m_workerType": "QuestGiver", "m_subType": "", "m_level": 0, "m_gender": "Male", "m_prefabName": "LostBoy", "m_characterPrefabName": "<PERSON>_<PERSON><PERSON>_The_Lost_<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>", "m_deathAlignment": "-0.04000", "m_positiveInteractionAlignment": "0.02000", "m_negativeInteractionAlignment": "-0.02000", "m_energy": "10.00", "m_productionPower": "0.250", "m_attack": "0.05", "m_lowWalkSpeed": "18.00000", "m_highWalkSpeed": "18.00000", "m_possessWalkSpeed": "2.00", "m_possessRunSpeed": "3.00", "m_autoFindJob": "False", "m_autoFindHome": "False", "m_lowSkinColor": "1.00", "m_highSkinColor": "1.00", "m_lowBodyColor": "1.00", "m_highBodyColor": "0.00", "m_lowHairColor": "1.00", "m_highHairColor": "0.00", "m_lowOverallScale": "1.00", "m_highOverallScale": "1.00", "m_lowHeightScale": "1.00", "m_highHeightScale": "1.00", "m_lowFatScale": "1.00", "m_highFatScale": "1.00", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "5.00", "m_health": "80.00", "m_tavernCost": 0, "m_tavernCostPerWorkerMultiplier": "0.15", "m_description": "The kid's lost. The question is, should he stay that way?", "m_likes": "", "m_dislikes": "", "m_persona": "", "m_spritePath": "lost_boy", "m_displayName": "The Lost Boy", "m_deathDropFavour": "", "m_deathDropChance": "0.00000", "m_defaultArmour": ""}, {"id": "6813a27a6ca81802dd876215", "m_name": "Lumber<PERSON>", "m_workerType": "QuestGiver", "m_subType": "", "m_level": 0, "m_gender": "Male", "m_prefabName": "Lumber<PERSON>", "m_characterPrefabName": "<PERSON>_<PERSON><PERSON>_<PERSON>mber<PERSON>_<PERSON><PERSON><PERSON><PERSON>", "m_deathAlignment": "-0.01000", "m_positiveInteractionAlignment": "0.00100", "m_negativeInteractionAlignment": "-0.00100", "m_energy": "10.00", "m_productionPower": "0.250", "m_attack": "0.10", "m_lowWalkSpeed": "10.00000", "m_highWalkSpeed": "14.00000", "m_possessWalkSpeed": "2.00", "m_possessRunSpeed": "3.00", "m_autoFindJob": "False", "m_autoFindHome": "False", "m_lowSkinColor": "1.00", "m_highSkinColor": "0.00", "m_lowBodyColor": "1.00", "m_highBodyColor": "0.00", "m_lowHairColor": "1.00", "m_highHairColor": "0.00", "m_lowOverallScale": "1.00", "m_highOverallScale": "1.00", "m_lowHeightScale": "1.00", "m_highHeightScale": "1.00", "m_lowFatScale": "1.00", "m_highFatScale": "1.00", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "50.00", "m_health": "60.00", "m_tavernCost": 1000, "m_tavernCostPerWorkerMultiplier": "", "m_description": "Lumber<PERSON>", "m_likes": "", "m_dislikes": "", "m_persona": "", "m_spritePath": "male_worker", "m_displayName": "<PERSON><PERSON> the Lumberjack", "m_deathDropFavour": "", "m_deathDropChance": "0.00000", "m_defaultArmour": ""}, {"id": "67cf0ad0af1ae4030cc26e6b", "m_name": "MessageBottleHusband", "m_workerType": "QuestGiver", "m_subType": "", "m_level": 0, "m_gender": "Male", "m_prefabName": "MessageBottleHusband", "m_characterPrefabName": "<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>", "m_deathAlignment": "-0.01000", "m_positiveInteractionAlignment": "0.00100", "m_negativeInteractionAlignment": "-0.00100", "m_energy": "10.00", "m_productionPower": "0.000", "m_attack": "0.80", "m_lowWalkSpeed": "10.00000", "m_highWalkSpeed": "14.00000", "m_possessWalkSpeed": "2.00", "m_possessRunSpeed": "3.00", "m_autoFindJob": "False", "m_autoFindHome": "False", "m_lowSkinColor": "0.00", "m_highSkinColor": "0.00", "m_lowBodyColor": "0.90", "m_highBodyColor": "0.90", "m_lowHairColor": "0.90", "m_highHairColor": "0.90", "m_lowOverallScale": "1.00", "m_highOverallScale": "1.00", "m_lowHeightScale": "0.90", "m_highHeightScale": "0.90", "m_lowFatScale": "0.90", "m_highFatScale": "0.90", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "50.00", "m_health": "60.00", "m_tavernCost": 3333, "m_tavernCostPerWorkerMultiplier": "0.10", "m_description": "The jilted husband of Captain <PERSON>", "m_likes": "", "m_dislikes": "", "m_persona": "", "m_spritePath": "male_worker", "m_displayName": "<PERSON>", "m_deathDropFavour": "", "m_deathDropChance": "0.00000", "m_defaultArmour": "6|MA_Male_Clothes_Mannequin@|MA_Clothes_Bandit_A_Male_Pants@|MA_Clothes_Worker_A_Male_Body@|MA_Clothes_Bandit_A_Male_Boots@|MA_Clothes_Worker_C_Hat@|MA_Clothes_Bandit_A_Male_Boots@|5|*******.0|*******.0|********.0|*******.0|********.0|\n"}, {"id": "6801108156281c03137010a5", "m_name": "MineEngineer", "m_workerType": "QuestGiver", "m_subType": "", "m_level": 0, "m_gender": "Male", "m_prefabName": "MineEngineer", "m_characterPrefabName": "<PERSON>_<PERSON>_Female_Ra<PERSON><PERSON><PERSON>", "m_deathAlignment": "-0.01000", "m_positiveInteractionAlignment": "0.00100", "m_negativeInteractionAlignment": "-0.00100", "m_energy": "10.00", "m_productionPower": "0.250", "m_attack": "0.10", "m_lowWalkSpeed": "10.00000", "m_highWalkSpeed": "14.00000", "m_possessWalkSpeed": "2.00", "m_possessRunSpeed": "3.00", "m_autoFindJob": "False", "m_autoFindHome": "False", "m_lowSkinColor": "0.30", "m_highSkinColor": "0.30", "m_lowBodyColor": "1.00", "m_highBodyColor": "0.00", "m_lowHairColor": "1.00", "m_highHairColor": "0.00", "m_lowOverallScale": "1.00", "m_highOverallScale": "0.90", "m_lowHeightScale": "0.90", "m_highHeightScale": "1.00", "m_lowFatScale": "1.20", "m_highFatScale": "1.20", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "50.00", "m_health": "60.00", "m_tavernCost": 1000, "m_tavernCostPerWorkerMultiplier": "", "m_description": "Metal Mine Chief Engineer", "m_likes": "", "m_dislikes": "", "m_persona": "", "m_spritePath": "population_worker_female", "m_displayName": "<PERSON><PERSON>", "m_deathDropFavour": "", "m_deathDropChance": "0.00000", "m_defaultArmour": "6|<PERSON>_<PERSON><PERSON>_Mannequin@|MA_<PERSON><PERSON>hes_WorkerB_Female_Feet@|MA_Clothes_WorkerB_Female_Feet@|MA_Costume_WorkerA_Female_Pants@|MA_Clothes_WorkerB_Female_Body@|MA_Clothes_Worker_C_Hat@|5|********.0|********.0|*******.0|*******.0|*******.0|"}, {"id": "67fad911d5528c02f0d83a4f", "m_name": "MineWorker", "m_workerType": "QuestGiver", "m_subType": "", "m_level": 0, "m_gender": "Male", "m_prefabName": "MineWorker", "m_characterPrefabName": "<PERSON><PERSON><PERSON>_Tourist_Commoner_<PERSON>_<PERSON><PERSON><PERSON><PERSON>", "m_deathAlignment": "-0.01000", "m_positiveInteractionAlignment": "0.00100", "m_negativeInteractionAlignment": "-0.00100", "m_energy": "90.00", "m_productionPower": "0.000", "m_attack": "0.20", "m_lowWalkSpeed": "9.00000", "m_highWalkSpeed": "9.00000", "m_possessWalkSpeed": "3.00", "m_possessRunSpeed": "6.00", "m_autoFindJob": "False", "m_autoFindHome": "False", "m_lowSkinColor": "0.70", "m_highSkinColor": "0.70", "m_lowBodyColor": "0.20", "m_highBodyColor": "0.20", "m_lowHairColor": "0.90", "m_highHairColor": "1.00", "m_lowOverallScale": "1.00", "m_highOverallScale": "1.00", "m_lowHeightScale": "1.00", "m_highHeightScale": "1.00", "m_lowFatScale": "0.50", "m_highFatScale": "0.50", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "50.00", "m_health": "200.00", "m_tavernCost": 200, "m_tavernCostPerWorkerMultiplier": "0.15", "m_description": "<PERSON>, very dour mine worker", "m_likes": "Metal", "m_dislikes": "Farmers", "m_persona": "Grumpy", "m_spritePath": "male_worker", "m_displayName": "<PERSON> Mine Worker", "m_deathDropFavour": "RoyalFavour", "m_deathDropChance": "0.50000", "m_defaultArmour": ""}, {"id": "67d419715ac625030eb0d683", "m_name": "PirateCaptain", "m_workerType": "QuestGiver", "m_subType": "", "m_level": 0, "m_gender": "Female", "m_prefabName": "PirateCaptain", "m_characterPrefabName": "<PERSON>_<PERSON>_Female_Ra<PERSON><PERSON><PERSON>", "m_deathAlignment": "-0.01000", "m_positiveInteractionAlignment": "0.00100", "m_negativeInteractionAlignment": "-0.00100", "m_energy": "10.00", "m_productionPower": "0.250", "m_attack": "0.10", "m_lowWalkSpeed": "10.00000", "m_highWalkSpeed": "14.00000", "m_possessWalkSpeed": "2.00", "m_possessRunSpeed": "3.00", "m_autoFindJob": "False", "m_autoFindHome": "False", "m_lowSkinColor": "1.00", "m_highSkinColor": "0.00", "m_lowBodyColor": "1.00", "m_highBodyColor": "0.00", "m_lowHairColor": "1.00", "m_highHairColor": "0.00", "m_lowOverallScale": "1.00", "m_highOverallScale": "1.00", "m_lowHeightScale": "1.00", "m_highHeightScale": "1.00", "m_lowFatScale": "1.00", "m_highFatScale": "1.00", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "50.00", "m_health": "60.00", "m_tavernCost": 1000, "m_tavernCostPerWorkerMultiplier": "", "m_description": "Pirate Captain", "m_likes": "", "m_dislikes": "", "m_persona": "", "m_spritePath": "population_worker_female", "m_displayName": "Pirate Captain", "m_deathDropFavour": "", "m_deathDropChance": "0.00000", "m_defaultArmour": "6|<PERSON>_<PERSON><PERSON>_Mannequin@|MA_<PERSON><PERSON><PERSON>_Royal_B_Boots@|MA_Clothes_Royal_B_Boots@|MA_Clothes_Pirate_B_Body@|MA_Costume_WorkerA_Female_Pants@|MA_Clothes_Pirate_B_Hat@|5|********.0|********.0|*******.0|*******.0|*******.0|"}, {"id": "67d41c27bd2ea203001ed43d", "m_name": "PirateFirstMate", "m_workerType": "QuestGiver", "m_subType": "", "m_level": 0, "m_gender": "Male", "m_prefabName": "PirateFirstMate", "m_characterPrefabName": "<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>", "m_deathAlignment": "-0.01000", "m_positiveInteractionAlignment": "0.00100", "m_negativeInteractionAlignment": "-0.00100", "m_energy": "10.00", "m_productionPower": "0.250", "m_attack": "0.10", "m_lowWalkSpeed": "10.00000", "m_highWalkSpeed": "14.00000", "m_possessWalkSpeed": "2.00", "m_possessRunSpeed": "3.00", "m_autoFindJob": "False", "m_autoFindHome": "False", "m_lowSkinColor": "1.00", "m_highSkinColor": "0.00", "m_lowBodyColor": "1.00", "m_highBodyColor": "0.00", "m_lowHairColor": "1.00", "m_highHairColor": "0.00", "m_lowOverallScale": "1.00", "m_highOverallScale": "1.00", "m_lowHeightScale": "1.00", "m_highHeightScale": "1.00", "m_lowFatScale": "1.00", "m_highFatScale": "1.00", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "50.00", "m_health": "60.00", "m_tavernCost": 1000, "m_tavernCostPerWorkerMultiplier": "", "m_description": "Pirate First Mate", "m_likes": "", "m_dislikes": "", "m_persona": "", "m_spritePath": "male_worker", "m_displayName": "Pirate First Mate", "m_deathDropFavour": "", "m_deathDropChance": "0.00000", "m_defaultArmour": "4|<PERSON>_<PERSON><PERSON>_Mannequin@|MA_Clothes_Pirate_C_Body@|MA_Clothes_Pirate_A_Pants@|MA_Clothes_Pirate_A_Hat@|3|*******.0|*******.0|*******.0|"}, {"id": "67d41c3fbd2ea203001ed44f", "m_name": "PirateSecondMate", "m_workerType": "QuestGiver", "m_subType": "", "m_level": 0, "m_gender": "Male", "m_prefabName": "PirateSecondMate", "m_characterPrefabName": "<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>", "m_deathAlignment": "-0.01000", "m_positiveInteractionAlignment": "0.00100", "m_negativeInteractionAlignment": "-0.00100", "m_energy": "10.00", "m_productionPower": "0.250", "m_attack": "0.10", "m_lowWalkSpeed": "10.00000", "m_highWalkSpeed": "14.00000", "m_possessWalkSpeed": "2.00", "m_possessRunSpeed": "3.00", "m_autoFindJob": "False", "m_autoFindHome": "False", "m_lowSkinColor": "1.00", "m_highSkinColor": "0.00", "m_lowBodyColor": "1.00", "m_highBodyColor": "0.00", "m_lowHairColor": "1.00", "m_highHairColor": "0.00", "m_lowOverallScale": "1.00", "m_highOverallScale": "1.00", "m_lowHeightScale": "1.00", "m_highHeightScale": "1.00", "m_lowFatScale": "1.00", "m_highFatScale": "1.00", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "50.00", "m_health": "60.00", "m_tavernCost": 1000, "m_tavernCostPerWorkerMultiplier": "", "m_description": "Pirate Second Mate ", "m_likes": "", "m_dislikes": "", "m_persona": "", "m_spritePath": "male_worker", "m_displayName": "Pirate Second Mate", "m_deathDropFavour": "", "m_deathDropChance": "0.00000", "m_defaultArmour": "4|<PERSON>_<PERSON><PERSON>_Mannequin@|MA_Clothes_Pirate_C_Pants@|MA_Clothes_Pirate_A_Body@|MA_Clothes_Pirate_A_Head@|3|*******.0|*******.0|*******.0|"}, {"id": "667e99805847ab0027322b26", "m_name": "RareWorker", "m_workerType": "Worker", "m_subType": "", "m_level": 4, "m_gender": "Female", "m_prefabName": "CommonWorker", "m_characterPrefabName": "<PERSON><PERSON><PERSON>_Worker_Rag<PERSON>", "m_deathAlignment": "-0.01000", "m_positiveInteractionAlignment": "0.00100", "m_negativeInteractionAlignment": "-0.00100", "m_energy": "10.00", "m_productionPower": "0.350", "m_attack": "0.10", "m_lowWalkSpeed": "16.00000", "m_highWalkSpeed": "16.00000", "m_possessWalkSpeed": "2.20", "m_possessRunSpeed": "4.40", "m_autoFindJob": "True", "m_autoFindHome": "True", "m_lowSkinColor": "0.00", "m_highSkinColor": "1.00", "m_lowBodyColor": "0.50", "m_highBodyColor": "0.50", "m_lowHairColor": "0.00", "m_highHairColor": "1.00", "m_lowOverallScale": "0.90", "m_highOverallScale": "0.90", "m_lowHeightScale": "0.90", "m_highHeightScale": "1.05", "m_lowFatScale": "0.90", "m_highFatScale": "1.30", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "30.00", "m_health": "50.00", "m_tavernCost": 75, "m_tavernCostPerWorkerMultiplier": "0.05", "m_description": "Rare as in never sodding well around when it's tie to work? Or rare in a diamond in the rough way?", "m_likes": "Drinking", "m_dislikes": "Zombies", "m_persona": "Miserable", "m_spritePath": "population_worker_female", "m_displayName": "Rare Worker", "m_deathDropFavour": "PeoplesFavour", "m_deathDropChance": "0.00100", "m_defaultArmour": ""}, {"id": "67292416327af702d711146d", "m_name": "<PERSON><PERSON><PERSON><PERSON>", "m_workerType": "QuestGiver", "m_subType": "", "m_level": 0, "m_gender": "Male", "m_prefabName": "<PERSON><PERSON><PERSON><PERSON>", "m_characterPrefabName": "SK_Thomas<PERSON><PERSON><PERSON>_Rag<PERSON>ll", "m_deathAlignment": "-0.01000", "m_positiveInteractionAlignment": "0.00100", "m_negativeInteractionAlignment": "-0.00100", "m_energy": "10.00", "m_productionPower": "0.250", "m_attack": "0.08", "m_lowWalkSpeed": "10.00000", "m_highWalkSpeed": "14.00000", "m_possessWalkSpeed": "2.00", "m_possessRunSpeed": "3.00", "m_autoFindJob": "False", "m_autoFindHome": "False", "m_lowSkinColor": "1.00", "m_highSkinColor": "0.00", "m_lowBodyColor": "1.00", "m_highBodyColor": "0.00", "m_lowHairColor": "1.00", "m_highHairColor": "0.00", "m_lowOverallScale": "1.00", "m_highOverallScale": "1.00", "m_lowHeightScale": "1.00", "m_highHeightScale": "1.00", "m_lowFatScale": "1.00", "m_highFatScale": "1.00", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "50.00", "m_health": "60.00", "m_tavernCost": 3333, "m_tavernCostPerWorkerMultiplier": "0.10", "m_description": "Good old lazy <PERSON>, know for his canny ways.", "m_likes": "", "m_dislikes": "", "m_persona": "", "m_spritePath": "male_worker", "m_displayName": "<PERSON>", "m_deathDropFavour": "PeoplesFavour", "m_deathDropChance": "0.01000", "m_defaultArmour": ""}, {"id": "65e996cbe9348200266b63cd", "m_name": "TouristMale", "m_workerType": "Tourist", "m_subType": "", "m_level": 0, "m_gender": "Male", "m_prefabName": "TouristMale", "m_characterPrefabName": "<PERSON><PERSON><PERSON>_Tourist_Commoner_<PERSON>_<PERSON><PERSON><PERSON><PERSON>", "m_deathAlignment": "-0.00100", "m_positiveInteractionAlignment": "0.00100", "m_negativeInteractionAlignment": "-0.00100", "m_energy": "10.00", "m_productionPower": "0.250", "m_attack": "0.05", "m_lowWalkSpeed": "14.00000", "m_highWalkSpeed": "14.00000", "m_possessWalkSpeed": "2.20", "m_possessRunSpeed": "4.40", "m_autoFindJob": "False", "m_autoFindHome": "False", "m_lowSkinColor": "0.25", "m_highSkinColor": "0.50", "m_lowBodyColor": "0.50", "m_highBodyColor": "0.75", "m_lowHairColor": "0.75", "m_highHairColor": "1.00", "m_lowOverallScale": "0.90", "m_highOverallScale": "0.90", "m_lowHeightScale": "0.90", "m_highHeightScale": "1.05", "m_lowFatScale": "0.90", "m_highFatScale": "1.30", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "5.00", "m_health": "20.00", "m_tavernCost": 0, "m_tavernCostPerWorkerMultiplier": "", "m_description": "", "m_likes": "", "m_dislikes": "", "m_persona": "", "m_spritePath": "", "m_displayName": "Tourist", "m_deathDropFavour": "", "m_deathDropChance": "", "m_defaultArmour": ""}, {"id": "67bb7bd0a9a9350e0799f0fd", "m_name": "<PERSON><PERSON><PERSON>", "m_workerType": "QuestGiver", "m_subType": "", "m_level": 0, "m_gender": "Female", "m_prefabName": "<PERSON><PERSON><PERSON>", "m_characterPrefabName": "SK_Ken<PERSON>_Valmey_Ragdoll", "m_deathAlignment": "-0.01000", "m_positiveInteractionAlignment": "0.00100", "m_negativeInteractionAlignment": "-0.00100", "m_energy": "20.00", "m_productionPower": "0.300", "m_attack": "0.10", "m_lowWalkSpeed": "18.00000", "m_highWalkSpeed": "18.00000", "m_possessWalkSpeed": "2.20", "m_possessRunSpeed": "4.40", "m_autoFindJob": "False", "m_autoFindHome": "False", "m_lowSkinColor": "1.00", "m_highSkinColor": "0.00", "m_lowBodyColor": "1.00", "m_highBodyColor": "0.00", "m_lowHairColor": "1.00", "m_highHairColor": "0.00", "m_lowOverallScale": "1.00", "m_highOverallScale": "1.00", "m_lowHeightScale": "1.00", "m_highHeightScale": "1.00", "m_lowFatScale": "1.00", "m_highFatScale": "1.00", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "75.00", "m_health": "20.00", "m_tavernCost": 5555, "m_tavernCostPerWorkerMultiplier": "0.15", "m_description": "It's <PERSON><PERSON><PERSON> expert in town management and production. Draws a mean Pint.", "m_likes": "Flirting", "m_dislikes": "Royals", "m_persona": "Jolly", "m_spritePath": "female_worker", "m_displayName": "<PERSON><PERSON><PERSON>", "m_deathDropFavour": "PeoplesFavour", "m_deathDropChance": "0.10000", "m_defaultArmour": ""}, {"id": "6882049d288a5d02b0de2925", "m_name": "WorshipperFemaleEvil", "m_workerType": "Worshipper", "m_subType": "Evil", "m_level": 0, "m_gender": "Male", "m_prefabName": "MAWorshipper", "m_characterPrefabName": "<PERSON>_<PERSON><PERSON>_Worshipper_Evil_Female_Ra<PERSON><PERSON><PERSON>", "m_deathAlignment": "0.00000", "m_positiveInteractionAlignment": "0.00000", "m_negativeInteractionAlignment": "0.00000", "m_energy": "10.00", "m_productionPower": "0.250", "m_attack": "0.05", "m_lowWalkSpeed": "14.00000", "m_highWalkSpeed": "14.00000", "m_possessWalkSpeed": "2.20", "m_possessRunSpeed": "4.40", "m_autoFindJob": "False", "m_autoFindHome": "False", "m_lowSkinColor": "0.25", "m_highSkinColor": "0.50", "m_lowBodyColor": "0.50", "m_highBodyColor": "0.75", "m_lowHairColor": "0.75", "m_highHairColor": "1.00", "m_lowOverallScale": "0.90", "m_highOverallScale": "0.90", "m_lowHeightScale": "0.90", "m_highHeightScale": "1.05", "m_lowFatScale": "0.90", "m_highFatScale": "1.30", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "12.00", "m_health": "25.00", "m_tavernCost": 0, "m_tavernCostPerWorkerMultiplier": "", "m_description": "They worship you", "m_likes": "", "m_dislikes": "", "m_persona": "", "m_spritePath": "", "m_displayName": "Evil Worshipper", "m_deathDropFavour": "LordsFavour<br />RoyalFavour<br />MysticFavour", "m_deathDropChance": "0.80000", "m_defaultArmour": ""}, {"id": "6882049d288a5d02b0de2931", "m_name": "WorshipperFemaleGood", "m_workerType": "Worshipper", "m_subType": "Good", "m_level": 0, "m_gender": "Male", "m_prefabName": "MAWorshipper", "m_characterPrefabName": "<PERSON>_<PERSON><PERSON>_Worshipper_Good_Female_<PERSON><PERSON><PERSON><PERSON>", "m_deathAlignment": "0.00000", "m_positiveInteractionAlignment": "0.00000", "m_negativeInteractionAlignment": "0.00000", "m_energy": "10.00", "m_productionPower": "0.250", "m_attack": "0.05", "m_lowWalkSpeed": "14.00000", "m_highWalkSpeed": "14.00000", "m_possessWalkSpeed": "2.20", "m_possessRunSpeed": "4.40", "m_autoFindJob": "False", "m_autoFindHome": "False", "m_lowSkinColor": "0.25", "m_highSkinColor": "0.50", "m_lowBodyColor": "0.50", "m_highBodyColor": "0.75", "m_lowHairColor": "0.75", "m_highHairColor": "1.00", "m_lowOverallScale": "0.90", "m_highOverallScale": "0.90", "m_lowHeightScale": "0.90", "m_highHeightScale": "1.05", "m_lowFatScale": "0.90", "m_highFatScale": "1.30", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "12.00", "m_health": "25.00", "m_tavernCost": 0, "m_tavernCostPerWorkerMultiplier": "", "m_description": "They worship you", "m_likes": "", "m_dislikes": "", "m_persona": "", "m_spritePath": "", "m_displayName": "Good Worshipper", "m_deathDropFavour": "LordsFavour<br />RoyalFavour<br />MysticFavour", "m_deathDropChance": "0.80000", "m_defaultArmour": ""}, {"id": "6882049d288a5d02b0de292b", "m_name": "WorshipperFemaleNeutral", "m_workerType": "Worshipper", "m_subType": "Neutral", "m_level": 0, "m_gender": "Male", "m_prefabName": "MAWorshipper", "m_characterPrefabName": "<PERSON>_<PERSON><PERSON>_Worshipper_Neutral_Female_Ra<PERSON><PERSON><PERSON>", "m_deathAlignment": "0.00000", "m_positiveInteractionAlignment": "0.00000", "m_negativeInteractionAlignment": "0.00000", "m_energy": "10.00", "m_productionPower": "0.250", "m_attack": "0.05", "m_lowWalkSpeed": "14.00000", "m_highWalkSpeed": "14.00000", "m_possessWalkSpeed": "2.20", "m_possessRunSpeed": "4.40", "m_autoFindJob": "False", "m_autoFindHome": "False", "m_lowSkinColor": "0.25", "m_highSkinColor": "0.50", "m_lowBodyColor": "0.50", "m_highBodyColor": "0.75", "m_lowHairColor": "0.75", "m_highHairColor": "1.00", "m_lowOverallScale": "0.90", "m_highOverallScale": "0.90", "m_lowHeightScale": "0.90", "m_highHeightScale": "1.05", "m_lowFatScale": "0.90", "m_highFatScale": "1.30", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "12.00", "m_health": "25.00", "m_tavernCost": 0, "m_tavernCostPerWorkerMultiplier": "", "m_description": "They worship you", "m_likes": "", "m_dislikes": "", "m_persona": "", "m_spritePath": "", "m_displayName": "Neutral Worshipper", "m_deathDropFavour": "LordsFavour<br />RoyalFavour<br />MysticFavour", "m_deathDropChance": "0.80000", "m_defaultArmour": ""}, {"id": "6882049d288a5d02b0de2928", "m_name": "WorshipperMaleEvil", "m_workerType": "Worshipper", "m_subType": "Evil", "m_level": 0, "m_gender": "Female", "m_prefabName": "MAWorshipper", "m_characterPrefabName": "<PERSON>_<PERSON><PERSON>_Worshipper_Evil_Male_Ra<PERSON><PERSON><PERSON>", "m_deathAlignment": "0.00000", "m_positiveInteractionAlignment": "0.00000", "m_negativeInteractionAlignment": "0.00000", "m_energy": "10.00", "m_productionPower": "0.250", "m_attack": "0.05", "m_lowWalkSpeed": "14.00000", "m_highWalkSpeed": "14.00000", "m_possessWalkSpeed": "2.20", "m_possessRunSpeed": "4.40", "m_autoFindJob": "False", "m_autoFindHome": "False", "m_lowSkinColor": "0.25", "m_highSkinColor": "0.50", "m_lowBodyColor": "0.50", "m_highBodyColor": "0.75", "m_lowHairColor": "0.75", "m_highHairColor": "1.00", "m_lowOverallScale": "0.90", "m_highOverallScale": "0.90", "m_lowHeightScale": "0.90", "m_highHeightScale": "1.05", "m_lowFatScale": "0.90", "m_highFatScale": "1.30", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "12.00", "m_health": "25.00", "m_tavernCost": 0, "m_tavernCostPerWorkerMultiplier": "", "m_description": "They worship you", "m_likes": "", "m_dislikes": "", "m_persona": "", "m_spritePath": "", "m_displayName": "Evil Worshipper", "m_deathDropFavour": "LordsFavour<br />RoyalFavour<br />MysticFavour", "m_deathDropChance": "0.80000", "m_defaultArmour": ""}, {"id": "6882049d288a5d02b0de2934", "m_name": "WorshipperMaleGood", "m_workerType": "Worshipper", "m_subType": "Good", "m_level": 0, "m_gender": "Female", "m_prefabName": "MAWorshipper", "m_characterPrefabName": "<PERSON>_<PERSON><PERSON>_Worshipper_Good_<PERSON>_<PERSON><PERSON><PERSON><PERSON>", "m_deathAlignment": "0.00000", "m_positiveInteractionAlignment": "0.00000", "m_negativeInteractionAlignment": "0.00000", "m_energy": "10.00", "m_productionPower": "0.250", "m_attack": "0.05", "m_lowWalkSpeed": "14.00000", "m_highWalkSpeed": "14.00000", "m_possessWalkSpeed": "2.20", "m_possessRunSpeed": "4.40", "m_autoFindJob": "False", "m_autoFindHome": "False", "m_lowSkinColor": "0.25", "m_highSkinColor": "0.50", "m_lowBodyColor": "0.50", "m_highBodyColor": "0.75", "m_lowHairColor": "0.75", "m_highHairColor": "1.00", "m_lowOverallScale": "0.90", "m_highOverallScale": "0.90", "m_lowHeightScale": "0.90", "m_highHeightScale": "1.05", "m_lowFatScale": "0.90", "m_highFatScale": "1.30", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "12.00", "m_health": "25.00", "m_tavernCost": 0, "m_tavernCostPerWorkerMultiplier": "", "m_description": "They worship you", "m_likes": "", "m_dislikes": "", "m_persona": "", "m_spritePath": "", "m_displayName": "Good Worshipper", "m_deathDropFavour": "LordsFavour<br />RoyalFavour<br />MysticFavour", "m_deathDropChance": "0.80000", "m_defaultArmour": ""}, {"id": "6882049d288a5d02b0de292e", "m_name": "WorshipperMaleNeutral", "m_workerType": "Worshipper", "m_subType": "Neutral", "m_level": 0, "m_gender": "Female", "m_prefabName": "MAWorshipper", "m_characterPrefabName": "SK_Ken<PERSON>_Worshipper_Neutral_Male_Ra<PERSON><PERSON><PERSON>", "m_deathAlignment": "0.00000", "m_positiveInteractionAlignment": "0.00000", "m_negativeInteractionAlignment": "0.00000", "m_energy": "10.00", "m_productionPower": "0.250", "m_attack": "0.05", "m_lowWalkSpeed": "14.00000", "m_highWalkSpeed": "14.00000", "m_possessWalkSpeed": "2.20", "m_possessRunSpeed": "4.40", "m_autoFindJob": "False", "m_autoFindHome": "False", "m_lowSkinColor": "0.25", "m_highSkinColor": "0.50", "m_lowBodyColor": "0.50", "m_highBodyColor": "0.75", "m_lowHairColor": "0.75", "m_highHairColor": "1.00", "m_lowOverallScale": "0.90", "m_highOverallScale": "0.90", "m_lowHeightScale": "0.90", "m_highHeightScale": "1.05", "m_lowFatScale": "0.90", "m_highFatScale": "1.30", "m_bloodColour": "", "m_bloodAmount": "", "m_mana": "12.00", "m_health": "25.00", "m_tavernCost": 0, "m_tavernCostPerWorkerMultiplier": "", "m_description": "They worship you", "m_likes": "", "m_dislikes": "", "m_persona": "", "m_spritePath": "", "m_displayName": "Neutral Worshipper", "m_deathDropFavour": "LordsFavour<br />RoyalFavour<br />MysticFavour", "m_deathDropChance": "0.80000", "m_defaultArmour": ""}]