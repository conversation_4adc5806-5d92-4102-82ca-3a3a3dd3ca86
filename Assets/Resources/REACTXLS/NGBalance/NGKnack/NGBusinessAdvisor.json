[{"id": "67daaca722155a031d40a107", "m_name": "CaveBlockerMetal", "m_givenName": "<PERSON><PERSON><PERSON><PERSON>", "m_title": "Cave Explorer", "m_firstName": "<PERSON>", "m_portraitSprite": "CaveBlockerMetalSmall", "m_tinySprite": "CaveBlockerMetalTiny", "m_sprites": "CaveBlockerMetalSmall", "m_info": "Character that blocks the cave in the metal region."}, {"id": "67cf0d83f1f58b0336bbc37b", "m_name": "MessageBottleHusband", "m_givenName": "<PERSON><PERSON>", "m_title": "Pirate Captain's Husband", "m_firstName": "<PERSON><PERSON>", "m_portraitSprite": "MessageBottleHusbandSmall", "m_tinySprite": "MessageBottleHusbandTiny", "m_sprites": "MessageBottleHusbandSmall", "m_info": "The jilted husband of pirate Captain <PERSON>."}, {"id": "67d41eed3df69302ee6ab639", "m_name": "PirateCaptain", "m_givenName": "<PERSON><PERSON>", "m_title": "Pirate Captain", "m_firstName": "<PERSON>", "m_portraitSprite": "PirateCaptainSmall", "m_tinySprite": "PirateCaptainTiny", "m_sprites": "PirateCaptainSmall", "m_info": "Pirate Captain"}, {"id": "6813a50cf3b47002e44a8c44", "m_name": "Lumber<PERSON>", "m_givenName": "<PERSON><PERSON><PERSON>", "m_title": "Lumber<PERSON>", "m_firstName": "<PERSON><PERSON>", "m_portraitSprite": "LumberjackSmall", "m_tinySprite": "LumberjackTiny", "m_sprites": "LumberjackSmall", "m_info": "<PERSON><PERSON> the Lumberjack"}, {"id": "6694f2d4e747f400252b38a5", "m_name": "Hippy", "m_givenName": "Cleric", "m_title": "Keeper of Stones", "m_firstName": "Privy", "m_portraitSprite": "StonnedHippy", "m_tinySprite": "StonnedHippyTiny", "m_sprites": "StonnedHippy", "m_info": "Hey man I am chilled"}, {"id": "659c0d0913d57a00275c24f4", "m_name": "<PERSON>", "m_givenName": "<PERSON><PERSON>", "m_title": "Lazy Worker", "m_firstName": "<PERSON>", "m_portraitSprite": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "m_tinySprite": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "m_sprites": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "m_info": ""}, {"id": "65a135f52551f00036f93353", "m_name": "<PERSON>", "m_givenName": "<PERSON><PERSON>", "m_title": "Lazy Worker", "m_firstName": "<PERSON>", "m_portraitSprite": "Thomas<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "m_tinySprite": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "m_sprites": "Thomas<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "m_info": ""}, {"id": "678f9d54109195030330432a", "m_name": "Cricket", "m_givenName": "Cricket", "m_title": "Keeper of Tomes", "m_firstName": "<PERSON>", "m_portraitSprite": "Oswald<PERSON><PERSON><PERSON>", "m_tinySprite": "Oswald<PERSON><PERSON><PERSON><PERSON>iny", "m_sprites": "Oswald<PERSON><PERSON><PERSON>", "m_info": ""}, {"id": "67fad8dcd5528c02f0d83a39", "m_name": "MineWorker", "m_givenName": "Diggle", "m_title": "Mine Worker", "m_firstName": "<PERSON>", "m_portraitSprite": "MineWorker", "m_tinySprite": "MineWorker", "m_sprites": "MineWorker", "m_info": ""}, {"id": "67fbbbd88f292802e95e2f1c", "m_name": "Clive<PERSON>ineWorker", "m_givenName": "Diggle", "m_title": "Mine Worker", "m_firstName": "<PERSON>", "m_portraitSprite": "Clive<PERSON>ineWorker", "m_tinySprite": "Clive<PERSON>ineWorker", "m_sprites": "Clive<PERSON>ineWorker", "m_info": ""}, {"id": "68df888da7bd5f0307aa0425", "m_name": "DrunkBandit", "m_givenName": "DrunkBandit", "m_title": "Bandit Marauder", "m_firstName": "Erland", "m_portraitSprite": "DrunkBandit", "m_tinySprite": "DrunkBanditTiny", "m_sprites": "DrunkBandit", "m_info": "Frrr blug it dubble!"}, {"id": "66bb3fb57431e90032c79d66", "m_name": "Lost Boy", "m_givenName": "Erstwhile", "m_title": "The Lost \"Boy\"", "m_firstName": "Recidivist", "m_portraitSprite": "LostBoy", "m_tinySprite": "LostBoyTiny", "m_sprites": "LostBoy", "m_info": "A boy who got lost"}, {"id": "67e517fdc5b21f02e5f902a4", "m_name": "<PERSON><PERSON><PERSON><PERSON>", "m_givenName": "Featherstone", "m_title": "Chicken Farmer", "m_firstName": "<PERSON>", "m_portraitSprite": "ChickenFarmerSmall", "m_tinySprite": "ChickenFarmerTiny", "m_sprites": "ChickenFarmerSmall", "m_info": "Quest giver chicken farmer"}, {"id": "6797a8a80d0a1002cf4f1700", "m_name": "Executioner", "m_givenName": "<PERSON><PERSON><PERSON><PERSON>", "m_title": "The Executioner", "m_firstName": "Lucan", "m_portraitSprite": "ExecutionerSmall", "m_tinySprite": "ExecutionerSmallTiny", "m_sprites": "ExecutionerSmall", "m_info": "The executioner will bring forth accused prisoners for your judgement. Stocks, Noose or set the Loose?"}, {"id": "67effe25f96dce02e770117e", "m_name": "<PERSON><PERSON><PERSON>", "m_givenName": "<PERSON><PERSON><PERSON>", "m_title": "Bandit King", "m_firstName": "<PERSON>", "m_portraitSprite": "<PERSON><PERSON><PERSON>", "m_tinySprite": "<PERSON><PERSON><PERSON>", "m_sprites": "<PERSON><PERSON><PERSON>", "m_info": ""}, {"id": "67effe85f96dce02e7701235", "m_name": "<PERSON><PERSON><PERSON><PERSON>", "m_givenName": "<PERSON><PERSON><PERSON><PERSON>", "m_title": "Gate Keeper", "m_firstName": "<PERSON>", "m_portraitSprite": "<PERSON><PERSON><PERSON><PERSON>", "m_tinySprite": "<PERSON><PERSON><PERSON><PERSON>", "m_sprites": "<PERSON><PERSON><PERSON><PERSON>", "m_info": ""}, {"id": "67f51ae84e4c0d02c1fc2cd3", "m_name": "<PERSON><PERSON><PERSON>", "m_givenName": "<PERSON><PERSON><PERSON>", "m_title": "Inspector", "m_firstName": "<PERSON>", "m_portraitSprite": "<PERSON><PERSON><PERSON>", "m_tinySprite": "<PERSON><PERSON><PERSON>", "m_sprites": "<PERSON><PERSON><PERSON>", "m_info": "The building inspector"}, {"id": "68011425062c0102e087f933", "m_name": "MineEngineer", "m_givenName": "Heathcote", "m_title": "Wyrmscar Mine Engineer", "m_firstName": "<PERSON><PERSON>", "m_portraitSprite": "MineEngineerSmall", "m_tinySprite": "MineEngineerTiny", "m_sprites": "MineEngineerSmall", "m_info": "Metal mine chief engineer"}, {"id": "680f62300ace1502d63f6d01", "m_name": "ThoraMineEngineer", "m_givenName": "Heathcote", "m_title": "Wyrmscar Mine Engineer", "m_firstName": "<PERSON><PERSON>", "m_portraitSprite": "ThoraMineEngineerSmall", "m_tinySprite": "ThoraMineEngineerTiny", "m_sprites": "ThoraMineEngineerSmall", "m_info": "Quest giver for the bridge repair quest in Wyrmscar"}, {"id": "658ea8cdae908f0029202f32", "m_name": "Keeper", "m_givenName": "Keeper", "m_title": "Task Giver", "m_firstName": "<PERSON><PERSON>", "m_portraitSprite": "KeeperWithPen", "m_tinySprite": "KeeperWithPenTiny", "m_sprites": "KeeperWithPen|KeeperBall", "m_info": ""}, {"id": "63398d750ea29d00226c97e6", "m_name": "Lombard", "m_givenName": "Lombard", "m_title": "Guild Master", "m_firstName": "<PERSON><PERSON><PERSON>", "m_portraitSprite": "<PERSON><PERSON><PERSON><PERSON>", "m_tinySprite": "MALombardTiny", "m_sprites": "<PERSON><PERSON><PERSON><PERSON>", "m_info": "Regional Guildmaster, Membership Officer and Preceptor, <PERSON><PERSON><PERSON> is your brother in mercantile arms."}, {"id": "67f4fc4a4e4c0d02c1fbc99b", "m_name": "PirateFirstMate", "m_givenName": "<PERSON><PERSON><PERSON><PERSON>", "m_title": "Pirate First Mate", "m_firstName": "<PERSON>", "m_portraitSprite": "PirateFirstMateSmall", "m_tinySprite": "PirateFirstMateTiny", "m_sprites": "PirateFirstMateSmall", "m_info": "The first mate of the three pirates."}, {"id": "679275673458050304bb44ef", "m_name": "ForemanWife", "m_givenName": "<PERSON><PERSON>", "m_title": "<PERSON>'s Wife", "m_firstName": "Florence", "m_portraitSprite": "ForemanWifeSmall", "m_tinySprite": "ForemanWifeSmallTiny", "m_sprites": "ForemanWifeSmall", "m_info": "<PERSON>'s wife, for the <PERSON><PERSON> quest."}, {"id": "68274fc012b72c02f550567d", "m_name": "JoshuaRedgrave", "m_givenName": "<PERSON><PERSON>", "m_title": "Wrymscar Miner", "m_firstName": "<PERSON>", "m_portraitSprite": "AccusedJoshuaSmall", "m_tinySprite": "Accused<PERSON><PERSON><PERSON><PERSON>in<PERSON>", "m_sprites": "AccusedJoshuaSmall", "m_info": "The miner accused of the foreman's murder"}, {"id": "651459093b7d500027dedf9f", "m_name": "<PERSON><PERSON><PERSON>", "m_givenName": "<PERSON><PERSON><PERSON>", "m_title": "Guild Advisor", "m_firstName": "<PERSON><PERSON><PERSON>", "m_portraitSprite": "<PERSON><PERSON>y_Confident", "m_tinySprite": "ValmeyWithBasketTiny", "m_sprites": "<PERSON><PERSON>y_Confident", "m_info": ""}, {"id": "67e1699659a34102d2783ab8", "m_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "m_givenName": "<PERSON><PERSON><PERSON>", "m_title": "The Foreman's Daughter", "m_firstName": "<PERSON>", "m_portraitSprite": "ForemanDaughterSmall", "m_tinySprite": "ForemanDaughterTiny", "m_sprites": "ForemanDaughterSmall", "m_info": "Daughter of the foreman of the metal mine."}]