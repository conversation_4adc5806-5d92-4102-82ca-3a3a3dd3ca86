[{"id": "614c60231897000dd47e1ec4", "m_name": "Build", "m_title": "Build", "m_prefab": "?", "m_description": "", "m_textSprite": "", "m_sprite": "?", "m_spriteColour": "#B76711FF", "m_bezierColor": ""}, {"id": "67bd9f05fc0ab902fe097067", "m_name": "Cotton", "m_title": "Raw Cotton", "m_prefab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "m_description": "Raw cotton, fresh from the field.", "m_textSprite": "Cotton", "m_sprite": "Cotton", "m_spriteColour": "#378A3FFF", "m_bezierColor": ""}, {"id": "67bd9f360b573702beca0e90", "m_name": "<PERSON><PERSON><PERSON>", "m_title": "<PERSON><PERSON><PERSON>", "m_prefab": "PickupFabric", "m_description": "Hand woven textile, ready for the factory.", "m_textSprite": "<PERSON><PERSON><PERSON>", "m_sprite": "<PERSON><PERSON><PERSON>", "m_spriteColour": "#378A3FFF", "m_bezierColor": ""}, {"id": "679a528f599a2c180f3b480b", "m_name": "Flour", "m_title": "Flour", "m_prefab": "PickupFlour", "m_description": "Fine powder from refined wheat, ready for the factory.", "m_textSprite": "Flour", "m_sprite": "Flour", "m_spriteColour": "#EA841CFF", "m_bezierColor": ""}, {"id": "65fc01f56ef494002764c9dd", "m_name": "LordsFavour", "m_title": "Lords Favour", "m_prefab": "", "m_description": "Your aristocratic friends offer their gratitude for your efforts.", "m_textSprite": "LordsFavour", "m_sprite": "MACoins/LordsResearchCoin", "m_spriteColour": "", "m_bezierColor": ""}, {"id": "679b624d0991de02c6d918ee", "m_name": "Metal", "m_title": "Metal", "m_prefab": "PickupMetal", "m_description": "Malleable metal bars, ready for the factory.", "m_textSprite": "Metal", "m_sprite": "Metal", "m_spriteColour": "#378A3FFF", "m_bezierColor": ""}, {"id": "614c60231897000dd47e1ec8", "m_name": "Money", "m_title": "Money", "m_prefab": "<PERSON><PERSON><PERSON><PERSON>", "m_description": "See a penny, pick it up, all day long you'll have good luck. ", "m_textSprite": "Money", "m_sprite": "MACoins/MA_Coin", "m_spriteColour": "#749824FF", "m_bezierColor": ""}, {"id": "686503bfcd196b02cf90745e", "m_name": "MysticFavour", "m_title": "Mystic Favour", "m_prefab": "", "m_description": "This Favour is useful in the Arcadium.", "m_textSprite": "MysticFavour", "m_sprite": "MACoins/MysticResearchCoin", "m_spriteColour": "", "m_bezierColor": ""}, {"id": "614e0692f96398001ea4e4a1", "m_name": "None", "m_title": "None", "m_prefab": "", "m_description": "", "m_textSprite": "", "m_sprite": "", "m_spriteColour": "", "m_bezierColor": ""}, {"id": "679b620082f5ce02cccf7c01", "m_name": "Ore", "m_title": "Ore", "m_prefab": "PickupOre", "m_description": "Raw metal nugget, dug from out of the mine.", "m_textSprite": "Ore", "m_sprite": "Ore", "m_spriteColour": "#378A3FFF", "m_bezierColor": ""}, {"id": "65df14831352470029d50f86", "m_name": "PeoplesFavor", "m_title": "Peoples Favour", "m_prefab": "", "m_description": "This Favour is useful in the Arcadium", "m_textSprite": "PeoplesFavour", "m_sprite": "MACoins/CommonersResearchCoin", "m_spriteColour": "", "m_bezierColor": ""}, {"id": "614c60231897000dd47e1ecc", "m_name": "Product", "m_title": "Product", "m_prefab": "PickupProduct", "m_description": "The finished article. Passed to the Dispatch for delivery.", "m_textSprite": "Product", "m_sprite": "?", "m_spriteColour": "#F136D5FF", "m_bezierColor": ""}, {"id": "66991abd545b7b00289f6211", "m_name": "QuestStone", "m_title": "Obelisk", "m_prefab": "PickupQuestStone", "m_description": "Used to help people in Albion", "m_textSprite": "", "m_sprite": "", "m_spriteColour": "", "m_bezierColor": ""}, {"id": "617142590dd7b9001efab523", "m_name": "RawMaterialAny", "m_title": "Any Material", "m_prefab": "PickupAnyMaterialCrate", "m_description": "", "m_textSprite": "", "m_sprite": "RawMaterial_ANY", "m_spriteColour": "#35C2F1FF", "m_bezierColor": "#ff00ff"}, {"id": "6177e9eb981e99001faf9ba5", "m_name": "RawResourceAny", "m_title": "Any Resource", "m_prefab": "PickupAnyResourceCrate", "m_description": "Deliver these materials to the <b>Factory</b>, <b>Mill</b> or <b>Processing Plant</b>. Each building will take only what it needs.", "m_textSprite": "", "m_sprite": "RawResource_ANY", "m_spriteColour": "#35C2F1FF", "m_bezierColor": "#00ff00"}, {"id": "614c60231897000dd47e1ed8", "m_name": "RawResourceClay", "m_title": "Raw Clay", "m_prefab": "PickupClayRaw", "m_description": "<b>Raw Clay</b> is just the thing to complete your construction. Your <b>Buildings</b> won't be complete without it!", "m_textSprite": "RawClay", "m_sprite": "Raw_Clay_3d", "m_spriteColour": "#ECA32CFF", "m_bezierColor": ""}, {"id": "67bd9f2afc0ab902fe09713a", "m_name": "RefinedCotton", "m_title": "<PERSON>", "m_prefab": "PickupRefinedCotton", "m_description": "Fresh from the gin, a bale of cotton.", "m_textSprite": "RefinedCotton", "m_sprite": "RefinedCotton", "m_spriteColour": "#378A3FFF", "m_bezierColor": ""}, {"id": "679b62380991de02c6d918b6", "m_name": "RefinedOre", "m_title": "Refined Ore", "m_prefab": "PickupRefinedOre", "m_description": "Metal ore, free of impurities.", "m_textSprite": "RefinedOre", "m_sprite": "RefinedOre", "m_spriteColour": "#378A3FFF", "m_bezierColor": ""}, {"id": "679a515961d55c17de685c4f", "m_name": "RefinedWheat", "m_title": "Wheat Kernels", "m_prefab": "PickupRefinedWheat", "m_description": "Refined wheat, bagged up and ready to go.", "m_textSprite": "RefinedWheat", "m_sprite": "RefinedWheat", "m_spriteColour": "#EA841CFF", "m_bezierColor": ""}, {"id": "679b62740991de02c6d919b2", "m_name": "RefinedWood", "m_title": "Refined Wood", "m_prefab": "PickupRefinedWood", "m_description": "", "m_textSprite": "RefinedWood", "m_sprite": "RefinedWood", "m_spriteColour": "#378A3FFF", "m_bezierColor": ""}, {"id": "62a1f3d5d3027a001eaf7fee", "m_name": "Research", "m_title": "Research", "m_prefab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "m_description": "Research object", "m_textSprite": "", "m_sprite": "", "m_spriteColour": "#FFFFFFFF", "m_bezierColor": ""}, {"id": "65fc01ef1778f900274eada0", "m_name": "RoyalFavour", "m_title": "Royal Favour", "m_prefab": "", "m_description": "Our monarch shines the regal light of recognition on you.", "m_textSprite": "RoyalFavour", "m_sprite": "MACoins/RoyalResearchCoin", "m_spriteColour": "", "m_bezierColor": ""}, {"id": "614de0504900c50024f08451", "m_name": "Silica", "m_title": "Silica", "m_prefab": "PickupSilicaCrate", "m_description": "Used to make patterns, stickers and paint", "m_textSprite": "RMSilica", "m_sprite": "RawMaterial_SILICA", "m_spriteColour": "#7FE5E1FF", "m_bezierColor": ""}, {"id": "679b6286db95b80306eb3a7a", "m_name": "<PERSON><PERSON>", "m_title": "<PERSON><PERSON>", "m_prefab": "Pickup<PERSON><PERSON><PERSON>", "m_description": "Timber, cut and planed, ready for use.", "m_textSprite": "<PERSON><PERSON>", "m_sprite": "<PERSON><PERSON>", "m_spriteColour": "#378A3FFF", "m_bezierColor": ""}, {"id": "679a509ece5d581851d34669", "m_name": "Wheat", "m_title": "<PERSON><PERSON>", "m_prefab": "PickupWheat", "m_description": "Harvested from the field, refined in the farm.", "m_textSprite": "Wheat", "m_sprite": "Wheat", "m_spriteColour": "#378A3FFF", "m_bezierColor": ""}, {"id": "679b625d5c6e4702dba72090", "m_name": "<PERSON>", "m_title": "<PERSON>", "m_prefab": "Pickup<PERSON>ood", "m_description": "Freshly felled log, ready for the mill.", "m_textSprite": "<PERSON>", "m_sprite": "<PERSON>", "m_spriteColour": "#378A3FFF", "m_bezierColor": ""}]