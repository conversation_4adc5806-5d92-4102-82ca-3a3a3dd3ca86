[{"id": "646dea1e9f04860028e0c9e3", "m_text": "A big thank you to <br/>you all and good <br/>luck next time!", "m_uld": "SpeechBubbleThanks"}, {"id": "6464d883482cfd002847ca0e", "m_text": "Accessories", "m_uld": "Accessories"}, {"id": "64805370c917290027268ded", "m_text": "Add parts to complete design", "m_uld": "Add parts to complete design"}, {"id": "64709653baf53f002963a3b5", "m_text": "A file is damaged in your installation.\\nPlease try re-installing Legacy.\\nLegacy will now quit.\\n", "m_uld": "filedamaged"}, {"id": "647db14417ad730028878c04", "m_text": "Ambition", "m_uld": "AMBITION"}, {"id": "6481a730aa5740002922adab", "m_text": "and", "m_uld": "and"}, {"id": "646f3e7f07392a0028871cd6", "m_text": "And now on to the  main event!", "m_uld": "SpeechBubbleMainEvent"}, {"id": "646de9fc12932200271e9298", "m_text": "And that concludes <br/>today's Business <br/>Awards!", "m_uld": "SpeechBubbleConclude"}, {"id": "646de9dd1fbee400287a4c83", "m_text": "And the winner is...", "m_uld": "SpeechB<PERSON>ble<PERSON><PERSON><PERSON>"}, {"id": "647098bb33f3270026e60247", "m_text": "An Error Occurred", "m_uld": "An Error Occurred"}, {"id": "6470b2368d0442002997af9c", "m_text": "An error occurred, please try again", "m_uld": "An error occurred please try again"}, {"id": "6470b2035b8f74002763f65e", "m_text": "An game error has occurred\\nPlease reload the game", "m_uld": "pleasereload"}, {"id": "646626bcaaa6640028596f73", "m_text": "ARABLE", "m_uld": "$DISTRICT_TITLE_ARABLE"}, {"id": "646b7600d104af0028872ef5", "m_text": "Are you sure you want to abort current research?", "m_uld": "abortresearch"}, {"id": "646b7d5086fb10002825f049", "m_text": "Are you sure you want to begin researching this item?", "m_uld": "beginresearching"}, {"id": "64709a10aa89330028e881cc", "m_text": "Are you sure you want to join", "m_uld": "Are you sure you want to join"}, {"id": "64709aceb32d520029dc17c8", "m_text": "Are you sure you want to kick", "m_uld": "Are you sure you want to kick"}, {"id": "64709a5cb32d520029dc1715", "m_text": "Are you sure you want to leave?", "m_uld": "Are you sure you want to leave?"}, {"id": "646b763a60b5030028897a4a", "m_text": "Are you sure you want to pay ", "m_uld": "surepay"}, {"id": "647098107bc6650028af001e", "m_text": "Auth error", "m_uld": "Auth error"}, {"id": "6453d643950f15002972b136", "m_text": "Avatars", "m_uld": "Avatars"}, {"id": "6467827443c0a800283eb863", "m_text": "Award for:", "m_uld": "Award for:"}, {"id": "646cddb4b47f5a0027b6f14f", "m_text": "Award Name", "m_uld": "Award Name"}, {"id": "646cdd8132b7640027c50b1b", "m_text": "Best Product", "m_uld": "Best Product"}, {"id": "6453d64febe80e002728a67c", "m_text": "Bicycles", "m_uld": "Bicycles"}, {"id": "6466285bacc5fe0028f904ca", "m_text": "Billboard", "m_uld": "Billboard"}, {"id": "645b6a7fbdc20a0029e2fab1", "m_text": "Block", "m_uld": "Block"}, {"id": "646626d0572272002976854e", "m_text": "BOATPASS", "m_uld": "$DISTRICT_TITLE_BOATPASS"}, {"id": "6465ee2ba866480028977b23", "m_text": "Buff", "m_uld": "Buff"}, {"id": "647dada896f6b100288a59db", "m_text": "Buff", "m_uld": "Buff"}, {"id": "645b693c57cfa50027fb57c1", "m_text": "Building", "m_uld": "Building"}, {"id": "647db09cb8b7ba0027effe60", "m_text": "BuildingCostLarge", "m_uld": "BuildingCostLarge"}, {"id": "647db094bc1ec90029fee5b7", "m_text": "BuildingCostSmall", "m_uld": "BuildingCostSmall"}, {"id": "6481a0e187464d002784c5fb", "m_text": "Building Empty", "m_uld": "Building Empty"}, {"id": "6463a8fa482cfd0028441cd4", "m_text": "Building Full", "m_uld": "Building Full"}, {"id": "6463a933482cfd0028441de1", "m_text": "Building Road", "m_uld": "Building Road"}, {"id": "646743a89924ea0027948841", "m_text": "Building Site", "m_uld": "Building Site"}, {"id": "647dad746922600029ef90a2", "m_text": "BuildingTicket", "m_uld": "BuildingTicket"}, {"id": "6463981b3b69dc00297a2b7e", "m_text": "Build the ", "m_uld": "Build the "}, {"id": "6453d65c0222d800275200ae", "m_text": "Burgers", "m_uld": "Burgers"}, {"id": "646f3dc9be7d2a0027328b7e", "m_text": "But before that, <br/>let's have a look at the <br/>previous winners!", "m_uld": "SpeechBubbleBefore"}, {"id": "647092ce041b660029d4f18c", "m_text": "Buy", "m_uld": "Buy"}, {"id": "647090e0b32d520029dbf4a5", "m_text": "Buy Cash:", "m_uld": "Buy Cash:"}, {"id": "6453d669ebe80e002728a6be", "m_text": "Cakes", "m_uld": "Cakes"}, {"id": "646f261d098cf90028f1c5a7", "m_text": "Calculating..", "m_uld": "Calculating.."}, {"id": "6453d67e512b8d002575da80", "m_text": "Cameras", "m_uld": "Cameras"}, {"id": "6467620f074bcc0027ff584a", "m_text": "Cancel", "m_uld": "Cancel"}, {"id": "646623dea29a390027fcc8fe", "m_text": "Capacity", "m_uld": "GUI_INFO_CAPACITY"}, {"id": "647dadb159a8950028324cfc", "m_text": "Card", "m_uld": "Card"}, {"id": "6481c340dee187002754e81c", "m_text": "Cargo Added:", "m_uld": "Cargo Added:"}, {"id": "6481c34d83bfa800297839d7", "m_text": "Cargo Value:", "m_uld": "Cargo Value:"}, {"id": "646626d591fc7e0029073c26", "m_text": "CASTLEWOODS", "m_uld": "$DISTRICT_TITLE_CASTLEWOODS"}, {"id": "64662565aaa6640028596bea", "m_text": "Caution", "m_uld": "GUI_CAUTION"}, {"id": "6453d6896310960028aa2202", "m_text": "Chairs", "m_uld": "Chairs"}, {"id": "645b6a1e57cfa50027fb5961", "m_text": "Choices Character", "m_uld": "Choices Character"}, {"id": "647dec35bc1ec90029ff8ee6", "m_text": "<PERSON><PERSON>", "m_uld": "<PERSON><PERSON>"}, {"id": "64636392773f0300277878c5", "m_text": "Choose Product Line", "m_uld": "Choose Product Line"}, {"id": "6453d6930222d800275200d4", "m_text": "Cinematography", "m_uld": "Cinematography"}, {"id": "64624628fe6333002884bde9", "m_text": "Clearing save...", "m_uld": "Clearing save..."}, {"id": "6453d69e171e390027e74f03", "m_text": "Clocks", "m_uld": "Clocks"}, {"id": "64709291504e700028d2b685", "m_text": "Close", "m_uld": "Close"}, {"id": "6465f7982ac294002791e887", "m_text": "cloth", "m_uld": "cloth"}, {"id": "646624ae8add730027be7060", "m_text": "<PERSON><PERSON><PERSON>", "m_uld": "MATERIAL_CLOTH"}, {"id": "646b57f5d104af002886db07", "m_text": "COLLECT YOUR REWARDS", "m_uld": "COLLECT YOUR REWARDS"}, {"id": "64662679ea706900299af166", "m_text": "Coming Soon", "m_uld": "Coming Soon"}, {"id": "6478c01c369500002a422f25", "m_text": "Common", "m_uld": "Common"}, {"id": "646cc37741a2d70028f4ca7b", "m_text": "confirm", "m_uld": "confirm"}, {"id": "646c8f6cac752500261cc505", "m_text": "Confirm", "m_uld": "Confirm"}, {"id": "646364159779ad0027db4c4a", "m_text": "Confirm Design", "m_uld": "Confirm Design"}, {"id": "64787cc398bb30002a568cfd", "m_text": "Conglomerate", "m_uld": "Conglomerate"}, {"id": "647db08b3dd0bd00282c1dee", "m_text": "ConstructionSpeedLarge", "m_uld": "ConstructionSpeedLarge"}, {"id": "647db08217ad7300288789ef", "m_text": "ConstructionSpeedSmall", "m_uld": "ConstructionSpeedSmall"}, {"id": "64511722b7a8e400287f7025", "m_text": "Continue", "m_uld": "Continue"}, {"id": "64787cae95086400282092cd", "m_text": "Corporation", "m_uld": "Corporation"}, {"id": "646363f1617f680029d4b0e8", "m_text": "Cost", "m_uld": "Cost"}, {"id": "6465f7cc2ac294002791e8f9", "m_text": "cotton", "m_uld": "cotton"}, {"id": "64675d4c074bcc0027ff4ef8", "m_text": "Create Design", "m_uld": "Create Design"}, {"id": "646626e4ea706900299af3d6", "m_text": "CRESCENTBEACH", "m_uld": "$DISTRICT_TITLE_CRESCENTBEACH"}, {"id": "64709637f50c0d00289161b0", "m_text": "Critical Error", "m_uld": "Critical Error"}, {"id": "6465f7d5ca0c40002991d800", "m_text": "crops", "m_uld": "crops"}, {"id": "646b8888ddef580028866cf8", "m_text": "Current event ends in", "m_uld": "endsin"}, {"id": "6463ab4fe3e09b0028d52e34", "m_text": "customer", "m_uld": "customer"}, {"id": "646626eebf805b0028d5350d", "m_text": "DAWNCLIFFS", "m_uld": "$DISTRICT_TITLE_DAWNCLIFFS"}, {"id": "6465ee371fb0c100291d7ff4", "m_text": "Debug", "m_uld": "Debug"}, {"id": "645b68cdc790c200270ae015", "m_text": "Decoration", "m_uld": "Decoration"}, {"id": "647dad84b8b7ba0027eff627", "m_text": "DecorationTicket", "m_uld": "DecorationTicket"}, {"id": "6481b9143cf5ad00290498c5", "m_text": "Delivering...", "m_uld": "Delivering..."}, {"id": "645a43cf4f8ea50028398cd0", "m_text": "Design", "m_uld": "Design"}, {"id": "646623fa5722720029767e90", "m_text": "Design", "m_uld": "GUI_DESIGN"}, {"id": "646782a45d5af50028045de6", "m_text": "Design Competition", "m_uld": "Design Competition"}, {"id": "646c8fa641a2d70028f41aeb", "m_text": "Design Product", "m_uld": "Design Product"}, {"id": "6467437db37f120029012b34", "m_text": "Dispatch", "m_uld": "Dispatch"}, {"id": "646743878e41610029e2a0da", "m_text": "Dock", "m_uld": "Dock"}, {"id": "646626fa8add730027be7600", "m_text": "DOCKSIDE", "m_uld": "$DISTRICT_TITLE_DOCKSIDE"}, {"id": "6470b176aa89330028e8bc13", "m_text": "does not allow connections from the unity editor!", "m_uld": "unitynetworkerr"}, {"id": "64709353e12f0500297cb29c", "m_text": "Do it anyway!", "m_uld": "Do it anyway!"}, {"id": "6453d6a8410861002719c494", "m_text": "<PERSON>", "m_uld": "<PERSON>"}, {"id": "6453d6b5ebe80e002728a7ae", "m_text": "Dresses", "m_uld": "Dresses"}, {"id": "6481b8ee6e8b110028cef6a1", "m_text": "Due In ", "m_uld": "Due In "}, {"id": "6481b9086e8b110028cef6d4", "m_text": "Due In...", "m_uld": "Due In..."}, {"id": "64639c88617f680029d53bae", "m_text": "Easy", "m_uld": "Easy"}, {"id": "6470931c33f3270026e5f380", "m_text": "Edit Design", "m_uld": "Edit Design"}, {"id": "646c8f93b47f5a0027b5ea59", "m_text": "Edit Roads", "m_uld": "Edit Roads"}, {"id": "6467465610b428002892d279", "m_text": "Efficiency", "m_uld": "Efficiency"}, {"id": "645a43d9ba05df0028c05d42", "m_text": "Eject", "m_uld": "Eject"}, {"id": "646623eeacc5fe0028f8f88f", "m_text": "Eject", "m_uld": "GUI_EJECT"}, {"id": "64787ccbec7846002901b361", "m_text": "<PERSON><PERSON><PERSON>", "m_uld": "<PERSON><PERSON><PERSON>"}, {"id": "647db13500cb470028749490", "m_text": "Empathy", "m_uld": "EMPATHY"}, {"id": "64538b4c064cab0028607216", "m_text": "English", "m_uld": "English"}, {"id": "64639c9c530ccf0027e20e32", "m_text": "Enormous", "m_uld": "Enormous"}, {"id": "646761ecb37f120029015aeb", "m_text": "Enter", "m_uld": "Enter"}, {"id": "6467605fd4fee70027591fd9", "m_text": "Enter now to win LegacyCoin!<br/>Time to enter <TIme>", "m_uld": "entercomp"}, {"id": "6478c013369500002a422f0f", "m_text": "Epic", "m_uld": "Epic"}, {"id": "647091d9f50c0d00289153c5", "m_text": "Error", "m_uld": "Error"}, {"id": "64709847aa89330028e87bb0", "m_text": "Error", "m_uld": "Error"}, {"id": "64675c61d4fee70027591221", "m_text": "ERROR NO SUCH EVENT.", "m_uld": "EventError"}, {"id": "64662756a29a390027fcd2f8", "m_text": "ESTUARY", "m_uld": "$DISTRICT_TITLE_ESTUARY"}, {"id": "64675e1b074bcc0027ff508d", "m_text": "Event ends in", "m_uld": "eventends"}, {"id": "646b87c3b47f5a0027b431e4", "m_text": "Event now open", "m_uld": "eventopen"}, {"id": "646b876430505c002839c380", "m_text": "Event opening", "m_uld": "Event opening"}, {"id": "64675a5834dcec0027072a5b", "m_text": "Event opens in ", "m_uld": "Event Opens"}, {"id": "64675cd7ad613c0029d79fbb", "m_text": "Events", "m_uld": "Events"}, {"id": "646b871f9356ff0028b2207d", "m_text": "Event type not unlocked!", "m_uld": "eventlocked"}, {"id": "6453d6bfefda630027fe3e51", "m_text": "Experimental", "m_uld": "Experimental"}, {"id": "6465f75d3192130027d4f729", "m_text": "extract some ore", "m_uld": "extract some ore"}, {"id": "6467434c8e41610029e2a09c", "m_text": "Factory", "m_uld": "Factory"}, {"id": "6470b0c57bc6650028af49b2", "m_text": "Failed to connect to Legacy servers! Please check your internet connection or check the service availability.", "m_uld": "Failed to connect"}, {"id": "647098263bdfc40027976cb0", "m_text": "Failed to get GalaTestLauncher payload, make sure GalaTestLauncher is running and has the correct Gala ID", "m_uld": "galaerror"}, {"id": "647098f0f50c0d00289167d8", "m_text": "<PERSON><PERSON><PERSON>", "m_uld": "<PERSON><PERSON><PERSON>"}, {"id": "646c8ebfb47f5a0027b5e86b", "m_text": "Finish Build", "m_uld": "Finish Build"}, {"id": "6453d6ca3fee460029bc4b31", "m_text": "Fireworks", "m_uld": "Fireworks"}, {"id": "64662762ef610e0028535809", "m_text": "FLUFFYGARDEN", "m_uld": "$DISTRICT_TITLE_FLUFFYGA<PERSON>EN"}, {"id": "64709c77504e700028d2d6be", "m_text": "for", "m_uld": "for"}, {"id": "64662503ea706900299aedb9", "m_text": "<PERSON><PERSON>", "m_uld": "ROLE_FOREMAN"}, {"id": "64538751a645de002a195b46", "m_text": "French", "m_uld": "French"}, {"id": "6470b0643e72410028a14779", "m_text": "from sale\\nAre you sure?", "m_uld": "fromsale"}, {"id": "64709ad97bc6650028af0703", "m_text": "from the guild?", "m_uld": "from the guild?"}, {"id": "6453bac5950f150029725011", "m_text": "Full", "m_uld": "Full"}, {"id": "6470b1f0baf53f002963f09e", "m_text": "Game Error", "m_uld": "Game Error"}, {"id": "6453875de7d32c002870e336", "m_text": "German", "m_uld": "German"}, {"id": "647092a7f50c0d0028915583", "m_text": "Get More Currency!", "m_uld": "Get More Currency!"}, {"id": "6470933ae4d64c00265a1068", "m_text": "Go Back", "m_uld": "Go Back"}, {"id": "64675cb6d4fee700275912be", "m_text": "Go design.", "m_uld": "god<PERSON>"}, {"id": "647dad5424e1300027f92edc", "m_text": "Gold", "m_uld": "Gold"}, {"id": "647092ff3bdfc40027975e21", "m_text": "Go To Trading House", "m_uld": "Go To Trading House"}, {"id": "6466276b207bac0028e72036", "m_text": "GREENHILL", "m_uld": "$DISTRICT_TITLE_GREENHILL"}, {"id": "646627756424ae0026d12ba8", "m_text": "GREENMESA", "m_uld": "$DISTRICT_TITLE_GREENMESA"}, {"id": "6465f82291fc7e002906a37b", "m_text": "grit", "m_uld": "grit"}, {"id": "64709a035b8f74002763b698", "m_text": "Guild", "m_uld": "Guild"}, {"id": "646c8f29ddef5800288889ce", "m_text": "Guild Info", "m_uld": "Guild Info"}, {"id": "6465f80570d4a00029e0403d", "m_text": "gumption", "m_uld": "gumption"}, {"id": "6466277d97ffed0028376acb", "m_text": "HALFWAYHILLS", "m_uld": "$DISTRICT_TITLE_HALFWAYHILLS"}, {"id": "64639d53773f0300277913f4", "m_text": "Hard", "m_uld": "Hard"}, {"id": "646398667f723f002719bcd5", "m_text": " harvester", "m_uld": " harvester"}, {"id": "6465f76e6424ae0026d0cbd5", "m_text": "harvest some crops", "m_uld": "harvest some crops"}, {"id": "6453d6d83fee460029bc4b68", "m_text": "Hats", "m_uld": "Hats"}, {"id": "6464d873be63ca0027033e1e", "m_text": "Heads & Hair", "m_uld": "Heads & Hair"}, {"id": "646de9b9d425b40028955daa", "m_text": "Here are the <br/>nominees!", "m_uld": "SpeechBubbleNominees"}, {"id": "6453bab5950f150029724ff8", "m_text": "High", "m_uld": "High"}, {"id": "647db12e00cb47002874947f", "m_text": "<PERSON><PERSON><PERSON>", "m_uld": "HONESTY"}, {"id": "64639858d3b25a002891bbfe", "m_text": " hotpot", "m_uld": " hotpot"}, {"id": "647090f3e4d64c00265a0b97", "m_text": "House Re-design", "m_uld": "House Re-design"}, {"id": "6453d703ad2d810028c72a94", "m_text": "IceCream", "m_uld": "IceCream"}, {"id": "64675c287e07bb0028049b69", "m_text": "Imaginativity! Wow them with your design skills!", "m_uld": "Imaginativity"}, {"id": "6470992ff50c0d0028916857", "m_text": "INCOME AWARDS", "m_uld": "INCOME AWARDS"}, {"id": "645a43c1ba05df0028c05d16", "m_text": "Info", "m_uld": "Info"}, {"id": "64662410ea706900299ae963", "m_text": "Info", "m_uld": "GUI_INFO"}, {"id": "6465f7a070d4a00029e03f77", "m_text": "ingredients", "m_uld": "ingredients"}, {"id": "6483095382eff10028894cf0", "m_text": "Input Stock", "m_uld": "Input Stock"}, {"id": "6465f8186424ae0026d0cce7", "m_text": "it", "m_uld": "it"}, {"id": "64675b83ad613c0029d79a69", "m_text": "It's all about profit, so design a product that sells for a lot!", "m_uld": "AboutProfit"}, {"id": "6470954fbaf53f0029639d09", "m_text": "It's time to vote!", "m_uld": "It's time to vote!"}, {"id": "6453d710efda630027fe3e98", "m_text": "Jackets", "m_uld": "Jackets"}, {"id": "6453d71de7d32c002871cdfe", "m_text": "Jewellery", "m_uld": "Jewellery"}, {"id": "64675db0e5187e002821157b", "m_text": "Join", "m_uld": "Join"}, {"id": "647099f9baf53f002963b3a6", "m_text": "Join Guild", "m_uld": "Join Guild"}, {"id": "64709ac333f3270026e60b51", "m_text": "Kick Key Holder?", "m_uld": "Kick Key Holder?"}, {"id": "646627866424ae0026d12beb", "m_text": "LAKESIDE", "m_uld": "$DISTRICT_TITLE_LAKESIDE"}, {"id": "6453d7280222d80027520160", "m_text": "<PERSON><PERSON>", "m_uld": "<PERSON><PERSON>"}, {"id": "64787ca498bb30002a568cab", "m_text": "Large Business", "m_uld": "Large Business"}, {"id": "646781d234dcec0027078f2b", "m_text": "League", "m_uld": "League"}, {"id": "64709a42041b660029d50d1d", "m_text": "Leave Guild?", "m_uld": "Leave Guild?"}, {"id": "64709c91b32d520029dc1e29", "m_text": "Legacy\\nAre you sure?", "m_uld": "legacysure"}, {"id": "6478c0d8ef1468002912432f", "m_text": "Legendary", "m_uld": "Legendary"}, {"id": "647879c9a00633002851ed9b", "m_text": "Let's Go!", "m_uld": "letsgo"}, {"id": "6464d42e482cfd002847c0bb", "m_text": "Level", "m_uld": "Level"}, {"id": "646cc26e30505c00283c5166", "m_text": "Life", "m_uld": "Life"}, {"id": "6467462f34dcec0027070caf", "m_text": "Living Space: ", "m_uld": "Living Space: "}, {"id": "6450ef95b107a20029fd5b51", "m_text": "Loading", "m_uld": "Loading"}, {"id": "6481b8fc838e730027579c17", "m_text": "Loading...", "m_uld": "Loading..."}, {"id": "6453baa5efda630027fdd700", "m_text": "Low", "m_uld": "Low"}, {"id": "646cdd8bcb8e810029478833", "m_text": "Main Category Name", "m_uld": "Main Category Name"}, {"id": "646cde16d78eba00299e7a90", "m_text": "Main Category Name", "m_uld": "Main Category Name"}, {"id": "6463a9979035520029e14f12", "m_text": "Making", "m_uld": "Making"}, {"id": "646364423b69dc002979b783", "m_text": "Market", "m_uld": "Market"}, {"id": "6465ee093192130027d4cb71", "m_text": "Market Effect", "m_uld": "Market Effect"}, {"id": "64807de51be51f0026794b10", "m_text": "Market Left", "m_uld": "Market Left"}, {"id": "6465eca9a866480028977116", "m_text": "Materials", "m_uld": "Materials"}, {"id": "64639c803861eb002988f6e1", "m_text": "Medium", "m_uld": "Medium"}, {"id": "64787c99369500002a414d0e", "m_text": "Medium Business", "m_uld": "Medium Business"}, {"id": "64787cd698bb30002a568d4f", "m_text": "Mega Corp", "m_uld": "Mega Corp"}, {"id": "6465f78e91fc7e002906a096", "m_text": "metal", "m_uld": "metal"}, {"id": "646624bb5722720029767ffe", "m_text": "Metal", "m_uld": "MATERIAL_METAL"}, {"id": "6478ca8b9508640028219c99", "m_text": "METAL", "m_uld": "METAL"}, {"id": "6453baac064cab002860da5d", "m_text": "Mid", "m_uld": "Mid"}, {"id": "647dad8e3c0192002722c5f7", "m_text": "Mind", "m_uld": "Mind"}, {"id": "6463aa63e3e09b0028d526cd", "m_text": "Mine Depleted, Awaiting replenishment!", "m_uld": "Mine Depleted Awaiting replenishment!"}, {"id": "646624c8bf805b0028d53135", "m_text": "Minerals", "m_uld": "MATERIAL_MINERALS"}, {"id": "647dad5ebc1ec90029fedb28", "m_text": "Money", "m_uld": "Money"}, {"id": "64675b4b34dcec0027072b8d", "m_text": "Money talks! Make more money than everyone else!", "m_uld": "MoneyTalks"}, {"id": "6465f9b7ca0c40002991dad3", "m_text": " more part", "m_uld": " more part"}, {"id": "6465f9b097ffed002837072c", "m_text": " more parts", "m_uld": " more parts"}, {"id": "646ce27da03e360029edaaba", "m_text": "Most Revenue Generated", "m_uld": "Most Revenue Generated"}, {"id": "646b2ce4a67dc40028f99129", "m_text": "Most Sales", "m_uld": "Most Sales"}, {"id": "645a43e6ad1a6d0027b7d724", "m_text": "Move Building", "m_uld": "Move Building"}, {"id": "646c8f4fb47f5a0027b5e9d9", "m_text": "Move Decoration", "m_uld": "Move Decoration"}, {"id": "647db0cfda10160027be505f", "m_text": "MoveSpeedLarge", "m_uld": "MoveSpeedLarge"}, {"id": "647db0c8da10160027be5045", "m_text": "MoveSpeedSmall", "m_uld": "MoveSpeedSmall"}, {"id": "64787cbaa00633002851f4bc", "m_text": "Multi National", "m_uld": "Multi National"}, {"id": "646b7e0b0b20310027d6b2ed", "m_text": "N/A", "m_uld": "N/A"}, {"id": "648198bbe1ca630028a51af0", "m_text": "nd", "m_uld": "nd"}, {"id": "6464a23d30932800278671d3", "m_text": "Needs: ", "m_uld": "Needs: "}, {"id": "64807ddb82cf780028eb96e5", "m_text": "Needs Redesign", "m_uld": "Needs Redesign"}, {"id": "6470b0aa0f8ad20027c7fed5", "m_text": "Network Connection Error", "m_uld": "Network Connection Error"}, {"id": "648096ad8cd9790027d6c822", "m_text": "New!", "m_uld": "New!"}, {"id": "646b5790889e0f0027698703", "m_text": "NEXT LETTER", "m_uld": "NEXT LETTER"}, {"id": "64709bb2aa89330028e88579", "m_text": "\\n\\n<PERSON><PERSON> Deed <PERSON>er!\\n\\nBefore we begin, you need to set up your Guild and player profile so that you can start giving keys to people and let them play and earn for you!\\n\\n", "m_uld": "welcomedeedholder"}, {"id": "64709bc8504e700028d2d318", "m_text": "\\n\\nHello Legacy Fan!\\n\\nTo play Legacy you need to first join a Guild and set up your player profile.\\n\\nWhen you join a Guild you are given a key that allows you into the game.\\n\\nWithout a key or a deed you will not be able to play.\\n\\nHave fun!\\n\\n", "m_uld": "welcomekeyholder"}, {"id": "64709a5133f3270026e60a8b", "m_text": "No", "m_uld": "No"}, {"id": "646b88b40b20310027d6db4c", "m_text": "No active design competition found", "m_uld": "dcnotfound"}, {"id": "6470911e504e700028d2b38d", "m_text": "No Blocks", "m_uld": "No Blocks"}, {"id": "6481b4d9c48ca20029202926", "m_text": "No Buffs", "m_uld": "No Buffs"}, {"id": "6463a9e19035520029e14fed", "m_text": "No customers to serve", "m_uld": "No customers to serve"}, {"id": "645ba3cd9b3fbe0028f40c55", "m_text": "No description available!", "m_uld": "No description available!"}, {"id": "646cdd9b412abf002907e3ed", "m_text": "Nominees", "m_uld": "Nominees"}, {"id": "6463aa28ea55af002729de85", "m_text": "No money available to pay workers", "m_uld": "No money available to pay workers"}, {"id": "645ba3e459fe1f0027651d73", "m_text": "No name available!", "m_uld": "No name available!"}, {"id": "6453bad339b5c300287eb886", "m_text": "None", "m_uld": "None"}, {"id": "6463aac789915300271aa525", "m_text": "No One Home", "m_uld": "No One Home"}, {"id": "6467433a13c4b0002870b7e1", "m_text": "No Priority", "m_uld": "No Priority"}, {"id": "64807e17f83d9500270edd72", "m_text": "No Product", "m_uld": "No Product"}, {"id": "6466278eef610e0028535847", "m_text": "NORTHHILLS", "m_uld": "$DISTRICT_TITLE_NORTHHILLS"}, {"id": "64709064aa89330028e85ba9", "m_text": "Not Enough Cash", "m_uld": "Not Enough Cash"}, {"id": "646b7da0730bba002823cd68", "m_text": "Not enough workers to complete research!", "m_uld": "notenoughresearch"}, {"id": "64709082f50c0d002891509f", "m_text": "No, Thanks", "m_uld": "No Thanks"}, {"id": "6463aa002c0471002817d5ed", "m_text": "No workers are waiting for pay", "m_uld": "No workers are waiting for pay"}, {"id": "6453bacb7db1d300272dbac1", "m_text": "Off", "m_uld": "Off"}, {"id": "6466254fea706900299aeed8", "m_text": "OK", "m_uld": "GUI_OK"}, {"id": "6463aae2c403ac002982e64f", "m_text": "On Promised Leave", "m_uld": "On Promised Leave"}, {"id": "6481a72883bfa8002977ad27", "m_text": "or", "m_uld": "or"}, {"id": "6465f7c4aaa66400285919d4", "m_text": "ore", "m_uld": "ore"}, {"id": "6453d736a645de002a1a3444", "m_text": "Packaging", "m_uld": "Packaging"}, {"id": "64662797ca0c400029923781", "m_text": "PARKLAND", "m_uld": "$DISTRICT_TITLE_PARKLAND"}, {"id": "6463a93e3093280027840de1", "m_text": "Paying workers", "m_uld": "Paying workers"}, {"id": "6463aa3fc403ac002982e1fe", "m_text": "Pay workers", "m_uld": "Pay workers"}, {"id": "6453d740ebe80e002728a865", "m_text": "PerfumeBottles", "m_uld": "<PERSON><PERSON><PERSON>"}, {"id": "646cc277cb8e810029472b00", "m_text": "<PERSON>a", "m_uld": "<PERSON>a"}, {"id": "6453d74be7d32c002871ce1f", "m_text": "Plants", "m_uld": "Plants"}, {"id": "645a43fea4578600286dbb22", "m_text": "Player Info", "m_uld": "Player Info"}, {"id": "6453d758ebe80e002728a879", "m_text": "PlushAnimals", "m_uld": "Plush Animals"}, {"id": "6463975cab39d900281b47a6", "m_text": "Popular!", "m_uld": "Popular!"}, {"id": "646b7def0467bc00282e67af", "m_text": "Positive", "m_uld": "Positive"}, {"id": "6466279facc5fe0028f90333", "m_text": "PRAIRIE", "m_uld": "$DISTRICT_TITLE_PRAIRIE"}, {"id": "6463633ff3c4080028bb12e5", "m_text": "Premium", "m_uld": "Premium"}, {"id": "646b885c730bba002823fca1", "m_text": "Preparing for next event", "m_uld": "Preparing for next event"}, {"id": "646de99a5abe720028aff84e", "m_text": "Presenting the <br/>award for...", "m_uld": "SpeechBubblePresenting"}, {"id": "646cdd754f79130027136e74", "m_text": "Previous Winners", "m_uld": "Previous Winners"}, {"id": "6465edfd207bac0028e63d8d", "m_text": "Price Effect", "m_uld": "Price Effect"}, {"id": "647db13c68fc3100276671f9", "m_text": "Principles", "m_uld": "PRINCIPLES"}, {"id": "6470910bab75d100290c408d", "m_text": "Problem", "m_uld": "Problem"}, {"id": "646624d58add730027be7087", "m_text": "Produce", "m_uld": "MATERIAL_PRODUCE"}, {"id": "647f60052b3cb200277c6b30", "m_text": "Produce Smelter", "m_uld": "Produce Smelter"}, {"id": "6465f763ca0c40002991d731", "m_text": "produce some cotton", "m_uld": "produce some cotton"}, {"id": "6481ad3687464d0027851167", "m_text": "Product", "m_uld": "Product"}, {"id": "646363b09a334f00278ab518", "m_text": "Product Design", "m_uld": "Product Design"}, {"id": "647db0bbeb4ee10025a3787a", "m_text": "ProductionCostLarge", "m_uld": "ProductionCostLarge"}, {"id": "647db0b43c0192002722cf02", "m_text": "ProductionCostSmall", "m_uld": "ProductionCostSmall"}, {"id": "6463aad6ea55af002729df85", "m_text": "Production Paused", "m_uld": "Production Paused"}, {"id": "647db0abbed6cf0029d1a093", "m_text": "ProductionSpeedLarge", "m_uld": "ProductionSpeedLarge"}, {"id": "647db0a43dd0bd00282c1e19", "m_text": "ProductionSpeedSmall", "m_uld": "ProductionSpeedSmall"}, {"id": "646f75b4030d5b0029f146bf", "m_text": "Product Supplies", "m_uld": "Product Supplies"}, {"id": "64709c365b8f74002763baa3", "m_text": "Purchase", "m_uld": "Purchase"}, {"id": "646627a75722720029768704", "m_text": "QUARRY", "m_uld": "$DISTRICT_TITLE_QUARRY"}, {"id": "64675d0540ae640028b55974", "m_text": "Quick create and submit a design", "m_uld": "quickdesign"}, {"id": "64675bb52a49b100289241ab", "m_text": "Raise a smile! Happiest workforce wins!", "m_uld": "RaiseSmile"}, {"id": "64675befd3c22c0028f8651f", "m_text": "Raise a smile! Make 'em happy, keep 'em happy. Happiest workforce wins!", "m_uld": "RaiseSmileLong"}, {"id": "6478c00a281b330027a80c8a", "m_text": "Rare", "m_uld": "Rare"}, {"id": "6465ec872ac294002791d378", "m_text": "<PERSON><PERSON>", "m_uld": "<PERSON><PERSON>"}, {"id": "648198c031a3660029b749e7", "m_text": "rd", "m_uld": "rd"}, {"id": "6470936d0f8ad20027c7a622", "m_text": "Read Design Notes", "m_uld": "Read Design Notes"}, {"id": "6463aabdea55af002729df60", "m_text": "Refreshing Worker", "m_uld": "Refreshing Worker"}, {"id": "647095e6b32d520029dc0411", "m_text": "Registration Closed", "m_uld": "Registration Closed"}, {"id": "64675c8cd4fee7002759127f", "m_text": "Registration closes in", "m_uld": "closesin"}, {"id": "64675da8e5187e0028211567", "m_text": "Registration closes in", "m_uld": "regcloses"}, {"id": "646b87ecb47f5a0027b43245", "m_text": "Registration closing", "m_uld": "regclosing"}, {"id": "646b8822d4f63a002917496e", "m_text": "Registration closing in", "m_uld": "regclosingin"}, {"id": "6470b22b5b8f74002763f6a0", "m_text": "Registration Error", "m_uld": "Registration Error"}, {"id": "647095f5009fe1002756737f", "m_text": "Registration has now closed, you have not entered this Design Competition", "m_uld": "Registration has now closed you have not entered this Design Competition"}, {"id": "6470b04033f3270026e66110", "m_text": "Remove From Sale", "m_uld": "Remove From Sale"}, {"id": "646c8eedd4f63a0029190ca3", "m_text": "Research", "m_uld": "Research"}, {"id": "646b75687e07bb00280a45ac", "m_text": "Research complete", "m_uld": "Research complete"}, {"id": "64662432ca0c400029923031", "m_text": "Research Guaranteed", "m_uld": "GUI_RESEARCH_GUARANTEED"}, {"id": "646b759160b50300288976f1", "m_text": "Researching ", "m_uld": "Researching "}, {"id": "646b570eccdc830028d4d2a9", "m_text": "Revenue Generated", "m_uld": "Revenue Generated"}, {"id": "646cc2d6730bba0028264ff5", "m_text": "<PERSON><PERSON>", "m_uld": "<PERSON><PERSON>"}, {"id": "646627afaaa664002859715d", "m_text": "SADDLERIDGE", "m_uld": "$DISTRICT_TITLE_SADDLERIDGE"}, {"id": "646c8f4a114f920027bc5d9c", "m_text": "Salvage Decoration", "m_uld": "Salvage Decoration"}, {"id": "64636450617f680029d4b17e", "m_text": "Satisfaction", "m_uld": "Satisfaction"}, {"id": "646363e99779ad0027db4bce", "m_text": "Score", "m_uld": "Score"}, {"id": "646365c24d9c62002870334b", "m_text": "Score for ", "m_uld": "Score for "}, {"id": "646c8ec6653e1a00285b9045", "m_text": "<PERSON>ll", "m_uld": "<PERSON>ll"}, {"id": "64675b0f8e41610029e2c43a", "m_text": "Sell, sell, sell! It's all about shifting units, so design a simple product and get your production lines ready!", "m_uld": "SellSellSellLong"}, {"id": "64675abb8e41610029e2c3b7", "m_text": "Sell, sell, sell! Sell more than anyone else!", "m_uld": "SellSellSell"}, {"id": "6470b127e4d64c00265a78e3", "m_text": "Server environment", "m_uld": "Server environment"}, {"id": "6463ab47c403ac002982e8fa", "m_text": "Serving", "m_uld": "Serving"}, {"id": "6463a94aad83b6002718144b", "m_text": "Serving customers", "m_uld": "Serving customers"}, {"id": "6470b17c0f8ad20027c80054", "m_text": "Session Ending", "m_uld": "Session Ending"}, {"id": "645a43f1bdc20a0029e0d76c", "m_text": "Settings", "m_uld": "Settings"}, {"id": "6453d7d23fee460029bc4c7c", "m_text": "Shoes", "m_uld": "Shoes"}, {"id": "6464d8611b981f0027f556bc", "m_text": "Shoulders", "m_uld": "Shoulders"}, {"id": "647db04e24e1300027f937d4", "m_text": "SkillTokenAccounting", "m_uld": "SkillTokenAccounting"}, {"id": "647db03e560c8500269a031f", "m_text": "SkillTokenDesign", "m_uld": "SkillTokenDesign"}, {"id": "647db0363dd0bd00282c1d14", "m_text": "SkillTokenEngineering", "m_uld": "SkillTokenEngineering"}, {"id": "647db0246922600029ef97c9", "m_text": "SkillTokenGeneric", "m_uld": "SkillTokenGeneric"}, {"id": "647db0463dd0bd00282c1d39", "m_text": "SkillTokenInvestment", "m_uld": "SkillTokenInvestment"}, {"id": "647db05d59a89500283254c5", "m_text": "SkillTokenLeadership", "m_uld": "SkillTokenLeadership"}, {"id": "647db02d3dd0bd00282c1cf6", "m_text": "SkillTokenManagement", "m_uld": "SkillTokenManagement"}, {"id": "647db07824e1300027f93833", "m_text": "SkillTokenMarketing", "m_uld": "SkillTokenMarketing"}, {"id": "647db05659a89500283254b1", "m_text": "SkillTokenNetworking", "m_uld": "SkillTokenNetworking"}, {"id": "647db0673c0192002722ce9b", "m_text": "SkillTokenSolving", "m_uld": "SkillTokenSolving"}, {"id": "647db06fb8b7ba0027effde1", "m_text": "SkillTokenStrategy", "m_uld": "SkillTokenStrategy"}, {"id": "646b57c27e07bb002809e927", "m_text": "SKIP", "m_uld": "SKIP"}, {"id": "64787c8c17435f0028d680f4", "m_text": "Small Business", "m_uld": "Small Business"}, {"id": "6465f80e6424ae0026d0ccd5", "m_text": "smarts", "m_uld": "smarts"}, {"id": "647098cfe12f0500297ccf29", "m_text": "Something went wrong when visiting, please try again", "m_uld": "visiterror"}, {"id": "648198b46e83b800298ed29e", "m_text": "st", "m_uld": "st"}, {"id": "6451171b22c1650029a34cd9", "m_text": "Start", "m_uld": "Start"}, {"id": "64787c83cf16590026efda79", "m_text": "Startup", "m_uld": "Startup"}, {"id": "6481c3616e83b800298fa979", "m_text": "Status:", "m_uld": "Status:"}, {"id": "6465f82b2ac294002791e97a", "m_text": "stones", "m_uld": "stones"}, {"id": "6467466f3d54620029605a5a", "m_text": "Students: ", "m_uld": "Students: "}, {"id": "6465fe69207bac0028e690b3", "m_text": "TAP HERE FOR AN EXCITING CHALLENGE", "m_uld": "TAP HERE FOR AN EXCITING CHALLENGE"}, {"id": "6464b1851b981f0027f4f1e0", "m_text": "Tap Hold Power", "m_uld": "Tap Hold Power"}, {"id": "6464b1702db6cb00280edbba", "m_text": "Tap Power", "m_uld": "Tap Power"}, {"id": "6465eca01fb0c100291d7a8e", "m_text": "Taps To Make", "m_uld": "Taps To Make"}, {"id": "6453d7dc950f15002972b461", "m_text": "Teapots", "m_uld": "Teapots"}, {"id": "6453d80d1927a40027976186", "m_text": "<PERSON>", "m_uld": "<PERSON>"}, {"id": "6453d7fd1927a4002797616f", "m_text": "Telephones", "m_uld": "Telephones"}, {"id": "648198c6739f6500276d0722", "m_text": "th", "m_uld": "th"}, {"id": "646cddf1b47f5a0027b6f1b9", "m_text": "Thanks for coming!", "m_uld": "Thanks for coming!"}, {"id": "647092113e72410028a0f33f", "m_text": "There was a problem submitting this design, please try again", "m_uld": "problemsubmitting"}, {"id": "646de97ccbf75a002aa2277c", "m_text": "These are the <br/>awards that are up <br/>for grabs today!", "m_uld": "SpeechBubbleGrabs"}, {"id": "646f2610be7d2a0027321dbd", "m_text": "This chart shows the current supply of products from other players", "m_uld": "This chart shows the current supply of products from other players"}, {"id": "6470922b009fe10027565e8e", "m_text": "This design is not valid", "m_uld": "This design is not valid"}, {"id": "647099ad3bdfc400279771cc", "m_text": "This guild is no longer available, try another", "m_uld": "guildunav"}, {"id": "6470b19d5b8f74002763f5f1", "m_text": "This player ID has been used on another device\\nThis session will now quit", "m_uld": "id<PERSON><PERSON><PERSON>"}, {"id": "6463657929ee330027d69b14", "m_text": "This will cost you", "m_uld": "This will cost you"}, {"id": "646627b7ef610e0028535891", "m_text": "TICKLESIDE", "m_uld": "$DISTRICT_TITLE_TICKLESIDE"}, {"id": "6470958a3bdfc40027976703", "m_text": "times", "m_uld": "times"}, {"id": "6465ee16ea706900299a8839", "m_text": "Times Used", "m_uld": "Times Used"}, {"id": "646b7dcba03e360029ea2fd8", "m_text": "Time to complete reseach ", "m_uld": "researchtime"}, {"id": "6464e1e4309328002787139b", "m_text": "TIP: <size=18>Try tap+hold to build.", "m_uld": "tipbuild"}, {"id": "6464e239309328002787141e", "m_text": "TIP: <size=18>Try tap+hold to make.", "m_uld": "tipmake"}, {"id": "6464e20391d3bc002a22e533", "m_text": "TIP: <size=18>Try tap+hold to pay.", "m_uld": "tippay"}, {"id": "6464e21f58daa50028b8bfff", "m_text": "TIP: <size=18>Try tap+hold to serve.", "m_uld": "tipserve"}, {"id": "646b766ae831ce0028d7bf0b", "m_text": " to complete current research?", "m_uld": "tocomplete"}, {"id": "646cdd6832b7640027c50adc", "m_text": "Todays Award", "m_uld": "Todays Award"}, {"id": "646b75b416e42d00289e692d", "m_text": "to Finish Now", "m_uld": " to Finish Now"}, {"id": "6481c3589a41a80027088078", "m_text": "Total Sales:", "m_uld": "Total Sales:"}, {"id": "646cc266ddef58002889034e", "m_text": "Town", "m_uld": "Town"}, {"id": "646627c0aaa6640028597187", "m_text": "TOWNCENTRE", "m_uld": "$DISTRICT_TITLE_TOWNCENTRE"}, {"id": "646b56f8a67dc40028fa09dd", "m_text": "Town Satisfaction", "m_uld": "Town Satisfaction"}, {"id": "6453d819efda630027fe4250", "m_text": "Toy Guns", "m_uld": "Toy Guns"}, {"id": "6453d8246310960028aa2596", "m_text": "ToyPlanes", "m_uld": "Toy Planes"}, {"id": "6453d82eebe80e002728aa71", "m_text": "ToySoldiers", "m_uld": "Toy Soldiers"}, {"id": "6467467d47e812002690793f", "m_text": "Training", "m_uld": "Training"}, {"id": "646745e1e5187e002820ed98", "m_text": "Train Station", "m_uld": "Train Station"}, {"id": "6453d839ad2d810028c72e7a", "m_text": "T-Shirts", "m_uld": "T-Shirts"}, {"id": "6481c332838e73002757cce6", "m_text": "TugBoat", "m_uld": "TugBoat"}, {"id": "6463afff039e990026f39db9", "m_text": "Unknown Product", "m_uld": "Unknown Product"}, {"id": "647093bf3e72410028a0f668", "m_text": "Unlock:", "m_uld": "Unlock:"}, {"id": "646398009779ad0027dbb8bf", "m_text": "Unlock a district with a ", "m_uld": "Unlock a district with a "}, {"id": "6470915ebaf53f002963863a", "m_text": "Unlock Salesman?", "m_uld": "Unlock Salesman?"}, {"id": "6463981129ee330027d73548", "m_text": "Unlock the ", "m_uld": "Unlock the "}, {"id": "647dad9d68fc3100276667c9", "m_text": "UpgradeToken", "m_uld": "UpgradeToken"}, {"id": "6465ee22a866480028977ae2", "m_text": "Usage Cap", "m_uld": "Usage Cap"}, {"id": "646363e0680c790027ef52b8", "m_text": "Value", "m_uld": "Value"}, {"id": "6465ec936424ae0026d0b712", "m_text": "<Value> Time To Make", "m_uld": "<Value> Time To Make"}, {"id": "6453d843950f15002972b5ed", "m_text": "Vehicles", "m_uld": "Vehicles"}, {"id": "64675d3fad613c0029d7a067", "m_text": "View Design", "m_uld": "View Design"}, {"id": "647092e53bdfc40027975dfd", "m_text": "View District", "m_uld": "View District"}, {"id": "64675de840ae640028b55b70", "m_text": "View Results", "m_uld": "View Results"}, {"id": "647095bd009fe10027567314", "m_text": "Vote!", "m_uld": "Vote!"}, {"id": "646b96d9114f920027bad3d6", "m_text": "Vote Now", "m_uld": "Vote Now"}, {"id": "646b89afa35495002909b5dd", "m_text": "Voting closing", "m_uld": "Voting closing"}, {"id": "646b89e9ac752500261a8c68", "m_text": "Voting open for", "m_uld": "votingfor"}, {"id": "64675e43d3c22c0028f86bcf", "m_text": "Waiting for next event", "m_uld": "waitingnext"}, {"id": "64675d6ce5187e00282114f2", "m_text": "Waiting for players to join", "m_uld": "waitingjoin"}, {"id": "646b7541b51b000028b663c4", "m_text": "Waiting for research", "m_uld": "Waiting for research"}, {"id": "646b752ec4e1e3002813ed7f", "m_text": "Waiting for workers", "m_uld": "Waiting for workers"}, {"id": "646743585b345000273b0da9", "m_text": "Warehouse", "m_uld": "Warehouse"}, {"id": "64709133b32d520029dbf52d", "m_text": "Warning", "m_uld": "Warning"}, {"id": "64709b9b3bdfc4002797758a", "m_text": "Welcome!", "m_uld": "Welcome!"}, {"id": "646de94d1fbee400287a4b25", "m_text": "Welcome everyone to the Business Awards!", "m_uld": "SpeechBubbleWelcome"}, {"id": "646c9798a3549500290be0f1", "m_text": "Welcome to this month's Award Ceremony!", "m_uld": "WelcomeCeremony"}, {"id": "646dee72d425b40028956e74", "m_text": "Welcome to this month's Award Ceremony!", "m_uld": "Welcome to this month's Award Ceremony!"}, {"id": "646627caca0c4000299237ee", "m_text": "WHITECLIFF", "m_uld": "$DISTRICT_TITLE_WHITECLIFF"}, {"id": "6453d84de7d32c002871cef9", "m_text": "WindInstruments", "m_uld": "Wind Instruments"}, {"id": "646cde01412abf002907e4a6", "m_text": "Winner", "m_uld": "Winner"}, {"id": "646624e0572272002976806f", "m_text": "<PERSON>", "m_uld": "MATERIAL_WOOD"}, {"id": "646624f597ffed0028376496", "m_text": "Worker", "m_uld": "ROLE_WORKER"}, {"id": "646746b316c663002811f662", "m_text": "Workers: ", "m_uld": "Workers: "}, {"id": "64709a4b0f8ad20027c7c17d", "m_text": "Yes", "m_uld": "Yes"}, {"id": "64709c6ce4d64c00265a2b65", "m_text": "You are about to purchase", "m_uld": "You are about to purchase"}, {"id": "6470b04e7bc6650028af48e5", "m_text": "You are about to remove", "m_uld": "You are about to remove"}, {"id": "6470b1c5f50c0d002891a73c", "m_text": "You have been kicked from the guild\\nYou will need to join a new guild", "m_uld": "kickedguild"}, {"id": "64636525773f030027787b78", "m_text": "You have increased living space by", "m_uld": "You have increased living space by"}, {"id": "64675a238e41610029e2c2f2", "m_text": "You have not unlocked Awards yet! This will come from your advisor.", "m_uld": "AwardsLocked"}, {"id": "646759f7b37f120029014d15", "m_text": "You have not unlocked Design Competitions yet! This will come from your advisor.", "m_uld": "DCLocked"}, {"id": "64636544773f030027787ba9", "m_text": "You have used more materials in your design than your factory can store", "m_uld": "You have used more materials in your design than your factory can store"}, {"id": "646b866430505c002839c04c", "m_text": "You must vote to be in for the rewards", "m_uld": "mustvote"}, {"id": "64709871b32d520029dc0d03", "m_text": "You need a Gala account with a valid Legacy token to play", "m_uld": "galatoken"}, {"id": "64709580041b660029d4fe5d", "m_text": "You need to vote", "m_uld": "You need to vote"}, {"id": "64709946f50c0d002891687a", "m_text": "Your balance has been upwardly adjusted after a recent Competition or Event finished while you were away. The following funds have been added to your account:\\n\\n", "m_uld": "fundsadded"}, {"id": "64709893e12f0500297ccec8", "m_text": "Your ID is being used in another play session. Please quit that game to play on this machine", "m_uld": "idinuse"}, {"id": "646364d9addfcc0028f229b7", "m_text": "Your town doesn't currently support material", "m_uld": "Your town doesn't currently support material"}]