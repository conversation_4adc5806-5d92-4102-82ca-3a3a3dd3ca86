<?xml version="1.0" encoding="utf-8"?>
<WwiseSettings xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <CopySoundBanksAsPreBuildStep>false</CopySoundBanksAsPreBuildStep>
  <GenerateSoundBanksAsPreBuildStep>false</GenerateSoundBanksAsPreBuildStep>
  <SoundbankPath>Audio/GeneratedSoundBanks</SoundbankPath>
  <CreatedPicker>true</CreatedPicker>
  <CreateWwiseGlobal>false</CreateWwiseGlobal>
  <CreateWwiseListener>false</CreateWwiseListener>
  <ObjectReferenceAutoCleanup>true</ObjectReferenceAutoCleanup>
  <LoadSoundEngineInEditMode>true</LoadSoundEngineInEditMode>
  <ShowMissingRigidBodyWarning>true</ShowMissingRigidBodyWarning>
  <ShowSpatialAudioWarningMsg>true</ShowSpatialAudioWarningMsg>
  <WwiseInstallationPathMac>/Applications/Audiokinetic/Wwise2024.1.5.8803/Wwise.app</WwiseInstallationPathMac>
  <WwiseInstallationPathWindows>C:\Program Files (x86)\Audiokinetic\Wwise 2024.1.4.8780\Authoring\x64\Release\bin</WwiseInstallationPathWindows>
  <WwiseProjectPath>../../moat_audio/MoatAudio/MoatAudio.wproj</WwiseProjectPath>
  <UseWaapi>true</UseWaapi>
  <WaapiPort>8080</WaapiPort>
  <WaapiIP>127.0.0.1</WaapiIP>
  <UseGitRepository>true</UseGitRepository>
  <PackageSource>https://github.com/audiokinetic/WwiseUnityAddressables.git#v24.1.5</PackageSource>
  <AddressableBankFolder>StreamingAssets/Audio/GeneratedSoundBanks</AddressableBankFolder>
  <UseCustomBuildScript>false</UseCustomBuildScript>
  <AddressableAssetBuilderPath>Assets/AddressableAssetsData/DataBuilders/BuildScriptWwisePacked.asset</AddressableAssetBuilderPath>
  <AutomaticallyUpdateExternalSourcesPath>false</AutomaticallyUpdateExternalSourcesPath>
  <ExternalSourcesPath>WwiseData/Bank</ExternalSourcesPath>
  <XMLTranslatorTimeout>10</XMLTranslatorTimeout>
  <WaapiTranslatorTimeout>0</WaapiTranslatorTimeout>
</WwiseSettings>