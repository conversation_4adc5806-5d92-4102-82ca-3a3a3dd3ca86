using System.Collections.Generic;
using UnityEngine;

namespace Cans.Analytics
{
    public class AnalyticsEvent
    {
        private string m_eventName = string.Empty;

        protected Dictionary<string, object> m_parameters = new Dictionary<string, object>();

        public string EventName => m_eventName;
        public Dictionary<string, object> Parameters => m_parameters;

        /// <summary>
        /// Returns Null if all parameters check out fine.
        /// </summary>
        public List<string> UnusedParameterList
        {
            get
            {
                List<string> emptyParameterValues = null;
                foreach (KeyValuePair<string, object> parameter in m_parameters)
                {
                    if(parameter.Value == null)
                    {
                        if(emptyParameterValues == null)
                            emptyParameterValues = new List<string>();
                        emptyParameterValues.Add(parameter.Key);
                    }
                }

                return emptyParameterValues;
            }
        }

        public AnalyticsEvent AddParameter(string parameterName, object parameterValue = null)
        {
            m_parameters[parameterName] = parameterValue;
            return this;
        }

        public AnalyticsEvent AddParamList(Dictionary<string, object> parameters)
        {
            foreach (KeyValuePair<string, object> pair in parameters)
            {
                AddParameter(pair.Key, pair.Value);
            }

            return this;
        }
        
        public AnalyticsEvent AddParamLists(params Dictionary<string, object>[] parameterLists)
        {
            foreach (Dictionary<string, object> parameterList in parameterLists)
            {
                AddParamList(parameterList);
            }

            return this;
        }

        
        public AnalyticsEvent(string eventName) // use to create custom event
        {
            m_eventName = eventName;
        }

        public AnalyticsEvent(AnalyticsEvent eventCopy) // use to gather pre-defined event
        {
            m_eventName = eventCopy.m_eventName;
            m_parameters = new Dictionary<string, object>(eventCopy.m_parameters);
        }

        public AnalyticsEvent(string eventName, string[] preDefinedParams) // use to create pre-defined event
        {
            m_eventName = eventName;
            foreach (var par in preDefinedParams)
            {
                if(string.IsNullOrEmpty(par) || m_parameters.TryAdd(par, null) == false)
                {
                    Debug.LogError($"Analytics - {GetType().Name} - Could Not Add parameter key {(!string.IsNullOrEmpty(par) ? "Duplicate?" : "Null/empty")}");
                }
            }
        }
    }
}