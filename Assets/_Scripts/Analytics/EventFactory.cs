using System;
using System.Collections.Generic;
using UnityEngine;

namespace Cans.Analytics
{
	public class EventFactory
	{
		public AnalyticsEvent CurrencyEarned => new AnalyticsEvent(currencyEarnedEvent).AddParamLists(PlayerParameters, SatisfactionParameters);
		public AnalyticsEvent CurrencySpent => new AnalyticsEvent(currencySpentEvent).AddParamLists(PlayerParameters, SatisfactionParameters);
		public AnalyticsEvent ProductSelectionInteraction => new AnalyticsEvent(productSelectionInteractionEvent).AddParamLists(PlayerParameters, SatisfactionParameters);
		public AnalyticsEvent TutorialStepComplete => new AnalyticsEvent(tutorialStepCompleteEvent).AddParamLists(PlayerParameters, SatisfactionParameters);
		public AnalyticsEvent BusinessFlowStepComplete => new AnalyticsEvent(businessFlowStepCompleteEvent).AddParamLists(PlayerParameters, SatisfactionParameters);
		public AnalyticsEvent BusinessGiftInteraction => new AnalyticsEvent(businessGiftInteractionEvent).AddParamLists(PlayerParameters, SatisfactionParameters);
		public AnalyticsEvent ChoicesInteraction => new AnalyticsEvent(choicesInteractionEvent).AddParamLists(PlayerParameters, SatisfactionParameters);
		public AnalyticsEvent DesignTableInteraction => new AnalyticsEvent(designTableInteractionEvent).AddParamLists(PlayerParameters, SatisfactionParameters);
		public AnalyticsEvent ProductionBuildingItemCreated => new AnalyticsEvent(productionBuildingItemCreatedEvent).AddParamLists(PlayerParameters, SatisfactionParameters);
		public AnalyticsEvent DispatchProduct => new AnalyticsEvent(dispatchProductEvent).AddParamLists(PlayerParameters, SatisfactionParameters);
		public AnalyticsEvent UnlockDistrict => new AnalyticsEvent(unlockDistrictEvent).AddParamLists(PlayerParameters, SatisfactionParameters);
		public AnalyticsEvent BuildingPlaced => new AnalyticsEvent(buildingPlacedEvent).AddParamLists(PlayerParameters, SatisfactionParameters);
		public AnalyticsEvent BuildingConstruction => new AnalyticsEvent(buildingConstructionEvent).AddParamLists(PlayerParameters, SatisfactionParameters);
		public AnalyticsEvent WorkerAssigned => new AnalyticsEvent(workerAssignedEvent).AddParamLists(PlayerParameters, SatisfactionParameters);
		public AnalyticsEvent EventInteraction => new AnalyticsEvent(eventInteractionEvent).AddParamLists(PlayerParameters, SatisfactionParameters);
		public AnalyticsEvent SignUp => new AnalyticsEvent(signUpEvent);
		public AnalyticsEvent TPP => new AnalyticsEvent(tppEvent).AddParamLists(PlayerParameters);
		public AnalyticsEvent FlowStepCompleted => new AnalyticsEvent(flowStepCompletedEvent);
		public AnalyticsEvent TimeActiveUpdate => new AnalyticsEvent(timeActiveUpdateEvent);
		
		public AnalyticsEvent DeviceNotFound => new AnalyticsEvent(deviceNotFoundEvent);
		public static Dictionary<string, object> SystemParameters => CreateDataFromParameterGetters(systemInformationParameterValueGetters);
		public static Dictionary<string, object> PlayerParameters => CreateDataFromParameterGetters(commonParameterValueGetters);
		public static Dictionary<string, object> SatisfactionParameters => CreateDataFromParameterGetters(satisfactionParameterValueGetters);

		private static ParameterHandler[] systemInformationParameterValueGetters =
		{
			new(EventParams.systemServerPlayerId, () =>
			{
				return GameManager.UserId ?? "";
			}),
			new(EventParams.systemServerPlayerName, () =>
			{
				return GameManager.UserName ?? "";
			}),
			new(EventParams.systemBuildVersion, () => BuildInfo.VersionString.ToString()),
			new(EventParams.systemGitRevision, () => BuildInfo.GitRevision.ToString()),
			new(EventParams.systemKnackVersion, () => ((NGManager.Me?.m_knackVersion ?? 0) == 0) ? "-" : NGManager.Me?.m_knackVersion.ToString()),
			/*new(EventParams.systemServerSubdomain, () => "-"),
			new(EventParams.systemServerEnvironment, () => BuildDetails.ServerEnv.ToString()),
			// performance tracking
			new(EventParams.systemPerfTown, () => GameManager.Me.m_state.m_gameInfo.m_townPerf.Average.ToString()),
			new(EventParams.systemPerfDesignTable, () => GameManager.Me.m_state.m_gameInfo.m_designTablePerf.Average.ToString()),
			new(EventParams.systemPerfOther, () => GameManager.Me.m_state.m_gameInfo.m_otherPerf.Average.ToString()),*/
		};
		
		private static ParameterHandler[] commonParameterValueGetters =
		{
			new(EventParams.playerAccountType, () => "Alpha"),
			new(EventParams.playerDeedType, () => "Alpha"),
			new(EventParams.playerTotalTimePlayedSecs, () =>
			{
				int secs = GameStateTimer.Me.UpdateSecsSpentInSaveGame();
				if (secs < 0) secs = 0;
				return secs;
			}),
			new(EventParams.playerCashBalance, () => NGPlayer.Me.m_cash.Balance),
			new(EventParams.playerTotalCashEarned, () => (int)(NGPlayer.Me.m_cash.Earned + NGPlayer.Me.m_cash.Bought + NGPlayer.Me.m_cash.Gifted)),
			new(EventParams.playerTotalCashSpent, () => (int)NGPlayer.Me.m_cash.Spent),
			new(EventParams.playerBusinessLevel, () => NGBusinessDecisionManager.Me.PlayerLevel),
			new(EventParams.playerTotalDistrictsUnlockedCount, () =>
			{
				return DistrictManager.Me.GetUnlockedDistrictIDs().Count;
			}),
			new(EventParams.playerTotalProductsDispatchedVan, () => NGManager.NumSalesDispatch),
			//new(EventParams.playerBuildingCount, () => NGManager.Me.m_NGCommanderList.FindAll(building => building.HasBeenFullyConstructedAtLeastOnce && !building.IsBeingConstructed).Count),
			//new(EventParams.playerBuildingSitesCount, () => GameManager.Me.m_state.m_buildings.FindAll(building => !building.m_hasEverBeenConstructed).Count),
			//new(EventParams.playerTownSatisfactionValueFloat, () => (float)Math.Round(NGTownSatisfactionManager.GetCurrent, 4)),
			};

		private static ParameterHandler[] satisfactionParameterValueGetters =
		{
			//0 - 200%
			// new(EventParams.townSatisfactionDecoration, () => (float)Math.Round(NGTownSatisfactionManager.Me.GetLevel("Decoration"), 2)),
			// new(EventParams.townSatisfactionDesign, () => (float)Math.Round(NGTownSatisfactionManager.Me.GetLevel("Design"), 2)),
			// new(EventParams.townSatisfactionEfficiency, () => (float)Math.Round(NGTownSatisfactionManager.Me.GetLevel("Contribution"), 2)),
			// new(EventParams.townSatisfactionLeisure, () => (float)Math.Round(NGTownSatisfactionManager.Me.GetLevel("Leisure"), 2)),
			// new(EventParams.townSatisfactionPeople, () => (float)Math.Round(NGTownSatisfactionManager.Me.CatagoryTotals.TryGetValue(NGTownSatisfaction.Catagory.Life, out float life) ? life : 0, 2)),
			// new(EventParams.townSatisfactionPersona, () => (float)Math.Round(NGTownSatisfactionManager.Me.CatagoryTotals.TryGetValue(NGTownSatisfaction.Catagory.Persona, out float persona) ? persona : 0, 2)),
		};
			
		//********* pre-defined event instantiation *********//
		
		private readonly AnalyticsEvent currencyEarnedEvent = new AnalyticsEvent(
			EventNames.currencyEarned, new string[]
			{
				EventParams.currencyType,
				EventParams.currencyEarnedAmount,
				EventParams.currencyEarnedSource,
				// EventParams.currencyEarnedSourceDetail,
				// EventParams.currencyEarnedSourceQuantity
			});
		
		private readonly AnalyticsEvent currencySpentEvent  = new AnalyticsEvent(
			EventNames.currencySpent, new string[]
			{
				EventParams.currencyType,
				EventParams.currencySpentAmount,
				EventParams.currencySpentSink,
				EventParams.currencySpentSinkDetail,
				//EventParams.currencySpentSinkQuantity
			});

		private readonly AnalyticsEvent productSelectionInteractionEvent =
			new AnalyticsEvent(EventNames.productSelectionInteraction, new string[]
			{
				EventParams.productSelectionProduct,
				EventParams.productSelectionInteraction,
				EventParams.productSelectionAvailableProducts,
				EventParams.productSelectionProductLineCount,
			});
		
		private readonly AnalyticsEvent tutorialStepCompleteEvent =
			new AnalyticsEvent(EventNames.tutorialStepComplete, new string[]
			{
				EventParams.tutorialType,
				EventParams.tutorialSection,
				EventParams.tutorialIndex,
				EventParams.tutorialMessageType,
				EventParams.tutorialMessage,
			});
		
		private readonly AnalyticsEvent businessFlowStepCompleteEvent =
			new AnalyticsEvent(EventNames.businessFlowStepComplete, new string[]
			{
				EventParams.businessFlowAdvisor,
				EventParams.businessFlowIndex,
				EventParams.businessFlowType,
				EventParams.businessFlowDecision,
				EventParams.businessFlowDecisionMultiplier,
				EventParams.businessFlowMessage,
				EventParams.businessFlowTutorialPhase,
				EventParams.businessFlowSectionType,
			});
		
		private readonly AnalyticsEvent businessGiftInteractionEvent =
			new AnalyticsEvent(EventNames.businessGiftInteraction, new string[]
			{
				EventParams.businessGiftFlowIndex,
				EventParams.businessGiftItemCount,
				EventParams.businessGiftItemMax,
				EventParams.businessGiftChosenReward,
				EventParams.businessGiftReward1,
				EventParams.businessGiftReward2,
				EventParams.businessGiftReward3,
				EventParams.businessGiftReward4,
				EventParams.businessGiftType,
				EventParams.businessGiftQuantity,
				EventParams.businessGiftAction
			});
		
		private readonly AnalyticsEvent choicesInteractionEvent = new AnalyticsEvent(
			EventNames.choicesInteraction, new string[]
			{
				EventParams.choicesCharacter,
				EventParams.choicesIndex,
				EventParams.choicesStoryBranchType,
				EventParams.choicesEventType,
				EventParams.choicesAction,
				EventParams.choicesChoiceSelected
			});
		
		private readonly AnalyticsEvent designTableInteractionEvent =
			new AnalyticsEvent(EventNames.designTableInteraction, new string[]
			{
				EventParams.designTableProductLine,
				EventParams.designTableInteractionPartCount,
				EventParams.designTableInteractionPaintCount,
				EventParams.designTableInteractionStickerCount,
				EventParams.designTableInteractionPatternCount,
				EventParams.designTableInteractionDesignTimeSecs,
				EventParams.designTableInteractionDesignType,
				EventParams.designTableInteractionMaterialType,
				EventParams.designTableInteractionMaterialsCount,
				EventParams.designTableInteractionType,
				EventParams.designTableInteractionDetail,
				EventParams.designTableInteractionPrice,
				EventParams.designTableInteractionScore,
			});
		
		private readonly AnalyticsEvent productionBuildingItemCreatedEvent =
			new AnalyticsEvent(EventNames.productionBuildingItemCreated, new string[]
			{
				EventParams.buildingType,
				EventParams.buildingTitle,
				EventParams.buildingOutputType,
				EventParams.buildingTapContribution,
				EventParams.buildingHoldContribution,
				EventParams.buildingWorkerContribution,
				EventParams.buildingOutputComplexity,
				EventParams.buildingTapAddsValue,
				EventParams.buildingHoldAddsValue,
				EventParams.productionBuildingInputsCount,
				EventParams.productionBuildingInputsCap,
				EventParams.productionBuildingOutputCount,
				EventParams.productionBuildingOutputCap,
				EventParams.buildingWorkerCount,
			});
		
		private readonly AnalyticsEvent dispatchProductEvent = new AnalyticsEvent(
			EventNames.dispatchProduct, new string[]
			{
				EventParams.dispatchType,
				EventParams.dispatchValue,
				EventParams.dispatchQuantity,
				EventParams.productLine,
			});

		private readonly AnalyticsEvent unlockDistrictEvent = new AnalyticsEvent(
			EventNames.unlockDistrict, new string[]
			{
				EventParams.districtName,
				EventParams.cost,
				EventParams.currencyType,
			});
		
		private readonly AnalyticsEvent buildingPlacedEvent = new AnalyticsEvent(
			EventNames.buildingPlaced, new string[]
			{
				//buildingPlaced	
				EventParams.buildingType,
				EventParams.buildingTitle,
				EventParams.buildingDetails,
				EventParams.buildingSource,
				EventParams.cost,
				EventParams.currencyType,
			});
		
		private readonly AnalyticsEvent buildingConstructionEvent =
			new AnalyticsEvent(EventNames.buildingConstruction, new string[]
			{
				EventParams.buildingType,
				EventParams.buildingTitle,
				EventParams.buildingMaterialType,
				EventParams.buildingMaterialCost,
				EventParams.buildingTapContribution,
				EventParams.buildingHoldContribution,
				EventParams.buildingWorkerContribution,
				EventParams.buildingTapAddsValue,
				EventParams.buildingHoldAddsValue,
				EventParams.buildingWorkerCount
			});
		
		private readonly AnalyticsEvent workerAssignedEvent = new AnalyticsEvent(
			EventNames.workerAssigned, new string[]
			{
				EventParams.buildingType,
				EventParams.buildingTitle,
				EventParams.buildingMaterialType,
				EventParams.buildingMaterialCost,
				EventParams.buildingTapContribution
			});
		
		private readonly AnalyticsEvent eventInteractionEvent = new AnalyticsEvent(
			EventNames.eventInteraction, new string[]
			{
				EventParams.eventType,
				EventParams.eventUniqueID,
				EventParams.eventDescription,
				EventParams.eventAction,
				EventParams.eventCost,
				EventParams.eventCostCurrencyType,
				EventParams.eventResultRank,
				EventParams.eventPrize,
				EventParams.eventPrizeCurrencyType,
				EventParams.eventFinalScore,
			});
		
		private readonly AnalyticsEvent signUpEvent = new AnalyticsEvent(
			EventNames.signUp, new string[]
			{
				EventParams.alignment,
				EventParams.playerName,
				EventParams.businessName,
				EventParams.businessDescription,
			});
		
		private readonly AnalyticsEvent tppEvent = new AnalyticsEvent(
			EventNames.tpp, new string[]
			{
				EventParams.tppMode,
			});

		private readonly AnalyticsEvent flowStepCompletedEvent = new AnalyticsEvent(
			EventNames.flowStepCompleted, new string[]
			{
				EventParams.flowStepFile,
				EventParams.flowStepLine,
			});

		private readonly AnalyticsEvent timeActiveUpdateEvent = new AnalyticsEvent(
			EventNames.timeActiveUpdate, new string[]
			{
				EventParams.timeActive,
			});

		private readonly AnalyticsEvent deviceNotFoundEvent = new AnalyticsEvent(EventNames.systemDeviceModelNotFound);
		
		
		public const char kStringDelimiter = ';';
//Helpers
		private class ParameterHandler
		{
			public string ParamKey { get; } = string.Empty;
			public System.Func<object>? ParamValueGetter { get; } = null;

			public ParameterHandler(string paramKey, System.Func<object> getter)
			{
				ParamKey = paramKey;
				ParamValueGetter = getter;
			}
		}
		
		private static Dictionary<string, object> CreateDataFromParameterGetters(ParameterHandler[] paramGetters)
		{
			IDictionary<string, object> commonParameters = new Dictionary<string, object>();
			if (paramGetters != null)
			{
				for (int iParam = 0; iParam < paramGetters.Length; iParam++)
				{
					ParameterHandler parameterHandler = paramGetters[iParam];
					KeyValuePair<string, object> keyValuePair = new KeyValuePair<string, object>
					(
						parameterHandler.ParamKey,
						parameterHandler.ParamValueGetter?.Invoke()
					);
					Debug.Assert(keyValuePair.Value != null,
						$"Analytics.EventFactory - CreateDataFromParameterGetters - null value found for parameter key: '{parameterHandler.ParamKey}'");

					commonParameters.Add(keyValuePair);
				}
			}
			return (Dictionary<string, object>)commonParameters;
		}
	}
}

/*
		
		// public static class EvFact
		// {
		//
		// }
		public static string[] GetFields(Type t)
		{
			FieldInfo[] fields = t.GetFields();
			string[] fieldNames = new string[fields.Length];
			for (int iField = 0; iField < fields.Length; iField++) fieldNames[iField] = fields[iField].Name;
			string s = "";
			foreach (var VARIABLE in fieldNames)
			{
				s += $"{VARIABLE} ";
			}
			Debug.Log(s);
			return fieldNames;
		}
		public static class CurrencySpent2
		{
			public const string Name = "currencySpent";
			public static class Params
			{
				public const string currencyType = EventParams.currencyType;
				public const string currencySpentAmount = EventParams.currencySpentAmount;
				public const string currencySpentSink = EventParams.currencySpentSink;
				public const string currencySpentSinkDetail = EventParams.currencySpentSinkDetail;
				public const string currencySpentSinkQuantity = EventParams.currencySpentSinkQuantity;
			}

			public static AnalyticsEvent Create => new(Name, GetFields(typeof(CurrencySpent2.Params)));
			//new string[] { Params.currencyType,Params.currencySpentAmount,Params.currencySpentSink,Params.currencySpentSinkDetail,Params.currencySpentSinkQuantity });
		}
		
		
		public AnalyticsEvent CurrencySpent3 => CurrencySpent2.Create;//new AnalyticsEvent(new EventCurrencySpent().Event);// AnalyticsManager.Me.Events.EventCurrencySpent.Event;
		*/