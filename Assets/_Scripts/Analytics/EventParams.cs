using System.Collections.Generic;

namespace Cans.Analytics
{
	public class EventParams
	{
		public const string c_CashName = "Cash";

		//system information params
		public const string systemServerPlayerId = "systemServerPlayerId";
		public const string systemServerPlayerName = "systemServerPlayerName";
		public const string systemBuildVersion = "systemBuildVersion";
		public const string systemGitRevision = "systemGitRevision";
		public const string systemKnackVersion = "systemKnackVersion";
		public const string systemServerSubdomain = "systemServerSubdomain";
		public const string systemServerEnvironment = "systemServerEnvironment";
		public const string systemPerfTown = "systemPerfTown";
		public const string systemPerfDesignTable = "systemPerfDesignTable";
		public const string systemPerfDC = "systemPerfDC";
		public const string systemPerfVoting = "systemPerfVoting";
		public const string systemPerfOther = "systemPerfOther";

		public const string systemDeviceModelID = "systemDeviceModelID";
		
		//common parameters
		public const string currencyType = "currencyType";
		public const string productLine = "productLine";
		public const string buildingType = "buildingType";
		public const string buildingTitle = "buildingTitle";
		public const string cost = "cost";
		public const string buildingTapContribution = "buildingTapContribution";
		public const string buildingHoldContribution = "buildingHoldContribution";
		public const string buildingWorkerContribution = "buildingWorkerContribution";
		public const string buildingTapAddsValue = "buildingTapAddsValue";
		public const string buildingHoldAddsValue = "buildingHoldAddsValue";
		public const string buildingWorkerCount = "buildingWorkerCount";
		public const string buildingMaterialType = "buildingMaterialType";
		public const string buildingMaterialCost = "buildingMaterialCost";

		//common player params
		public const string playerAccountType = "playerAccountType";
		public const string playerDeedType = "playerDeedType";
		public const string playerTotalTimePlayedSecs = "playerTotalTimePlayedSecs";
		public const string playerProductLine = "playerProductLine";
		public const string playerCashBalance = "playerCashBalance";
		public const string playerTotalCashEarned = "playerTotalCashEarned";
		public const string playerTotalCashSpent = "playerTotalCashSpent";
		public const string playerLegacyCoinBalance = "playerLegacyCoinBalance";
		public const string playerTotalLegacyCoinEarned = "playerTotalLegacyCoinEarned";
		public const string playerTotalLegacyCoinSpent = "playerTotalLegacyCoinSpent";
		public const string playerLegacyGemBalance = "playerLegacyGemBalance";
		public const string playerTotalLegacyGemsEarned = "playerTotalLegacyGemsEarned";
		public const string playerTotalLegacyGemsSpent = "playerTotalLegacyGemsSpent";
		public const string playerLegacyTicketBalance = "playerLegacyTicketBalance";
		public const string playerTotalLegacyTicketsEarned = "playerTotalLegacyTicketsEarned";
		public const string playerTotalLegacyTicketsSpent = "playerTotalLegacyTicketsSpent";
		public const string playerBusinessLevel = "playerBusinessLevel";
		public const string playerTotalDistrictsUnlockedCount = "playerTotalDistrictsUnlockedCount";
		public const string playerTotalProductsDispatchedVan = "playerTotalProductsDispatchedVan";
		public const string playerTotalProductsDispatchedTrain = "playerTotalProductsDispatchedTrain";
		public const string playerWorkerCount = "playerWorkerCount";
		public const string playerWorkersUnemployedCount = "playerWorkersUnemployedCount";
		public const string playerBuildingCount = "playerBuildingCount";
		public const string playerBuildingSitesCount = "playerBuildingSitesCount";
		public const string playerTownSatisfactionValueFloat = "playerTownSatisfactionValueFloat";

		//Common townSatisfaction Properties
		public const string townSatisfactionDecoration = "townSatisfactionDecorationFloat";
		public const string townSatisfactionDesign = "townSatisfactionDesignFloat";
		public const string townSatisfactionEfficiency = "townSatisfactionEfficiencyFloat";
		public const string townSatisfactionLeisure = "townSatisfactionLeisureFloat";
		public const string townSatisfactionPeople = "townSatisfactionPeopleFloat";
		public const string townSatisfactionPersona = "townSatisfactionPersonaFloat";

		//Currency Source & Sink	
		//currencyEarned
		public const string currencyEarnedAmount = "currencyEarnedAmount";
		public const string currencyEarnedSource = "currencyEarnedSource";
		//public const string currencyEarnedSourceDetail = "currencyEarnedSourceDetail";//TODO TS blocked
		//public const string currencyEarnedSourceQuantity = "currencyEarnedSourceQuantity";//TODO TS blocked

		//currencySpent
		public const string currencySpentAmount = "currencySpentAmount";
		public const string currencySpentSink = "currencySpentSink";
		public const string currencySpentSinkDetail = "currencySpentSinkDetail";
		//public const string currencySpentSinkQuantity = "currencySpentSinkQuantity";//TODO TS blocked

		//Progression Events	
		//productSelectionInteraction
		public const string productSelectionProduct = "productSelectionProduct";
		public const string productSelectionInteraction = "productSelectionInteraction";
		public const string productSelectionAvailableProducts = "productSelectionAvailableProducts";
		public const string productSelectionProductLineCount = "productSelectionProductLineCount";

		//tutorialStepComplete	
		public const string tutorialType = "tutorialType";
		public const string tutorialSection = "tutorialSection";
		public const string tutorialIndex = "tutorialIndex";
		public const string tutorialMessageType = "tutorialMessageType";
		public const string tutorialMessage = "tutorialMessage";

		//businessFlowStepComplete	
		public const string businessFlowAdvisor = "businessFlowAdvisor";
		public const string businessFlowIndex = "businessFlowIndex";
		public const string businessFlowType = "businessFlowType";
		public const string businessFlowDecision = "businessFlowDecision";
		public const string businessFlowDecisionMultiplier = "businessFlowDecisionMultiplier";
		public const string businessFlowMessage = "businessFlowMessage";
		public const string businessFlowTutorialPhase = "businessFlowTutorialPhase";
		public const string businessFlowSectionType = "businessFlowSectionType";

		//businessGiftInteraction	
		public const string businessGiftFlowIndex = "businessGiftFlowIndexFloat"; //TODO: TS MUST MAKE FLOAT 
		public const string businessGiftItemCount = "businessGiftItemCount";
		public const string businessGiftItemMax = "businessGiftItemMax";
		public const string businessGiftChosenReward = "businessGiftChosenReward";
		public const string businessGiftReward1 = "businessGiftReward"; //TODO: TS APPEND '1' OM DASH
		public const string businessGiftReward2 = "businessGiftReward2";
		public const string businessGiftReward3 = "businessGiftReward3";
		public const string businessGiftReward4 = "businessGiftReward4";
		public const string businessGiftType = "businessGiftType";
		public const string businessGiftQuantity = "businessGiftQuantity";
		public const string businessGiftAction = "businessGiftAction";

		//choicesInteraction	
		public const string choicesCharacter = "choicesCharacter";
		public const string choicesIndex = "choicesIndex";
		public const string choicesStoryBranchType = "choicesStoryBranchType";
		public const string choicesEventType = "choicesEventType";
		public const string choicesAction = "choicesAction";
		public const string choicesChoiceSelected = "choicesChoiceSelected";

		//Core Systems	
		//designTableInteraction	
		public const string designTableProductLine = "designTableProductLine";
		public const string designTableInteractionPartCount = "designTableInteractionPartCount";
		public const string designTableInteractionPaintCount = "designTableInteractionPaintCount";
		public const string designTableInteractionStickerCount = "designTableInteractionStickerCount";
		public const string designTableInteractionPatternCount = "designTableInteractionPatternCount";
		public const string designTableInteractionDesignTimeSecs = "designTableInteractionDesignTimeSecs";
		public const string designTableInteractionDesignType = "designTableInteractionDesignType";
		public const string designTableInteractionType = "designTableInteractionType";
		public const string designTableInteractionDetail = "designTableInteractionDetail";
		public const string designTableInteractionPrice = "designTableInteractionPrice";
		public const string designTableInteractionScore = "designTableInteractionScore";
		public const string designTableInteractionMaterialType = "designTableInteractionMaterialType";
		public const string designTableInteractionMaterialsCount = "designTableInteractionMaterialsCount";

		//productionBuildingItemCreated	
		public const string buildingOutputType = "buildingOutputType";
		public const string buildingOutputComplexity = "buildingOutputComplexity";
		public const string productionBuildingInputsCount = "productionBuildingInputsCount";
		public const string productionBuildingInputsCap = "productionBuildingInputsCap";
		public const string productionBuildingOutputCount = "productionBuildingOutputCount";
		public const string productionBuildingOutputCap = "productionBuildingOutputCap";

		//dispatchProduct	despatchType
		public const string dispatchType = "dispatchType";
		public const string dispatchValue = "dispatchValue";
		public const string dispatchQuantity = "dispatchQuantity";

		//unlockDistrict	
		public const string districtName = "districtName";

		//buildingPlaced	
		public const string buildingDetails = "buildingDetails";
		public const string buildingSource = "buildingSource";

		//buildingConstruction	 (only common params)

		//workerAssigned	(only common params)

		//trainArrived //TODO TS & JS under consideration
		// public const string 
		// public const string 

		//Events
		//eventInteraction
		public const string eventType = "eventType";
		public const string eventUniqueID = "eventUniqueID"; //changed
		public const string eventDescription = "eventDescription";
		public const string eventAction = "eventAction";
		public const string eventProductMaterialCost = "eventProductMaterialCost";
		public const string eventProductMaterialType = "eventProductMaterialType";
		public const string eventCost = "eventCost";
		public const string eventCostCurrencyType = "eventCostCurrencyType";
		public const string eventResultRank = "eventResultRank";
		public const string eventPrize = "eventPrize";
		public const string eventPrizeCurrencyType = "eventPrizeCurrencyType";
		public const string eventFinalScore = "eventFinalScore";
		
		//SignUp
		public const string playerName = "playerName";
		public const string businessName = "companyName";//TODO: TS CHANGE TO BUSINESS NAME
		public const string businessDescription = "businessDescription";
		public const string alignment = "alignment";

		public const string tppMode = "tppMode";

		// MoA-specific params
		public const string flowStepFile = "flowStepFile";
		public const string flowStepLine = "flowStepLine";
		public const string timeActive = "timeActive";
	}
}