using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class BuildingMarker : MonoBehaviour {
	public List<string> m_animations = new List<string>();
	public float m_weight = 1.0f;
	public float m_orientationVariance = 0.0f;
	public float m_positionVariance = 0.0f;
    public bool m_inUse = false;

	private void Awake()
	{
		// CM170719: These values were being manually set on instaniate. I've just moved it here.
		transform.up = Vector3.up;
		m_inUse = false;
	}

#if UNITY_EDITOR

	private static GameObject defaultWorkerReference = null;

	private void OnValidate()
	{
		if( defaultWorkerReference == null ) {
			defaultWorkerReference = UnityEditor.AssetDatabase.LoadAssetAtPath( "Assets/_Art/Meshes/Products/Clothing/Jackets/SM_Worker_Male_1850_LOD0.fbx", typeof(GameObject) ) as GameObject;
		}

		if( _prefabToShow == null ) { 
			_prefabToShow = defaultWorkerReference;
		}
	}

	[Header("Gizmo")]//, Sirenix.OdinInspector.ShowInInspector]
	private GameObject _prefabToShow;

	private void OnDrawGizmosSelected()
	{
		if( _prefabToShow == null ) {
			return;
		}

		MeshFilter mesh = _prefabToShow.GetComponentInChildren<MeshFilter>();

		if( mesh == null ) {
			return;
		}

		Gizmos.DrawMesh( mesh.sharedMesh, this.transform.position, this.transform.rotation, Vector3.one );
		// Graphics.DrawMesh( , renderer.sharedMaterial, 0, Camera.current );
	}

#endif
}
