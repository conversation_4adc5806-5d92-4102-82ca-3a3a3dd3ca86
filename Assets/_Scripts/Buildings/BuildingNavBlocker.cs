using UnityEngine;

public class BuildingNavBlocker : MonoBehaviour
{
    public bool m_addNavMargin = false;
    public bool m_hasEntrance = false;
    public bool m_isBaked = true;
    public GlobalData.NavCostTypes m_navCostType = GlobalData.NavCostTypes.LowNoNav;
    
    private PathBlock m_blocker;
    
    public const float c_navMargin = .5f;

    private void Start()
    {
        if (GetComponentInParent<DTDragPalette>() != null) // if this is a palette item, don't block paths
            return;
        gameObject.layer = GameManager.c_layerIgnoreRaycast;
        var col = GetComponent<Collider>();
        if (col.isTrigger == false)
            Debug.LogError($"BuildingNavBlocker {name} has non-trigger collider, this is likely to cause problems [{transform.Path()}]", gameObject);
        var existingPathBlock = gameObject.GetComponent<PathBlock>();
        if (existingPathBlock != null) Destroy(existingPathBlock);
        var margin = m_addNavMargin ? c_navMargin : 0;
        float width, depth;
        switch (col)
        {
            case SphereCollider sC:
				width = sC.bounds.size.x;
				depth = sC.bounds.size.z;
				m_blocker = PathBlock.Create(gameObject, width * 0.5f + margin, depth * 0.5f + margin, 0, m_isBaked, true, m_hasEntrance);
				break;
            case BoxCollider bC:
                m_blocker = PathBlock.Create(gameObject, bC, margin, m_isBaked, m_hasEntrance);
                break;
        }
        if (m_blocker != null) m_blocker.SetNavCostType(m_navCostType);
    }

    public void ApplyScale(Vector3 _scale)
    {
        var col = GetComponent<Collider>();
        switch (col)
        {
            case SphereCollider sC:
                sC.radius *= _scale.x;
                break;
            case BoxCollider bC:
                bC.size = Vector3.Scale(bC.size, _scale);
                break;
        }
    }

    public (Vector3, Vector3, Vector3) GetBounds()
    {
        var margin = m_addNavMargin ? c_navMargin : 0;
        var col = GetComponent<Collider>();
        switch (col)
        {
            case SphereCollider sC:
                var center = sC.center;
                var radius = sC.radius + margin;
                return (center, Vector3.right * radius, Vector3.forward * radius); 
            case BoxCollider bC:
                var bounds = bC.bounds;
                var extents = Vector3.Scale(bC.size, transform.lossyScale) * .5f;
                return (bounds.center, transform.right * (extents.x + margin), transform.forward * (extents.z + margin));
        }
        return default;
    }
}
