using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Plot : MonoBehaviour {
	Transform m_arrow;
	TerrainSectionMesh m_terrainSectionMesh;
	void Awake() {
		m_terrainSectionMesh = GetComponentInChildren<TerrainSectionMesh>();
		transform.localPosition = m_localPositionTarget;
		if (m_terrainSectionMesh == null)
		{
			transform.localScale = m_localScaleTarget;
			m_arrow = transform.GetChild(0);
		}
	}
	void Update() {
		UpdateState();
		if (m_terrainSectionMesh == null)
			m_arrow.position = transform.position + transform.forward * (Mathf.Sin(Time.time * 8f) * .1f + transform.localScale.z * .5f);
	}
	Vector3 m_localPositionTarget = Vector3.zero;
	Vector3 m_localScaleTarget = Vector3.one * .01f;
	float m_localRaiseTarget = 0;
	void UpdateState() {
		transform.localPosition = Vector3.Lerp(transform.localPosition, m_localPositionTarget, .4f);
		if (m_terrainSectionMesh != null)
		{
			m_terrainSectionMesh.SetSize(Vector3.Lerp(m_terrainSectionMesh.m_size, m_localScaleTarget, .4f));
			m_terrainSectionMesh.SetRaise(Mathf.Lerp(m_terrainSectionMesh.m_raise, m_localRaiseTarget, .4f));
		}
		else
			transform.localScale = Vector3.Lerp(transform.localScale, m_localScaleTarget, .4f);
	}

	public Vector3 FinalPos()
	{
		var lp = transform.localPosition;
		var ls = transform.localScale;
		var lpt = m_localPositionTarget;
		var lst = m_localScaleTarget;
		SetTargets(true);
		transform.localPosition = m_localPositionTarget;
		transform.localScale = m_localScaleTarget;
		var finalPos = transform.position;
		transform.localPosition = lp;
		transform.localScale = ls;
		m_localPositionTarget = lpt;
		m_localScaleTarget = lst;
		return finalPos;
	}

	int m_width = 1, m_height = 1;
	public void SetState(bool _active, int _w, int _h)
	{
		var mr = GetComponentInChildren<MeshRenderer>();
		if (m_terrainSectionMesh == null)
			mr.sharedMaterial = _active ? GlobalData.Me.m_plotMaterialSelected : GlobalData.Me.m_plotMaterialUnselected;
		else
			m_terrainSectionMesh.SetMaterial(_active ? GlobalData.Me.m_plotMaterial : GlobalData.Me.m_plotUnselectedMaterial);
		m_width = _w; m_height = _h;
		SetTargets(_active);
	}
	private void SetTargets(bool _active)
	{
		float yUp = m_terrainSectionMesh == null ? .2f : 0;
		if (_active || !NGManager.Me.m_granularBuildingPlacement) {
			m_localPositionTarget = Vector3.zero;
			m_localScaleTarget = new Vector3(BuildingPlacementManager.c_buildingTileSize * m_width, .2f, BuildingPlacementManager.c_buildingTileSize * m_height);
			m_localRaiseTarget = .4f;
		} else {
			float singleScale = RoadManager.Me.IsUsingPaths ? .5f : 1;
			m_localPositionTarget = transform.forward * (BuildingPlacementManager.c_buildingTileSize * ((m_height - singleScale) * .5f)) + Vector3.up * yUp;
			m_localScaleTarget = new Vector3(BuildingPlacementManager.c_buildingTileSize * .9f * singleScale, .2f, BuildingPlacementManager.c_buildingTileSize * .9f * singleScale);
			m_localRaiseTarget = .35f;
		}
	}
}
