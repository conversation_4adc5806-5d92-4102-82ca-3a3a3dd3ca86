using ClipperLib;
using System.Collections.Generic;
using UnityEngine;

using Polygon = System.Collections.Generic.List<ClipperLib.IntPoint>;
using PolygonCollection = System.Collections.Generic.List<System.Collections.Generic.List<ClipperLib.IntPoint>>;

[System.Serializable]
public class PolygonWrapper {
	[SerializeField]
	public List<IntPoint> items;
	public PolygonWrapper( List<IntPoint> b ) {
		items = b;
	}
}

[System.Serializable]
public class PolygonCollectionWrapper {
	
	[SerializeField]
	private List<PolygonWrapper> items;
 
	public PolygonCollectionWrapper( List<PolygonWrapper> b ) 
	{
		items = b;
	}

	public static implicit operator PolygonCollection( PolygonCollectionWrapper ts ) {
		return ts.items.ConvertAll( (x) => x.items );
	}

	public static explicit operator PolygonCollectionWrapper(PolygonCollection b) {
		return new PolygonCollectionWrapper( b.ConvertAll( (x) => new PolygonWrapper(x) ) );
	}
}

public static class ClipperUtility
{
	private const float kScaler = 1000f;

	private static Clipper clipper = new Clipper();
	private static Polygon temp_triangle = new Polygon(3);

	private static List<Vector3> verts = new List<Vector3>();
	private static List<int> indicies = new List<int>();

	static ClipperUtility()
	{
		// Temp polygon for each triangle of the mesh.
		temp_triangle.Add(default); temp_triangle.Add(default); temp_triangle.Add(default);
	}

	public static PolygonCollection MeshToPolygon( MeshFilter filter )
	{
		return MeshToPolygon( filter.mesh, filter.transform );
	}

	public static PolygonCollection MeshToPolygon( Mesh mesh, Transform offset )
	{
		// Mesh is in local space, so get data to convert to world space.
		Vector3 offsetP = ( offset != null ) ? offset.position : Vector3.zero;
		Quaternion offsetR = (offset != null ) ? offset.rotation : Quaternion.identity;

		return MeshToPolygon( mesh, offsetP, offsetR, offset.lossyScale ); 
	}

	public static PolygonCollection MeshToPolygon( Mesh mesh, Vector3 offsetPosition, Quaternion offsetRotation, Vector3 scale )
	{
		PolygonCollection mainPoly = new PolygonCollection();

		// Get mesh data
		mesh.GetVertices(verts);
		mesh.GetIndices(indicies,0);

		// Reset any existing data.
		clipper.Clear();

		// Cycle through all the triangles and add them to clipper to join.
		int c = indicies.Count;
		for( int i = 0; i < c; i += 3 )
		{
			for( int j = 0; j < 3; ++j )
			{
				Vector3 p = offsetPosition + offsetRotation * ( Vector3.Scale( verts[ indicies[i + j] ], scale ) );
				IntPoint ip = new IntPoint( (long)(p.x * kScaler), (long)(p.z * kScaler) );
				temp_triangle[j] = ip;
			}
			clipper.AddPath(temp_triangle, PolyType.ptSubject, true );
		}

		// Execute as Union to join all triangles, and use NonZero because we want to add overlapping triangles, not remove them.
		clipper.Execute( ClipType.ctUnion, mainPoly, PolyFillType.pftNonZero );

		return mainPoly;
	}

}

public class QuadClipper
{
	private const float kScaler = 1000f;

	public PolygonCollection solution { get; private set; } = new PolygonCollection();

	private PolygonCollection mainPoly;
	private Polygon quad = new Polygon(4);

	Clipper clip = new Clipper();

	public QuadClipper( PolygonCollection basePolygon ) {
		mainPoly = basePolygon;

		// Fill with 4 elements.
		quad.Add( default ); quad.Add( default ); 
		quad.Add( default ); quad.Add( default ); 
	}


	// Calculate how much area this section coverts the polygon.
	public float GetCellCoverage( Vector3 worldPosition, float cellSize = 8.0f )
	{
		int x = (worldPosition.x < 0) ? -1 : 0;
		int y = (worldPosition.z < 0) ? -1 : 0;

		x = (int)( worldPosition.x / cellSize + x );
		y = (int)( worldPosition.z / cellSize + y );

		return GetCellCoverage( x, y, cellSize );
	}

	public float GetCellCoverage( int x, int y, float cellSize = 8.0f )
	{
		double coverage = 0;

		SetQuad( x, y, cellSize );

		clip.Clear();
		clip.AddPaths( mainPoly, PolyType.ptSubject, true ); 
		clip.AddPath( quad, PolyType.ptClip, true );
		clip.Execute( ClipType.ctIntersection, solution );

		for( int i = 0; i < solution.Count; ++i ) {
			// Area will be negative if the winding order is backwards.
			// If the winding order is backwards, it's likely that it's a "hole"
			// In this instance, we don't want to add the area, rather subtract it.
			// Something to think about if it becomes an issue.
			coverage += (float)(Clipper.Area( solution[i] ) );
		}

		return (float)(coverage / (kScaler*kScaler) / (cellSize*cellSize) );
	}

	private void SetQuad( int x, int y, float cellSize = 8.0f )
	{
		long offsetX = (long)(x * cellSize);
		long offsetY = (long)(y * cellSize);

		quad[0] = ( new IntPoint(  offsetX * kScaler,  			 	(offsetY + cellSize) * kScaler ) );
		quad[1] = ( new IntPoint( (offsetX + cellSize) * kScaler,  	(offsetY + cellSize) * kScaler ) );
		quad[2] = ( new IntPoint( (offsetX + cellSize) * kScaler,    offsetY * kScaler ) );
		quad[3] = ( new IntPoint(  offsetX * kScaler,  			 	 offsetY * kScaler ) );
	}
}