using UnityEngine;
using UnityEngine.Playables;

[System.Serializable]
public class CameraControlOffsetBehaviour : PlayableBehaviour
{
	public Vector3 offset_camera_position = Vector3.zero;
	public Vector3 offset_camera_rotation = Vector3.zero;
	public float offset_pivot_rotation = 0f;

	public override void ProcessFrame(Playable playable, FrameData info, object playerData)
	{
		var transitionReference = playerData as CameraTransitionReference;
		Debug.Assert( transitionReference != null, "Camera transition reference is null." );
		transitionReference.reference.ApplyOffsetBehaviour( this );
	}
}