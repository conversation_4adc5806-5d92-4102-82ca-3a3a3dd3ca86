using UnityEngine;
using UnityEngine.Playables;

public class CameraControlResetBehaviour : PlayableBehaviour
{
	public override void ProcessFrame(Playable playable, FrameData info, object playerData)
	{
		var transitionReference = playerData as CameraTransitionReference;
		Debug.Assert( transitionReference != null, "Camera transition reference is null." );
		transitionReference.reference.Reset();
	}
}