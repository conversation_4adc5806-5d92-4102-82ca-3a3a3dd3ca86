using System;
using System.Collections.Generic;
using UnityEngine;
using Object = UnityEngine.Object;
using Unity.Mathematics;
using UnityEngine.Serialization;

#if UNITY_EDITOR
using UnityEditor;
#endif

[Serializable]
public class DistrictEdge
{
	public int m_vertex1, m_vertex2;
	public int m_district1, m_district2;
	public void Replace(int _old, int _new)
	{
		if (m_vertex1 == _old) m_vertex1 = _new;
		if (m_vertex2 == _old) m_vertex2 = _new;
	}

	public bool Contains(int _vertex) => m_vertex1 == _vertex || m_vertex2 == _vertex;
	
	public int Other(DistrictEdge _other)
	{
		if (_other.m_vertex1 == m_vertex1 || _other.m_vertex2 == m_vertex1) return m_vertex2;
		if (_other.m_vertex1 == m_vertex2 || _other.m_vertex2 == m_vertex2) return m_vertex1;
		return -1;
	}

	public int Same(DistrictEdge _other)
	{
		if (_other.m_vertex1 == m_vertex1 || _other.m_vertex2 == m_vertex1) return m_vertex1;
		if (_other.m_vertex1 == m_vertex2 || _other.m_vertex2 == m_vertex2) return m_vertex2;
		return -1;
	}

	public bool Is(DistrictEdge _other)
	{
		return (m_vertex1 == _other.m_vertex1 && m_vertex2 == _other.m_vertex2) || (m_vertex1 == _other.m_vertex2 && m_vertex2 == _other.m_vertex1);
	}

	public void Absorb(DistrictEdge _other)
	{
		List<int> districts = new();
		if (m_district1 != -1) districts.Add(m_district1);
		if (m_district2 != -1) districts.Add(m_district2);
		if (_other.m_district1 != -1) districts.AddUnique(_other.m_district1);
		if (_other.m_district2 != -1) districts.AddUnique(_other.m_district2);
		m_district1 = -1; m_district2 = -1;
		if (districts.Count > 0) m_district1 = districts[0];
		if (districts.Count > 1) m_district2 = districts[1];
		if (districts.Count > 2)
			Debug.LogError($"Error absorbing edge: have districts {m_district1}, {m_district2} absorbing edge with districts {_other.m_district1}, {_other.m_district2}");
	}
}

[Serializable]
public class DistrictPolygon
{
	public List<int> m_edges = new();
	public string m_name; // ID
	public string m_audioID; public string AudioID => string.IsNullOrEmpty(m_audioID) ? Sanitise(Name).Replace(" ", "_") : m_audioID;

	public static string Sanitise(string _s)
	{
		var lastSpace = _s.LastIndexOf(" ");
		if (lastSpace != -1 && lastSpace < _s.Length - 2 && _s[lastSpace + 1] == 'D' && char.IsNumber(_s[lastSpace + 2]))
			return _s[..lastSpace];
		return _s;
	}

	public void PlayUnlockAudio()
	{
		if (DistrictManager.Me.m_unlockAudio != null)
		{
			AudioClipManager.Me.SetMusicSwitch("RegionUnlocking", $"Region_{AudioID}");
			GameManager.Me.SetFlowMusicState("RegionUnlock");
			DistrictManager.Me.m_unlockAudio.Play(GameManager.Me.gameObject, AkEventHolder.EBus.Music);
		}
	}

	private int m_index;
	private bool m_isUnlocked = false;
	public int Index => m_index;
	public void SetIndex(int _index) => m_index = _index;
	public bool IsCapture { get; set; }
	public bool IsUnlocked {
	#if UNITY_EDITOR
		get => DistrictManager.Me.m_data.UnlockOverride(m_index, m_isUnlocked);
	#else
		get => m_isUnlocked;
	#endif
		set => m_isUnlocked = value;
	}
	
	private float m_size, m_width, m_height;
	public float Size => m_size;
	public float Width => m_width;
	public float Height => m_height;
	
	public string ID => m_name;
	public string Name => string.IsNullOrEmpty(m_name) == false && ReactDistrictTable.s_records != null && ReactDistrictTable.s_records.Count > 0 ? ReactDistrictTable.GetEntryByID(ID)?.m_districtName ?? "" : "";

	public struct DistrictTriangle
	{
		// this is an expansion of the point-in-tri routine in GeometryUtilities.PointInTriangleXZ, essentially a set of 2D edge rotations by 90 degrees
		private float m_x1, m_x2, m_x3, m_z1, m_z2, m_z3, m_a1, m_a2, m_a3;
		public DistrictTriangle(Vector3 _v1, Vector3 _v2, Vector3 _v3)
		{
			m_x1 = _v1.z - _v2.z; m_x2 = _v2.z - _v3.z; m_x3 = _v3.z - _v1.z;
			m_z1 = _v2.x - _v1.x; m_z2 = _v3.x - _v2.x; m_z3 = _v1.x - _v3.x;
			m_a1 = _v2.z * (_v1.x - _v2.x) - _v2.x * (_v1.z - _v2.z);
			m_a2 = _v3.z * (_v2.x - _v3.x) - _v3.x * (_v2.z - _v3.z);
			m_a3 = _v1.z * (_v3.x - _v1.x) - _v1.x * (_v3.z - _v1.z);
		}
		
		public bool IsPointInside(Vector3 _p)
		{
			float d1 = _p.x * m_x1 + _p.z * m_z1 + m_a1;
			float d2 = _p.x * m_x2 + _p.z * m_z2 + m_a2;
			float d3 = _p.x * m_x3 + _p.z * m_z3 + m_a3;
			// get sign of each distance (asuint is a fair bit slower, at least in Unity, 1600 to 1200 ms for 1,000,000 full tests)
			//uint s1 = math.asuint(d1) >> 31, s2 = math.asuint(d2) >> 31, s3 = math.asuint(d3) >> 31;
			int s1 = d1 < 0 ? 1 : 0, s2 = d2 < 0 ? 1 : 0, s3 = d3 < 0 ? 1 : 0;
			// 0 or 3 (0 positives or 0 negatives) means all were on the same side wrt triangle winding, must be inside (can't be all three on the outside)
			var sum = s1 + s2 + s3;
			return ((sum & 1) ^ (sum >> 1)) == 0; // 0 or 3, not 1 or 2
		}
	}

	public bool PointInsideSlow(Vector3 _pos) // old version, about 20x slower than the new
	{
		var mesh = Mesh();
		var verts = mesh.vertices;
		var inds = mesh.GetIndices(0);
		int indsToCheck = inds.Length;
		if (m_meshHasExtrude) indsToCheck -= 6 * m_edges.Count;
		for (int i = 0; i < indsToCheck; i += 3)
		{
			var v0 = verts[inds[i + 0]];
			var v1 = verts[inds[i + 1]];
			var v2 = verts[inds[i + 2]];
			if (GeometryUtilities.PointInTriangleXZ(_pos, v0, v1, v2))
				return true;
		}
		return false;
	}

	public bool PointInside(Vector3 _pos)
	{
		if (m_triangles == null) return false;
		for (int i = 0; i < m_triangles.Length; ++i)
			if (m_triangles[i].IsPointInside(_pos))
				return true;
		return false;
	}

	public void Replace(int _old, int _new)
	{
		for (int i = 0; i < m_edges.Count; ++i)
			if (m_edges[i] == _old) m_edges[i] = _new;
	}
	
	private DistrictTriangle[] m_triangles;
	private Mesh m_mesh, m_meshNoExtrude;
	private Vector3 m_bestLabelPos; public Vector3 BestLabelPosition => m_bestLabelPos;
	private float m_containingRadius;
	private bool m_meshHasExtrude;
	private bool m_meshWasUnlocked;
	private float m_blendLevel;
	private Vector3 m_currentBlendFocus, m_currentBlendDirection = Vector3.forward;

	public void Dirty() => m_mesh = null;
	
	public void SetBlendLevel(float _level) => m_blendLevel = _level;
	public Vector3 CurrentBlendFocus => m_currentBlendFocus;
	public Vector3 CurrentBlendDirection => m_currentBlendDirection;

	public static bool IsUnlockSequenceInProgress = false;
	public void StartCameraSequence()
	{
		IsUnlockSequenceInProgress = true;
		CameraPanNode.StartCameraPanSequence(ID, _finishAction: () => {
			IsUnlockSequenceInProgress = false;
			const int texW = 512, texH = 160;
			var tex = DistrictManager.Me.m_data.CaptureDistrict(ID, texW, texH);
			var sprite = Sprite.Create(tex, new Rect(0, 0, texW, texH), Vector2.one * .5f);
			MARegionUnlockDialog.Create(ReactDistrictTable.GetEntryByID(ID), sprite);
		});
	}

	public Vector3 BestLabelPos
	{
		get
		{
			if (m_mesh == null) Mesh();
			return m_bestLabelPos;
		}
	}

	public bool IsAnimating => IsUnlocked && m_blendLevel < 1;

	public bool StepAnimation(float _dt)
	{
		if (IsAnimating == false) return false;
		m_blendLevel = Mathf.Clamp01(m_blendLevel + _dt);
		Dirty();
		return true;
	}

#if UNITY_EDITOR
	Dictionary<int, float> m_debug_vertexStages;

	public void DrawGizmos()
	{
		if (m_debug_vertexStages == null) return;
		var rawVerts = DistrictManager.Me.m_data.m_districtVertices;
		foreach (var kvp in m_debug_vertexStages)
		{
			var vert = rawVerts[kvp.Key];
			Handles.Label(vert.V3XZ().NewY(DistrictDrag.c_districtDragHeight), kvp.Value.ToString("F0"));
		}
	}
#endif

	public Mesh NoExtrudeMesh()
	{
		Mesh();
		return m_meshNoExtrude;
	}

	private Material m_unlockMaterial;
	public Material GetRenderMaterial(Vector3 _offset, float _scale)
	{
		if (IsUnlocked && m_blendLevel > 0 && m_blendLevel < 1)
		{
			if (m_unlockMaterial == null)
				m_unlockMaterial = new Material(DistrictManager.Me.RenderMaterial);
			const float c_margin = 4;
			var radius = Mathf.Max(.1f, m_blendLevel * (m_containingRadius + c_margin));
			var radiusInner = Mathf.Max(radius - c_margin, 0);
			var labelPosXformed = m_bestLabelPos * _scale + _offset;
			var v = new Vector4(labelPosXformed.x, radiusInner * radiusInner, labelPosXformed.z, 1.0f / (radius * radius - radiusInner * radiusInner));
			m_unlockMaterial.SetVector("_Blend", v);
			return m_unlockMaterial;
		}
		return DistrictManager.Me.RenderMaterial;
	}

	public static Vector3 ClampToSea(Vector3 _v)
	{
		_v.y = Mathf.Max(_v.y, GlobalData.c_seaLevel);
		return _v;
	}

	public Mesh Mesh()
	{
		if (m_mesh != null && m_meshWasUnlocked == IsUnlocked)
			return m_mesh;

		var rawDistricts = DistrictManager.Me.m_data.m_districtPolygons;
		var rawEdges = DistrictManager.Me.m_data.m_districtEdges;
		var rawVerts = DistrictManager.Me.m_data.m_districtVertices;

		float vertColour = IsUnlocked ? 1 : 0;
		var clrLocked = Color.red;
		var clrUnlocked = Color.yellow;
		var clrOuter = Color.red;

		Dictionary<int, float> vertexStages = null;
		float maxStage = 0;

		float VertexValue(int _vertex, float _blendAdd = 0)
		{
			if (vertexStages == null) return vertColour;
			if (vertexStages.TryGetValue(_vertex, out var stage))
			{
				float f = (stage / maxStage) * .9f + .1f;
				return Mathf.InverseLerp(f - .1f, f, m_blendLevel + _blendAdd);
			}
			return 0;
		}

		m_currentBlendFocus = Vector3.zero;
#if false
		if (IsUnlocked && m_blendLevel > 0 && m_blendLevel < 1)
		{
			vertexStages = new Dictionary<int, float>();
			int startEdge = -1;
			for (int i = 0; i < m_edges.Count; ++i)
			{
				var edge = rawEdges[m_edges[i]];
				var otherDistrictIndex = edge.m_district1 == m_index ? edge.m_district2 : edge.m_district1;
				if (otherDistrictIndex == -1) continue;
				if (rawDistricts[otherDistrictIndex].m_isUnlocked == false) continue;
				vertexStages[edge.m_vertex1] = 0;
				vertexStages[edge.m_vertex2] = 0;
				startEdge = i;
			}
			for (int i = 0; i < m_edges.Count; ++i)
			{
				void FillEdgeVertex(int _edgeIndex)
				{
					var edge = rawEdges[m_edges[_edgeIndex]];
					var vertex1Stage = vertexStages.TryGetValue(edge.m_vertex1, out var stage1) ? stage1 : 1e23f;
					var vertex2Stage = vertexStages.TryGetValue(edge.m_vertex2, out var stage2) ? stage2 : 1e23f;
					var vertex1 = rawVerts[edge.m_vertex1];
					var vertex2 = rawVerts[edge.m_vertex2];
					var edgeLength = (vertex2 - vertex1).magnitude;
					if (vertex1Stage + edgeLength < vertex2Stage) vertexStages[edge.m_vertex2] = vertex1Stage + edgeLength;
					else if (vertex2Stage + edgeLength < vertex1Stage) vertexStages[edge.m_vertex1] = vertex2Stage + edgeLength;
				}

				int edgeIndex1 = (startEdge + 1 + i) % m_edges.Count;
				int edgeIndex2 = (startEdge - 1 - i + m_edges.Count + m_edges.Count) % m_edges.Count;
				FillEdgeVertex(edgeIndex1);
				FillEdgeVertex(edgeIndex2);
			}
			
#if UNITY_EDITOR
			m_debug_vertexStages = vertexStages;
#endif
			foreach (var kvp in vertexStages)
				if (kvp.Value > maxStage)
					maxStage = kvp.Value;

			var average = Vector2.zero;
			var averageFwd = Vector2.zero;
			var averageCount = 0f;
			var averageFwdCount = 0f;
			for (int i = 0; i < m_edges.Count; ++i)
			{
				for (int j = 0; j < 2; ++j)
				{
					var edge = rawEdges[m_edges[i]];
					var v1 = VertexValue(edge.m_vertex1, j * .01f);
					var v2 = VertexValue(edge.m_vertex2, j * .01f);
					const float c_threshold = .999f;
					var lerp = -1f;
					if (v1 >= c_threshold && v2 < c_threshold) // edge between v1 and v2
						lerp = v2;
					else if (v2 >= c_threshold && v1 < c_threshold) // edge between v2 and v1
						lerp = 1 - v1;
					else
						continue;
					var lerpPoint = Vector2.Lerp(rawVerts[edge.m_vertex1], rawVerts[edge.m_vertex2], lerp);
					if (j == 0)
					{
						average += lerpPoint;
						averageCount += 1f;
					}
					else
					{
						averageFwd += lerpPoint;
						averageFwdCount += 1f;
					}
				}
			}
			if (averageCount > 0 && averageFwdCount > 0)
			{
				average /= averageCount;
				averageFwd /= averageFwdCount;
				m_currentBlendFocus = average.V3XZ();
				m_currentBlendDirection = (averageFwd - average).V3XZ().normalized;
			}
		}
#endif

		Color VertexColour(float _stage) => Color.Lerp(clrLocked, clrUnlocked, _stage);

		var tessVerts = new LibTessDotNet.ContourVertex[m_edges.Count];
		for (int i = 0; i < m_edges.Count; ++i)
		{
			var edge0 = rawEdges[m_edges[i]];
			var edge1 = rawEdges[m_edges[(i + 1) % m_edges.Count]];
			var toAdd = edge0.m_vertex1 == edge1.m_vertex1 || edge0.m_vertex1 == edge1.m_vertex2 ? edge0.m_vertex2 : edge0.m_vertex1;
			var addVert = rawVerts[toAdd];
			tessVerts[i].Position = new LibTessDotNet.Vec3 {X = addVert.x, Y = 0, Z = addVert.y};
			tessVerts[i].Data = VertexValue(toAdd);
		}

		var tess = new LibTessDotNet.Tess();
		tess.AddContour(tessVerts);
		tess.Tessellate(LibTessDotNet.WindingRule.NonZero, LibTessDotNet.ElementType.Polygons, 3);
		
		int addedEdges = m_edges.Count;
		
		var verts = new Vector3[tess.VertexCount + addedEdges * 4];
		var clrs = new Color[tess.VertexCount + addedEdges * 4];
		tessVerts = tess.Vertices;
		for (int i = 0; i < tess.VertexCount; ++i)
		{
			var vert = tessVerts[i].Position;
			var value = (float)tessVerts[i].Data;
			verts[i] = new Vector3(vert.X, 0f, vert.Z);
			clrs[i] = VertexColour(value);
		}

		m_triangles = new DistrictTriangle[tess.ElementCount];
		for (int i = 0, index = 0; i < tess.ElementCount; ++i)
		{
			var v0 = verts[tess.Elements[index++]];
			var v1 = verts[tess.Elements[index++]];
			var v2 = verts[tess.Elements[index++]];
			m_triangles[i] = new DistrictTriangle(v0, v1, v2);
		}
		
		var inds = new int[tess.Elements.Length + addedEdges * 6];
		for (int i = 0; i < tess.Elements.Length; ++i)
			inds[i] = tess.Elements[i];
		
		var noExtrudeMesh = new Mesh();
		for (int i = 0; i < verts.Length; ++i)
			verts[i] = ClampToSea(verts[i].GroundPosition(DistrictData.c_edgeRaise));
		noExtrudeMesh.SetVertices(verts);
		noExtrudeMesh.SetColors(clrs);
		noExtrudeMesh.SetIndices(inds, MeshTopology.Triangles, 0);
		noExtrudeMesh.UploadMeshData(false);

		const float c_extrude = 5;
		int firstOuter = tess.VertexCount;
		int firstOuterIndex = tess.Elements.Length;
		for (int i = 0; i < addedEdges; ++i)
		{
			var edgeThis = rawEdges[m_edges[i]];
			var edgePrev = rawEdges[m_edges[(i + m_edges.Count - 1) % m_edges.Count]];
			var edgeNext = rawEdges[m_edges[(i + 1) % m_edges.Count]];
			int vertPrev = edgePrev.Other(edgeThis);
			int vertFirst = edgePrev.Same(edgeThis);
			int vertSecond = edgeNext.Same(edgeThis);
			int vertNext = edgeNext.Other(edgeThis);
			var vPrev = rawVerts[vertPrev];
			var vFirst = rawVerts[vertFirst];
			var vSecond = rawVerts[vertSecond];
			var vNext = rawVerts[vertNext];
			var prevDir = (vFirst - vPrev).normalized;
			var thisDir = (vSecond - vFirst).normalized;
			var nextDir = (vNext - vSecond).normalized;
			var prevThisDir = (prevDir + thisDir).normalized;
			var thisNextDir = (thisDir + nextDir).normalized;
			var prevThisNrm = new Vector2(prevThisDir.y, -prevThisDir.x);
			var thisNextNrm = new Vector2(thisNextDir.y, -thisNextDir.x);
			var vFirstOuter = vFirst + prevThisNrm * c_extrude;
			var vSecondOuter = vSecond + thisNextNrm * c_extrude;
			var clr1 = VertexColour(VertexValue(vertFirst));
			var clr2 = VertexColour(VertexValue(vertSecond));
			verts[firstOuter + i * 4 + 0] = vFirst.V3XZ();
			verts[firstOuter + i * 4 + 1] = vSecond.V3XZ();
			verts[firstOuter + i * 4 + 2] = vSecondOuter.V3XZ();
			verts[firstOuter + i * 4 + 3] = vFirstOuter.V3XZ();
			clrs[firstOuter + i * 4 + 0] = clr1;
			clrs[firstOuter + i * 4 + 1] = clr2;
			clrs[firstOuter + i * 4 + 2] = clrOuter;
			clrs[firstOuter + i * 4 + 3] = clrOuter;
			
			inds[firstOuterIndex + i * 6 + 0] = firstOuter + i * 4 + 0;
			inds[firstOuterIndex + i * 6 + 1] = firstOuter + i * 4 + 1;
			inds[firstOuterIndex + i * 6 + 2] = firstOuter + i * 4 + 2;
			inds[firstOuterIndex + i * 6 + 3] = firstOuter + i * 4 + 0;
			inds[firstOuterIndex + i * 6 + 4] = firstOuter + i * 4 + 2;
			inds[firstOuterIndex + i * 6 + 5] = firstOuter + i * 4 + 3;
		}

		var mesh = new Mesh();
		mesh.SetVertices(verts);
		mesh.SetColors(clrs);
		mesh.SetIndices(inds, MeshTopology.Triangles, 0);
		mesh.UploadMeshData(false);

		Vector2 minPos = Vector2.one * 1e23f, maxPos = Vector2.one * -1e23f, avgPos = Vector2.zero;
		for (int i = 0; i < m_edges.Count; ++i)
		{
			var edge = rawEdges[m_edges[i]];
			var v1 = rawVerts[edge.m_vertex1];
			var v2 = rawVerts[edge.m_vertex2];
			avgPos += v1 + v2;
			minPos = Vector2.Min(minPos, v1);
			maxPos = Vector2.Max(maxPos, v1);
			minPos = Vector2.Min(minPos, v2);
			maxPos = Vector2.Max(maxPos, v2);
		}
		var centerPos = (minPos + maxPos) * .5f;
		avgPos /= m_edges.Count * 2;
		float totalArea = 0;
		float bestArea = 0;
		Vector3 bestCenter = Vector3.zero;
		for (int i = 0; i < tess.Elements.Length; i += 3)
		{
			var v0 = verts[tess.Elements[i + 0]];
			var v1 = verts[tess.Elements[i + 1]];
			var v2 = verts[tess.Elements[i + 2]];
			var area = 0.5f * Mathf.Abs(v0.x * (v1.z - v2.z) + v1.x * (v2.z - v0.z) + v2.x * (v0.z - v1.z));
			totalArea += area;
			if (area > bestArea)
			{
				bestCenter = (v0 + v1 + v2) / 3f;
				bestArea = area;
			}
		}
		if (PointInside(centerPos.V3XZ()))
			m_bestLabelPos = centerPos.V3XZ();
		else if (PointInside(avgPos.V3XZ()))
			m_bestLabelPos = avgPos;
		else
			m_bestLabelPos = bestCenter;
		m_size = totalArea;
		m_width = maxPos.x - minPos.x;
		m_height = maxPos.y - minPos.y;
		
		float radiusSqrd = 0;
		for (int i = 0; i < m_edges.Count; ++i)
		{
			var edge = rawEdges[m_edges[i]];
			var v1 = rawVerts[edge.m_vertex1];
			var v2 = rawVerts[edge.m_vertex2];
			var offset = v1 - m_bestLabelPos.GetXZVector2();
			radiusSqrd = Mathf.Max(radiusSqrd, offset.sqrMagnitude);
			offset = v2 - m_bestLabelPos.GetXZVector2();
			radiusSqrd = Mathf.Max(radiusSqrd, offset.sqrMagnitude);
		}
		m_containingRadius = Mathf.Sqrt(radiusSqrd);
		
		
		m_meshNoExtrude = noExtrudeMesh;
		m_mesh = mesh;
		m_meshWasUnlocked = IsUnlocked;
		
		return mesh;
	}
}

public class DistrictDrag : DragBase
{
	public int m_index;
	
	public const float c_districtDragHeight = 150;

	override public Vector3 DragPlaneNormal => Vector3.up;
	override public Vector3 DragPlaneOrigin => Vector3.up * c_districtDragHeight;

	private Vector3 m_dragOffset;

	public override void OnDragStart()
	{
		m_dragOffset = RaycastMouse - transform.position;
	}
	
	public override void OnDragUpdate(Vector3 _dragPoint, Vector3 _totalScreenDrag)
	{
		transform.position = _dragPoint - m_dragOffset;
		DragUpdate();
	}
	
	public override void OnDragEnd(bool _undo)
	{
		if (_undo)
			DistrictManager.Me.m_data.Load();
		else
		{
			DragEnd();
			DistrictManager.Me.m_data.Save();
		}
	}

	public virtual void DragUpdate() => DistrictManager.Me.m_data.DragVertex(m_index, transform.position);
	public virtual void DragEnd() => DistrictManager.Me.m_data.EndDragVertex(m_index);
}

public class DistrictAudioDrag : DistrictDrag
{
	public override void DragUpdate() => DistrictManager.Me.m_data.DragAudioOverride(m_index, transform.position);
	public override void DragEnd() => DistrictManager.Me.m_data.EndDragAudioOverride(m_index);
}

[Serializable]
public class DistrictAudioOverrideSphere
{
	public Vector3 m_pos;
	public float m_radius;
	public string m_audioID;
	public bool m_overridesDistricts = true;

	public bool IntersectsRay(Ray _ray)
	{
		var toCenter = m_pos - _ray.origin;
		var proj = Vector3.Dot(toCenter, _ray.direction);
		if (proj < 0) return false;
		var distSqrd = toCenter.sqrMagnitude;
		var projDistSqrd = distSqrd - proj * proj;
		return projDistSqrd < m_radius * m_radius;
	}

	public string Apply(string _original)
	{
		if (string.IsNullOrEmpty(m_audioID) == false)
			if (m_overridesDistricts || string.IsNullOrEmpty(_original))
				return m_audioID;
		return _original;
	}
}

[Serializable]
public class DistrictData
{
	public List<DistrictPolygon> m_districtPolygons = new();
	public List<DistrictEdge> m_districtEdges = new();
	public List<Vector2> m_districtVertices = new();
	public List<DistrictAudioOverrideSphere> m_districtAudioOverrideSpheres = new();

	private GameObject m_districtEditVisuals = null;
	public void ClearEditVisuals()
	{
		if (m_districtEditVisuals == null) return;
		Object.Destroy(m_districtEditVisuals);
		m_districtEditVisuals = null;
	}

	public void GenerateEditVisuals()
	{
		ClearEditVisuals();
		m_districtEditVisuals = new GameObject("DistrictEdit");
		for (int i = 0; i < m_districtVertices.Count; ++i)
		{
			var v = m_districtVertices[i];
			var go = GameObject.CreatePrimitive(PrimitiveType.Sphere);
			go.name = $"Vertex {i}";
			go.transform.SetParent(m_districtEditVisuals.transform);
			go.transform.position = new Vector3(v.x, DistrictDrag.c_districtDragHeight, v.y);
			go.transform.localScale = Vector3.one * (c_baseVertexHandleSize * m_handleSize);
			go.transform.parent = m_districtEditVisuals.transform;
			SetColour(go, Color.blue, Color.blue * .5f);
			var drag = go.AddComponent<DistrictDrag>();
			drag.m_index = i;
		}
		for (int i = 0; i < m_districtEdges.Count; ++i)
		{
			var owner = new GameObject($"Edge {i}");
			owner.transform.SetParent(m_districtEditVisuals.transform);
			var go = GameObject.CreatePrimitive(PrimitiveType.Cube);
			go.name = "Edge";
			go.transform.SetParent(owner.transform);
			var goTop = GameObject.CreatePrimitive(PrimitiveType.Cube);
			goTop.transform.SetParent(owner.transform);
			goTop.name = "Top";
			RefreshEdgeVisuals(i);
		}
		for (int i = 0; i < m_districtPolygons.Count; ++i)
		{
			var district = m_districtPolygons[i];
			var label = $"[{district.Index + 1}] {district.m_name}\n{district.Name}\n{district.AudioID}";
			var go = new GameObject(label);
			go.transform.SetParent(m_districtEditVisuals.transform);
			CreateLabel(go.transform, label);
			RefreshDistrictVisuals(i);
		}
		GenerateEditAudioOverrideVisuals();
		RefreshEditAudioOverrideVisuals();

		Render();
		m_inhibitVisuals = false;
	}

	private (GameObject, GameObject) CreateLabel(Transform _parent, string _label, float _fontSize = 9)
	{
		var box = GameObject.CreatePrimitive(PrimitiveType.Cube);
		box.name = "Box";
		box.transform.SetParent(_parent);
		box.GetComponent<Collider>().enabled = false;
		SetColour(box, Color.black, Color.black);

		var canvasObj = new GameObject("Canvas");
		canvasObj.transform.SetParent(_parent);
		canvasObj.transform.localPosition = Vector3.up * .1f;
		canvasObj.transform.localEulerAngles = Vector3.right * 90;
		var canvas = canvasObj.AddComponent<Canvas>();
		canvas.renderMode = RenderMode.WorldSpace;
		var tmp = canvasObj.AddComponent<TMPro.TextMeshPro>();
		tmp.text = _label;
		tmp.fontSize = _fontSize;
		tmp.fontWeight = TMPro.FontWeight.Bold;
		tmp.alignment = TMPro.TextAlignmentOptions.Center;
		tmp.textWrappingMode = TMPro.TextWrappingModes.NoWrap;
		tmp.raycastTarget = false;
		
		return (box, canvasObj);
	}
	
	private GameObject m_districtMapVisuals;
	private void ClearMapVisuals()
	{
		if (m_districtMapVisuals == null) return;
		m_mapCanvasGroups.Clear();
		m_mapIcons.Clear();
		Object.Destroy(m_districtMapVisuals);
		m_districtMapVisuals = null;
	}

	private bool m_districtMapShowInProgress;
	public void SetDistrictMapIntensity(float _intensity, bool _showingMap)
	{
		m_districtMapShowInProgress = _showingMap;
		if (m_districtEditVisuals != null) _intensity = 0;
		if (_intensity < .001f)
		{
			if (m_districtMapVisuals != null && m_mapIcons.Count == 0)
				ClearMapVisuals();
		}
		else
		{
			if (m_districtMapVisuals == null)
				GenerateMapVisuals();
			UpdateMapVisualAlpha(_intensity);
		}
	}
	
	private static DebugConsole.Command s_captureDistrictCmd = new ("capturedistrict", _s => {
		var tex = DistrictManager.Me.m_data.CaptureDistrict(_s, 512, 256);
		var png = tex.EncodeToPNG();
		System.IO.File.WriteAllBytes($"DistrictCapture_{_s}.png", png);
	}, "Capture a district and return an image of it", "<name or ID>");

	private float m_captureZ = -.4f, m_captureDist = 1;
	public Texture2D CaptureDistrict(string _nameOrID, int _width, int _height)
	{
		var id = SanitiseID(_nameOrID);
		var poly = GetPolygon(id);
		if (poly == null)
		{
			Debug.LogError($"DistrictData.CaptureDistrict: No district found with name or ID '{_nameOrID}'");
			return null;
		}
		
		poly.IsCapture = true;
		var wasInMap = m_districtMapVisuals != null;
		var wasMapAlpha = m_mapAlpha;
		if (wasInMap) ClearMapVisuals();
		GenerateMapVisuals(true);
		UpdateMapVisualAlpha(1);
		TerrainPopulation.Me.ShowMapVisuals(1);
		Marcos_Procedural_Sky.Me.UpdateSky(true);
		UpdateMapIcons();
		var captureCenter = poly.BestLabelPos;
		var captureFwd = new Vector3(0, -1, m_captureZ).normalized;
		var capturePos = captureCenter + captureFwd * (-m_captureDist * Mathf.Max(poly.Width, poly.Height, 250));
		var captureSide = Vector3.Cross(captureFwd, Vector3.up).normalized;
		var captureUp = Vector3.Cross(captureSide, captureFwd).normalized;
		foreach (var afc in m_districtMapVisuals.GetComponentsInChildren<AlwaysFaceCamera>())
			afc.FaceCamera(capturePos, captureFwd, captureUp);
		// Capture
		var tex = CaptureObjectImage.Me.CaptureGeneric(capturePos, captureFwd, -1, _width, _height);
		// Clean up
		ClearMapVisuals();
		poly.IsCapture = false;
		if (wasInMap) GenerateMapVisuals();
		m_mapAlpha = wasMapAlpha;
		TerrainPopulation.Me.ShowMapVisuals(m_mapAlpha > .5f ? 1 : 0);
		Marcos_Procedural_Sky.Me.UpdateSky(false);
		return tex;
	}

	public const float c_edgeRaise = 0;
	public const float c_labelRaise = 0;
	public const float c_lineWidth = 2;

	private static bool s_showDistrictDebug = false;
	private static DebugConsole.Command s_showDistrictDebugCmd = new ("showdistrictdebug", _s => {
		Utility.SetOrToggle(ref s_showDistrictDebug, _s);
		DistrictManager.Me.m_data.ClearMapVisuals();
	}, "Show district debug visuals", "<bool>");
	
	private float m_mapAlpha = 0;
	private Material m_mapEdgeMaterial;
	private Material m_mapTopLockedMaterial, m_mapTopUnlockedMaterial, m_mapTopHighlightMaterial;
	private List<CanvasGroup> m_mapCanvasGroups = new();
	private void GenerateMapVisuals(bool _isDistrictCapture = false)
	{
		if (m_mapEdgeMaterial == null) m_mapEdgeMaterial = new Material(DistrictManager.Me.m_mapEdgeMaterial);
		if (m_mapTopLockedMaterial == null) m_mapTopLockedMaterial = new Material(DistrictManager.Me.m_mapFillLockedMaterial);
		if (m_mapTopUnlockedMaterial == null) m_mapTopUnlockedMaterial = new Material(DistrictManager.Me.m_mapFillUnlockedMaterial);
		if (m_mapTopHighlightMaterial == null) m_mapTopHighlightMaterial = new Material(DistrictManager.Me.m_mapTopHighlightMaterial);
		
		var raise = Vector3.up * c_edgeRaise;
		m_districtMapVisuals = new GameObject("DistrictMap");
		for (int i = 0; i < m_districtPolygons.Count; ++i)
		{
			var poly = m_districtPolygons[i];
			if (string.IsNullOrEmpty(poly.Name)) continue;

			var ownerTop = new GameObject($"PolyTop {i}");
			ownerTop.transform.SetParent(m_districtMapVisuals.transform);
			var mrTop = ownerTop.AddComponent<MeshRenderer>();
			var mfTop = ownerTop.AddComponent<MeshFilter>();
			if (_isDistrictCapture)
				mrTop.sharedMaterial = poly.IsCapture ? m_mapTopUnlockedMaterial : m_mapTopLockedMaterial;
			else
				mrTop.sharedMaterial = poly.IsUnlocked ? m_mapTopUnlockedMaterial : m_mapTopLockedMaterial;
			mrTop.shadowCastingMode = UnityEngine.Rendering.ShadowCastingMode.Off;
			mfTop.sharedMesh = poly.NoExtrudeMesh();
			
			var owner = new GameObject($"Poly {i}");
			owner.transform.SetParent(m_districtMapVisuals.transform);

#if true
			var lineRenderer = owner.AddComponent<LineRenderer>();

			var positions = new List<Vector3>();

			void CompleteLine()
			{
				if (positions.Count >= 2)
				{
					if (lineRenderer == null)
					{
						var newLine = new GameObject("ExtraLines");
						newLine.transform.SetParent(owner.transform);
						lineRenderer = newLine.AddComponent<LineRenderer>();
					}
					lineRenderer.positionCount = positions.Count;
					lineRenderer.SetPositions(positions.ToArray());
					lineRenderer.sharedMaterial = m_mapEdgeMaterial;
					lineRenderer.startWidth = lineRenderer.endWidth = c_lineWidth;
					lineRenderer.numCornerVertices = 4;
					lineRenderer.textureMode = LineTextureMode.Tile;
					lineRenderer.textureScale = new Vector2(0.1f, 1f);
					lineRenderer.shadowCastingMode = UnityEngine.Rendering.ShadowCastingMode.Off;
					lineRenderer = null;
				}
				positions.Clear();
			}

			Vector3 ClampVert(int _vv)
			{
				var v = m_districtVertices[_vv].GetVector3XZ().GroundPosition(c_edgeRaise);
				v.y = Mathf.Max(v.y, GlobalData.c_seaLevel);
				return v;
			}

			for (int j = 0; j < poly.m_edges.Count; ++j)
			{
				var e = m_districtEdges[poly.m_edges[(j % poly.m_edges.Count)]];
				var ePrev = m_districtEdges[poly.m_edges[(j + poly.m_edges.Count - 1) % poly.m_edges.Count]];
				var eNext = m_districtEdges[poly.m_edges[(j + 1) % poly.m_edges.Count]];
				var vv1 = e.m_vertex1;
				var vv2 = e.m_vertex2;
				if (ePrev.m_vertex1 == vv2 || ePrev.m_vertex2 == vv2) (vv1, vv2) = (vv2, vv1);
				if (e.m_district1 != -1 && e.m_district2 != -1 && i >= e.m_district1 && i >= e.m_district2)
					CompleteLine(); // only draw a shared edge once, pick the lowest district ID
				else if (j == 0)
					positions.Add(ClampVert(vv1));
				positions.Add(ClampVert(vv2));
			}
			CompleteLine();
#else
			var mesh = new Mesh();
			var verts = new List<Vector3>();
			var nrms = new List<Vector3>();
			var uvs = new List<Vector2>();
			var inds = new List<int>();

			float uvi = 0.0f, uvo = 0.0f;
			for (int j = 0; j < poly.m_edges.Count; ++j)
			{
				var e = m_districtEdges[poly.m_edges[j]];
				var ePrev = m_districtEdges[poly.m_edges[(j + poly.m_edges.Count - 1) % poly.m_edges.Count]];
				var eNext =	m_districtEdges[poly.m_edges[(j + 1) % poly.m_edges.Count]];
				var vv1 = e.m_vertex1;
				var vv2 = e.m_vertex2;
				if (ePrev.m_vertex1 == vv2 || ePrev.m_vertex2 == vv2) (vv1, vv2) = (vv2, vv1);
				var vv0 = ePrev.m_vertex1 == vv1 ? ePrev.m_vertex2 : ePrev.m_vertex1;
				var vv3 = eNext.m_vertex1 == vv2 ? eNext.m_vertex2 : eNext.m_vertex1;
				var v0 = m_districtVertices[vv0].GetVector3XZ().GroundPosition(c_edgeRaise);
				var v1 = m_districtVertices[vv1].GetVector3XZ().GroundPosition(c_edgeRaise);
				var v2 = m_districtVertices[vv2].GetVector3XZ().GroundPosition(c_edgeRaise);
				var v3 = m_districtVertices[vv3].GetVector3XZ().GroundPosition(c_edgeRaise);
				
				var fwd12 = (v2 - v1).normalized;
				var fwd01 = (v1 - v0).normalized;
				var fwd23 = (v3 - v2).normalized;
				var fwd02 = (fwd01 + fwd12).normalized;
				var fwd13 = (fwd12 + fwd23).normalized;
				var side1 = new Vector3(fwd02.z, fwd02.y, -fwd02.x);
				var side2 = new Vector3(fwd13.z, fwd13.y, -fwd13.x);
				var miteredLineWidth1 = c_lineWidth * Mathf.Sqrt(2.0f / (1 + Vector3.Dot(fwd01, fwd12)));
				var miteredLineWidth2 = c_lineWidth * Mathf.Sqrt(2.0f / (1 + Vector3.Dot(fwd12, fwd23)));
				var v1i = v1 - side1 * miteredLineWidth1;
				var v1o = v1 + side1 * miteredLineWidth1;
				var v2i = v2 - side2 * miteredLineWidth2;
				var v2o = v2 + side2 * miteredLineWidth2;
				
				const float c_lineUVScale = 100;
				var uvi2 = uvi + (v2i - v1i).magnitude / c_lineUVScale;
				var uvo2 = uvo + (v2o - v1o).magnitude / c_lineUVScale;
				int firstVert = verts.Count;
				var nrm = Vector3.up;
				verts.Add(v1i); verts.Add(v2i); verts.Add(v2o); verts.Add(v1o);
				uvs.Add(new Vector2(0, uvi)); uvs.Add(new Vector2(0, uvi2)); uvs.Add(new Vector2(1, uvo2)); uvs.Add(new Vector2(1, uvo));
				nrms.Add(nrm); nrms.Add(nrm); nrms.Add(nrm); nrms.Add(nrm);
				inds.Add(firstVert + 0); inds.Add(firstVert + 1); inds.Add(firstVert + 2);  inds.Add(firstVert + 0); inds.Add(firstVert + 2); inds.Add(firstVert + 3);
				uvi = uvi2; uvo = uvo2;
			}
			mesh.SetVertices(verts);
			mesh.SetNormals(nrms);
			mesh.SetUVs(0, uvs);
			mesh.SetIndices(inds, MeshTopology.Triangles, 0);
			mesh.UploadMeshData(false);
			
			var mr = owner.AddComponent<MeshRenderer>();
			var mf = owner.AddComponent<MeshFilter>();
			mr.sharedMaterial = m_mapEdgeMaterial;
			mr.shadowCastingMode = UnityEngine.Rendering.ShadowCastingMode.Off;
			mf.sharedMesh = mesh;
#endif

			var canvasObj = new GameObject($"Canvas {i}");
			canvasObj.transform.SetParent(m_districtMapVisuals.transform);
			canvasObj.transform.position = poly.BestLabelPosition.GroundPosition(c_labelRaise);
			canvasObj.transform.localEulerAngles = Vector3.right * 90;
			var canvas = canvasObj.AddComponent<Canvas>();
			canvas.renderMode = RenderMode.WorldSpace;
			canvas.sortingOrder = 2;
			var canvasGroup = canvasObj.AddComponent<CanvasGroup>();
			canvasGroup.alpha = 0;
			m_mapCanvasGroups.Add(canvasGroup);
			var label = poly.Name;
			if (s_showDistrictDebug) label = $"{label} [{poly.ID.Replace("District", "D")}]";
			if (_isDistrictCapture == false || poly.IsCapture)
			{
				var text = AddOutlinedText(label, 20, canvasObj.transform);
				if (_isDistrictCapture)
				{
					var scale = Mathf.Max(250, poly.Width, poly.Height) / 500;
					text.transform.localScale = Vector3.one * scale;
				}
			}
		}
		m_mapAlpha = 0;
	}

	private GameObject AddOutlinedText(string _label, float _fontSize, Transform _parent)
	{
		var holder = new GameObject("TextHolder");
		holder.transform.SetParent(_parent);
		var outlineColour = new Color(1, 1, 1, .7f);
		for (int s = 0; s < 5; ++s)
		{
			var label = new GameObject(_label);
			label.transform.SetParent(holder.transform);
			float sx = (s & 1) * 2 - 1;
			float sy = ((s >> 1) & 1) * 2 - 1;
			if (s == 4) sx = sy = 0;
			label.transform.localRotation = Quaternion.identity;
			label.transform.localPosition = new Vector3(sx, sy, 0);
			var tmp = label.AddComponent<TMPro.TextMeshProUGUI>();
			tmp.text = _label;
			tmp.font = GlobalData.Me.m_mainFont;
			tmp.fontSize = _fontSize;
			tmp.fontStyle = TMPro.FontStyles.Bold;
			tmp.fontWeight = TMPro.FontWeight.Bold;
			tmp.characterSpacing = -8;
			tmp.alignment = TMPro.TextAlignmentOptions.Center;
			tmp.textWrappingMode = TMPro.TextWrappingModes.NoWrap;
			tmp.raycastTarget = false;
			tmp.color = s < 4 ? outlineColour : Color.black;
		}
		var afc = holder.AddComponent<AlwaysFaceCamera>();
		afc.m_pushForward = 100;
		afc.m_lockTo2D = true;
		afc.m_freezeXZ = false;
		return holder;
	}

	private void UpdateMapVisualAlpha(float _alpha)
	{
		if (m_mapAlpha.Nearly(_alpha, .001f)) return;
		m_mapAlpha = _alpha;
		m_mapEdgeMaterial.SetFloat("_AlphaMultiply", _alpha);
		const float c_topAlphaLocked = .4f;
		const float c_topAlphaUnlocked = .2f;
		m_mapTopUnlockedMaterial.SetFloat("_AlphaMultiply", _alpha * c_topAlphaUnlocked);
		m_mapTopLockedMaterial.SetFloat("_AlphaMultiply", _alpha * c_topAlphaLocked);
		foreach (var cg in m_mapCanvasGroups)
			cg.alpha = _alpha;
		foreach (var kvp in m_mapIcons)
		{
			var (canvas, xform, raise) = kvp.Value;
			var cg = canvas.GetComponent<CanvasGroup>();
			cg.alpha = _alpha;
		}
	}

	private Dictionary<MonoBehaviour, (Canvas, Transform, float)> m_mapIcons = new();
	private HashSet<MonoBehaviour> m_mapIconsToRemove = new();

	private void AddMapIconsForComponent<T>(string _icon3D) where T : BCBase
	{
		foreach (var building in NGManager.Me.m_maBuildings)
		{
			if (building.HasBuildingComponent<T>() == false) continue;
			UpdateMapIcon(building, building.Name, "", _icon3D);
		}
	}

	private void UpdateMapIcons()
	{
		m_mapIconsToRemove.Clear();
		foreach (var kvp in m_mapIcons)
			m_mapIconsToRemove.Add(kvp.Key);
		var crypt = MASpecialMABuilding.Crypt();
		UpdateMapIcon(crypt, "Crypt", "_Art/Sprites/Decorations/Decoration_Deco_Outhouse", "Crypt");
		foreach (var hero in NGManager.Me.m_MAHeroList)
			UpdateMapIcon(hero, "Hero", "_Art/Sprites/BusinessRewards/GiantHero", "Hero1");
		if (MapUIController.s_quests)
			foreach (var quest in MAQuestManager.Me.m_activeQuests)
			{
				if (quest.ShouldShowMapIcon())
				{
					UpdateMapIcon(quest, quest.m_challengeText, "", "Quest");
				}
			}
		if (MapUIController.s_factories)
			AddMapIconsForComponent<BCFactory>("Factory");
		if (MapUIController.s_defences)
			AddMapIconsForComponent<BCActionTurret>("Turret");
		if (MapUIController.s_dispatches)
			AddMapIconsForComponent<BCActionDispatch>("Dispatch");
		if (MapUIController.s_taverns)
			AddMapIconsForComponent<BCActionTavern>("Tavern");
		if (MapUIController.s_resources)
			AddMapIconsForComponent<BCActionGatherer>("Gatherer");
		if (MapUIController.s_caves)
			foreach (var gss in GenericSubSceneEnterInteraction.s_all)
				UpdateMapIcon(gss, "Cave"/*gss.name*/, "", "Cave");
		foreach (var item in m_mapIconsToRemove)
		{
			if (m_mapIcons.TryGetValue(item, out var canvasAndTransform) == false) continue;
			var raise = canvasAndTransform.Item3;
			const float c_icon3DRaiseDisappear = 100;
			if (raise < c_icon3DRaiseDisappear)
			{
				raise += c_icon3DDropSpeed * Time.deltaTime;
				canvasAndTransform.Item3 = raise;
				canvasAndTransform.Item2.position = canvasAndTransform.Item2.position.GroundPosition(c_labelRaise + raise);
				var raiseFraction = raise / c_icon3DRaiseDisappear;
				var raiseScale = Mathf.Clamp01(3 - (raiseFraction * 3));
				canvasAndTransform.Item2.localScale = Vector3.one * (raiseScale * c_icon3DScale);
				m_mapIcons[item] = canvasAndTransform;
			}
			else
			{
				m_mapIcons.Remove(item);
				Object.Destroy(canvasAndTransform.Item1.gameObject);
			}
		}
	}

	public void DoDoubleClick()
	{
		float bestDSqrd = 40 * 40;
		Transform bestHit = null;
		if (GameManager.Me.RaycastAtPoint(Utility.mousePosition, out var hit, GameManager.c_layerTerrainBit))
		{
			foreach (var (_mb, (_canvas, _transform, height)) in m_mapIcons)
			{
				var dSqrd = (_transform.position - hit.point).xzSqrMagnitude();
				if (dSqrd < bestDSqrd)
				{
					bestDSqrd = dSqrd;
					bestHit = _mb.transform;
				}
			}
		}
		GameManager.Me.LeaveMap(bestHit);
	}

	private Dictionary<string, GameObject> m_icon3DCache = null;
	private GameObject Get3DIcon(string _icon)
	{
		if (m_icon3DCache == null)
		{
			m_icon3DCache = new Dictionary<string, GameObject>();
			foreach (var icon in DistrictManager.Me.m_mapIconPrefabs)
			{
				var name = icon.name;
				var lastUnderscore = name.LastIndexOf('_');
				name = name.Substring(lastUnderscore + 1);
				m_icon3DCache[name] = icon;
			}
		}
		if (m_icon3DCache.TryGetValue(_icon, out var prefab))
			return prefab;
		return m_icon3DCache["Crypt"];
	}
	const float c_icon3DScale = 400;
	const float c_icon3DStartHeightMin = 100, c_icon3DStartHeightMax = 150;
	const float c_icon3DDropSpeed = 200;
	private void UpdateMapIcon(MonoBehaviour _item, string _label, string _icon, string _icon3D)
	{
		if (_item == null) return;
		if (m_districtMapShowInProgress == false) return;
		m_mapIconsToRemove.Remove(_item);
		if (m_mapIcons.TryGetValue(_item, out var canvasAndTransform) == false)
		{
			var go = new GameObject(_item.name);
			go.transform.SetParent(m_districtMapVisuals.transform);
			var canvas = go.AddComponent<Canvas>();
			canvas.renderMode = RenderMode.WorldSpace;
			canvas.sortingOrder = 1;
			go.AddComponent<CanvasGroup>();
			/*var img = new GameObject("Image").AddComponent<UnityEngine.UI.Image>();
			img.transform.SetParent(go.transform);
			img.rectTransform.sizeDelta = new Vector2(20, 20);
			img.rectTransform.anchoredPosition = Vector2.zero;
			img.sprite = Resources.Load<Sprite>(_icon);*/
			var label = new GameObject(_label);
			AddOutlinedText(_label, 14, label.transform);
			var icon3DPrefab = Get3DIcon(_icon3D);
			var icon3D = Object.Instantiate(icon3DPrefab, go.transform);
			icon3D.transform.rotation = Quaternion.identity;
			label.transform.SetParent(icon3D.transform);
			label.transform.localPosition = Vector3.up * (-15 / c_icon3DScale);
			label.transform.localScale = Vector3.one * (1 / c_icon3DScale);
			canvasAndTransform = (canvas, icon3D.transform, UnityEngine.Random.Range(c_icon3DStartHeightMin, c_icon3DStartHeightMax));
			m_mapIcons[_item] = canvasAndTransform;
		}
		var raise = canvasAndTransform.Item3;
		raise = Mathf.Max(0, raise - c_icon3DDropSpeed * Time.deltaTime);
		canvasAndTransform.Item3 = raise;
		canvasAndTransform.Item2.position = _item.transform.position.GroundPosition(c_labelRaise + raise);
		canvasAndTransform.Item2.localScale = Vector3.one * c_icon3DScale;
		canvasAndTransform.Item2.rotation = _item.transform.rotation;
		m_mapIcons[_item] = canvasAndTransform;
	}

	private void UpdateMap()
	{
		if (m_districtMapVisuals == null) return; 
		UpdateMapIcons();
	}

	const float c_baseVertexHandleSize = 1.5f;
	const float c_baseEdgeHandleSize = .5f;
	float m_handleSize = 1;
	private void SetHandleSize(float _size)
	{
		m_handleSize = _size;
		if (m_districtEditVisuals == null) return;
		float vertSize = c_baseVertexHandleSize * _size;
		float edgeSize = c_baseEdgeHandleSize * _size;
		for (int i = 0; i < m_districtVertices.Count; ++i)
		{
			var tr = m_districtEditVisuals.transform.GetChild(i);
			tr.localScale = Vector3.one * vertSize;
		}

		for (int i = 0; i < m_districtEdges.Count; ++i)
		{
			var tr = m_districtEditVisuals.transform.GetChild(m_districtVertices.Count + i);
			var top = tr.GetChild(1);
			top.localScale = new Vector3(edgeSize, edgeSize, top.localScale.z);
		}

		for (int i = 0; i < m_districtPolygons.Count; ++i)
		{
			var tr = m_districtEditVisuals.transform.GetChild(m_districtVertices.Count + m_districtEdges.Count + i);
			var trBox = tr.GetChild(0);
			var trCanvas = tr.GetChild(1);
			trBox.transform.localScale = new Vector3(10, .1f, 4) * _size;
			trCanvas.transform.localScale = Vector3.one * _size;
			trCanvas.transform.localPosition = Vector3.up * (.1f * _size);
		}
	}
	
	private void RefreshEditVisuals()
	{
		for (int i = 0; i < m_districtVertices.Count; ++i)
			RefreshVertexVisuals(i);
		for (int i = 0; i < m_districtEdges.Count; ++i)
			RefreshEdgeVisuals(i);
		for (int i = 0; i < m_districtPolygons.Count; ++i)
			RefreshDistrictVisuals(i);
		RefreshEditAudioOverrideVisuals();
	}

	private void RefreshEditAudioOverrideVisuals()
	{
		for (int i = 0; i < m_districtAudioOverrideSpheres.Count; ++i)
		{
			var audioSphere = m_districtAudioOverrideSpheres[i];
			var sphere = m_districtEditVisuals.transform.GetChild(m_districtVertices.Count + m_districtEdges.Count + m_districtPolygons.Count + i);
			var sphereObj = sphere.GetChild(0);
			var textObj= sphere.GetChild(1);
			sphere.transform.position = audioSphere.m_pos.NewY(DistrictDrag.c_districtDragHeight);
			sphereObj.transform.localScale = new Vector3(audioSphere.m_radius, 1, audioSphere.m_radius);
			bool selected = m_selectedAudioSphere == audioSphere;
			var clr = selected ? Color.yellow : Color.grey;
			SetColour(sphereObj.gameObject, clr, clr * .5f);
			sphere.gameObject.SetActive(m_hideAudioOverrides == false);
		}
	}
	
	GameObject m_cylinderPrefab;
	GameObject CreateCylinderPrimitive()
	{
		if (m_cylinderPrefab == null)
		{
			var verts = new List<Vector3>();
			var nrms = new List<Vector3>();
			var cllVerts = new List<Vector3>();
			var cllNrms = new List<Vector3>();
			var inds = new List<int>();
			var cllInds = new List<int>();
			var layerYs = new float[] { 0, 0, -500, -500 };
			var layerNrms = new Vector3[] { Vector3.up, Vector3.zero, Vector3.zero, Vector3.down };
			const int c_radials = 32;
			for (int layer = 0; layer < 4; ++layer)
			{
				var y = layerYs[layer];
				var layerNrm = layerNrms[layer];
				for (int i = 0; i < c_radials; ++i)
				{
					float theta = 360f * i / c_radials;
					float sin = Mathf.Sin(theta * Mathf.Deg2Rad), cos = Mathf.Cos(theta * Mathf.Deg2Rad);
					var pos = new Vector3(sin * .5f, y, cos * .5f);
					var nrm = layerNrm;
					if (i == 1 || i == 2) nrm = new Vector3(sin, 0, cos);
					verts.Add(pos);
					nrms.Add(nrm);
					if (layer == 0)
					{
						cllVerts.Add(pos);
						cllNrms.Add(nrm);
					}
				}
			}
			for (int i = 1; i < c_radials - 1; ++i)
			{
				int i0 = 0, i1 = i, i2 = i + 1;
				inds.Add(i0); inds.Add(i1); inds.Add(i2);
				cllInds.Add(i0); cllInds.Add(i1); cllInds.Add(i2);
				inds.Add(3 * c_radials + i0); inds.Add(3 * c_radials + i2); inds.Add(3 * c_radials + i1);
			}
			for (int i = 0; i < c_radials; ++i)
			{
				int ip1 = (i + 1) % c_radials;
				int i0 = i + c_radials * 1, i1 = ip1 + c_radials * 1, i2 = i + c_radials * 2, i3 = ip1 + c_radials * 2;
				inds.Add(i0); inds.Add(i3); inds.Add(i1);
				inds.Add(i0); inds.Add(i2); inds.Add(i3);
			}

			var mesh = new Mesh();
			mesh.SetVertices(verts);
			mesh.SetNormals(nrms);
			mesh.SetIndices(inds, MeshTopology.Triangles, 0);
			mesh.UploadMeshData(false);
			
			var cllMesh = new Mesh();
			cllMesh.SetVertices(cllVerts);
			cllMesh.SetNormals(cllNrms);
			cllMesh.SetIndices(cllInds, MeshTopology.Triangles, 0);
			cllMesh.UploadMeshData(false);

			m_cylinderPrefab = new GameObject("Cylinder");
			m_cylinderPrefab.transform.SetParent(GlobalData.Me.transform);
			m_cylinderPrefab.SetActive(false);
			var mf = m_cylinderPrefab.AddComponent<MeshFilter>();
			var mr = m_cylinderPrefab.AddComponent<MeshRenderer>();
			mf.sharedMesh = mesh;
			var mc = m_cylinderPrefab.AddComponent<MeshCollider>();
			mc.sharedMesh = mesh;
			mc.convex = true;
		}
		var inst = GameObject.Instantiate(m_cylinderPrefab);
		inst.SetActive(true);
		return inst;
	}

	private void GenerateEditAudioOverrideVisuals()
	{
		for (int i = 0; i < m_districtAudioOverrideSpheres.Count; ++i)
		{
			var audioSphere = m_districtAudioOverrideSpheres[i];
			var label = "AudioSphere {i}";
			var holder = new GameObject(label);
			holder.transform.SetParent(m_districtEditVisuals.transform);
			
			var sphere = CreateCylinderPrimitive();
			//var sphere = GameObject.CreatePrimitive(PrimitiveType.Sphere);
			sphere.transform.SetParent(holder.transform);
			var drag = sphere.AddComponent<DistrictAudioDrag>();
			drag.m_index = i;
			
			var (box, text) = CreateLabel(holder.transform, audioSphere.m_audioID, 7);
			box.transform.localScale = new Vector3(30, .1f, 8);
			box.transform.localPosition = Vector3.up * 1f;
			text.transform.localPosition = Vector3.up * 1.1f;
			text.transform.localScale = Vector3.one * 6;
		}
	}

	private void RefreshDistrictVisuals(int _district)
	{
		var tr = m_districtEditVisuals.transform.GetChild(m_districtVertices.Count + m_districtEdges.Count + _district);
		var poly = m_districtPolygons[_district];
		var labelPos = poly.BestLabelPos;
		tr.position = labelPos.NewY(DistrictDrag.c_districtDragHeight);
		var trBox = tr.GetChild(0).gameObject;
		var clr = Color.green * (poly == m_selected ? .2f : (poly == m_highlighted ? .1f : 0));
		SetColour(trBox, clr, clr * .5f);
	}
	
	void RefreshEdgeVisuals(int _edge)
	{
		var e = m_districtEdges[_edge];
		var v1 = m_districtVertices[e.m_vertex1];
		var v2 = m_districtVertices[e.m_vertex2];
		var center = (v1 + v2) * .5f;
		var forward = v2 - v1;
		float length;
		if (forward.sqrMagnitude < .001f * .001f)
		{
			length = 1;
			forward = Vector2.right;
		}
		else
		{
			length = forward.magnitude;
			forward /= length;
		}
		var trRoot = m_districtEditVisuals.transform.GetChild(m_districtVertices.Count + _edge);
		var tr = trRoot.GetChild(0);
		tr.position = new Vector3(center.x, DistrictDrag.c_districtDragHeight * .5f, center.y);
		tr.localScale = new Vector3(0.1f, DistrictDrag.c_districtDragHeight, length);
		tr.forward = new Vector3(forward.x, 0, forward.y);
		var clr = (e.m_district1 != -1 && e.m_district2 != -1) ? new Color(.35f, .35f, .35f) : new Color(.35f, .25f, .25f);
		var clrEmissive = (e.m_district1 != -1 && e.m_district2 != -1) ? new Color(.35f, .35f, .35f) : new Color(.15f, 0, 0);
		SetColour(tr.gameObject, clr, clrEmissive);
		var trTop = trRoot.GetChild(1);
		trTop.localPosition = tr.position + Vector3.up * (DistrictDrag.c_districtDragHeight * .5f);
		trTop.localScale = new Vector3(c_baseEdgeHandleSize * m_handleSize, c_baseEdgeHandleSize * m_handleSize, length);
		trTop.forward = tr.forward;
		int highlight = 0;
		if (m_selected != null)
			highlight |= m_selected.m_edges.Contains(_edge) ? 1 : 0;
		if (m_highlighted != null)
			highlight |= m_highlighted.m_edges.Contains(_edge) ? 2 : 0;
		var edgeClr = Color.black;
		edgeClr.g = highlight & 1;
		edgeClr.r = ((highlight >> 1) & 1) * .25f;
		SetColour(trTop.gameObject, edgeClr, edgeClr * .5f);
	}

	void RefreshVertexVisuals(int _vertex)
	{
		var tr = m_districtEditVisuals.transform.GetChild(_vertex);
		tr.position = new Vector3(m_districtVertices[_vertex].x, DistrictDrag.c_districtDragHeight, m_districtVertices[_vertex].y);

		bool highlight = false;
		if (m_selected != null)
		{
			foreach (var edge in m_selected.m_edges)
				if (m_districtEdges[edge].Contains(_vertex))
					highlight = true;
		}
		var clr = highlight ? new Color(0, .6f, 1f) : Color.blue;
		SetColour(tr.gameObject, clr, clr * .5f);
	}

	public void DragVertex(int _index, Vector3 _dragPoint)
	{
		float threshold = 1.0f * m_handleSize;
		float sqrThreshold = threshold * threshold;
		m_draggingIndex = _index;
		var pos = new Vector2(_dragPoint.x, _dragPoint.z);
		for (int i = 0; i < m_districtVertices.Count; ++i)
		{
			if (i == _index) continue;
			if ((m_districtVertices[i] - pos).sqrMagnitude < sqrThreshold)
			{
				pos = m_districtVertices[i];
				break;
			}
		}
		m_districtVertices[_index] = pos;
		RefreshVertexVisuals(_index);
		for (int i = 0; i < m_districtEdges.Count; ++i)
		{
			var e = m_districtEdges[i];
			if (e.m_vertex1 == _index || e.m_vertex2 == _index)
			{
				RefreshEdgeVisuals(i);
				foreach (var d in m_districtPolygons)
					if (d.m_edges.Contains(i))
					{
						d.Dirty();
						RefreshDistrictVisuals(m_districtPolygons.IndexOf(d));
					}
			}
		}
		Render();
	}

	public void EndDragVertex(int _index)
	{
		m_draggingIndex = -1;
		var pos = m_districtVertices[_index];
		for (int i = 0; i < m_districtVertices.Count; ++i)
		{
			if (i == _index) continue;
			if ((m_districtVertices[i] - pos).sqrMagnitude < 1f * 1f)
			{
				// replace all edges containing vertex _index with vertex i
				int deleteVertex = _index;
				foreach (var e in m_districtEdges)
					e.Replace(_index, i);
				// merge any duplicate edges
				var deleteEdges = MergeDuplicateEdges();
				// now delete as required
				int lastVert = m_districtVertices.Count - 1;
				m_districtVertices[deleteVertex] = m_districtVertices[lastVert];
				m_districtVertices.RemoveAt(lastVert);
				foreach (var e in m_districtEdges)
					e.Replace(lastVert, deleteVertex);
				DeleteEdges(deleteEdges);
				GenerateEditVisuals();
				break;
			}
		}
	}

	private List<int> MergeDuplicateEdges()
	{
		List<int> deleteEdges = new();
		for (int j = 0; j < m_districtEdges.Count; ++j)
		{
			var edgeJ = m_districtEdges[j];
			for (int k = j + 1; k < m_districtEdges.Count; ++k)
			{
				var edgeK = m_districtEdges[k];
				if (edgeJ.Is(edgeK))
				{
					edgeJ.Absorb(edgeK);
					deleteEdges.Add(k);
					foreach (var d in m_districtPolygons)
						d.Replace(k, j);
				}
			}
		}
		return deleteEdges;
	}

	private void DeleteEdges(List<int> deleteEdges)
	{
		for (int j = 0; j < deleteEdges.Count; ++j)
			DeleteEdge(deleteEdges[j]);
	}



	private void UpdateEditInsert()
	{
		var (pos, edge) = GetInsertPoint();
		if (edge == -1) return;
		m_districtEditInsert.transform.position = pos + Vector3.up * (DistrictDrag.c_districtDragHeight * -.5f);
	}
	private (Vector3, int) GetInsertPoint()
	{
		var ray = Camera.main.ScreenPointToRay(Input.mousePosition);
		Vector3 pos = Vector3.zero;
		float distance = 1e23f;
		int edgeIndex = -1;
		foreach (var hit in Physics.RaycastAll(ray))
		{
			if (hit.transform.IsChildOf(m_districtEditVisuals.transform))
			{
				var edge = hit.transform.parent.GetSiblingIndex() - m_districtVertices.Count;
				if (edge < 0 || edge >= m_districtEdges.Count)
					continue;
				if (hit.distance < distance)
				{
					distance = hit.distance;
					edgeIndex = edge;
					pos = hit.point;
				}
			}
		}
		return (pos.NewY(DistrictDrag.c_districtDragHeight), edgeIndex);
	}

	private GameObject m_districtEditInsert = null;
	
	private int m_draggingIndex = -1;

	private bool DeleteVertex(int _index)
	{
		AbandonVertexDrag(_index);
		
		List<int> removeEdges = new();
		for (int i = 0; i < m_districtPolygons.Count; ++i) 
		{
			var poly = m_districtPolygons[i];
			for (int j = 1; j < poly.m_edges.Count; ++j)
			{
				var edgeIndex = poly.m_edges[j];
				var edge = m_districtEdges[edgeIndex];
				if (edge.m_vertex1 == _index || edge.m_vertex2 == _index)
				{
					int jNext = (j + 1) % poly.m_edges.Count;
					int edgeNextIndex = poly.m_edges[jNext];
					int other1 = edge.m_vertex1 == _index ? edge.m_vertex2 : edge.m_vertex1, other2 = -1;
					var nextEdge = m_districtEdges[edgeNextIndex];
					if (nextEdge.m_vertex1 == _index) other2 = nextEdge.m_vertex2;
					else if (nextEdge.m_vertex2 == _index) other2 = nextEdge.m_vertex1;
					if (other2 != -1)
					{
						removeEdges.AddUnique(edgeIndex);
						removeEdges.AddUnique(edgeNextIndex);
						int nextEdgeIndex = m_districtEdges.Count;
						m_districtEdges.Add(new DistrictEdge { m_vertex1 = other1, m_vertex2 = other2, m_district1 = i, m_district2 = -1 });
						poly.m_edges[j] = nextEdgeIndex;
						poly.m_edges.RemoveAt(jNext);
						break;
					}
				}
			}
		}
		// now remove all to-delete edges
		for (int i = 0; i < removeEdges.Count; ++i)
			DeleteEdge(removeEdges[i]);

		// remove the vertex
		int lastVertex = m_districtVertices.Count - 1;
		m_districtVertices[_index] = m_districtVertices[lastVertex];
		m_districtVertices.RemoveAt(lastVertex);
		foreach (var e in m_districtEdges)
			e.Replace(lastVertex, _index);
		
		DeleteEdges(MergeDuplicateEdges());
		ClearDegenerates();
		return true;
	}

	private void ClearDegenerates()
	{
		// finally delete any polygons that are now invalid (< 3 edges)
		for (int i = m_districtPolygons.Count - 1; i >= 0; --i)
		{
			var district = m_districtPolygons[i];
			if (district.m_edges.Count < 3)
				DeleteDistrict(district);
		}
	}
	
	private void DeleteEdge(int _index)
	{
		var lastEdge = m_districtEdges.Count - 1;
		m_districtEdges[_index] = m_districtEdges[lastEdge];
		m_districtEdges.RemoveAt(lastEdge);
		foreach (var d in m_districtPolygons)
			d.Replace(lastEdge, _index);
	}

	private void AbandonVertexDrag(int _index)
	{
		if (m_districtEditVisuals == null) return;
		var tr = m_districtEditVisuals.transform.GetChild(_index);
		var drag = tr.GetComponent<DistrictDrag>();
		if (drag != null)
			drag.EndDrag();
	}

	private void SetColour(GameObject _o, Color _colour, Color _emissive)
	{
		_o.GetComponent<Renderer>().material.color = _colour;
		_o.GetComponent<Renderer>().material.SetColor("_EmissiveColor", _emissive);
	}
	
	private DistrictPolygon GetPolygonFromVisuals(MeshRenderer _mr)
	{
		if (_mr == null) return null;
		var tr = _mr.transform.parent;
		var index = tr.GetSiblingIndex();
		if (index < m_districtVertices.Count + m_districtEdges.Count)
			return null;
		return m_districtPolygons[index - m_districtVertices.Count - m_districtEdges.Count];
	}
	
	public DistrictPolygon DistrictAtPoint(Vector3 _pos)
	{
		float bestSize = 1e23f;
		DistrictPolygon best = null;
		for (int i = 0; i < m_districtPolygons.Count; ++i)
		{
			if (m_districtPolygons[i].Size < bestSize && m_districtPolygons[i].PointInside(_pos))
			{
				best = m_districtPolygons[i];
				bestSize = best.Size; 
			}
		}
		return best;
	}

	public List<Vector3> m_localDistrictUnlocks = new();

	public bool FindLocalUnlockAtPoint(Vector3 _pos)
	{
		foreach (var local in m_localDistrictUnlocks)
		{
			var d = _pos  - local;
			if (d.x * d.x + d.z * d.z < local.y * local.y) return true;
		}
		return false;
	}

	public void AddLocalUnlock(Vector3 _pos, float _radius)
	{
		_pos.y = _radius;
		m_localDistrictUnlocks.Add(_pos);
		Render();
	}

	public void RemoveLocalUnlock(Vector3 _pos)
	{
		for (int i = 0; i < m_localDistrictUnlocks.Count; ++i)
		{
			if ((_pos - m_localDistrictUnlocks[i]).xzSqrMagnitude() < .1f * .1f)
			{
				m_localDistrictUnlocks.RemoveAt(i);
				Render();
				break;
			}
		}
	}


	public DistrictAudioOverrideSphere FindAudioOverrideAtPoint(Vector3 _pos)
	{
		foreach (var sphere in m_districtAudioOverrideSpheres)
		{
			var d = _pos - sphere.m_pos;
			if (d.x * d.x + d.z * d.z < d.y * d.y) return sphere;
		}
		return null;
	}

	public void AddAudioOverride(Vector3 _pos, float _radius)
	{
		m_districtAudioOverrideSpheres.Add(new DistrictAudioOverrideSphere() { m_pos = _pos, m_radius = _radius, m_audioID = "" });
		if (m_districtEditVisuals != null)
		{
			m_selectedAudioSphere = m_districtAudioOverrideSpheres[^1];
			GenerateEditVisuals();
			Save();
		}
	}
	
	public void DragAudioOverride(int _index, Vector3 _dragPoint)
	{
		m_districtAudioOverrideSpheres[_index].m_pos = _dragPoint;
		RefreshEditAudioOverrideVisuals();
	}

	public void EndDragAudioOverride(int _index)
	{
	}

	public void RemoveAudioOverride(Vector3 _pos)
	{
		for (int i = 0; i < m_districtAudioOverrideSpheres.Count; ++i)
		{
			if ((_pos - m_districtAudioOverrideSpheres[i].m_pos).xzSqrMagnitude() < .1f * .1f)
			{
				if (m_selectedAudioSphere == m_districtAudioOverrideSpheres[i])
					m_selectedAudioSphere = null;
				m_districtAudioOverrideSpheres.RemoveAt(i);
				if (m_districtEditVisuals != null)
				{
					GenerateEditVisuals();
					Save();
				}
				break;
			}
		}
	}
	
	public Vector3 NearestPointInOwnedDistrict(Vector3 _pos, Vector3 _other, float _otherDistanceCheck, float _distanceInside)
	{
		var other2D = _other.GetXZVector2();
		bool considerOther = other2D.sqrMagnitude > 0.01f * 0.01f;
		var districtAt = DistrictAtPoint(_pos);
		if (districtAt != null && districtAt.IsUnlocked) return _pos;
		var pos2D = _pos.GetXZVector2();
		float bestDistSqrd = 1e23f;
		Vector2 bestPos = Vector2.zero;
		foreach (var district in m_districtPolygons)
		{
			if (district.IsUnlocked == false) continue;
			foreach (var edgeIndex in district.m_edges)
			{
				var edge = m_districtEdges[edgeIndex];
				var edgeVert1 = m_districtVertices[edge.m_vertex1];
				var edgeVert2 = m_districtVertices[edge.m_vertex2];
				var ab = edgeVert2 - edgeVert1;
				var abSqrMag = ab.sqrMagnitude;
				if (abSqrMag < .01f * .01f)
					continue;
				var ap = pos2D - edgeVert1;
				var closestDistanceAlong01 = Mathf.Clamp01(Vector2.Dot(ap, ab) / abSqrMag);
				var pointOnEdge = edgeVert1 + ab * closestDistanceAlong01;
				var distSqrd = (pointOnEdge - pos2D).sqrMagnitude;
				if (considerOther)
				{
					var otherD2 = (pointOnEdge - other2D).sqrMagnitude;
					if (_otherDistanceCheck > 0 && otherD2 > _otherDistanceCheck * _otherDistanceCheck) continue; // don't allow a point too far from the comparative point
					if (_otherDistanceCheck <= 0) distSqrd += otherD2; // distanceCheck zero means just take it into account as sum-of-squares, no upper limit
				}
				if (distSqrd > bestDistSqrd) continue;
				bestDistSqrd = distSqrd;
				bestPos = pointOnEdge;
			}
		}
		if (bestDistSqrd > 1e22f) return Vector3.zero;
		// push inside by _distanceInside
		var toNew = bestPos - pos2D;
		return (bestPos + toNew.normalized * _distanceInside).V3XZ();
	}

	public DistrictPolygon DistrictFromID(string _id)
	{
		for (int i = 0; i < m_districtPolygons.Count; ++i)
			if (m_districtPolygons[i].ID == _id || m_districtPolygons[i].Name == _id)
				return m_districtPolygons[i];
		return null;
	}

	private static bool s_showUnlockDebug = false;
	private static DebugConsole.Command s_showUnlockDebugCmd = new ("debugdistrictunlock", _s => Utility.SetOrToggle(ref s_showUnlockDebug, _s));

	private Dictionary<string, GameObject> m_districtUnlockDebugVisuals = new();
	private void UpdateAnimationDebug(string _id, bool _isAnimating, Vector3 _focus)
	{
		if (_id == null) return;
		if (s_showUnlockDebug == false)
		{
			if (m_districtUnlockDebugVisuals.Count > 0)
			{
				foreach (var kvp in m_districtUnlockDebugVisuals) GameObject.Destroy(kvp.Value);
				m_districtUnlockDebugVisuals.Clear();
			}
			return;
		}
		if (_isAnimating)
		{
			if (m_districtUnlockDebugVisuals.TryGetValue(_id, out var go) == false)
			{
				go = GameObject.CreatePrimitive(PrimitiveType.Sphere);
				go.transform.localScale = Vector3.one * 5;
				go.name = _id;
				m_districtUnlockDebugVisuals[_id] = go;
			}
			go.transform.position = _focus.GroundPosition(1f);
		}
		else if (m_districtUnlockDebugVisuals.TryGetValue(_id, out var go))
		{
			GameObject.Destroy(go);
			m_districtUnlockDebugVisuals.Remove(_id);
		}
	}

	Vector3 m_mouseClickDown;
	bool m_inhibitVisuals = false;
	Vector3 m_currentCameraFocus = Vector3.zero, m_currentCameraForward = Vector3.forward;
	Vector3 m_lastCameraFocus = Vector3.zero, m_lastCameraForward = Vector3.forward;
	int m_currentCameraAnimateCooldown = 0;

	public float m_unlockAnimationSpeed = 1;
	public void UpdateAnimation()
	{
		UpdateMap();

		const float c_animationDuration = 1;
		float dt = Time.deltaTime * m_unlockAnimationSpeed / c_animationDuration;
		bool anyAnimating = false;
		//Vector3 cameraFocus = Vector3.zero, cameraForward = Vector3.zero;
		foreach (var district in m_districtPolygons)
		{
			var animating = district.StepAnimation(dt);
			UpdateAnimationDebug(district.ID, animating, district.CurrentBlendFocus);
			//if (animating) (cameraFocus, cameraForward) = (district.CurrentBlendFocus, district.CurrentBlendDirection);
			anyAnimating |= animating;
		}
		if (anyAnimating)
			Render();
		//UpdateCameraAnimation(anyAnimating, cameraFocus, cameraForward);
	}

	private void UpdateCameraAnimation(bool anyAnimating, Vector3 cameraFocus, Vector3 cameraForward)
	{
		if (anyAnimating)
		{
			m_currentCameraAnimateCooldown = 0;
			if (cameraFocus.sqrMagnitude < .001f * .001f)
			{
				cameraFocus = m_lastCameraFocus;
				cameraForward = m_lastCameraForward;
			}
			m_lastCameraFocus = cameraFocus;
			m_lastCameraForward = cameraForward;
			if (cameraFocus.sqrMagnitude > 0)
			{
				if (m_currentCameraFocus.sqrMagnitude < .001f * .001f)
				{
					m_currentCameraFocus = cameraFocus;
					m_currentCameraForward = cameraForward;
				}
				m_currentCameraFocus = Vector3.Lerp(m_currentCameraFocus, cameraFocus, .02f);
				m_currentCameraForward = Vector3.Slerp(m_currentCameraForward, cameraForward, .01f);
				cameraFocus = m_currentCameraFocus;
				cameraForward = m_currentCameraForward;

				var cam = Camera.main.transform;
				cameraFocus = cameraFocus.NewY(120);
				cam.position = cameraFocus + Vector3.up * 40 + cameraForward * -100;
				cam.LookAt(cameraFocus, Vector3.up);
			}
		}
		else if (++m_currentCameraAnimateCooldown == 20)
		{
			m_currentCameraFocus = Vector3.zero;
			m_lastCameraFocus = Vector3.zero;
		}
	}

	public void UpdateEdit()
	{
#if _ENABLE_IMPORT
		if (Utility.GetKeyDown(KeyCode.I))
			ImportFromOld();
#endif
		
		UpdateUI();
		
		bool click = false;
		if (Utility.GetMouseButtonDown(0))
			m_mouseClickDown = Utility.IsMouseOverUI() ? Vector3.zero : Utility.mousePosition;
		float clickThreshold = Screen.height * .05f;
		if (Utility.GetMouseButtonUp(0) && (Utility.mousePosition - m_mouseClickDown).sqrMagnitude < clickThreshold * clickThreshold)
			click = true;
		
		var handleScale = 1 + (GameManager.Me.m_camera.transform.position.y - c_baseVertexHandleSize) * .01f;
		SetHandleSize(handleScale);

		MeshRenderer highlight = null;
		var ray = GameManager.Me.m_camera.ScreenPointToRay(Utility.mousePosition);
		new Plane(Vector3.up, Vector3.up * DistrictDrag.c_districtDragHeight).Raycast(ray, out var distance);
		var hitPos = ray.GetPoint(distance);
		var hitDistrict = DistrictAtPoint(hitPos);
		EditHighlight(hitDistrict);

		if (click)
		{
			m_selected = null;
			m_selectedAudioSphere = null;
			if (m_hideAudioOverrides == false)
				foreach (var sphere in m_districtAudioOverrideSpheres)
					if (sphere.IntersectsRay(ray))
					{
						m_selectedAudioSphere = sphere;
						RefreshEditVisuals();
					}
			if (hitDistrict != null && m_selectedAudioSphere == null)
				EditSelect(hitDistrict);
		}

		GameManager.Me.RaycastAtPoint(Utility.mousePosition, out var hit, GameManager.c_layerTerrainBit);
		var pos = new Vector2(hit.point.x, hit.point.z);
		if (Utility.GetKeyDown(KeyCode.Equals) && Utility.GetMouseButton(0) == false)
		{
			int firstVert = m_districtVertices.Count;
			int firstEdge = m_districtEdges.Count;
			int firstDistrict = m_districtPolygons.Count;
			m_districtVertices.Add(pos + new Vector2(-5, -5));
			m_districtVertices.Add(pos + new Vector2(-5, 5));
			m_districtVertices.Add(pos + new Vector2(5, 5));
			m_districtVertices.Add(pos + new Vector2(5, -5));
			for (int i = 0; i < 4; ++i)
				m_districtEdges.Add(new DistrictEdge { m_vertex1 = firstVert + i, m_vertex2 = firstVert + ((i + 1) & 3), m_district1 = firstDistrict, m_district2 = -1 });
			var poly = new DistrictPolygon();
			poly.SetIndex(m_districtPolygons.Count);
			for (int i = 0; i < 4; ++i)
				poly.m_edges.Add(firstEdge + i);
			m_districtPolygons.Add(poly);
			GenerateEditVisuals();
			Save();
		}
		if (Utility.GetKeyDown(KeyCode.Minus) && m_draggingIndex != -1)
		{
			if (DeleteVertex(m_draggingIndex))
			{
				GenerateEditVisuals();
				m_draggingIndex = -1;
			}
		}
		if (Utility.GetKeyDown(KeyCode.LeftShift) || Utility.GetKeyDown(KeyCode.RightShift))
		{
			m_districtEditInsert = GameObject.CreatePrimitive(PrimitiveType.Cube);
			m_districtEditInsert.GetComponentInChildren<Collider>().enabled = false;
			m_districtEditInsert.transform.localScale = new Vector3(1, DistrictDrag.c_districtDragHeight, 1);
			SetColour(m_districtEditInsert, Color.green, Color.green * .5f);
			UpdateEditInsert();
		}
		else if (Utility.GetKey(KeyCode.LeftShift) || Utility.GetKey(KeyCode.RightShift))
		{
			UpdateEditInsert();
			if (Utility.GetMouseButtonDown(0))
			{
				var (insertPoint, edge) = GetInsertPoint();
				if (edge != -1)
				{
					int nextVertex = m_districtVertices.Count;
					m_districtVertices.Add(new Vector2(insertPoint.x, insertPoint.z));
					var oldEdge = m_districtEdges[edge];
					var newEdge = new DistrictEdge { m_vertex1 = oldEdge.m_vertex2, m_vertex2 = nextVertex, m_district1 = oldEdge.m_district1, m_district2 = oldEdge.m_district2 };
					oldEdge.m_vertex2 = nextVertex;
					int nextEdge = m_districtEdges.Count;
					m_districtEdges.Add(newEdge);
					if (oldEdge.m_district1 != -1)
						AddEdgeToPoly(oldEdge.m_district1, edge, nextEdge);
					if (oldEdge.m_district2 != -1)
						AddEdgeToPoly(oldEdge.m_district2, edge, nextEdge);
					GenerateEditVisuals();
					Save();
				}
			}
		}
		else
		{
			if (m_districtEditInsert != null)
			{
				Object.Destroy(m_districtEditInsert);
				m_districtEditInsert = null;
			}
		}

		if (Utility.ModifiedKey(KeyCode.O, false, true, false, "Add Audio Override", false))
		{
			m_hideAudioOverrides = false;
			AddAudioOverride(hitPos, 50);
		}

		var inhibitVisuals = Utility.GetKey(KeyCode.V) ^ m_hideVisuals;
		if (inhibitVisuals != m_inhibitVisuals)
		{
			m_inhibitVisuals = inhibitVisuals;
			if (m_districtEditVisuals != null)
				foreach (var renderer in m_districtEditVisuals.GetComponentsInChildren<MeshRenderer>())
					renderer.enabled = !m_inhibitVisuals;
		}
	}

	void AddEdgeToPoly(int _poly, int _followEdge, int _insertEdge)
	{
		var poly = m_districtPolygons[_poly];
		int edgeIndex = poly.m_edges.IndexOf(_followEdge);
		var follow = m_districtEdges[_followEdge];
		var followPrevious = m_districtEdges[poly.m_edges[(edgeIndex + poly.m_edges.Count - 1) % poly.m_edges.Count]];
		if (followPrevious.Contains(follow.m_vertex1) || followPrevious.Contains(follow.m_vertex2))
			poly.m_edges.Insert((edgeIndex + 1) % poly.m_edges.Count, _insertEdge);
		else
			poly.m_edges.Insert(edgeIndex, _insertEdge);
	}
	

	public string Export()
	{
		return JsonUtility.ToJson(this, true);
	}

	public void Import(string _json)
	{
		JsonUtility.FromJsonOverwrite(_json, this);
	}

	const string c_resName = "MOADistricts";
	private string ResPath => $"Assets/Resources/{c_resName}.json";
	private bool m_saveDirty = false;
	List<string> m_undo = new();
	int m_undoHead = 0;

	private void AddToUndo()
	{
		if (m_undoHead < m_undo.Count - 1)
			m_undo.RemoveRange(m_undoHead + 1, m_undo.Count - m_undoHead - 1);
		m_undo.Add(Export());
		if (m_undo.Count > 50)
			m_undo.RemoveAt(0);
		m_undoHead = m_undo.Count - 1;
		UpdateUndo();
	}

	private void Undo()
	{
		--m_undoHead;
		Import(m_undo[m_undoHead]);
		UpdateUndo();
		WriteSave();
		GenerateEditVisuals();
	}

	private void Redo()
	{
		++m_undoHead;
		Import(m_undo[m_undoHead]);
		UpdateUndo();
		WriteSave();
		GenerateEditVisuals();
	}
	
	private bool CanUndo() => m_undoHead > 0;
	private bool CanRedo() => m_undoHead < m_undo.Count - 1;

	public void Save()
	{
		WriteSave();
		AddToUndo();
	}
	
	private void WriteSave()
	{
#if UNITY_EDITOR
		Render();
		var path = ResPath;
		System.IO.File.WriteAllText(path, Export());
		m_saveDirty = true;
#endif
	}

	public void Load()
	{
#if UNITY_EDITOR
		if (m_saveDirty)
		{
			AssetDatabase.ImportAsset(ResPath);
			m_saveDirty = false;
		}
#endif
		var res = Resources.Load<TextAsset>(c_resName);
		if (res != null)
			Import(res.text);
		if (m_districtEditVisuals != null)
			GenerateEditVisuals();
		for (int i = 0; i < m_districtPolygons.Count; ++i)
		{
			m_districtPolygons[i].SetIndex(i);
			m_districtPolygons[i].Dirty();
		}
		Render();
		AddToUndo();
	}
	
	private Mesh m_sphereMesh;
	public void Render()
	{
		var cam = DistrictManager.Me.RenderCamera;
		var rt = RenderTexture.GetTemporary(DistrictManager.c_textureSize, DistrictManager.c_textureSize, 0, RenderTextureFormat.RG16, RenderTextureReadWrite.Linear);
		cam.targetTexture = rt;

		float scale = GlobalData.c_terrainXZScale * 1024 / GlobalData.c_heightmapW;
		var offset = new Vector3(-GlobalData.c_terrainOriginX * scale - 512, 0, -GlobalData.c_terrainOriginZ * scale - 512);

		const int c_cullingLayer = 27;
		for (int i = 0; i < m_districtPolygons.Count; ++i)
		{
			var poly = m_districtPolygons[i];
			var mesh = poly.Mesh();
			var mat = poly.GetRenderMaterial(offset, scale);
			Graphics.DrawMesh(mesh, Matrix4x4.TRS(offset, Quaternion.identity, Vector3.one * scale), mat, c_cullingLayer, cam);
		}
		if (m_sphereMesh == null)
		{
			var sphere = GameObject.CreatePrimitive(PrimitiveType.Sphere);
			sphere.transform.SetParent(DistrictManager.Me.transform);
			m_sphereMesh = sphere.GetComponent<MeshFilter>().mesh;
		}
		foreach (var local in m_localDistrictUnlocks)
			Graphics.DrawMesh(m_sphereMesh, Matrix4x4.TRS(local + offset, Quaternion.identity, Vector3.one * (local.y * scale)), DistrictManager.Me.RenderMaterial, c_cullingLayer, cam);
		cam.cullingMask = 1 << c_cullingLayer;
		cam.Render();

		var tex = DistrictManager.Me.m_texture;
		
		cam.targetTexture = null;
		RenderTexture.active = rt;
		tex.ReadPixels(new Rect(0, 0, DistrictManager.c_textureSize, DistrictManager.c_textureSize), 0, 0);
		tex.Apply();
		RenderTexture.active = null;
		RenderTexture.ReleaseTemporary(rt);
		DistrictManager.Me.m_textureData = tex.GetRawTextureData<byte>();
	}
	
	private DistrictPolygon GetPolygon(string _id)
	{
		_id = _id.ToLower();
		foreach (var p in m_districtPolygons)
			if (p.ID.ToLower() == _id)
				return p;
		return null;
	}
	
	private void UnlockContainedWildBlocks(DistrictPolygon _poly)
	{
		MAParser.UnlockRegionDrawers(_poly.Name);
		foreach (var wb in GameManager.Me.m_state.m_wildBlocks)
		{
			var pos = wb.m_position;
			if (_poly.PointInside(pos))
			{
				var info = DesignUtilities.GetDesignData(wb.m_blockDesign);
				foreach (var block in info)
					GameManager.Me.AddBlockUnlockAnimation(block.m_blockID, pos, _poly.Name);
			}
		}
		
		void AddAllBuildings(List<NGCommanderBase> _list)
		{
			foreach (var ng in _list)
			{
				var pos = ng.transform.position;
				if (_poly.PointInside(pos))
				{
					var blocks = ng.GetComponentsInChildren<Block>();
					foreach (var block in blocks)
						GameManager.Me.AddBlockUnlockAnimation(block.BlockID, pos, _poly.Name);
				}
			}
		}
		AddAllBuildings(NGManager.Me.m_NGCommanderList);
		AddAllBuildings(NGManager.Me.m_NGCommanderListOutOfRange);
	}

	private List<string> m_unlockedIDs = new();
	public List<string> UnlockedIDs => m_unlockedIDs;
	
	private string m_lastUnlockedDistrictInSession = null;
	
	public bool LastUnlockFlybyComplete => m_lastUnlockedDistrictInSession != null && DistrictPolygon.IsUnlockSequenceInProgress == false;

	public void Unlock(string _id, bool _unlock, bool _render = true)
	{
		if (_id == "*")
		{
			for (int i = 0; i < m_districtPolygons.Count; ++i)
				if (_unlock || m_districtPolygons[i].ID != "District1") // don't lock initial district
					Unlock(m_districtPolygons[i].ID, _unlock, false);
		}
		else
		{
			_id = _id.ToLower();
			var poly = GetPolygon(_id);
			if (poly == null)
			{
				Debug.LogError($"District { _id } not found");
				return;
			}
			poly.IsUnlocked = _unlock;
			poly.SetBlendLevel(0);
			m_unlockAnimationSpeed = 1;
			if (_render && _unlock)
			{
				poly.StartCameraSequence();
				m_lastUnlockedDistrictInSession = _id;
			}
			if (_unlock) m_unlockedIDs.AddUnique(_id);
			else m_unlockedIDs.Remove(_id);
			if (_unlock)
			{
				poly.PlayUnlockAudio();
				UnlockContainedWildBlocks(poly);
			}
		}
		if (_render)
			Render();
	}

	public void LoadUnlocks(SaveContainers.SaveDistrictData _data)
	{
		m_unlockedIDs = _data.unlockedDistrictIDs;
		for (int i = 0; i < m_unlockedIDs.Count; ++i)
		{
			m_unlockedIDs[i] = m_unlockedIDs[i].ToLower();
			var district = GetPolygon(m_unlockedIDs[i]);
			if (district != null)
			{
				district.IsUnlocked = true;
				district.SetBlendLevel(1);
			}
		}
	}
	public SaveContainers.SaveDistrictData SaveUnlocks()
	{
		return new SaveContainers.SaveDistrictData() {
			unlockedDistrictIDs = m_unlockedIDs
		};
	}


	private DistrictAudioOverrideSphere m_selectedAudioSphere = null, m_lastUIAudioSphere = null;
	private DistrictPolygon m_selected = null, m_highlighted = null;
	private DistrictPolygon m_lastUI = null;
	private bool m_forceUI = false;
	public void ForceUI() => m_forceUI = true;

	public void EditHighlight(DistrictPolygon _poly)
	{
		if (m_highlighted != _poly)
		{
			m_highlighted = _poly;
			RefreshEditVisuals();
		}
	}

	public void EditSelect(DistrictPolygon _poly)
	{
		if (m_selected != _poly)
		{
			m_selected = _poly;
			RefreshEditVisuals();
		}
	}

	private bool m_hideVisuals = false;
	private void ToggleHideVisuals(bool _hide)
	{
		m_hideVisuals = _hide;
	}
	
	private bool m_overrideUnlocks = false;
	private UInt64 m_overrideUnlocksBitmap = 0;
	public bool UnlockOverride(int _index, bool _value)
	{
		if (m_districtEditVisuals != null &&  m_overrideUnlocks)
			return (m_overrideUnlocksBitmap & (1ul << _index)) != 0;
		return _value;
	}

	private bool m_hideAudioOverrides = false;
	private void ToggleHideAudioOverrides(bool _hide)
	{
		m_hideAudioOverrides = _hide;
		RefreshEditAudioOverrideVisuals();
		if (_hide)
			m_selectedAudioSphere = null;
	}

	private void ToggleOverrideUnlocks(bool _override)
	{
		m_overrideUnlocks = _override;
		UIManager.Me.m_districtEditOverrideUnlocksUI.gameObject.SetActive(_override);
		if (_override)
		{
			int count = m_districtPolygons.Count;
			if (UIManager.Me.m_districtEditOverrideUnlocksUI.childCount > count)
			{
				for (int i = UIManager.Me.m_districtEditOverrideUnlocksUI.childCount - 1; i >= count; --i)
					Object.Destroy(UIManager.Me.m_districtEditOverrideUnlocksUI.GetChild(i).gameObject);
			}
			else if (UIManager.Me.m_districtEditOverrideUnlocksUI.childCount < count)
			{
				var first = UIManager.Me.m_districtEditOverrideUnlocksUI.GetChild(0);
				for (int i = UIManager.Me.m_districtEditOverrideUnlocksUI.childCount; i < count; ++i)
				{
					var go = GameObject.Instantiate(first);
					go.transform.SetParent(UIManager.Me.m_districtEditOverrideUnlocksUI);
				}
			}
			for (int i = 0; i < count; ++i)
			{
				var toggle = UIManager.Me.m_districtEditOverrideUnlocksUI.GetChild(i).GetComponent<UnityEngine.UI.Toggle>();
				toggle.SetIsOnWithoutNotify((m_overrideUnlocksBitmap & (1ul << i)) != 0);
				toggle.onValueChanged.RemoveAllListeners();
				int index = i;
				toggle.onValueChanged.AddListener(_b => {
					if (_b)
						m_overrideUnlocksBitmap |= 1ul << index;
					else
						m_overrideUnlocksBitmap &= ~(1ul << index);
					Render();
				});
			}
		}
		Render();
	}

	private void DeleteDistrict(DistrictPolygon _poly)
	{
		ClearEditVisuals();
		if (m_selected == _poly) m_selected = null;
		if (m_highlighted == _poly) m_highlighted = null;
		int index = m_districtPolygons.IndexOf(_poly);
		foreach (var edge in m_districtEdges)
		{
			if (edge.m_district1 == index) edge.m_district1 = -1;
			if (edge.m_district2 == index) edge.m_district2 = -1;
		}
		int swapFrom = m_districtPolygons.Count - 1;
		m_districtPolygons[index] = m_districtPolygons[swapFrom];
		foreach (var edge in m_districtEdges)
		{
			if (edge.m_district1 == swapFrom) edge.m_district1 = index;
			if (edge.m_district2 == swapFrom) edge.m_district2 = index;
		}
		m_districtPolygons.RemoveAt(swapFrom);
		for (int i = m_districtEdges.Count - 1; i >= 0; --i)
		{
			var edge = m_districtEdges[i];
			if (edge.m_district1 == -1 && edge.m_district2 == -1)
				DeleteEdge(i);
		}
		byte[] vertUsed = new byte[m_districtVertices.Count];
		for (int i = m_districtEdges.Count - 1; i >= 0; --i)
		{
			var edge = m_districtEdges[i];
			if (edge.m_vertex1 >= 0)
				vertUsed[edge.m_vertex1] = 1;
			if (edge.m_vertex2 >= 0)
				vertUsed[edge.m_vertex2] = 1;
		}
		for (int i = m_districtVertices.Count - 1; i >= 0; --i)
			if (vertUsed[i] == 0)
				DeleteVertex(i);
		
		GenerateEditVisuals();
		Save();
	}

	private void SetName(DistrictPolygon _poly, string _name)
	{
		_poly.m_name = _name;
		GenerateEditVisuals();
		Save();
	}

	private void SetAudio(DistrictPolygon _poly, string _name)
	{
		_poly.m_audioID = _name;
		GenerateEditVisuals();
		Save();
	}

	private void SetAudio(DistrictAudioOverrideSphere _poly, string _name)
	{
		_poly.m_audioID = _name;
		GenerateEditVisuals();
		Save();
	}

	private void SetAudioRadius(DistrictAudioOverrideSphere _poly, string _radiusStr)
	{
		if (floatinv.TryParse(_radiusStr, out var radius))
		{
			_poly.m_radius = radius;
			GenerateEditVisuals();
			Save();
		}
	}

	void UpdateUI()
	{
		if (m_selected == m_lastUI && m_selectedAudioSphere == m_lastUIAudioSphere && m_forceUI == false) return;
		m_lastUI = m_selected;
		m_lastUIAudioSphere = m_selectedAudioSphere;
		m_forceUI = false;
		if (m_selectedAudioSphere != null)
		{
			UIManager.Me.m_districtEditID.text = "Audio Override";
			UIManager.Me.m_districtEditName.SetTextWithoutNotify($"{m_selectedAudioSphere.m_radius}");
			UIManager.Me.m_districtEditName.interactable = true;
			UIManager.Me.m_districtEditName.onValueChanged.RemoveAllListeners();
			UIManager.Me.m_districtEditName.onValueChanged.AddListener(_s => SetAudioRadius(m_selectedAudioSphere, _s));
			UIManager.Me.m_districtEditAudioName.SetTextWithoutNotify(m_selectedAudioSphere.m_audioID);
			UIManager.Me.m_districtEditAudioName.interactable = true;
			UIManager.Me.m_districtEditAudioName.onValueChanged.RemoveAllListeners();
			UIManager.Me.m_districtEditAudioName.onValueChanged.AddListener(_s => SetAudio(m_selectedAudioSphere, _s));
			UIManager.Me.m_districtEditDelete.interactable = true;
			UIManager.Me.m_districtEditDelete.onClick.RemoveAllListeners();
			UIManager.Me.m_districtEditDelete.onClick.AddListener(() => ConfirmDelete(m_selectedAudioSphere));
			UIManager.Me.m_districtEditOverridesDistricts.SetIsOnWithoutNotify(m_selectedAudioSphere.m_overridesDistricts);
			UIManager.Me.m_districtEditOverridesDistricts.onValueChanged.RemoveAllListeners();
			UIManager.Me.m_districtEditOverridesDistricts.onValueChanged.AddListener(_b => m_selectedAudioSphere.m_overridesDistricts = _b);
			UIManager.Me.m_districtEditOverridesDistricts.gameObject.SetActive(true);
		}
		else if (m_selected != null)
		{
			UIManager.Me.m_districtEditID.text = $"Index: {m_selected.Index + 1}";
			UIManager.Me.m_districtEditName.SetTextWithoutNotify(m_selected.m_name);
			UIManager.Me.m_districtEditName.interactable = true;
			UIManager.Me.m_districtEditName.onValueChanged.RemoveAllListeners();
			UIManager.Me.m_districtEditName.onValueChanged.AddListener(_s => SetName(m_selected, _s));
			UIManager.Me.m_districtEditAudioName.SetTextWithoutNotify(m_selected.m_audioID);
			UIManager.Me.m_districtEditAudioName.interactable = true;
			UIManager.Me.m_districtEditAudioName.onValueChanged.RemoveAllListeners();
			UIManager.Me.m_districtEditAudioName.onValueChanged.AddListener(_s => SetAudio(m_selected, _s));
			UIManager.Me.m_districtEditDelete.interactable = true;
			UIManager.Me.m_districtEditDelete.onClick.RemoveAllListeners();
			UIManager.Me.m_districtEditDelete.onClick.AddListener(() => ConfirmDelete(m_selected));
			UIManager.Me.m_districtEditOverridesDistricts.gameObject.SetActive(false);
		}
		else
		{
			UIManager.Me.m_districtEditID.text = $"Index: <none selected>";
			UIManager.Me.m_districtEditName.SetTextWithoutNotify("");
			UIManager.Me.m_districtEditName.interactable = false;
			UIManager.Me.m_districtEditAudioName.SetTextWithoutNotify("");
			UIManager.Me.m_districtEditAudioName.interactable = false;
			UIManager.Me.m_districtEditDelete.interactable = false;
			UIManager.Me.m_districtEditOverridesDistricts.gameObject.SetActive(false);
		}
		UIManager.Me.m_districtEditClose.onClick.RemoveAllListeners();
		UIManager.Me.m_districtEditClose.onClick.AddListener(DistrictManager.Me.CloseEditDistricts);
		UIManager.Me.m_districtEditHideVisuals.SetIsOnWithoutNotify(m_hideVisuals);
		UIManager.Me.m_districtEditHideVisuals.onValueChanged.RemoveAllListeners();
		UIManager.Me.m_districtEditHideVisuals.onValueChanged.AddListener(ToggleHideVisuals);
		UIManager.Me.m_districtEditOverrideUnlocks.SetIsOnWithoutNotify(m_overrideUnlocks);
		UIManager.Me.m_districtEditOverrideUnlocks.onValueChanged.RemoveAllListeners();
		UIManager.Me.m_districtEditOverrideUnlocks.onValueChanged.AddListener(ToggleOverrideUnlocks);
		UIManager.Me.m_districtEditHideAudioOverrides.SetIsOnWithoutNotify(m_hideAudioOverrides);
		UIManager.Me.m_districtEditHideAudioOverrides.onValueChanged.RemoveAllListeners();
		UIManager.Me.m_districtEditHideAudioOverrides.onValueChanged.AddListener(ToggleHideAudioOverrides);
		UIManager.Me.m_districtUndo.onClick.RemoveAllListeners();
		UIManager.Me.m_districtUndo.onClick.AddListener(Undo);
		UIManager.Me.m_districtRedo.onClick.RemoveAllListeners();
		UIManager.Me.m_districtRedo.onClick.AddListener(Redo);
		ToggleOverrideUnlocks(m_overrideUnlocks);
	}

	void UpdateUndo()
	{
		UIManager.Me.m_districtUndo.interactable = CanUndo();
		UIManager.Me.m_districtRedo.interactable = CanRedo();
	}

	void CloseDeleteUI()
	{
		UIManager.Me.m_districtDeleteUI.gameObject.SetActive(false);
	}

	void CloseDeleteUI(DistrictPolygon _district)
	{
		CloseDeleteUI();
		if (_district != null) DeleteDistrict(_district);
	}

	void CloseDeleteUI(DistrictAudioOverrideSphere _sphere)
	{
		CloseDeleteUI();
		if (_sphere != null) RemoveAudioOverride(_sphere.m_pos);
	}

	void ConfirmDelete(DistrictPolygon _district)
	{
		UIManager.Me.m_districtDeleteUI.gameObject.SetActive(true);
		UIManager.Me.m_districtDeleteNo.onClick.RemoveAllListeners();
		UIManager.Me.m_districtDeleteNo.onClick.AddListener(() => CloseDeleteUI());
		UIManager.Me.m_districtDeleteYes.onClick.RemoveAllListeners();
		UIManager.Me.m_districtDeleteYes.onClick.AddListener(() => CloseDeleteUI(_district));
	}

	void ConfirmDelete(DistrictAudioOverrideSphere _audio)
	{
		UIManager.Me.m_districtDeleteUI.gameObject.SetActive(true);
		UIManager.Me.m_districtDeleteNo.onClick.RemoveAllListeners();
		UIManager.Me.m_districtDeleteNo.onClick.AddListener(() => CloseDeleteUI());
		UIManager.Me.m_districtDeleteYes.onClick.RemoveAllListeners();
		UIManager.Me.m_districtDeleteYes.onClick.AddListener(() => CloseDeleteUI(_audio));
	}

	public string SanitiseID(string _id)
	{
		var idLower = _id.ToLower();
		if (idLower.Length >= 2 && idLower[0] == 'd' && char.IsNumber(idLower[1]))
		{
			var num = idLower[1..];
			_id = $"District{num}";
			idLower = $"district{num}";
		}
		else if (idLower.Length >= 1 && char.IsNumber(idLower[0]))
		{
			_id = $"District{idLower}";
			idLower = $"district{idLower}";
		}
		foreach (var district in m_districtPolygons)
		{
			if (district.m_name.ToLower() == idLower || district.Name.ToLower() == idLower)
				return district.m_name;
		}
		return _id;
	}

	void ImportFromOld()
	{
#if _ENABLE_IMPORT
		// will need to add DistrictSet script back in 
		var data = GlobalData.Me.m_moaDistricts;
		var points = data.Points;
		var districts = data.Districts;
		m_districtVertices.Clear();
		m_districtEdges.Clear();
		m_districtPolygons.Clear();
		for (int i = 0; i < points.Length; ++i)
			m_districtVertices.Add(new Vector2(points[i].X, points[i].Y));
		Dictionary<int, int> edgeLookup = new();
		for (int i = 0; i < districts.Length; ++i)
		{
			var district = districts[i];
			var id = district.DistrictID;
			var edges = district.Edges;
			var newDistrict = new DistrictPolygon()
			{
				m_name = id,
				m_edges = new List<int>(),
			};
			newDistrict.SetIndex(i);
			m_districtPolygons.Add(newDistrict);

			int lastKey = district.EdgeKey;
			for (int j = 0; j < edges.Length; ++j)
			{
				int edgeId = edges[j];
				int nextKey = lastKey ^ (edgeId & 0xFFFF);
				edgeId = nextKey < lastKey ? (nextKey << 16) | lastKey : (lastKey << 16) | nextKey;

				if (edgeLookup.TryGetValue(edgeId, out var newEdge) == false)
				{
					int v1 = edgeId & 0xFFFF;
					int v2 = edgeId >> 16;
					if (v1 < 0 || v2 < 0 || v1 >= m_districtVertices.Count || v2 >= m_districtVertices.Count)
						newEdge = -1;
					else
					{
						newEdge = m_districtEdges.Count;
						m_districtEdges.Add(new DistrictEdge {m_vertex1 = v1, m_vertex2 = v2, m_district1 = i, m_district2 = -1});
					}
					edgeLookup[edgeId] = newEdge;
				}
				else if (newEdge != -1)
					m_districtEdges[newEdge].m_district2 = i;
				if (newEdge != -1)
					newDistrict.m_edges.Add(newEdge);
				
				lastKey = nextKey;
			}
		}
		//ClearDegenerates();
		GenerateEditVisuals();
#endif
	}
}
