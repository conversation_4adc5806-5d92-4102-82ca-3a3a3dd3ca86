using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.Reflection;
using UnityEngine;
using UnityEngine.Assertions;
using JsonConvert = Newtonsoft.Json.JsonConvert;


#if UNITY_EDITOR
using UnityEditor;
[CustomEditor(typeof(Transform))]
class TransformEditor : Editor
{
	private static Editor m_defaultTransformEditor;
	private static bool s_showWorldValues = false;

	void OnEnable()
	{
		/*if (targets == null || targets.Length == 0)
		{
			Debug.LogError("Targets array is null or empty");
			return;
		}
		foreach (var t in targets)
		{
			if (t == null || t is not Transform)
			{
				Debug.LogError("Target is either null or not a Transform");
				return;
			}
		}*/
	}

	public void OnDestroy()
	{
		if (m_defaultTransformEditor != null)
		{
			DestroyImmediate(m_defaultTransformEditor);
			m_defaultTransformEditor = null;
		}
	}

	public override void OnInspectorGUI()
	{
		if (targets == null || targets.Length == 0) return;
		foreach (var t in targets) if (t == null || t is not Transform) return;
		
		CreateCachedEditor(targets, Type.GetType("UnityEditor.TransformInspector, UnityEditor"), ref m_defaultTransformEditor);
		if (m_defaultTransformEditor == null || m_defaultTransformEditor.serializedObject == null) return;
		m_defaultTransformEditor?.OnInspectorGUI();

		s_showWorldValues = EditorGUILayout.Foldout(s_showWorldValues, "World");
		if (s_showWorldValues)
		{
			var xform = target as Transform;
			Undo.RecordObject(xform, $"Change {xform.name} World Transform");
			xform.position = EditorGUILayout.Vector3Field("  World Position", xform.position);
			AddCopyPasteContextMenu("Position", xform, xform.position, (_v) => xform.position = _v);
			xform.eulerAngles = EditorGUILayout.Vector3Field("  World Rotation", xform.eulerAngles);
			AddCopyPasteContextMenu("Rotation", xform, xform.eulerAngles, (_v) => xform.eulerAngles = _v);
			var newLossyScale = EditorGUILayout.Vector3Field("  World Scale", xform.lossyScale);
			AddCopyPasteContextMenu("Scale", xform, xform.lossyScale, (_v) => xform.SetLossyScale(_v));
			if ((newLossyScale - xform.lossyScale).sqrMagnitude > 0.0001f)
				xform.SetLossyScale(newLossyScale);
			Undo.FlushUndoRecordObjects();
		}
		
		void AddCopyPasteContextMenu(string _context, UnityEngine.Object _obj, Vector3 _value, System.Action<Vector3> _setter)
		{
			Rect clickArea = GUILayoutUtility.GetLastRect(); //EditorGUILayout.GetControlRect();

			Event current = Event.current;
			if (clickArea.Contains(current.mousePosition) && current.type == EventType.ContextClick)
			{
				GenericMenu menu = new GenericMenu();
				menu.AddDisabledItem(new GUIContent(_context));
				menu.AddItem(new GUIContent("Copy"), false, () => { WriteVectorToClipboard(_value); });
				if (CanReadVectorFromClipboard())
					menu.AddItem(new GUIContent("Paste"), false, () => {
						Undo.RecordObject(_obj, $"Paste {_context}");
						_setter(ReadVectorFromClipboard());
						Undo.FlushUndoRecordObjects();
					});
				else
					menu.AddDisabledItem(new GUIContent("Paste"));
				menu.ShowAsContext();
				current.Use();
			}
		}
	}

	public static void WriteVectorToClipboard(Vector3 _v)
	{
		GUIUtility.systemCopyBuffer = $"Vector3{_v}";
	}

	public static bool CanReadVectorFromClipboard()
	{
		return GUIUtility.systemCopyBuffer.StartsWith("Vector3(") && GUIUtility.systemCopyBuffer.EndsWith(")");
	}

	public static Vector3 ReadVectorFromClipboard()
	{
		var s = GUIUtility.systemCopyBuffer;
		s = s["Vector3(".Length..^1];
		var bits = s.Split(',');
		var v = Vector3.zero;
		if (bits.Length >= 3)
		{
			v.x = floatinv.Parse(bits[0]);
			v.y = floatinv.Parse(bits[1]);
			v.z = floatinv.Parse(bits[2]);
		}
		return v;
	}
}
#endif

public static class Extensions {
	public static string Path(this Transform _this) { return _this?.parent?.parent?.name + "/" + _this?.parent?.name + "/" + _this?.name; }

	public static Type GetUnderlyingType(this MemberInfo member)
	{
		switch (member.MemberType)
		{
			case MemberTypes.Event:
				return ((EventInfo)member).EventHandlerType;
			case MemberTypes.Field:
				return ((FieldInfo)member).FieldType;
			case MemberTypes.Method:
				return ((MethodInfo)member).ReturnType;
			case MemberTypes.Property:
				return ((PropertyInfo)member).PropertyType;
			default:
				throw new ArgumentException
				(
					"Input MemberInfo must be if type EventInfo, FieldInfo, MethodInfo, or PropertyInfo"
				);
		}
	}
	
	public static string ConvertMultiplierToString(this float _multiplier, bool _hideIfOne = false)
	{
		double mult = Math.Round(_multiplier, 2);
		var format = "P2";
		if((mult*1000) % 100 == 0) format = "P0";
		if(mult == 1)
		{
			if(_hideIfOne)
				return "";
			return $"{(mult-1).ToString(format)}";
		}
		if(mult > 1)
			return $"<color={MAGUIBase.GreenColor}>+{(mult - 1).ToString(format)}</color>";
		return $"<color={MAGUIBase.RedColor}>-{(1-mult).ToString(format)}</color>";
	}
	
	public static string Path(this Transform _this, int _depth)
	{
		var s = "";
		for (int i = 0; i < _depth; ++i)
		{
			if (_this == null) break;
			s = $"{_this.name}/{s}";
			_this = _this.parent;
		}
		return s;
	}
	public static void ResetLocal(this Transform t) {
		t.localPosition = Vector3.zero;
		t.localRotation = Quaternion.identity;
		t.localScale = Vector3.one;
	}
	

	public static void SetLossyScale(this Transform _t, Vector3 _lossyScale)
	{
		var parentLossyScale = _t.parent.lossyScale;
		var plsSqrd = Vector3.Scale(parentLossyScale, parentLossyScale);
		if (plsSqrd.x > .001f && plsSqrd.y > .001f && plsSqrd.z > .001f)
		{
			var newLocalScale = new Vector3(_lossyScale.x / parentLossyScale.x, _lossyScale.y / parentLossyScale.y, _lossyScale.z / parentLossyScale.z);
			_t.localScale = newLocalScale;
		}
	}	

	public static bool Is200Range(this System.Net.HttpStatusCode _code)
	{
		return ((int)_code >= 200 && (int)_code < 300);
	}

	public static Vector3 ScreenToCanvasPosition(this Canvas canvas, Vector3 screenPosition)
	{
		var viewportPosition = new Vector3(screenPosition.x / Screen.width,
			screenPosition.y / Screen.height,
			0);
		return canvas.ViewportToCanvasPosition(viewportPosition);
	}

	public static Vector3 ViewportToCanvasPosition(this Canvas canvas, Vector3 viewportPosition)
	{
		var centerBasedViewPortPosition = viewportPosition - new Vector3(0.5f, 0.5f, 0);
		var canvasRect = canvas.GetComponent<RectTransform>();
		var scale = canvasRect.sizeDelta;
		return Vector3.Scale(centerBasedViewPortPosition, scale);
	}
	
	public static Vector2 WorldToCanvas(this Canvas canvas,
		Vector3 world_position,
		Camera camera = null)
	{
		var c3D = WorldToCanvas3D(canvas, world_position, camera);
		return new Vector2(c3D.x, c3D.y);
	}

	public static Vector3 WorldToCanvas3D(this Canvas canvas,
										Vector3 world_position,
										Camera camera = null)
	{
		if (camera == null)
		{
			camera = Camera.main;
		}

		//var canvas_rect = canvas.gameObject.transform.parent.GetComponent<RectTransform>();
		var canvas_rect = canvas.GetComponent<RectTransform>();
		var viewport_position = camera.WorldToViewportPoint(world_position);

		var canvasSize = canvas_rect.rect.size;// / canvas.scaleFactor;
		Vector2 proportionalPosition = new Vector2(viewport_position.x * canvasSize.x, viewport_position.y * canvasSize.y);

		var uiOffset = new Vector2((float)canvasSize.x * canvas_rect.pivot.x, (float)canvasSize.y * canvas_rect.pivot.y);
		         
         // Set the position and remove the screen offset
         var res = proportionalPosition - uiOffset;
         return new Vector3(res.x, res.y, viewport_position.z);
	}

	public static bool IsChildOf(this Transform _this, Transform _parent, Transform _exclude) {
		while (_this != null) {
			if (_this == _parent) return true;
			if (_this == _exclude) return false;
			_this = _this.parent;
		}
		return false;
	}

	public static long ConvertToLong(this float _value, int _digits = 2)
	{
		return (long)(_value * Mathf.Pow(10,_digits));
	}
	
	public static float ConvertToFloat(this long _value, int _digits = 2)
	{
		return _value / Mathf.Pow(10,_digits);
	}
	
	public static float Round(this float _value, int _digits)
	{
		float mult = Mathf.Pow(10.0f, (float)_digits);
		return Mathf.Round(_value * mult) / mult;
	}
	
	public static void SetActivityOfChildren(this Transform _transform, bool _enable)
	{
		for (int c1 = _transform.childCount - 1; c1 >= 0; c1--)
			_transform.GetChild(c1).gameObject.SetActive(_enable);
	}

	public static Transform GetHeadTransform(this Transform _this) {
		return _this.FindChildRecursiveByName("Head");
	}

	// Compensate a lerp value for the current frame rate, assuming a base rate of 60fps
	public static float TCLerp(this float _this, float _dt = -1)
	{
		if (_dt < 0) _dt = Time.deltaTime;
		return 1 - Mathf.Pow(1 - _this, _dt * 60);
	}

	// Returns integer 0 if _this <= _edge otherwise integer 1
	[System.Runtime.CompilerServices.MethodImpl(System.Runtime.CompilerServices.MethodImplOptions.AggressiveInlining)]
	public static int StepToInt(this float _this, float _edge)
	{
		return (int)(Unity.Mathematics.math.asuint(_edge - _this) >> 31);
	}

	public	static	bool	IsZero(this float _value) {
		const float eps = 0.00001f;
		return	_value * _value <= eps * eps; 
	}

	public static bool IsAbsLessThan(this float _value, float _epsilon)
	{
		return _value * _value < _epsilon * _epsilon;
	}
	
	public static int Sign(this float _a)
	{
		if (_a < 0) return -1;
		if (_a > 0) return 1;
		return 0;
	}

	public	static	float	Remainder(this float _value)
	{
		return	_value - Mathf.Floor (_value);
	}
	
	public static string FormatTime(this double totalSeconds)
	{
		TimeSpan time = TimeSpan.FromSeconds(totalSeconds);

		if (time.TotalDays >= 1)
			return $"{(int)time.TotalDays} days, {time.Hours} hours";
		if (time.TotalHours >= 1)
			return $"{(int)time.TotalHours} hours, {time.Minutes} minutes";
		if (time.TotalMinutes >= 1)
			return $"{(int)time.TotalMinutes} minutes, {time.Seconds} seconds";
        
		return $"{(int)time.TotalSeconds} seconds";
	}
	
	public	static	string	ToTimeString(this float _time)
	{
		int	seconds = 	(int)((_time/60f-Mathf.Floor(_time/60f))*60f);
		int	minutes	=  	(int)(_time/60f)%60; 
		int	hours	= 	(int)(_time/(60f*60f));
		return	hours.ToString("D2")+":"+minutes.ToString("D2")+":"+seconds.ToString("D2");
	}

	public static string ToDHMS(this System.TimeSpan duration) {
		if(duration.Days > 0) {
			return string.Format("{0:d}d {1:d}h", duration.Days, duration.Hours);
		} else if(duration.Hours > 0) {
			return string.Format("{0:d}h {1:d}m", duration.Hours, duration.Minutes);
		} else if(duration.Minutes > 0) {
			return string.Format("{0:d}m {1:d}s", duration.Minutes, duration.Seconds);
		} else {
			return string.Format("{0:d}s", duration.Seconds);
		}
	}

	public static string ToHMSTimeString(this float _time)
    {
		int seconds = (int)(_time % 60f);
        int minutes = (int)(_time / 60f) % 60;
        int hours = (int)(_time / (60f * 60f));
        var ret = "";
        if (hours > 0)
        {
            ret += hours.ToString(hours > 9 ? "D2" : "D1") + "h ";
            if (minutes > 0)
                ret += minutes.ToString(minutes > 9 ? "D2" : "D1") + "m";
        }
        else
        {
            if (minutes > 0)
                ret += minutes.ToString(minutes > 9 ? "D2" : "D1") + "m ";
            if (seconds >= 0)
                ret += seconds.ToString(seconds > 9 ? "D2" : "D1") + "s";
        }
        return ret;
    }

	public static string ToPercentTimeString(this float _percent) {
		float percent = _percent * 100f;

		if(percent < 1f) {
			percent *= 60f;
			if(percent < 1f) {
				percent *= 60f;
				return percent.ToString("F2") + "% Per Hour";
			} else {
				return percent.ToString("F2") + "% Per Minute";
			}
		} else {
			return percent.ToString("F2") + "% Per Second";
		}
	}
	
	public static bool IsDown(this EKeyboardFunction _fn, int _subKey = 0) => KeyboardController.GetKeyDown(_fn, _subKey);
	public static bool IsHeld(this EKeyboardFunction _fn, int _subKey = 0) => KeyboardController.GetKeyHeld(_fn, _subKey);
	public static bool IsClicked(this EKeyboardFunction _fn, int _subKey = 0) => KeyboardController.GetKeyClicked(_fn, _subKey);
	public static bool AnyDown(this EKeyboardFunction _fn) => KeyboardController.AnyKeyDown(_fn);
	public static bool AnyHeld(this EKeyboardFunction _fn) => KeyboardController.AnyKeyHeld(_fn);
	public static bool AnyClicked(this EKeyboardFunction _fn) => KeyboardController.AnyKeyClicked(_fn);
	public static KeyCode GetAnyClickedKeyCode(this EKeyboardFunction _fn) => KeyboardController.GetKeyClicked(_fn);
	public static KeyCode Key(this EKeyboardFunction _fn, int _index = 0) => KeyboardController.Me.GetKey(_fn, _index);

    public static bool IsContentVisible(RectTransform viewPort, RectTransform content)
    {
        float[] parentCorners = GetRelevantCorners(viewPort);
        float[] objectCorners = GetRelevantCorners(content);

        //1 is the top corners y value, 0 is the bottom corners y value

        if (objectCorners[1] < parentCorners[0]) //objects top corners are below contents bottom corners 
            return false;
        if (objectCorners[0] > parentCorners[1]) //objects bottom corners are above contents top corners
            return false;

        return true;
    }

    static float[] GetRelevantCorners(RectTransform objectToFind)
    {
        RectTransform rect = objectToFind;
        Vector3[] rectCorners = new Vector3[4];
        rect.GetWorldCorners(rectCorners);

        return new float[2] { rectCorners[0].y, rectCorners[2].y };
    }

    public static string ToHMSTimeString(this System.TimeSpan _time) {
		float seconds = (float)_time.TotalSeconds;
		return seconds.ToHMSTimeString();
	}

	public	static string ToTimeString(this System.TimeSpan _time) {
		float seconds = (float)_time.TotalSeconds;
		return seconds.ToTimeString();
	}
	public	static	string	ToDateString(this float _time, int _digits = 2)
	{
		string[]	append = new string[] 		{  	"s ", 	"m ", 		"h ", 			"D ", 		"W ", 		"Y ", 			"C ",		};
		float[] 	results = new float[append.Length];


		results [6] =	_time * (1/100f);				//Centuries
		results	[5] = results[6].Remainder() * 100f;	//weeks
		results	[4] = results[5].Remainder() * 52f;		//weeks
		results	[3] = results[4].Remainder() * 7f;		//days
		results	[2] = results[3].Remainder() * 24f;		//hours
		results	[1] = results[2].Remainder() * 60f;		//minutes
		results	[0] = results[1].Remainder() * 60f;		//seconds

		string	result_string = "";
		for (int c1 = results.Length - 1; c1 >= 0 && _digits > 0; c1--)
		{
			float	res = Mathf.Floor (results [c1]);
			if (res >= 1f || c1 == 0)
			{
				result_string = result_string + res.ToString () + append [c1];
				_digits--;
			}
		}
		if (result_string.Length > 1)
			result_string = result_string.Remove(result_string.Length - 1);
		return		result_string;
	}

	public static string ToMoneyString(this long _value, string _prefix = null)
	{
		if (_prefix == null) _prefix = GlobalData.CurrencySymbol;
		string negative = string.Empty;
		if (_value < 0)
		{
			negative = "-";
			_value = -_value;
		}
		
        return	string.Format("{2}{1}{0:#,##0}", _value, _prefix, negative);
	}
	
    public static string ToMoneyString(this int _value, string _prefix = null)
	{
		if (_prefix == null) _prefix = GlobalData.CurrencySymbol;
		string negative = string.Empty;
		if (_value < 0)
		{
			negative = "-";
			_value = -_value;
		}

		return string.Format("{2}{1}{0:#,##0}", _value, _prefix, negative);
	}
	
	public	static	string	ToMoneyString(this float _value, string _prefix = null, bool _allowDecimals=false)
	{
		if (_prefix == null) _prefix = GlobalData.CurrencySymbol;
		if (_allowDecimals)
		{
			return	string.Format("{1}{0:#,##0.00}", _value, _prefix);
		}

		return	string.Format("{1}{0:#,##0}", _value, _prefix);
	}

	public static string ToCurrencyString(this float _value, int _remainderSize = 20, string _prefix = null, bool _allowDecimals = true)
	{
		if (_prefix == null) _prefix = GlobalData.CurrencySymbol;
		if (_allowDecimals)
		{
			var signPrefix = "";
			if (_value < 0)
			{
				_value = -_value;
				signPrefix = "-";
			}
			var remainder = Mathf.FloorToInt((_value - (int) _value) * 100);
			var t = String.Format("{0:00}", remainder);
			var txt = $"{signPrefix}{_prefix}{(int)_value}<size={_remainderSize}%>.{t}</size>";
			return txt;
		}
		return	string.Format("{1}{0:#,##0}", _value, _prefix);
	}
	
	public	static	string	ToScoreString(this float _value, string _format = "F0")
	{
		// 999 or 99k or 99M
		string[]	append = new string[] { "", "K", "M", "B", "T", "Z", "ERR" };
		int c1 = 0;
		for (; c1 < append.Length-1; c1++)
		{
			if (_value / 1000 < 1)
				break;
			_value /= 1000f;
		}
		return		_value.ToString (_format) + append [c1];
	}

	public static Vector2 ToVector2(this Vector3 _value)
	{
		return new Vector2(_value.x, _value.z);
	}
	public	static	Vector3 ToVector3(this Vector2 _value)
	{
		return new Vector3(_value.x, 0f, _value.y);
	}
	
	public	static	Vector3 ToVector3Screen(this Vector2 _value)
	{
		return new Vector3(_value.x,_value.y,0);
	}

	public	static	Transform	DestroyChildren(this Transform _transform, bool _disable = true, int _remaining = 0)
	{
		for(int c1 = _transform.childCount-1; c1 >= _remaining; c1--)
        {
            var go = _transform.GetChild(c1).gameObject;
            if(_disable) go.SetActive(false);
			GameObject.Destroy (go);
        }
		return	_transform;
	}
	public	static	Transform	DestroyChildrenImmediately(this Transform _transform, bool _disable = true)
	{
		for(int c1 = _transform.childCount-1; c1 >= 0; c1--)
        {
            var go = _transform.GetChild(c1).gameObject;
            if(_disable) go.SetActive(false);
			GameObject.DestroyImmediate(go, true);
        }
		return	_transform;
	}
	
	public static float DistanceSq(this Vector3 a, Vector3 b)
	{
		return (a - b).sqrMagnitude;
	}
	
	public static float DistanceXZSq(this Vector3 a, Vector3 b)
	{
		return (a - b).xzSqrMagnitude();
	}
	
	public static float DistanceSq(this Vector2 _a, Vector2 _b)
	{
		return (_a - _b).SqrMagnitude();
	}

	public	static	Transform	DestroyImmediateChildren(this Transform _transform)
	{
		return _transform.DestroyChildrenImmediately(false);
	}
	public static T FindComponentInParents<T>(this Transform _this)
	{
		T target = default(T);
		var transform = _this.parent;
		while(transform != null)
		{
			target = transform.GetComponent<T>();
			if (target != null)
				return target;
			transform = transform.parent;
		}
		return target;
	}
	
	public static void IgnoreDistrictFilter(this GameObject _o, bool _ignore = true)
	{
		float ignoreValue = _ignore ? 1 : 0;
		foreach (var rnd in _o.GetComponentsInChildren<Renderer>(true))
			foreach (var mat in rnd.materials)
				if (mat != null)
					mat.SetFloat("_IgnoreDistrictFilter", ignoreValue);
	}

	public static void IgnoreDistrictFilterShared(this GameObject _o, bool _ignore = true)
	{
		float ignoreValue = _ignore ? 1 : 0;
		foreach (var rnd in _o.GetComponentsInChildren<Renderer>(true))
			foreach (var mat in rnd.sharedMaterials)
				if (mat != null)
					mat.SetFloat("_IgnoreDistrictFilter", ignoreValue);
	}
	
	public static void StartRotating(this GameObject _o, float _speed, System.Func<bool> _cancel = null) {
		var mb = _o.GetComponentInChildren<MonoBehaviour>();
		if (mb != null)
			mb.StartCoroutine(Co_Rotate(_o, _speed, _cancel));
	}
	public static IEnumerator Co_Rotate(GameObject _o, float _speed, System.Func<bool> _cancel) {
		Vector3 eulers = _o.transform.localEulerAngles;
		while (true) {
			if (_cancel != null && _cancel()) break;
			eulers.y += _speed * Time.deltaTime;
			if (eulers.y > 180) eulers.y -= 360;
			else if (eulers.y < -180) eulers.y += 360;
			_o.transform.localEulerAngles = eulers;
			yield return null;
		}
	}
	#if UNITY_EDITOR
	public static void DrawExtentsGizmo(this GameObject _o)
	{
		var bounds = ManagedBlock.GetTotalVisualBounds(_o);
		Gizmos.color = new Color(.2f, .3f, .6f, .4f);
		Gizmos.DrawCube(bounds.center, bounds.size);
		Gizmos.color = new Color(.2f, .3f, .6f, 1);
		Gizmos.DrawWireCube(bounds.center, bounds.size);
	}
	#endif
	public static void PositionAndScale(this GameObject _o, Vector3 _bottomCenterPosition, float _requiredSize, bool _moveChildren = false, float _maxYSize = 0, float _maxScale = 0) {
		_o.transform.localScale = Vector3.one;
		var bounds = ManagedBlock.GetTotalVisualBounds(_o);
		var bottomCenter = _o.transform.position; 
		if (bounds.size.sqrMagnitude > 0) {
			float biggestAxisExtent = Mathf.Max(bounds.extents.x, bounds.extents.z);
			float yExtent = bounds.extents.y;
			float scale = _requiredSize / biggestAxisExtent;
			if (_maxYSize > 0 && yExtent > 0)
			{
				float yScale = _maxYSize / yExtent;
				scale = Mathf.Min(scale, yScale);
			}
			if (_maxScale > 0) scale = Mathf.Min(_maxScale, scale);
			_o.transform.localScale = Vector3.one * scale;
			bounds = ManagedBlock.GetTotalVisualBounds(_o);
			bottomCenter = bounds.center - Vector3.up * bounds.extents.y;
		}
		var translate = _bottomCenterPosition - bottomCenter;
		if (_moveChildren) {
			foreach (Transform t in _o.transform) t.position += translate;
		} else {
			_o.transform.position += translate;
		}
	}
	public static void PositionAndScale(this GameObject _o, Vector3 _bottomCenterPosition, Vector3 _requiredSize, bool _moveChildren = false) {
		_o.transform.localScale = Vector3.one;
		var bounds = ManagedBlock.GetTotalVisualBounds(_o);
		var bottomCenter = _o.transform.position;
		Vector3 size = _requiredSize;
		if (bounds.size.sqrMagnitude > 0) {
			bottomCenter = bounds.center - Vector3.up * bounds.extents.y;
			size = Vector3.Max(size, bounds.extents);
		}
		float scaleX = _requiredSize.x / size.x;
		float scaleY = _requiredSize.y / size.y;
		float scaleZ = _requiredSize.z / size.z;
		float scale = Mathf.Min(scaleX, Mathf.Min(scaleY, scaleZ));
		var translate = (_bottomCenterPosition - bottomCenter) * scale;
		if (_moveChildren) {
			foreach (Transform t in _o.transform) t.position += translate;
		} else {
			_o.transform.position += translate;
		}
		_o.transform.localScale = Vector3.one * scale;
	}

	public static bool IsSmallDraggable(this GameObject _this)
	{
		if (_this.GetComponentInParent<NGMovingObject>() != null) return true;
		if (_this.GetComponentInParent<DTDragPalette>() != null) return true;
		if (_this.GetComponentInParent<DTDragBlock>() != null) return true;
		return false;
	}

	public static T FindComponentInParents<T>(this GameObject _this)
	{
		if (_this == null)
			return default;
		return _this.transform.FindComponentInParents<T>();
	}
	
	public static bool IsChildOf(this GameObject _this, GameObject _test) {
		if (_this == _test) return true;
		if (_this.transform.parent == null) return false;
		return _this.transform.parent.gameObject.IsChildOf(_test);
	}

	public static void FastUnorderedRemoveAt<T>(this List<T> _this, int _index)
	{
		if (_this.Count > _index) _this[_index] = _this[_this.Count - 1];
		_this.RemoveAt(_this.Count - 1);
	}

	public static List<TTo> ToListType<TFrom, TTo>(this List<TFrom> _thisList) where TTo : TFrom
	{
		List<TTo> toList = new List<TTo>(_thisList.Count);
		foreach(TFrom entry in _thisList)
		{
			if (entry is TTo) // don't try casting to TTo unless you know it's possible otherwise it'll throw
				toList.Add((TTo)entry);
		}
		return toList;
	}
	
	public static T PickRandom<T>(this T[] _array)
	{
		if (_array == null || _array.Length == 0)
			return default;
		return _array[UnityEngine.Random.Range(0, _array.Length)];
	}

	// Add element to a list only if it's a unique element.
	// Returns true if added, false if it's already added.
	public static bool AddUnique<T>( this List<T> _array, T _target )
	{
		if( _target == null || _array.Contains(_target) ) {
			return false;
		}

		_array.Add( _target );
		return true;
	}

	public static T PickRandom<T>(this T[] _array, List<T> _objectsToIgnore)
	{
		if (_array == null || _array.Length <= _objectsToIgnore.Count)
			return default(T);
		int index = UnityEngine.Random.Range(0, _array.Length);
		while (_objectsToIgnore.Contains(_array[index]))
			index = UnityEngine.Random.Range(0, _array.Length);
		return _array[index];
	}

	public static T PickRandom<T>(this T[] _array, params T[] _objectsToIgnore)
	{
		if (_array == null || _array.Length <= _objectsToIgnore.Length)
			return default(T);
		int index = UnityEngine.Random.Range(0, _array.Length);
		while (_objectsToIgnore.Contains(_array[index]))
			index = UnityEngine.Random.Range(0, _array.Length);
		return _array[index];
	}

	public static T PickRandom<T>(this T[] _array, params int[] _indeciesToIgnore)
	{
		if (_array == null || _indeciesToIgnore.Length >= _array.Length)
			return default(T);
		int index = UnityEngine.Random.Range(0, _array.Length);
		while (_indeciesToIgnore.Contains(index))
			index = UnityEngine.Random.Range(0, _array.Length);
		return _array[index];
	}

	public static T PickRandom<T>(this List<T> _list)
	{
		if (_list.IsNullOrEmpty())
			return default(T);
		return _list[UnityEngine.Random.Range(0, _list.Count)];
	}	
	
	public static List<string> ConvertToStringList<T>(this List<T> items, System.Func<T, string> returnString)
	{
		Assert.IsNotNull(returnString, "GetMemberNames - Helper method given null return-Func reference.");
		Assert.IsNotNull(items, "GetMemberNames - Helper method given null list.");
		
		List<string> uniqueMemberNames = new List<string>();
		for (int i = 0; i < items.Count; i++)
			uniqueMemberNames.Add(returnString(items[i]));

		//List<string> testList = ConvertToTypeList<T, string>(items, returnString);
		return uniqueMemberNames;
	}
	
	/// <summary>
	/// This Converts to a list of non-null references only
	/// </summary>
	public static List<TOut> ConvertToTypeList<TIn, TOut>(this List<TIn> _input, Func<TIn, TOut> _convert)
	{
		Assert.IsNotNull(_convert, "GetMemberNames - Helper method given null return-Func reference.");
		Assert.IsNotNull(_input, "GetMemberNames - Helper method given null list.");
		return ConvertToTypeIEnumerable(_input, _convert) as List<TOut>;
	}	
	
	public static IEnumerable<TOut> ConvertToTypeIEnumerable<TIn, TOut>(this IEnumerable<TIn> _input, Func<TIn, TOut> _convert)
	{
		Assert.IsNotNull(_convert, "GetMemberNames - Helper method given null return-Func reference.");
		Assert.IsNotNull(_input, "GetMemberNames - Helper method given null list.");
		
		List<TOut> output = new List<TOut>();
		int i = 0;
		foreach (TIn inp in _input)
		{
			TOut converted = _convert(inp);
			if(converted != null) output.Add(converted);
			i++;
		}

		return output;
	}
	
	public static TOut[] ConvertToTypeArray<TIn, TOut>(this List<TIn> _input, Func<TIn, TOut> _convert)
	{
		Assert.IsNotNull(_convert, "GetMemberNames - Helper method given null return-Func reference.");
		Assert.IsNotNull(_input, "GetMemberNames - Helper method given null list.");
		
		TOut[] output = new TOut[_input.Count];
		for(int i = 0; i < _input.Count; i++)
		{
			TOut converted = _convert(_input[i]);
			if(converted != null) output[i] = converted;
		}

		return output;
	}
	public static List<T> GetWithoutDuplicates<T>(this List<T> items)
	{
		Assert.IsNotNull(items, "RemoveDuplicatesSet - Helper method given null list.");

		var result = new List<T>();
		var set = new HashSet<T>();
		for (int i = 0; i < items.Count; i++)
			if (!set.Contains(items[i]))
			{
				result.Add(items[i]);
				set.Add(items[i]);
			}
		return result;
	}

	public static Vector3 GetVector3XZ(this Vector2 _this) {
		return new Vector3(_this.x, 0, _this.y);
	}
	public static Vector3 GetVector3XY(this Vector2 _this) {
		return new Vector3(_this.x, _this.y, 0);
	}
	public static Vector3 GetXZ(this Vector3 v)
	{
		Vector3 temp = v;
		temp.y = 0;
		return temp;
	}
	public static Vector3 GetXZNorm(this Vector3 _v)
	{
		Vector3 temp = _v;
		temp.y = 0;
		return temp.normalized;
	}
	public static Vector3 PerpendicularXZ(this Vector3 a) {
		return new Vector3( a.z, a.y, -a.x );
	}


	public	static 	Vector3		SetYToHeight( this Vector3 _vector3 )
	{
		return GameManager.Me.VectorAtPoint(_vector3);
	}


	public static Vector2 GetXZVector2(this Vector3 _this) {
		return new Vector2(_this.x, _this.z);
	}
	public static Vector3 RotateAbout(this Vector3 _this, Vector3 _about, float _byRadians) {
		float c = Mathf.Cos(_byRadians), s = Mathf.Sin(_byRadians);
		return _this * c + Vector3.Cross(_about, _this) * s + _about * (Vector3.Dot(_about, _this) * (1-c));
	}

	public static int Hash(this Vector3 _this, int _baseValue = 0x73717)
	{
		int Rotate(int _a, int _by) => (_a << _by) | (_a >> (32 - _by));
		int x = System.BitConverter.SingleToInt32Bits(_this.x);
		int y = System.BitConverter.SingleToInt32Bits(_this.y);
		int z = System.BitConverter.SingleToInt32Bits(_this.z);
		int hash = _baseValue ^ x; hash = Rotate(hash, 7);
		hash = hash * 0x3717 ^ y; hash = Rotate(hash, 7);
		hash = hash * 0x3717 ^ z; hash = Rotate(hash, 7);
		return hash;
	}

	public static string AddKeyValue(this string _this, string _key, string _value, string _separator = ":") {
		return (_this ?? "") + _separator + _key + _separator + _value;
	}
	public static string GetKeyValue(this string _this, string _key, string _separator = ":") {
		var bits = _this.Split(new string[] { _separator }, StringSplitOptions.None);
		for (int i = 0; i < bits.Length / 2; ++i) {
			if (bits[1+i*2+0] == _key) return bits[1+i*2+1];
		}
		return null;
	}
	
	public static string SanitiseFilename(this string _this)
	{
		_this = _this.Replace(" ", "_");
		for (int i = _this.Length - 1; i >= 0; --i)
		{
			var chr = _this[i];
			if (Char.IsLetterOrDigit(chr) == false && chr != '_')
				_this = _this.Remove(i, 1);
		}
		return _this;
	}

	public static string FrameId(this string _this) => $"{_this}_{Time.frameCount}";
	public static string TimeId(this string _this) => $"{_this}_{Time.time}";
	public static string Id(this GameObject _obj, string _label) => $"{_label}_{_obj.GetInstanceID()}";
	
	private static System.Collections.Generic.Dictionary<string, int> s_lookup = new();
	public static int ShaderId(this string _this)
	{
		if (s_lookup.TryGetValue(_this, out int id)) return id;
		id = Shader.PropertyToID(_this);
		s_lookup[_this] = id;
		return id;
	}

	public static bool IsEmptyDesign(this string _this)
	{
		if (string.IsNullOrEmpty(_this)) return true;
		if (_this.StartsWith("0|")) return true;
		if (_this.StartsWith("1|#")) return true; // empty plot indicator
		return false;
	}

	public static string Esc(this string _s)
	{
		return UnityEngine.Networking.UnityWebRequest.EscapeURL(_s);
	}
	
	public static string FirstCharToLower(this string _input)
    {
		if (char.IsLetter(_input[0]) && char.IsUpper(_input[0]))
			return char.ToLower(_input[0]) + _input.Substring(1);
		return _input;
	}

	public static bool IsNullOrWhiteSpace(this string value)
	{
		if (value == null) return true;
		return string.IsNullOrEmpty(value.Trim());
	}

	public static bool IsValidTimeValue(this string value, out float timeValue)
	{
		timeValue = 0.0f;
		if (value == null)
		{
			return false;
		}
		if (float.TryParse(value.Trim(), out float result))
		{
			timeValue = result;
			return true;
		}
		return false;
	}
	
	public static string PulseColour(this string _this, UInt32 _colour1, UInt32 _colour2, float _speed = 1)
	{
		var lerp = Mathf.Sin(Time.unscaledTime * 5.0f * _speed) * .5f + .5f;
		var b = (int)Mathf.Lerp((_colour1 >> 0) & 0xFF, (_colour2 >> 0) & 0xFF, lerp);
		var g = (int)Mathf.Lerp((_colour1 >> 8) & 0xFF, (_colour2 >> 8) & 0xFF, lerp);
		var r = (int)Mathf.Lerp((_colour1 >> 16) & 0xFF, (_colour2 >> 16) & 0xFF, lerp);
		return $"<color=#{r:X2}{g:X2}{b:X2}>{_this}</color>";
	}
	
	public static string Truncate(this string _this, int _maxLength)
	{
		if (string.IsNullOrEmpty(_this)) return _this;
		return _this.Length <= _maxLength ? _this : _this.Substring(0, _maxLength); 
	}
	
	public static string ToHMS(this float _value)
	{
		return ToHMS((int) _value);
	}
	public static string ToHMS(this int _value)
	{
		TimeSpan t = TimeSpan.FromSeconds(_value);
		string result;
		if (t.Days > 0)
			result = $"{t.Days}d {t.Hours}h";
		else if (t.Hours > 0)
			result = $"{t.Hours}h {t.Minutes}m";
		else if (t.Minutes > 0)
			result = $"{t.Minutes}m {t.Seconds}s";
		else
			result = $"{t.Seconds}s";
		return result;
	}

	const string SINGLE_SPACE = " ";
	public static string RemoveWhiteSpaceAndToLower(this string _source)
	{
		if (_source == null)
			return null;
		return _source.Replace(SINGLE_SPACE, string.Empty).Replace("\t", string.Empty).ToLower();
	}


	// Recursive search of the child by name.
	public static Transform FindChildRecursiveByName(this Transform ThisGObj, string ThisName)	
	{	
		Transform ReturnObj;
		
		// If the name match, we're return it
		if( ThisGObj.name == ThisName )	
			return ThisGObj.transform;
		
		// Else, we go continue the search horizontaly and verticaly
		foreach( Transform child in ThisGObj )	
		{	
			ReturnObj = child.FindChildRecursiveByName( ThisName );
			
			if( ReturnObj != null )	
				return ReturnObj;	
		}
		
		return null;	
	}
	
	public static List<Transform> FindChildrenRecursiveByNameContains(this Transform _thisGObj, string _lookingForName, bool _ignoreCase = false)	
	{	
		List<Transform> returnObjs = new List<Transform>();
		_lookingForName = _ignoreCase ? _lookingForName.ToLower() : _lookingForName;
		string thisGOName = _ignoreCase ? _thisGObj.name.ToLower() : _thisGObj.name;
		// If the name match, we're return it
		if(thisGOName.Contains(_lookingForName))
		{
			returnObjs.Add(_thisGObj.transform);
		}

		// Else, we go continue the search horizontaly and verticaly
		foreach (Transform child in _thisGObj)	
		{	
			returnObjs.AddRange(child.FindChildrenRecursiveByNameContains(_lookingForName, _ignoreCase));
		}
		
		return returnObjs;	
	}
	
	public static T FindRecursiveByName<T>(this Transform _this_transform, string _name) where T:class 
	{	
		T ReturnObj = default(T);
		
		// If the name match, we're return it
		if( _this_transform.name == _name )	
			return _this_transform.GetComponent<T>();
		
		// Else, we go continue the search horizontaly and verticaly
		foreach( Transform child in _this_transform )	
		{	
			ReturnObj = child.FindRecursiveByName<T>( _name );
			
			if( ReturnObj != null )	
				return ReturnObj;	
		}
		
		return null;	
	}

	public static void DoNextFrame(this MonoBehaviour _this, System.Action _action)
	{
		_this.StartCoroutine(Utility.Co_DoNextFrame(_action));
	}

	public static void DoAfter(this MonoBehaviour _this, float _delay, System.Action _action)
	{
		_this.StartCoroutine(Utility.Co_DoAfter(_delay, _action));
	}

	public static void Do(this MonoBehaviour _this, System.Func<bool> _action)
	{
		_this.StartCoroutine(Utility.Co_Do(_action));
	}

	public static void DoFor(this MonoBehaviour _this, float _duration, Func<float, bool> _action, Action _finalAction)
	{
		_this.StartCoroutine(Utility.Co_DoFor(_duration, _action, _finalAction));
	}

	public static void DoPostLoad(this MonoBehaviour _this, System.Action _action)
	{
		_this.StartCoroutine(Utility.Co_DoPostLoad(_action));
	}
	
	public static bool ValidateMemberInChildren<T>( this Component _component, ref T _member, bool _includeInactive = false ) {
		return _member != null || (_member = _component.GetComponentInChildren<T>(_includeInactive)) != null;
	}

	public static Coroutine StartCoAction(this MonoBehaviour _this, System.Func<bool> _action)
	{
		return _this.StartCoroutine(Co_RunAction(_action));
	}

	private static IEnumerator Co_RunAction(System.Func<bool> _action)
	{
		while (_action())
			yield return null;
	}
	

	public static bool Contains(this string[] _in, string _what)
	{
		foreach(var i in _in) if(_what == i) return true;
		return false;
	}

	public static bool Contains(this string[] _in, string[] _what)
	{
		foreach(var i in _in)
			foreach(var w in _what)
				if(i == w) return true;
		return false;
	}

	public static bool Contains(this List<string> _in, List<string> _what)
	{
		foreach(var i in _in)
			foreach(var w in _what)
				if(i == w) return true;
		return false;
	}
	
	public static bool Contains<T>(this List<T> _in, List<string> _what, Func<T, string> _getCompareString)
	{
		if(_getCompareString == null)
			return false;
		foreach(var i in _in)
			foreach(var w in _what)
				if(_getCompareString(i) == w) return true;
		return false;
	}

	public static bool IsNullOrEmpty<T>(this T[] _array)
	{
		return _array == null || _array.Length == 0;
	}

	public static bool IsNullOrEmpty<T>(this List<T> _list)
	{
		return _list == null || _list.Count == 0;
	}

	public static List<T> StringToList<T>(this string _source, Func<string, T> _converter)
	{
		var results = new List<T>();
		if (_source is null)
			return results;
		foreach (var s in _source.Split(',', '|', '\n', ';'))
		{
			var con = _converter(s);
			if(con != null)
				results.Add(con);
		}

		return results;
	}

	public static string FirstChrs(this string _this, int _n)
	{
		if (_this.Length <= _n) return _this;
		return _this[.._n];
	}

	public static T Find<T>(this T[] _array, Predicate<T> _match)
	{
		if (_array == null || _match == null)
			return default(T);
		for (int i = 0; i < _array.Length; i++)
			if (_match(_array[i]))
				return _array[i];
		return default(T);
	}

	public static bool Contains<T>(this T[] _array, T _target)
	{
		if (_array == null)
			return false;
		for (int i = 0; i < _array.Length; i++)
		{
			if (_array[i] == null && _target == null)
				continue;
			if (_array[i] != null && _array[i].Equals(_target))
				return true;
		}
		return false;
	}
		
	public static bool Contains<T>(this T[] _array, System.Func<T, bool> _check)
	{
		if (_array == null || _check == null)
			return false;

		for (int i = 0; i < _array.Length; i++)
			if (_check (_array [i]))
				return true;

		return false;
	}

	public static string MakeNice(this string _source)
    {
		_source = _source.Trim();
		if (_source.StartsWith("NG"))
			_source = _source.Substring(2);
		var nice = "";
		for (int i = 0; i < _source.Length; i++)
        {
			var c = _source[i];
			if (char.IsUpper(c) && i > 0)
				nice += " ";
			nice += c;
		}
		return nice;
	}

	public static string Pluralise(this string str)
	{
		return str + "s";
	}

	public static string Depluralise(this string str)
	{
		if (str[str.Length - 1] == 's')
			return str.Remove(str.Length - 1);
		return str;
	}

	public static string ToRTBold( this string _string )
	{
		return $"<b>{_string}</b>";
	}

	public static Vector3 ToVector3(this string _text) {
		if (_text.StartsWith ("(") && _text.EndsWith (")")) 
			_text = _text.Substring(1, _text.Length-2);
		var numbers = _text.Split(',');
		if(numbers.Length != 3) return Vector3.zero;
		var pos = new Vector3(float.Parse(numbers[0], CultureInfo.InvariantCulture), 
			float.Parse(numbers[1], CultureInfo.InvariantCulture), 
			float.Parse(numbers[2], CultureInfo.InvariantCulture));
		return pos;
	}
	
	public static Vector2 ToVector2(this string _text) {
		if (_text.StartsWith ("(") && _text.EndsWith (")")) 
			_text = _text.Substring(1, _text.Length-2);
		var numbers = _text.Split(',');
		if(numbers.Length != 2) return Vector3.zero;
		var pos = new Vector2(float.Parse(numbers[0], CultureInfo.InvariantCulture), 
			float.Parse(numbers[1], CultureInfo.InvariantCulture));
		return pos;
	}

	public static Vector3 ToCSVVector3(this string _text) { 
		var s = _text.Split('|');
		if(s.Length != 3) return Vector3.zero;
		var pos = new Vector3(float.Parse(s[0], CultureInfo.InvariantCulture), 
			float.Parse(s[1], CultureInfo.InvariantCulture), 
			float.Parse(s[2], CultureInfo.InvariantCulture));
		return pos;
	}
	public static string ToCSVString(this Vector3 _pos) {
		return _pos.x.ToString("F3")+"|"+_pos.y.ToString("F3")+"|"+_pos.z.ToString("F3");
	}

	public static string TrimQuote(this string s) {
		return s.Trim('\u201d', '\u2018', '\u2019', '\u201c', '\t', ' ', '\"', '\'');
	}

	public static float ToFloatInv(this string _s, float _default = 0)
	{
		if (_s.TryFloatInv(out var value))
			return value;
		return _default;
	}

	public static bool TryFloatInv(this string _s, out float _value)
	{
		return float.TryParse(_s, System.Globalization.NumberStyles.Float, System.Globalization.CultureInfo.InvariantCulture, out _value);
	}

	private static IFormatProvider s_cultureInv = System.Globalization.CultureInfo.InvariantCulture.NumberFormat;
	public static string ToStringInv<T>(this T _object, string _format = null)
	{
		return (_format == null) ? FormattableString.Invariant($"{_object}") : String.Format(s_cultureInv, $"{{0:{_format}}}", _object);
	}
	
	// Split a string according to our standard field separators, removing empty entries
	public static string[] SplitFields(this string _contents)
	{
		return _contents.Split(new [] { ':', ';', '|', '\n' }, StringSplitOptions.RemoveEmptyEntries);
	}
	public static List<string> SplitFirst(this string _contents, params char[] _characters)
	{
		var ret = new List<string>();
		if (_contents.IsNullOrWhiteSpace()) return ret;

		for (int i = 0; i < _contents.Length; i++)
		{
			var c = _contents[i];
			if (_characters.Contains(c))
			{
				if (i > 0)
					ret.Add(_contents.Substring(0, i));
				int j = _contents.Length - 1;
				for (; j > i; j--)
				{
					c = _contents[j];
					if (_characters.Contains(c))
					{
						var cl = j - i - 1;
						if(cl > 0)
							ret.Add(_contents.Substring(i+1, cl));
						else
							ret.Add("");
						break;
					}
				}
				if (j < _contents.Length - 1)
				{
					ret.Add(_contents.Substring(j+1, _contents.Length - j-1));
				}
				return ret;
			}
		}		
		ret.Add(_contents);
		return ret;
	}
	public static List<string> SplitSpecial(this string _contents, params char[] _characters)
	{
		var ret = new List<string>();
		var inQuote = false;
		var startIndex = 0;
		for (int i = 0; i < _contents.Length; i++)
		{
			var c = _contents[i];
			switch (c)
			{
				case '"':
					inQuote = !inQuote;
					break;
			}
			if (_characters.Contains(c) && inQuote == false)
			{
				ret.Add(_contents.Substring(startIndex, i - startIndex));
				startIndex = i + 1;
			}
		}
		if (startIndex < _contents.Length)
			ret.Add(_contents.Substring(startIndex));
		return ret;
	}
	
	static readonly string[] s_timeUnits = new string[] {"d", "h", "m", "s"};
	public static string PrettyTimeSpan(TimeSpan _span, bool _use2SigFig = true, bool _showFraction = false)
	{ 
		if(_span.Days > 0)
			if (_use2SigFig)
				return string.Format("{0}d {1}h", _span.Days, _span.Hours);
			else
				return string.Format("{0}d", _span.Days);
		if(_span.Hours> 0)
			if (_use2SigFig)
				return string.Format("{0}h {1}m", _span.Hours, _span.Minutes);
			else
				return string.Format("{0}h", _span.Hours);
		if(_span.Minutes > 0)
			if (_use2SigFig)
				return string.Format("{0}m {1}s", _span.Minutes, _span.Seconds);
			else
				return string.Format("{0}m", _span.Minutes);
		if (!_showFraction)
			return $"{_span.Seconds}s";
		
		float fracSeconds = _span.Seconds + _span.Milliseconds * .001f;
		var strSeconds = $"{fracSeconds:n2}";
		if (strSeconds.Contains('.'))
			strSeconds = strSeconds.TrimEnd('0').TrimEnd('.'); // note these are separate so we don't trim significant zeros
		return $"{strSeconds}s";
	}
	public static string PrettyTimeSpan(float _seconds, bool _use2SigFig = true, bool _showFraction = false)
	{
		int wholeSeconds = (int)_seconds;
		int ms = (int)((_seconds - wholeSeconds) * 1000);
		return PrettyTimeSpan(new TimeSpan(0, 0, 0, wholeSeconds, ms), _use2SigFig, _showFraction);
	}

	public static string ToShortTimeString(this float _seconds, bool _use2SigFig = true, bool _inhibitNegatives = false, bool _inhibitZero = false, bool _showFraction = false)
	{
		if (_seconds <= 0.0001f && _inhibitZero) return "-";
		if (_seconds < 0 && _inhibitNegatives) _seconds = 0;
		return PrettyTimeSpan(_seconds, _use2SigFig, _showFraction);
	}


	public	static	void	SetLayerRecursively(this GameObject _game_object, int _new_layer)
	{
		_game_object.layer = _new_layer;
		foreach( Transform t in _game_object.transform )
		{
			SetLayerRecursively( t.gameObject, _new_layer );
		}
	}

	public static void SetStaticRecursively(this GameObject _game_object, bool _isStatic)
	{
		_game_object.isStatic = _isStatic;
		foreach (Transform t in _game_object.transform)
		{
			SetStaticRecursively(t.gameObject, _isStatic);
		}
	}

	public static void EnableKeywordRecursive(this GameObject _go, string _keyword, bool _enable)
	{
		foreach (var r in _go.GetComponentsInChildren<Renderer>(true))
		{
			foreach (var m in r.materials)
			{
				if (_enable) m.EnableKeyword(_keyword);
				else m.DisableKeyword(_keyword);
			}
		}		
	}

	public static void StopAllCoroutines(this GameObject _g)
	{
		foreach (var mb in _g.GetComponents<MonoBehaviour>())
			mb.StopAllCoroutines();
	}

	public static T GetOrAddComponent<T>(this GameObject _g) where T : Component
	{
		return _g.TryGetComponent<T>(out var cmp) ? cmp : _g.AddComponent<T>();
	}

	public static Vector3 ClampToScreen(Vector3 _pos)
	{
		_pos.x = Mathf.Clamp(_pos.x, 0, Screen.width);
		_pos.y = Mathf.Clamp(_pos.y, 0, Screen.height);
		return _pos;
	}

	public static Ray RayAtScreenPosition(this Camera _this, Vector3 _pos, bool _includeFingerOffset = false) {
		return _this.ScreenPointToRay(_pos.ApplyFingerOffset(_includeFingerOffset));
	}
	public static Ray RayAtMouse(this Camera _this, bool _includeFingerOffset = false) {
		return _this.RayAtScreenPosition(TouchManager.FirstInputPosition, _includeFingerOffset);
	}

	public static Ray RayAtClampedMouse(this Camera _this, bool _includeFingerOffset = false)
	{
		return _this.RayAtScreenPosition(ClampToScreen(TouchManager.FirstInputPosition), _includeFingerOffset);
	}

	public static Ray RayAtMouse(this Camera _this, int _inputId, bool _includeFingerOffset = false) {
		return _this.RayAtScreenPosition(GameManager.InputPosition(_inputId), _includeFingerOffset);
	}

	public static bool IsTypeOrSubclassOf(this Type _this, Type _baseClass)
	{
		if (_baseClass == null) return false;
		if (_this == _baseClass) return true;
		return _this.IsSubclassOf(_baseClass);
	}


	public static int DigitCount(this int _value)
	{
		if (_value > -10 && _value < 10)
			return 1;
		return 1 + (_value / 10).DigitCount();
	}

	public static int DigitCount(this long _value)
	{
		if (_value > -10 && _value < 10)
			return 1;
		return 1 + (_value / 10).DigitCount();
	}

	public static int GetDigit(this int _number, int _digit)
	{
		return Mathf.Abs((_number / (int)Mathf.Pow(10, _digit)) % 10);
	}

	public static int GetDigit(this long _number, int _digit)
	{
		int result = (int)((_number / (long) Mathf.Pow(10, _digit)) % 10);
		if (result < 0)
			return -result;
		return result;
	}

	public static int 		SetDigit(this int _number, int _digit, int _value)
	{
		var number = _number < 0 ? -_number : _number;
		number = number + (int)Mathf.Pow(10f, _digit) * (_value - number.GetDigit(_digit));
		return _number < 0 ? -number : number;
	}

	public static long SetDigit(this long _number, int _digit, int _value)
	{
		long number = _number < 0 ? -_number : _number;
		number = number + (int) Mathf.Pow(10f, _digit) * (_value - number.GetDigit(_digit));
		return _number < 0 ? -number : number;
	}


	public static bool ShowAllMaterialInstancingCases = false;
	public static void ShowMaterialInstancePenaltyWarning(GameObject _o, bool _always = false) {
		if (_always || ShowAllMaterialInstancingCases)
			Debug.LogError($"MaterialInstancing occurring - watch the cost", _o);
	}
	public static void SetTintWindowColour(Transform _t, int _window, Color _colour, bool _includeSMRs = false, bool _applyUVOffsets = false, bool _enableUVOffsets = false) {
		if (_t != null) SetTintWindowColour(_t.gameObject, _window, _colour, _includeSMRs, _applyUVOffsets, _enableUVOffsets);
	}
	public static void SetTintWindowColour(Renderer _rnd, int _window, Color _colour, bool _applyUVOffsets, bool _enableUVOffsets, bool _ignoreIfAlreadyTinted) {
		if (_rnd == null) return;
		var mats = _rnd.materials;
		for (int x = 0; x < mats.Length; ++x)
		{
			if (mats[x] == null || !mats[x].HasProperty(Decoration.c_tint1ColourProperty))
				continue;
			Decoration.SetTintProperty(mats[x], _window, _colour);
		}
	}
	static void SetTintWindowColour(Renderer _rnd, int _window, Dictionary<Material, Color> _colour, bool _applyUVOffsets, bool _enableUVOffsets, bool _ignoreIfAlreadyTinted) {
		if (_rnd == null) return;
		var mats = _rnd.materials;
		for (int x = 0; x < mats.Length; ++x)
		{
			if (mats[x] == null || !mats[x].HasProperty(Decoration.c_tint1ColourProperty))
				continue;
			if (_colour.TryGetValue(mats[x], out Color c))
				Decoration.SetTintProperty(mats[x], _window, c);
		}
	}
	public static void SetTintWindowColour(GameObject _go, int _window, Color _colour, bool _includeSMRs = false,
		bool _applyUVOffsets = false, bool _enableUVOffsets = false, Transform _ignore = null, bool _ignoreIfAlreadyTinted = false) {
		if (_go != null) {
			int ignoreLayers = 1 << LayerMask.NameToLayer("BlobShadows");
			foreach(Transform child in _go.transform) {
				// Don't continue recursion for child object we want to ignore
				if(_ignore != null && child == _ignore)
					continue;
				if ((ignoreLayers & (1 << _go.layer)) != 0)
					continue;
				
				SetTintWindowColour(child.gameObject.GetComponent<MeshRenderer>(), _window, _colour, _applyUVOffsets, _enableUVOffsets, _ignoreIfAlreadyTinted);
				if (_includeSMRs) {
					SetTintWindowColour(child.gameObject.GetComponent<SkinnedMeshRenderer>(), _window, _colour, _applyUVOffsets, _enableUVOffsets, _ignoreIfAlreadyTinted);
				}
				if(child.childCount > 0)
					SetTintWindowColour(child.gameObject, _window, _colour, _includeSMRs, _applyUVOffsets, _enableUVOffsets, _ignore, _ignoreIfAlreadyTinted);
			}
		}
	}
	public static Color GetTintWindowColour(GameObject _go, int _window) {
		if (_go != null) {
			foreach (var r in _go.GetComponentsInChildren<MeshRenderer>()) {
				if (r.sharedMaterials == null) ShowMaterialInstancePenaltyWarning(_go, true);
				var mats = r.sharedMaterials; if (mats == null || mats.Length == 0) mats = r.materials;
				for (int x = 0; x < mats.Length; x++) {
					if (mats[x].HasProperty(Decoration.c_tint1ColourProperty)) {
						var clr = Decoration.GetTintProperty(mats[x], _window);
						if (clr.a > 0) return clr;
					}
				}
			}
		}
		return Color.white;
	}
	public static void SetTintWindowColours(GameObject _go, int _window, Dictionary<Material, Color> _colour, bool _includeSMRs = false, bool _applyUVOffsets = false, bool _enableUVOffsets = false, bool _ignoreIfAlreadyTinted = false) {
		if (_go != null) {
			foreach (var r in _go.GetComponentsInChildren<MeshRenderer>()) {
				SetTintWindowColour(r, _window, _colour, _applyUVOffsets, _enableUVOffsets, _ignoreIfAlreadyTinted);
			}
			if (_includeSMRs) {
				foreach (var r in _go.GetComponentsInChildren<SkinnedMeshRenderer>()) {
					SetTintWindowColour(r, _window, _colour, _applyUVOffsets, _enableUVOffsets, _ignoreIfAlreadyTinted);
				}
			}
		}
	}
	public static void StoreMaterialColours(Renderer _rnd, int _window, Dictionary<Material, Color> _dic) {
		if (_rnd.sharedMaterials == null) ShowMaterialInstancePenaltyWarning(_rnd.gameObject, true);
		var mats = _rnd.sharedMaterials; if (mats == null || mats.Length == 0) mats = _rnd.materials;
		for (int x = 0; x < mats.Length; x++) {
			if (mats[x] == null) continue;
			if (mats[x].HasProperty(Decoration.c_tint1ColourProperty)) {
				var clr = Decoration.GetTintProperty(mats[x], _window);
				if (clr.a > 0) _dic[mats[x]] = clr;
			}
		}
	}
	public static Dictionary<Material, Color> GetTintWindowColours(GameObject _go, int _window) {
		if (_go != null) {
			var colours = new Dictionary<Material, Color>();
			foreach (var r in _go.GetComponentsInChildren<MeshRenderer>()) {
				StoreMaterialColours(r, _window, colours);
			}
			foreach (var r in _go.GetComponentsInChildren<SkinnedMeshRenderer>()) {
				StoreMaterialColours(r, _window, colours);
			}
			return colours;
		}
		return null;
	}

	public static void CopyFields<T>(this T _to, T from)
	{
		var fields = _to.GetType().GetFields();
		foreach(var field in fields)
		{
			field.SetValue(_to, field.GetValue(from));
		}
	}

	public static void CopyProperties<T>(this T _to, T from)
	{
		var props = _to.GetType().GetProperties();
        foreach (var prop in props)
        {
            if (!prop.CanWrite || !prop.CanWrite || prop.Name == "name") continue;
            prop.SetValue(_to, prop.GetValue(from, null), null);
        }
	}

	public static void CopyComponent<T>(this T _to, T from)
	{
		_to.CopyFields(from);
		_to.CopyProperties(from);
	}


	static public Vector3 WorldPosition(this RectTransform _rt, float _fractionX = .5f, float _fractionY = .5f, Camera _camera = null, float _depth = 10) {
		if (_camera == null) _camera = Camera.main;
		var corners = new Vector3[4];
		_rt.GetWorldCorners(corners);
		var edge1 = Vector3.Lerp(corners[0], corners[3], _fractionX);
		var edge2 = Vector3.Lerp(corners[1], corners[2], _fractionX);
		var pos = Vector3.Lerp(edge1, edge2, _fractionY);
		var ray = _camera.RayAtScreenPosition(pos);
		return ray.GetPoint(_depth);
	}
	static public Vector3 ScreenPosition(this RectTransform _rt, float _fractionX = .5f, float _fractionY = .5f, Camera _camera = null) {
		if (_camera == null) _camera = Camera.main;
		var wpos = _rt.WorldPosition(_fractionX, _fractionY, _camera);
		return _camera.WorldToScreenPoint(wpos);
	}
	
	static public Rect TransformRectIntoCanvasSpace(this RectTransform _fromRect, Canvas _toCanvas) 
	{
		var fromCanvas = _fromRect.GetComponentInParent<Canvas>();
		var centerPoint = Vector2.zero;
		var size = Vector2.zero;
        
		switch(fromCanvas.renderMode)
		{
			case RenderMode.ScreenSpaceOverlay:
				var worldPos = _fromRect.TransformPoint(_fromRect.rect.center);
				var worldSize = _fromRect.TransformVector(_fromRect.rect.size);
				centerPoint = _toCanvas.GetComponent<RectTransform>().InverseTransformPoint(worldPos);
				size = _toCanvas.GetComponent<RectTransform>().InverseTransformVector(worldSize);
				break;
			case RenderMode.WorldSpace:
				Vector3[] corners = new Vector3[4];
				_fromRect.GetWorldCorners(corners);
				var p0 = _toCanvas.WorldToCanvas3D(corners[0]);
				var p1 = _toCanvas.WorldToCanvas3D(corners[1]);
				var p2 = _toCanvas.WorldToCanvas3D(corners[2]);
				var p3 = _toCanvas.WorldToCanvas3D(corners[3]);

				const float c_nearPlane = 10;
				if (p0.z < c_nearPlane || p1.z < c_nearPlane || p2.z < c_nearPlane || p3.z < c_nearPlane)
				{
					size = Vector2.zero;
					centerPoint = Vector2.one * 1e23f;
					break;
				}
				var minX = Mathf.Min(p0.x, p1.x, p2.x, p3.x);
				var maxX = Mathf.Max(p0.x, p1.x, p2.x, p3.x);
				var minY = Mathf.Min(p0.y, p1.y, p2.y, p3.y);
				var maxY = Mathf.Max(p0.y, p1.y, p2.y, p3.y);
				size = new Vector2(Mathf.Abs(maxX-minX), Mathf.Abs(maxY - minY));
				centerPoint = (p0+p1+p2+p3)/4f;
				break;
		}
		return new Rect(centerPoint - size/2f, size);
	}
}

public static class AnimatorExt {
	public static int GetParameterHandle(this Animator _this, string _name) {
		_name = _name.ToLower();
		foreach (var p in _this.parameters) {
			if (p.name.ToLower() == _name) {
				return Animator.StringToHash(p.name);
			}
		}
		return 0;
	}

	public static bool HasParameter(this Animator _this, string _name)
	{
		_name = _name.ToLower();
		foreach (var param in _this.parameters)
			if (param.name.ToLower() == _name)
				return true;
		return false;
	}
}

public static class VecExt {
	/// LookRotation - returns the euler angles (ZXY) orienting this as forward around global up (with Z zero)
	/// Created to be equivalent to the Quaternion.LookRotation function wiith an up of Vector3.up
	public static Vector3 LookRotation(this Vector3 _fwd) {
		var v = _fwd;
		v.z = 0f;
		v.y = 90f - (float)System.Math.Atan2(_fwd.z, _fwd.x) * Mathf.Rad2Deg;
		v.x = -(float)System.Math.Atan2(_fwd.y, (float)System.Math.Sqrt(_fwd.x*_fwd.x+_fwd.z*_fwd.z)) * Mathf.Rad2Deg;
		return v;
	}
	public static Vector3 GetLookAtVector(this Quaternion _rot, Vector3 _fromPosition) => _fromPosition + _rot * Vector3.forward;
	public static void FastNormalize(this Vector3 _v, ref Vector3 _res) {
		float m = (float)System.Math.Sqrt(_v.x * _v.x + _v.y * _v.y + _v.z * _v.z);
		_res.x = _v.x / m; _res.y = _v.y / m; _res.z = _v.z / m;
	}
	public static Vector3 FastNormalize(this Vector3 _v) {
		float m = (float)System.Math.Sqrt(_v.x * _v.x + _v.y * _v.y + _v.z * _v.z);
		if (m < .00001f) return _v; // GL - unity vector3 normalize appears to handle degenerate vectors this way
		var r = _v;
		r.x = _v.x / m; r.y = _v.y / m; r.z = _v.z / m;
		//Debug.LogError($"nrm {_v:n3} - m:{m:n4} - res:{r:n3} org:{_v.normalized:n3}");
		return r;
	}
	public static Vector3 FastMul(this Vector3 _v, float _m) {
		var r =  _v;
		r.x *= _m; r.y *= _m; r.z *= _m;
		return r;
	}
	public static Vector3 FastAdd(this Vector3 _v, Vector3 _a) {
		var r =  _v;
		r.x += _a.x; r.y += _a.y; r.z += _a.z;
		return r;
	}
	public static void FastAddSelf(ref this Vector3 _v, Vector3 _a) {
		_v.x += _a.x; _v.y += _a.y; _v.z += _a.z;
	}
	public static Vector3 FastNeg(this Vector3 _v) {
		var r = _v;
		r.x = -r.x; r.y = -r.y; r.z = -r.z;
		return r;
	}
	public static Vector3 FastLerp(this Vector3 _v, Vector3 _to, float _t) {
		var r = _v;
		r.x += (_to.x - r.x) * _t;
		r.y += (_to.y - r.y) * _t;
		r.z += (_to.z - r.z) * _t;
		return r;
	}
	
	public static int AddCount<T>(this Dictionary<T, int> _dictionary, T _key, int _value) {
		if (!_dictionary.ContainsKey(_key)) _dictionary[_key] = _value;
		else _dictionary[_key] += _value;
		return _dictionary[_key];
	}
	public static float AddCount<T>(this Dictionary<T, float> _dictionary, T _key, float _value) {
		if (!_dictionary.ContainsKey(_key)) _dictionary[_key] = _value;
		else _dictionary[_key] += _value;
		return _dictionary[_key];
	}
	
	public static bool Approximately(this Vector2 _vectorThis, Vector2 _vectorOther, float epsilon)
	{
		return Mathf.Abs(_vectorThis.x - _vectorOther.x) < epsilon &&
		       Mathf.Abs(_vectorThis.y - _vectorOther.y) < epsilon;
	}

	public static bool Approximately(this Vector3 _vectorThis, Vector3 _vectorOther, float epsilon)
	{
		return Mathf.Abs(_vectorThis.x - _vectorOther.x) < epsilon &&
		       Mathf.Abs(_vectorThis.y - _vectorOther.y) < epsilon &&
		       Mathf.Abs(_vectorThis.z - _vectorOther.z) < epsilon;
	}
	
	public static bool Approximately(this Vector3 _vectorThis, Vector3 _vectorOther)
	{
		return Mathf.Approximately(_vectorThis.x, _vectorOther.x) &&
		       Mathf.Approximately(_vectorThis.y, _vectorOther.y) &&
		       Mathf.Approximately(_vectorThis.z, _vectorOther.z);
	}
		
	public static bool Approximately(this Vector2 _vectorThis, Vector2 _vectorOther)
	{
		return Mathf.Approximately(_vectorThis.x, _vectorOther.x) &&
		       Mathf.Approximately(_vectorThis.y, _vectorOther.y);
	}
	
	public static Vector3 ApplyFingerOffset(this Vector3 _this, bool _includeFingerOffset = false)
	{
#if UNITY_IOS || UNITY_ANDROID
		if (_includeFingerOffset) 
			_this += TouchManager.FingerRaise;
#endif
		return _this;
	}
}

public static class MaterialExt
{
	public static void SetColourClip(this Material _this, UInt32 _clip)
	{
		_this.SetFloat("_ColourClipBitmap", _clip);
		_this.EnableKeyword("_COLOURCLIP");
		UnityEngine.Rendering.HighDefinition.HDMaterial.SetAlphaClipping(_this, true);
	}
	
	public static void ClearColourClip(this Material _this)
	{
		_this.DisableKeyword("_COLOURCLIP");
	}
}

public static class MatrixExt {
	/// FastSetTRyS - set Translate Rotate (y-axis only) and Scale, assumes the matrix has been identitied previously
	public static void FastSetTRyS(ref this Matrix4x4 _mat, ref Vector3 _pos, ref Vector3 _euler, ref Vector3 _scale)
	{
		float scl = _scale.x;
		var orient = _euler.y * Mathf.Deg2Rad;
		float sO = (float)System.Math.Sin(orient), cO = (float)System.Math.Cos(orient);
		_mat.m00 = cO * scl;  _mat.m01 = 0;  _mat.m02 = sO * scl;  _mat.m03 = _pos.x;
		_mat.m10 = 0f;  _mat.m11 = scl;  _mat.m12 = 0;  _mat.m13 = _pos.y;
		_mat.m20 = -sO * scl;  _mat.m21 = 0;  _mat.m22 = cO * scl;  _mat.m23 = _pos.z;
	}
	/// FastSetTRyS - set Translate Rotate (x- and y-axis only) and Scale, assumes the matrix has been identitied previously
	public static void FastSetTRxyS(ref this Matrix4x4 _mat, ref Vector3 _pos, ref Vector3 _euler, ref Vector3 _scale)
	{
		float scl = _scale.x;
		var orient = _euler.y * Mathf.Deg2Rad;
		var elev = _euler.x * Mathf.Deg2Rad;
		float sO = (float)System.Math.Sin(orient), cO = (float)System.Math.Cos(orient);
		float sE = (float)System.Math.Sin(elev), cE = (float)System.Math.Cos(elev);
		_mat.m00 = cO * scl;  _mat.m01 = sO * sE * scl;  _mat.m02 = sO * cE * scl;  _mat.m03 = _pos.x;
		_mat.m10 = 0f;  _mat.m11 = cE * scl;  _mat.m12 = -sE * scl;  _mat.m13 = _pos.y;
		_mat.m20 = -sO * scl;  _mat.m21 = cO * sE * scl;  _mat.m22 = cO * cE * scl;  _mat.m23 = _pos.z;
	}
	/// FastSetTRyS - set Translate Rotate and Scale, assumes the matrix has been identitied previously
	public static void FastSetTRzxyS(ref this Matrix4x4 _mat, ref Vector3 _pos, ref Vector3 _euler, ref Vector3 _scale)
	{
		float scl = _scale.x;
		var orient = _euler.y * Mathf.Deg2Rad;
		float sO = (float)System.Math.Sin(orient), cO = (float)System.Math.Cos(orient);
		var elev = _euler.x * Mathf.Deg2Rad;
		float sE = (float)System.Math.Sin(elev), cE = (float)System.Math.Cos(elev);
		var twist = _euler.z * Mathf.Deg2Rad;
		float sT = (float)System.Math.Sin(twist), cT = (float)System.Math.Cos(twist);
		_mat.m00 = (cO*cT + sO*sE*sT) * scl;  _mat.m01 = (-cO*sT+sO*sE*cT) * scl;  _mat.m02 = sO * cE * scl;  _mat.m03 = _pos.x;
		_mat.m10 = cE*sT * scl;  _mat.m11 = cE*cT * scl;  _mat.m12 = -sE * scl;  _mat.m13 = _pos.y;
		_mat.m20 = (-sO*cT + cO*sE*sT) * scl;  _mat.m21 = (sO*sT + cO*sE*cT) * scl;  _mat.m22 = cO * cE * scl;  _mat.m23 = _pos.z;	
	}
	/// FastSetTRStest - test routine for the other FastSet functions - checks against the plain Unity SetTRS and outputs any errors
	public static void FastSetTRStest(ref this Matrix4x4 _mat, ref Vector3 _pos, ref Vector3 _euler, ref Vector3 _scale)
	{
		float scl = _scale.x;
		var orient = _euler.y * Mathf.Deg2Rad;
		float sO = Mathf.Sin(orient), cO = Mathf.Cos(orient);
		float sE = 0, cE = 1, sT = 0, cT = 1;
		if (_euler.x * _euler.x < .001f * .001f) {
			_mat.m00 = cO * scl;  _mat.m01 = 0;  _mat.m02 = sO * scl;  _mat.m03 = _pos.x;
			_mat.m10 = 0f;  _mat.m11 = scl;  _mat.m12 = 0;  _mat.m13 = _pos.y;
			_mat.m20 = -sO * scl;  _mat.m21 = 0;  _mat.m22 = cO * scl;  _mat.m23 = _pos.z;
		} else {
			var elev = _euler.x * Mathf.Deg2Rad;
			sE = Mathf.Sin(elev); cE = Mathf.Cos(elev);
			if (_euler.z * _euler.z < .001f * .001f) {
				_mat.m00 = cO * scl;  _mat.m01 = sO * sE * scl;  _mat.m02 = sO * cE * scl;  _mat.m03 = _pos.x;
				_mat.m10 = 0f;  _mat.m11 = cE * scl;  _mat.m12 = -sE * scl;  _mat.m13 = _pos.y;
				_mat.m20 = -sO * scl;  _mat.m21 = cO * sE * scl;  _mat.m22 = cO * cE * scl;  _mat.m23 = _pos.z;
			} else {
				var twist = _euler.z * Mathf.Deg2Rad;
				sT = Mathf.Sin(twist); cT = Mathf.Cos(twist);
				
				_mat.m00 = (cO*cT + sO*sE*sT) * scl;  _mat.m01 = (-cO*sT+sO*sE*cT) * scl;  _mat.m02 = sO * cE * scl;  _mat.m03 = _pos.x;
				_mat.m10 = cE*sT * scl;  _mat.m11 = cE*cT * scl;  _mat.m12 = -sE * scl;  _mat.m13 = _pos.y;
				_mat.m20 = (-sO*cT + cO*sE*sT) * scl;  _mat.m21 = (sO*sT + cO*sE*cT) * scl;  _mat.m22 = cO * cE * scl;  _mat.m23 = _pos.z;	
			}
		}

		var mtx = new Matrix4x4();
		mtx.SetTRS(_pos, Quaternion.Euler(_euler), _scale);
		var df = Matrix4x4.identity;
		df.m00 = mtx.m00 - _mat.m00; df.m01 = mtx.m01 - _mat.m01; df.m02 = mtx.m02 - _mat.m02; df.m03 = mtx.m03 - _mat.m03;
		df.m10 = mtx.m10 - _mat.m10; df.m11 = mtx.m11 - _mat.m11; df.m12 = mtx.m12 - _mat.m12; df.m13 = mtx.m13 - _mat.m13;
		df.m20 = mtx.m20 - _mat.m20; df.m21 = mtx.m21 - _mat.m21; df.m22 = mtx.m22 - _mat.m22; df.m23 = mtx.m23 - _mat.m23;
		df.m30 = mtx.m30 - _mat.m30; df.m31 = mtx.m31 - _mat.m31; df.m32 = mtx.m32 - _mat.m32; df.m33 = mtx.m33 - _mat.m33;
		var d2 =
			df.m00*df.m00 + df.m01*df.m01 + df.m02*df.m02+df.m03*df.m03+
			df.m10*df.m10 + df.m11*df.m11 + df.m12*df.m12+df.m13*df.m13+
			df.m20*df.m20 + df.m21*df.m21 + df.m22*df.m22+df.m23*df.m23+
			df.m30*df.m30 + df.m31*df.m31 + df.m32*df.m32+df.m33*df.m33;
		if (d2 > .001f*.001f)
			UnityEngine.Debug.LogError($"{_pos} {_euler} {_scale} =>\nsO:{sO:n2} cO:{cO:n2} sE:{sE:n2} cE:{cE:n2} sT:{sT:n2} cT:{cT:n2}\n\n{mtx.ToString("n2")}\n\n{_mat.ToString("n2")}\n\n{Matrix4x4.Rotate(Quaternion.Euler(_euler)).ToString("n2")}\n\nAngles:{_euler.x:n2}, {_euler.y:n2}, {_euler.z:n2}\n\n");
	}
	
	public static void LazySetText(this TMPro.TextMeshProUGUI _this, string _value)
	{
		if (_this == null) return;
		if (_this.text != _value) _this.text = _value;
	}

	public static void LazySetFill(this UnityEngine.UI.Image _this, float _value)
	{
		if (_this == null) return;
		if (!_this.fillAmount.Nearly(_value)) _this.fillAmount = _value;
	}
}

public static class QuatExt
{
	public static bool Approximately(this Quaternion quaternionThis, Quaternion quaternionOther, float epsilon)
	{
		return 1 - Mathf.Abs(Quaternion.Dot(quaternionThis, quaternionOther)) < epsilon;
	}

	public static Vector3 up(this Quaternion rotation)
	{
		float num1 = rotation.x * 2f;
		float num2 = rotation.y * 2f;
		float num3 = rotation.z * 2f;
		float num4 = rotation.x * num1;
		float num6 = rotation.z * num3;
		float num7 = rotation.x * num2;
		float num9 = rotation.y * num3;
		float num10 = rotation.w * num1;
		float num12 = rotation.w * num3;
		Vector3 vector3;
		vector3.x = num7 - num12;
		vector3.y = 1.0f - (num4 + num6);
		vector3.z = num9 + num10;
		return vector3;
	}

	public static Vector3 right(this Quaternion rotation)
	{
		float num2 = rotation.y * 2f;
		float num3 = rotation.z * 2f;
		float num5 = rotation.y * num2;
		float num6 = rotation.z * num3;
		float num7 = rotation.x * num2;
		float num8 = rotation.x * num3;
		float num11 = rotation.w * num2;
		float num12 = rotation.w * num3;
		Vector3 vector3;
		vector3.x = 1.0f - (num5 + num6);
		vector3.y = num7 + num12;
		vector3.z = num8 - num11;
		return vector3;
	}

	public static Vector3 forward(this Quaternion rotation)
	{
		float num1 = rotation.x * 2f;
		float num2 = rotation.y * 2f;
		float num3 = rotation.z * 2f;
		float num4 = rotation.x * num1;
		float num5 = rotation.y * num2;
		float num8 = rotation.x * num3;
		float num9 = rotation.y * num3;
		float num10 = rotation.w * num1;
		float num11 = rotation.w * num2;
		Vector3 vector3;
		vector3.x = num8 + num11;
		vector3.y = num9 - num10;
		vector3.z = 1.0f - (num4 + num5);
		return vector3;
	}
}

public static class TessExt
{
	public static void AddQuad(this LibTessDotNet.Tess _this, Vector3 _p1, Vector3 _p2, Vector3 _p3, Vector3 _p4, int _id, LibTessDotNet.ContourOrientation _orientation)
	{
		var contour = new LibTessDotNet.ContourVertex[4];
		contour[0].Position = V2V(_p1); contour[0].Data = _id;
		contour[1].Position = V2V(_p2); contour[1].Data = _id;
		contour[2].Position = V2V(_p3); contour[2].Data = _id;
		contour[3].Position = V2V(_p4); contour[3].Data = _id;
		_this.AddContour(contour, _orientation);
	}
	public static LibTessDotNet.Vec3 V2V(Vector3 _v) { return new LibTessDotNet.Vec3() { X = _v.x, Y = _v.y, Z = _v.z }; }
	public static LibTessDotNet.Vec3 V2V0(Vector3 _v) { return new LibTessDotNet.Vec3() { X = _v.x, Y = 0, Z = _v.z }; }
	//public static LibTessDotNet.Vec3 VI2V0(IntPoint _v, float _scale) { return new LibTessDotNet.Vec3() { X = (float)_v.X/_scale, Y = 0.0f, Z = (float)_v.Y/_scale }; }
}

public static class DotNetExtensions {
	public static bool TryParsePercentage(string _what, out float _value) { return Utility.TryParsePercentage(_what, out _value); }
}

public static class PhysicsExtension
{
	public static bool IsInside(this Collider c, Vector3 point)
	{
		Vector3 closest = c.ClosestPoint(point);
		return closest == point;
	}
}

public static class LightExtension
{
	public static void SetIntensity(this Light _light, float _intensity)
	{
		_light.lightUnit = UnityEngine.Rendering.LightUnit.Lumen;
		_light.intensity = _intensity * 1000;
	}

	public static void SetRadius(this Light _light, float _radius)
	{
		_light.lightUnit = UnityEngine.Rendering.LightUnit.Lumen;
		var hd = _light.GetComponent<UnityEngine.Rendering.HighDefinition.HDAdditionalLightData>();
		if (hd != null)
			hd.shapeRadius = _radius;
	}

	public static void SetShadowIntensity(this Light _this, float _intensity)
	{
		if (GameSettings.SRPOptions.IsURP)
		{
			_this.shadowStrength = _intensity;
		}
		else
		{
			var hdlight = _this.GetComponent<UnityEngine.Rendering.HighDefinition.HDAdditionalLightData>();
			if (hdlight != null)
				hdlight.shadowDimmer = _intensity;
		}
		if (_intensity < .0001f) _this.shadows = LightShadows.None;
		else _this.shadows = LightShadows.Soft;
	}

	public static void SetShadowResolution(this Light _light, int _res)
	{
		if (GameSettings.SRPOptions.IsURP)
		{
		}
		else
		{
			var data = _light.GetComponent<UnityEngine.Rendering.HighDefinition.HDAdditionalLightData>();
			data.SetShadowResolutionOverride(true);
			data.SetShadowResolution(_res);
		}
	}
	public static string ReplaceInputTokens(this string _message)
	{
		return MAHelper.ReplaceInputTokens(_message);
	}
}

public static class DateTimeExtensions
{
	public static int MoonPhase(this DateTime _this)
	{
		DateTime knownNewMoon = new DateTime(2000, 1, 6);
		const float c_lunarCycle = 29.53059f;
		var cycle = (float) (_this - knownNewMoon).TotalDays / c_lunarCycle + 1.0f/16.0f;
		cycle -= Mathf.Floor(cycle);
		return (int)(cycle * 8);
	}
}
