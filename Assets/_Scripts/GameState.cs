//#define CAN_SERIALIZE_DICTIONARY
using System;
using System.Collections;
using System.Collections.Generic;
using Unity.Mathematics;
using UnityEngine;

[Serializable]
#if CAN_SERIALIZE_DICTIONARY
public class SDictionary<K, V> : Dictionary<K, V> {} // more efficient to use a real dictionary if we can serialize it
#else
public class SDictionary<K, V> where K : IComparable {
	Dictionary<K, V> m_base;
	int m_baseValidIncrementer;
	public Dictionary<K, V> Base
	{
		get
		{
			CheckBase();
			return m_base;
		}
	}
	public List<K> m_keys = new List<K>(); //These need to be public for System.Text.Json's serializer to be able to serialize them
	public List<V> m_values = new List<V>();
	void CheckBase() {
		if (m_base != null && m_baseValidIncrementer == GameState.CurrentSDictionaryBaseValidIncrementer) return;
		m_baseValidIncrementer = GameState.CurrentSDictionaryBaseValidIncrementer;
		m_base = new Dictionary<K, V>();
		for (int i = 0; i < m_keys.Count; ++i) {
			m_base[m_keys[i]] = m_values[i];
		}
	}
	public bool TryGetValue(K _key, out V _value) {
		CheckBase();
		return m_base.TryGetValue(_key, out _value);
	}
	public V GetValue(K _key, V _default = default(V)) {
		V value;
		if (TryGetValue(_key, out value)) return value;
		return _default;
	}
	public bool ContainsKey(K _key) {
		CheckBase();
		return m_base.ContainsKey(_key);
	}
	public bool RemoveKey(K _key) {
		CheckBase();
		int index = m_keys.IndexOf(_key);
		if (index != -1)
		{
			m_keys.RemoveAt(index);
			m_values.RemoveAt(index);
		}
		return m_base.Remove(_key);
	}

	public List<K> Keys => m_keys;
	
	public int Count
	{
		get
		{
			CheckBase();
			return m_base.Count;
		}
	}

	public bool Add(K _key, V _value) {
		CheckBase();
		if (m_base.ContainsKey(_key)) return false;
		m_base[_key] = _value;
		m_keys.Add(_key);
		m_values.Add(_value);
		return true;
	}

	public V AddValue(K _key, V _value, Func<V,V,V> _add)
	{
		CheckBase();
		if (m_base.TryGetValue(_key, out var current)) _value = _add(_value, current);
		this[_key] = _value;
		return _value;
	}
	
	public V this[K _key] {
		get { CheckBase(); return m_base[_key]; }
		set {
			if (Add(_key, value)) return;
			// already exists, change stored value - could use a Key to Index dictionary
			m_base[_key] = value;
			if (m_multipleOperationsInProgress == 0) {
				for (int i = 0; i < m_keys.Count; ++i) {
					if (m_keys[i].CompareTo(_key) == 0) {
						m_values[i] = value;
						break;
					}
				}
			}
		}
	}
	
	private int m_multipleOperationsInProgress = 0;
	public void StartMultipleOperations()
	{
		++m_multipleOperationsInProgress;
	}
	public void EndMultipleOperations()
	{
		--m_multipleOperationsInProgress;
		if (m_multipleOperationsInProgress == 0)
		{
			m_keys.Clear();
			m_values.Clear();
			foreach (var kvp in m_base)
			{
				m_keys.Add(kvp.Key);
				m_values.Add(kvp.Value);
			}
		}
	}

	public void Clear() {
		m_base?.Clear(); m_keys.Clear(); m_values.Clear();
	}
	public static implicit operator Dictionary<K, V>(SDictionary<K, V> _in) {
		_in.CheckBase();
		return _in.m_base;
	}
	public static implicit operator SDictionary<K, V>(Dictionary<K, V> _in) {
		var sd = new SDictionary<K, V>();
		foreach (var kvp in _in) {
			sd.Add(kvp.Key, kvp.Value);
		}
		return sd;
	}
}
#endif

[Serializable]
public class GameState_DesignFitness {
	[Serializable]
	public class LogicalDesignData {
		public string m_partType;
		public int m_totalPartsRequired;
		public SDictionary<EDesignFitnessType, int> m_requiredDesignPartTypes = new SDictionary<EDesignFitnessType, int>();
	}
	public List<LogicalDesignData> m_logicalDesigns = new List<LogicalDesignData>();
	public SDictionary<string, float> m_blockAges = new SDictionary<string, float>();
	public SDictionary<string, float> m_decorationAges = new SDictionary<string, float>();
	
	public SDictionary<string, float> m_blockBoredom = new SDictionary<string, float>();

	public void AddBoredom(string _id, float _add = 1) {
		float boredom = m_blockBoredom.GetValue(_id);
		m_blockBoredom[_id] = boredom + _add;
	}
}


[Serializable]
public class GameState_Building {
	public GameState_Building() { }
	public GameState_Building(float _x, float _z, float _dir, GameState_Design _design, int _w, int _h, int _id, float _constructionFraction) {
		m_x = _x; m_z = _z; m_direction = _dir; m_buildingDesign = _design; m_id = _id;
		m_cachedWidth = _w; m_cachedHeight = _h;
	}
	
	[SerializeReference]public GameState_Design m_buildingDesign;
	public string m_name, m_title;
	public string m_setPiecePrefab;
	public float m_x, m_z, m_direction;
	public int m_id;
	public float m_plantLevel;
	public int m_cachedWidth, m_cachedHeight;
	public string m_typeSpecificData;
	public bool m_hasEverBeenConstructed;
	public float m_totalNumMoved;		// NGFactory
	public float m_totalNumSales;		// NGShop
	public float m_totalNumMoney;		// NGShop
	public string m_priorityDeliverTarget; // NGFactory
	public bool m_isPaused;
	public bool m_removeBaseVisuals = false;
	public long m_builtTime;
	public string m_designedBy;
	public bool m_isNonPlayerBuilding;
	public bool m_shouldLoadSeedData;
	public float m_healthNorm = 1;
	public List<int> m_lastWildBlocks;
	public bool m_lockedFromPlayer;	// Meaning player can't design or move
	public int m_orderId = -1;
	public Vector3 Pos() { return new Vector3((float)m_x * RoadManager.c_roadTileScale, 0, (float)m_z * RoadManager.c_roadTileScale); }

	public bool IsSetPiece => string.IsNullOrEmpty(m_setPiecePrefab) == false;
	
	public void MoveLocally(int _blocksX, int _blocksZ)
	{
		int dx = _blocksX, dz = _blocksZ;
		switch (m_direction)
		{
			case 0: default: break;
			case 1: (dx, dz) = (dz, -dx); break;
			case 2: dx = -dx; dz = -dz; break;
			case 3: (dx, dz) = (-dz, dx); break;
		}
		m_x += dx * (int)BuildingPlacementManager.c_buildingTileSize;
		m_z += dz * (int)BuildingPlacementManager.c_buildingTileSize;
	}
}

[Serializable]
public class GameState_MovingObject
{	
	public int m_id;
	public string m_type;
	public string m_creatureInfoName;
	public Vector3 m_pos;
	public Vector3 m_destPos;
	public string m_serverUID;
	public int m_state;
	//public int m_home, m_work;
	public long m_homeComponentId, m_jobComponentId;
	[NonSerialized] public BCBase m_home;
	[NonSerialized] public BCBase m_job;
	public int m_leaderId;
	public float m_energy, m_health, m_speed, m_armour;
	public string m_targetObject;
	public string m_subScene = null;
	public bool m_isPossessed = false;

	public int m_inside, m_destBuilding; //reaction points only?
	public bool m_canPickupOverride, m_canPickupValue;
	public Vector3 m_teleportBackTo;

	public bool m_blockPossession = false;
	public Vector3 m_lastLookAt;
}

[Serializable]
public class GameState_Person : GameState_Character
{ //Ideally we want a superclass GameState_MovingObject, but a lot of worker functionality would need to be moved out of NGMovingObject

	public int m_peepAction = 0;
	public int m_carryingQuantity;
	public string m_carryingProductUID;
	public int m_carryingSpawnedFrom;
    public bool m_waitingForPay;
    public bool m_isMale;
    public string m_workerInfo;
    public string m_carrying;
	public int m_chopObjectPhase = 0;

	public int m_treeChopId
	{
		get => m_harvestHotspotId;
		set => m_harvestHotspotId = value;
	}
	
	public bool m_canBeTargeted = false;
	public int m_nightsHomeless = 0;

	public virtual void Create()
    {
		MAWorker.Load(this);
    }
}

public class GameState_Tourist : GameState_Person
{
	public Vector3 m_leavePos;
	
	public override void Create()
	{
		MATourist.Load(this);
	}
}

public class GameState_FlowCharacter : GameState_Person
{
	public Vector3 m_leavePos;
	
	public override void Create()
	{
		MAFlowCharacter.Load(this);
	}
}

[Serializable]
public class GameState_Hero : GameState_Character 
{
	public float m_maxTags = 0;
	public bool m_isMale = true;
}

[Serializable]
public class GameState_Character : GameState_MovingObject
{
	public GameState_Character()
	{
		m_characterExperience = new CharacterExperience();
	}
	
	public float m_healthMultiplier = 1;
	public float m_speedMultiplier = 1;
	public float m_attackMultiplier = 1;
	public float m_defenceMultiplier = 1;
	public float m_swordMultiplier = 1;
	public float m_hammerMultiplier = 1;
	public float m_maceMultiplier = 1;
	public float m_axeMultiplier = 1;
	
	public float WalkSpeed => m_walkSpeed * m_speedMultiplier;
	public float AttackSpeed => m_attackSpeed * m_speedMultiplier;
	
	public string m_name;
	public string m_bodyType;
	public string m_subtype;
	public Vector3 m_rotation;
	public Vector3 m_targetPos;
	public Vector3 m_spawnPos;
	public string m_spawnPointName;
	public string m_savedUpdateState;
	public string m_defaultState;
	public string m_initialState;
	public float m_timeInState;
	public float m_aliveDuration;
	public float m_walkSpeed;
	public float m_attackSpeed; //rename to runSpeed. this is not the attack-speed

	public Vector3 m_objectiveWaypoint;
	public string m_objectiveTarget;
	public bool m_guardObjectiveWaypoint;
	public float m_guardRadius; //TS - recommend using m_character.GuardRadius to gather clamped, asserted guard radius
	public bool m_guardAreaIsBespoke;
	public int m_consciousness;
	public float m_unconsciousTimeLeft;
	public List<float3> m_patrolPath;
	public Vector3 m_optionalDespawnPosition;
	public int m_kills;
	public bool m_immortal;
	public double m_timePossessed;
	public double m_timeUnpossessed;
	public long m_dayHired = -1;
	public int m_timesKnockedDown;
	[SerializeReference]public GameState_Design m_weaponDesign;
	[SerializeReference]public GameState_Design m_armourDesign;
	
	public float m_wantsToPlay;
		
	public CharacterExperience m_characterExperience;
	public int CharacterLevel => m_characterExperience.CharacterLevel;
	public bool RequiresTraining => m_desiredExperienceLevel > CharacterLevel;
	public int m_desiredExperienceLevel;
	public string m_backupState = "";
	public int m_backupMovementState = (int)NGMovingObject.STATE.NONE;
	
	public Vector3 m_customStartingPosition;
	
	public float m_lastDayPooed;
	public float m_lastDayPeed;
	
	public bool IsValidSave => m_aliveDuration >= 0; 
	public bool IsAlive => m_health > 0;
	
	public int m_groupId = -1;
	public Vector3 m_groupPatrolPosition = Vector3.zero;
	public float m_groupPatrolRadius = 0f;
	public Vector3 m_groupStationaryPosition = Vector3.zero;
	public string m_groupUnlockOnDeath = null;
	public bool m_groupInSubScene = false;

	public float m_distanceTravelled = 0f;
	public int m_itemsCollected = 0;

	public int m_harvestHotspotId = -1;
	public bool m_isQuestCharacter;
	public string m_lookAtTarget;
}

[Serializable]
public class GameState_CreatureBase : GameState_Character {
	public List<Vector3> m_path;
	public int m_waypoint = 0;
}

[Serializable]
public class GameState_NamedPoint {
	public string m_name;
	public Vector2 m_position;
	public int m_spawnedGroupID = -1;
	public int m_daySpawnedGroupDied = -1;
	public string m_subScene = "";
	public Vector3 Position => m_position.GetVector3XZ().GroundPosition();
	public void PrepareForSeedSaveWrite()
	{
		m_spawnedGroupID = -1;
		m_daySpawnedGroupDied = -1;
	}
}

[Serializable]
public class GameState_Deccoration {
	public string m_name;
	public string m_displayName;
	public Vector3 m_position;
	public Vector3 m_rotation;
	public Vector3 m_scale;
	public bool m_wasEverInOwnedDistrict;
	public long m_creationTicks;
	public float m_health;
	public int m_impactCount;
	public NGDecoration Decoration { get; set; }
	[SerializeReference] public GameState_DerrivedBaseState m_derrivedState;
}

[Serializable]
public class GameState_DerrivedBaseState
{
}

[Serializable]
public class GameState_PilloryStocks : GameState_DerrivedBaseState
{
	public int m_prisonerID;
	public long m_releaseDay;
}

[Serializable]
public class GameState_Gallows : GameState_DerrivedBaseState
{
	public int m_condemnedID;
}

[Serializable]
public class GameState_TableSaw : GameState_DerrivedBaseState
{
	public int m_prisonerID;
}

[Serializable]
public class GameState_Performance
{
	public long m_totalTenthMS;
	public long m_totalCount;

	public void Track(long _tenthMSpF)
	{
		if (_tenthMSpF <= 0 || _tenthMSpF > 2000) return; // avoid polluting the data averages with outliers (<0ms or >200ms)
		m_totalTenthMS += _tenthMSpF;
		++m_totalCount;
	}
	public long Average => m_totalCount > 0 ? m_totalTenthMS / m_totalCount : 0;
}
[Serializable]
public class GameState_Info
{
	public string m_chosenGender;
	public string m_chosenRace;
	public float m_numSales;
	public float m_valueOfSales;
	public bool m_unlockAllParts;
	public GameState_Performance m_townPerf = new GameState_Performance();
	public GameState_Performance m_designTablePerf = new GameState_Performance();
	public GameState_Performance m_otherPerf = new GameState_Performance();
	
	public float m_skintone = 1;
	
	//specific totals
	public int m_numSalesDispatch;
	public int m_timeSecsSpentInGame;
	public float m_timeSinceLastPay;
	
	public int m_numBuildingClicks;
	public int m_numBuildingHolds;
	public int m_numBuildingDrags;
	
	public int m_numWeaponsMade;
	public int m_numWeaponsDelivered;
	public int m_numWeaponsCollected;
	
	public int m_inFailState = 0;
	public int m_totalTaskFails = 0;
	public int m_totalCryptFails = 0;
	public bool m_heroKnockedDownToday = false;
	public bool m_failedThisDay = false;
}

[Serializable]
public struct GameState_DayTask
{
	public string m_taskID;
	public string m_advisorName;
	public bool m_isHighlighted;
	public bool m_isComplete;

	public GameState_DayTask(CalendarUIManager.DayTask _dayTask)
	{
		m_taskID = _dayTask.m_taskID;
		m_advisorName = _dayTask.m_advisor.m_name;
		m_isHighlighted = _dayTask.m_isHighlighted;
		m_isComplete = _dayTask.m_isComplete;
	}

	public CalendarUIManager.DayTask DayTask()
    {
		return new CalendarUIManager.DayTask(m_taskID, NGBusinessAdvisor.GetInfo(m_advisorName), m_isHighlighted, m_isComplete);
	}
}

[Serializable]
public class GameState_ChallengeIcons
{
	public int m_day;
	public List<GameState_DayTask> m_dayTasks;

	public GameState_ChallengeIcons()
	{
		m_day = -1;
	}

	public GameState_ChallengeIcons(int _day, Dictionary<string, CalendarUIManager.DayTask> _dayTasks)
	{
		m_day = _day;

		m_dayTasks = new List<GameState_DayTask>();

		foreach (var dayTask in _dayTasks)
        {
			m_dayTasks.Add(new GameState_DayTask(dayTask.Value));
		}
	}

	public static void Set(int _day, Dictionary<string, CalendarUIManager.DayTask> _dayTasks)
	{
		GameManager.Me.m_state.m_challengeIcons = new GameState_ChallengeIcons(_day, _dayTasks);
	}

	public static void Load()
	{
		var icons = GameManager.Me.m_state.m_challengeIcons;
		if (icons != null && icons.m_day >= 0)
		{
			List<CalendarUIManager.DayTask> dayTasks = new List<CalendarUIManager.DayTask>();

			foreach(var dayTask in icons.m_dayTasks)
            {
				dayTasks.Add(dayTask.DayTask());
			}

			CalendarUIManager.Me.SetDayTasks(icons.m_day, dayTasks);
		}
	}
}


[Serializable] public class ArrayWrapper<T> 
{ 
	public T[] m_array; 
	
	public ArrayWrapper(int _size)
	{
		m_array = new T[_size];
	}
}

[Serializable]
public class GameState_Design {
	public string m_design;
	
	public float m_defenseScore;
	public float m_nutritionScore;
	public float m_beautyScore;
	public float m_attackScore;
	public string m_productLine;
	
	public SDictionary<int,ArrayWrapper<long>> m_componentIds;

	public GameState_Design() { }
	public GameState_Design(string _design) {
		SetDesign(_design);
	}
	public GameState_Design(string _design, SDictionary<int, ArrayWrapper<long>> _cmpIds) {
		SetDesign(_design);
		m_componentIds = _cmpIds;
	}
	public static GameObject BuildProduct(GameState_Design _design, Transform _parent, bool _addDecorations = true, bool _onlyVisual = false, Action<GameObject, bool> _onComplete = null) {
		var go = new GameObject("Product");
		go.transform.SetParent(_parent, false);
		DesignTableManager.Me.RestoreDesign(_onlyVisual ? DesignTableManager.RestoreType.ProductOnlyVisual : DesignTableManager.RestoreType.Product, _design.m_design, go.transform, _onComplete);
		return go;
	}
	public void SetDesign(string _design)
	{
		m_design = _design;
		var dsi = NGDesignInterface.Get(m_design);
		CacheValues(dsi);
	}
	public int GetNumBlocks() { return GetNumberOfPartsInDesign(); }
	public bool HasDesign => !string.IsNullOrEmpty(m_design) && m_design[0] != '0'; // must have at least one part
	public bool HasValidDesign => HasDesign && m_design.StartsWith("1|MABase") == false; // must have at least one non-base part
	public int GetNumberOfPartsInDesign() {return DesignUtilities.GetDesignPartCountFromDesign(m_design); }
	
	public void CacheValues(NGDesignInterface.DesignScoreInterface _dsi) 
	{
		m_defenseScore = _dsi.TotalDefense;
		m_nutritionScore = _dsi.TotalNutrition;
		m_beautyScore = _dsi.TotalBeauty;
		m_attackScore = _dsi.TotalAttack;
		m_productLine = _dsi.ProductLineName;
	}
	
	public string GetDominantMaterial()
	{
		var di = NGDesignInterface.Get(this);
		return di.GetDominantResource().Name;
	}
}

[Serializable]
public class GameState_GameTime
{
	public float m_gameTime;
	public bool m_gameTimeLocked = false;
	public float m_totalWallClockTime;
    public int m_daysPlayed;
    public int m_hoursPlayed;

	public double m_secondsActive;
	public double m_lastActivityEventUploadTime;

	[NonSerializedAttribute]
	public float m_lastPlayerActionTime;

    public GameState_GameTime() {
		m_daysPlayed = 0;
		m_hoursPlayed = 0;
		m_lastPlayerActionTime = float.MinValue;

		m_secondsActive = 0;
		m_lastActivityEventUploadTime = 0;
    }
}

[Serializable]
public class GameState_StoryProgress
{
    public string m_givenName;
    public bool m_offWork;
    public int m_storyIndex;
    public int m_lastChoice;
    public float m_lastBranchIndex;
    public bool m_shownIntro = false;
    public bool m_storyFinishedFlag = false;
	public string m_playerSalutation = "";
	public string m_playerSex = "";
	public int m_addNasty;
	public int m_addNice;
    public bool m_storyNastyChoicesFlag;
    public bool m_storyNiceChoicesFlag;
	public int m_loadedHomeID;
	//public List<c_promise> m_activePromises = new List<c_promise>();

	public GameState_StoryProgress() { }
	public GameState_StoryProgress(GameState_StoryProgress sp) {

        m_givenName = sp.m_givenName;
        m_offWork = sp.m_offWork;
        m_storyIndex = sp.m_storyIndex;
        m_lastChoice = sp.m_lastChoice;
        m_lastBranchIndex = sp.m_lastBranchIndex;
        m_shownIntro = sp.m_shownIntro;
		m_storyFinishedFlag = sp.m_storyFinishedFlag;
		m_playerSalutation = sp.m_playerSalutation;
		m_playerSex = sp.m_playerSex;
		m_addNasty = sp.m_addNasty;
		m_addNice = sp.m_addNice;
		m_loadedHomeID = sp.m_loadedHomeID;
        m_storyNastyChoicesFlag = sp.m_storyNastyChoicesFlag;
        m_storyNiceChoicesFlag = sp.m_storyNiceChoicesFlag;

		// foreach(c_promise p in sp.m_activePromises)
  //       {
		// 	m_activePromises.Add(p);
  //       }
	}
	public GameState_StoryProgress(string name) {

        m_givenName = name;
        m_offWork = false;
        m_storyIndex = 0;
        m_lastChoice = 0;
        m_lastBranchIndex = 0;
        m_shownIntro = false;
        m_storyFinishedFlag = false;
		m_playerSalutation = "";
		m_playerSex = "";
		m_addNasty = 0;
		m_addNice = 0;
		m_loadedHomeID = 0;
		//m_activePromises = new List<c_promise>();
        m_storyNastyChoicesFlag = false;
        m_storyNiceChoicesFlag = false;
	}
}

[Serializable]
public class GameState_MAVehicle : GameState_MovingObject
{
	public Vector3 m_rotation;
	public int m_vehicleState;
	public float m_timeWaited;
	public int m_maxCargoSpace;
	public int m_queueSlot;
	public int m_gateQueueSlot;
	
	//public List<OrderStockItem> m_cargo;
	public List<GameState_Product> m_products;

	public void PreSave(MAVehicle _maVehicle)
	{
		Transform tr = _maVehicle.transform;
		m_pos = tr.position;
		m_rotation = _maVehicle.FrontAxleRigidBody.transform.eulerAngles;
		m_vehicleState = (int)_maVehicle.VehicleState.State;
		m_timeWaited = Mathf.RoundToInt(_maVehicle.VehicleState.WaitingTimeLeft);
	}
}

[Serializable]
public class GameState_Product
{
	/// <summary>
	/// This method creates product definition that will permanently exist in GameManager.Me.m_state.m_products
	/// It will have a unique Id and retain its index in that saved List.
	/// Please keep in mind that it's not this method's job to check if a design string is identical or not
	/// </summary>
	public static GameState_Product CreateUniquePersistentProduct(GameState_Product _gameStateProductTemplate)
	{
		GameState_Product gameStateProduct = new(_gameStateProductTemplate);
		gameStateProduct.m_uniqueID = GameManager.Me.m_state.m_products.Count.ToString();
		GameManager.Me.m_state.m_products.Add(gameStateProduct);
		return gameStateProduct;
	}
	
	public GameState_Product() {}
	
	public bool IsUnique => string.IsNullOrWhiteSpace(m_uniqueID) == false;
	
	[SerializeReference]public GameState_Design m_design;
	public int m_orderId;
	public string m_uniqueID;
	public string m_productLine;
	public float m_pricePerProduct;
	public float m_costToManufacture;
	public string m_tag;
	public int m_sales;
	public int m_placeOfManufacture;
	
	[NonSerialized] private MAOrder m_linkedOrder = null;
	
	public GameState_Product(string _productLine)
	{
		m_productLine = _productLine;
	}
	
	public GameState_Product(GameState_Product _newProductType)
	{
		m_orderId = _newProductType.m_orderId;
		m_uniqueID = _newProductType.m_uniqueID;
		m_productLine = _newProductType.m_productLine;
		m_pricePerProduct = _newProductType.m_pricePerProduct;
		m_costToManufacture = _newProductType.m_costToManufacture;
		m_tag = _newProductType.m_tag;
		m_sales = _newProductType.m_sales;
		m_placeOfManufacture = _newProductType.m_placeOfManufacture;
	}
	
	public bool GetLinkedOrder(out MAOrder _order)
	{
		if(m_linkedOrder != null)
		{
			_order = m_linkedOrder;
			return true;
		}
		
		var order = GetLinkedOrder();
		if(order.IsNullOrEmpty() == false && order.IsValid)
		{
			_order = order;
			m_linkedOrder = order;
			return true;
		}
		_order = MAOrder.EmptyOrder;
		return false;
	}
	
	public MAOrder GetLinkedOrder()
	{
		if(m_orderId <= 0)
			return null;
		m_linkedOrder = MAOrderDataManager.Me.FindOrderByID(m_orderId);
		return m_linkedOrder;
	}
	
	public GameState_Product(MAOrder _order)
	{
		m_orderId = _order.OrderId;
		m_productLine = _order.ProductLine;
	}
	
	public GameState_Product(MAOrder _order, GameState_Design _productDesign) {
		m_orderId = _order.OrderId;
		m_productLine = _order.ProductLine;
		m_tag = $"OrderID: '{m_orderId}'";
		
		SetDesign(_productDesign);
	}
	
	public NGProductInfo ProductInfo => NGProductInfo.GetInfo(m_productLine); 
	
	public NGCarriableResource ResourceType => NGCarriableResource.GetInfo($"{NGCarriableResource.c_product}:{m_uniqueID}");
	public bool HasDesign => m_design != null;
	public Sprite DesignSprite(Action<Sprite> _onComplete) { return GameManager.Me.GetDesignSprite(Design, CaptureObjectImage.Use.Product, _onComplete); }
	public string Name => string.IsNullOrEmpty(m_tag) ? m_productLine : $"{m_tag} {m_productLine}";
	public string ProductLineType => m_productLine;
	public void OpenInDesignTable(NGCommanderBase _factory) { DesignTableManager.LoadProductLaunch(_factory); }
	public GameState_Design Design => m_design;
	public void SetDesign(GameState_Design _design) { m_design = _design; }
}

[Serializable]
public class GameState_RoadSegment { }
[Serializable]
public class GameState_Pickup_Basic : IEquatable<GameState_Pickup_Basic>
{
	public int m_data;
	public Vector3 m_position;
	public Vector3 m_rotation;
	public int m_holderObject;
	public int m_spawnedFrom;
	public float m_uniqueCreationTime;

	public bool Equals(GameState_Pickup_Basic _other)
	{
		return AreEqual(this, _other);
	}

	public static bool AreEqual(GameState_Pickup_Basic _a, GameState_Pickup_Basic _b)
	{
		return _a != null && _b != null && IsCreationTimeEqual(_a.m_uniqueCreationTime, _b.m_uniqueCreationTime);
	}
	
	public static bool IsCreationTimeEqual(float _timeA, float _timeB)
	{
		return Math.Abs(_timeA - _timeB) < 0.000001;
	}
	
	public override int GetHashCode()
    {
        return m_uniqueCreationTime.GetHashCode();
    }
    
	public GameState_Pickup_Basic() {} // must have a default constructor for TPP json deserialize 
	public GameState_Pickup_Basic(GameState_Pickup _originPickup)
	{
		if(_originPickup.Pickup != null)
		{
			Transform tr = _originPickup.Pickup.transform;
			m_position = tr.position;
			m_rotation = tr.eulerAngles;
			m_uniqueCreationTime = _originPickup.Pickup.m_timeSinceCreation;
		}
		else
		{
			m_position = _originPickup.m_position;
			m_uniqueCreationTime = Time.time;
		}

		m_data = _originPickup.m_data;
		m_holderObject = _originPickup.m_holderObject;
		m_spawnedFrom = _originPickup.m_spawnedFrom;
	}
}

[Serializable]
public class GameState_Pickup {
	public int m_id;
	public Vector3 m_position;
	public string m_type;
	public int m_data;
	public int m_holderObject;
	public int m_holderDest;
	public int m_spawnedFrom;
	public int m_quantity;
	public string m_serverId;
	public bool m_wasEverInOwnedDistrict;
	public ReactPickupPersistent Pickup { get; set; }
	public void PreSave() {
		if (Pickup != null)
		{
			m_position = Pickup.transform.position;
			m_quantity = (int)Pickup.Quantity;
			return;
		}
		m_position = Vector3.zero;
		m_quantity = 0;
	}
}

[Serializable]
public class GameState_TerrainPopulation {
	public List<int> m_deletes = new List<int>();
	private HashSet<int> m_deletesCache;
	//

	public void Clear()
	{
		m_deletes.Clear();
		m_deletesCache.Clear();
	}

	void CheckCache()
	{
		if (m_deletesCache == null) m_deletesCache = new (m_deletes);
	}

	int PosToId(Vector3 _pos) {
		int x = (int)(_pos.x * 10);
		int z = (int)(_pos.z * 10);
		return x + (z << 16);
	}
	public bool IsDeleted(Vector3 _pos) {
		CheckCache();
		return m_deletesCache.Contains(PosToId(_pos));
	}
	public void Delete(Vector3 _pos) {
		CheckCache();
		var id = PosToId(_pos);
		if (!m_deletesCache.Contains(id))
		{
			m_deletes.Add(id);
			m_deletesCache.Add(id);
		}
	}
}

[Serializable]
public class GameState_WildBlock
{
	public int m_id;
	public string m_blockDesign;
	public Vector3 m_position;
	public Vector3 m_rotation;
	public Vector3 m_velocity;
	public Vector3 m_angularVelocity;
	public bool m_doNotAllowDelete;
	public SDictionary<int,ArrayWrapper<long>> m_componentIds;
	public float m_plantLevel;
	public bool m_hasEverBeenUnlocked;

	public GameState_WildBlock Copy()
	{
		return new GameState_WildBlock
		{
			m_id = m_id,
			m_blockDesign = m_blockDesign,
			m_position = m_position,
			m_rotation = m_rotation,
			m_velocity = m_velocity,
			m_angularVelocity = m_angularVelocity,
			m_componentIds = m_componentIds,
			m_hasEverBeenUnlocked = m_hasEverBeenUnlocked,
		};
	}

	public GameObject Obj { get; set; }
	public MAWildBlock WildBlock { get; set; }

	public static GameState_WildBlock Find(int _wildBlockID)
	{
		var wildBlocks = GameManager.Me.m_state.m_wildBlocks;
		for (int i = 0; i < wildBlocks.Count; ++i)
			if (wildBlocks[i].m_id == _wildBlockID)
				return wildBlocks[i];
		return null;
	}
}

[Serializable]
public class GameState_Lock
{
	public string m_type; // key/lock type
	public string m_variant; // visual variant
	public bool m_locked;
	public bool m_isPrimed = false;
	public Vector3 m_position;
	public Vector3 m_rotation;
	public MALock Instance { get; set; }
}

[Serializable]
public class GameState_KeyContainer
{
	public string m_type; // key/lock type
	public string m_variant; // visual variant
	public Vector3 m_position;
	public Vector3 m_rotation;
	public MAKeyDrag Instance { get; set; }
}

[Serializable]
public class GameState_DesignTable
{
	public SDictionary<string, int> m_lastDrawerIndexByCategory = new();
	public SDictionary<string, float> m_lastDrawerPositionByCategoryAndIndex = new();
	public int m_currentBuildingDesignID = 0; // 0 - not in design mode, 1+ building ID for either building or product (depending on isDesigningProduct), -1 unfocused design globally
	public int m_isDesigningProductOrderId = -1;
	public List<string> m_scatteredBlocks = new();
	public string m_drawerSelection;
	public SDictionary<string,bool> m_unlockedDrawers = new();
	public SDictionary<string, string> m_lastDesignByCategory = new();
	
	public List<string> GetScatteredCopy() { return m_scatteredBlocks.GetRange(0, m_scatteredBlocks.Count); }
}

[Serializable]
public class GameState_BuildHelper
{
	public int m_wildBlockID;
	public int m_buildingID;
	public int m_padIndex;
	public int m_height;
	public int m_direction; // as SnapHinge.EType, 0 also top

	private void AddBuildHelper(GameObject _obj)
	{
		var buildingHelper = _obj.GetComponent<BuildHelper>();
		if (buildingHelper == null) buildingHelper = _obj.AddComponent<BuildHelper>();
		buildingHelper.Set(m_buildingID, m_padIndex, m_height, m_direction, m_wildBlockID);
	}

	public bool Apply()
	{
		var wildBlock = GameState_WildBlock.Find(m_wildBlockID);
		if (wildBlock != null)
		{
			AddBuildHelper(wildBlock.Obj);
			return true;
		}
		return false;
	}

	public void Unapply()
	{
		var wildBlock = GameState_WildBlock.Find(m_wildBlockID);
		if (wildBlock != null)
			UnityEngine.Object.Destroy(wildBlock.Obj.GetComponent<BuildHelper>());
	}

	public static void LoadForBuildings()
	{
		Dictionary<int, Block> lookup = new();
		void ProcessCommanders(List<NGCommanderBase> _list)
		{
			foreach (var cmd in _list)
				if (cmd.GetComponentInChildren<BCBeacon>() is {} beacon && beacon.IsComplete() == false)
					foreach (var block in cmd.Visuals.GetComponentsInChildren<Block>())
						if (block.m_lastWildBlockID != 0)
							lookup[block.m_lastWildBlockID] = block;
		}
		ProcessCommanders(NGManager.Me.m_NGCommanderList);
		ProcessCommanders(NGManager.Me.m_NGCommanderListOutOfRange);
		foreach (var bh in GameManager.Me.m_state.m_buildHelpers)
		{
			if (lookup.TryGetValue(bh.m_wildBlockID, out var block))
			{
				if(block.gameObject.GetComponent<DTDragBlock>() == null)
					block.gameObject.AddComponent<DTDragBlock>();
				bh.AddBuildHelper(block.gameObject);
			}
		}
	}

	public static void Load(GameState_WildBlock _wild)
	{
		var buildHelpers = GameManager.Me.m_state.m_buildHelpers;
		for (int i = 0; i < buildHelpers.Count; ++i)
			if (buildHelpers[i].m_wildBlockID == _wild.m_id)
				buildHelpers[i].Apply();
	}
	
	public static bool Add(int _wildBlockID, int _buildingID, int _padIndex, int _height, int _direction)
	{
		if (GameState_WildBlock.Find(_wildBlockID) == null)
		{
			Debug.LogError($"Couldn't find wild block {_wildBlockID}");
			return false;
		}
		var buildHelpers = GameManager.Me.m_state.m_buildHelpers;
		for (int i = 0; i < buildHelpers.Count; ++i)
		{
			if (buildHelpers[i].m_wildBlockID == _wildBlockID)
			{
				if (_buildingID == -1)
				{
					buildHelpers[i].Unapply();
					buildHelpers.RemoveAt(i);
					Debug.LogError($"Removed BuildHelper for wild block {_wildBlockID}");
					return true;
				}
				buildHelpers[i].m_buildingID = _buildingID;
				buildHelpers[i].m_padIndex = _padIndex;
				buildHelpers[i].m_height = _height;
				buildHelpers[i].m_direction = _direction;
				buildHelpers[i].Apply();
				Debug.LogError($"Replaced BuildHelper for wild block {_wildBlockID} to building {_buildingID} pad {_padIndex} height {_height}");
				return true;
			}
		}
		var newBuildHelper = new GameState_BuildHelper() {m_wildBlockID = _wildBlockID, m_buildingID = _buildingID, m_padIndex = _padIndex, m_height = _height, m_direction = _direction};
		buildHelpers.Add(newBuildHelper);
		newBuildHelper.Apply();
		Debug.LogError($"Set new BuildHelper for wild block {_wildBlockID} to building {_buildingID} pad {_padIndex} height {_height}");
		return true;
	}
}

[Serializable]
public class GameState_ManaBall
{
	public Vector3 m_position;
	public float m_content;
	public string m_contentType;
	public float m_heightBase;
	public GameObject Object { get; set; }
}

[Serializable]
public class GameState_PathBreak
{
	public Vector3 m_position;
	public float m_repairLevel;
	public int m_repairOrder;
	public int m_pathBreakType;
}

[Serializable]
public class GameState_TreasurePit
{
	public Vector3 m_position;
	public float m_rotation;
	public float m_depth;
	public string m_content;
	public MATreasurePit Object { get; set; }
}

[Serializable]
public class GameState_TreasureChest
{
	public string m_prefab;
	public Vector3 m_position;
	public float m_rotation;
	public string m_content;
	public bool m_isOpen;
	public TreasureChestInteraction Object { get; set; }
}

[Serializable]
public class GameState_Grotesque
{
	public string m_prefab;
	public Vector3 m_position;
	public float m_rotation;
	public MAGrotesque Object { get; set; }
}

[Serializable]
public class GameState_CollectableManager
{
	public List<int> m_insultCounts = new List<int>((int)InsultTarget.Count);
}

[Serializable]
public class GameState_OrderGiver
{
	public string m_name;
	public float m_reputationScore;
	public int m_highestStarRating;
}

[Serializable]
public class GameState_MovementBlocker
{
	public string m_id;
	public Vector3 m_start, m_end;
	public bool m_enabled;
	private BoxCollider m_collider; 
	private PathBlock m_navBlocker;
	
	public void Update()
	{
		if (m_collider == null)
		{
			var go = new GameObject("MovementBlocker");
			go.transform.SetParent(RoadManager.Me.m_pathHolder);
			m_collider = go.AddComponent<BoxCollider>();
			go.layer = LayerMask.NameToLayer("Ignore Raycast");
			m_navBlocker = PathBlock.Create(m_collider.gameObject, 0, 0, 0, true, false);
		}
		m_collider.transform.position = ((m_start + m_end) * .5f).GroundPosition();
		m_collider.transform.forward = (m_end - m_start).GetXZNorm(); 
		m_collider.center = Vector3.zero;
		m_collider.size = new Vector3(.1f, 10, (m_end - m_start).magnitude);
		m_collider.enabled = m_enabled;
		m_navBlocker.m_width = 0.5f; // min width for nav grid
		m_navBlocker.m_length = m_collider.size.z * .5f;
		m_navBlocker.m_isActive = m_enabled;
	}
	
	public void EnableByPrefix(string _prefix, bool _enable)
	{
		if (m_id != null && m_id.StartsWith(_prefix, StringComparison.OrdinalIgnoreCase))
		{
			m_enabled = _enable;
			Update();
		}
	}
	public static void Enable(string _prefix, bool _enable)
	{
		var blockers = GameManager.Me.m_state.m_movementBlockers;
		for (int i = 0; i < blockers.Count; ++i)
			blockers[i].EnableByPrefix(_prefix, _enable);
	}

	public static void Initialise()
	{
		var blockers = GameManager.Me.m_state.m_movementBlockers;
		for (int i = 0; i < blockers.Count; ++i)
			blockers[i].Update();
	}
}

[Serializable]
public class GameState_SubScene
{
	public string m_current;
	public string m_lastExitUsed;
	public string m_lastEntranceUsed;
	public Vector3 m_originalPosition; // used to return a character if we're aborting due to day fail
	public GenericSubScene Current { get; set; }
	public string LastExitUsed => m_lastExitUsed;
	public string LastEntranceUsed => m_lastEntranceUsed;

	public void Load()
	{
		if (string.IsNullOrEmpty(m_current)) return;
		GenericSubScene.Open(m_current);
	}
}

[Serializable]
public class GameState_ExplodeInteraction
{
	public string m_id;
	public List<Vector3> m_positions = new List<Vector3>();
	public List<Vector3> m_rotations = new List<Vector3>();
	public int m_isEnabled = 0;
	public int m_isAttackable = 0;
}

[Serializable]
public class GameState_Rings
{
	public List<PlayerHandManager.RingType> m_ownedRings = new List<PlayerHandManager.RingType>();
	public List<PlayerHandManager.RingType> m_equippedRings = new List<PlayerHandManager.RingType>();

	public void Clear()
    {
		m_ownedRings.Clear();
		m_equippedRings.Clear();
    }
}

[Serializable]
public class GameState_Alignment
{
	[Serializable]
	public class ActionRecord
	{
		public float m_value;
		public int m_count;
	}
	public SDictionary<string, ActionRecord> m_actionHistory = new ();
	public float m_value = 0f;

	public void Clear()
	{
		m_value = 0f;
		m_actionHistory.Clear();
	}
}

[Serializable]
public class GameState_MarketForces
{
	public SDictionary<string, BlockMarketInfo> m_blockMarketInfo = new ();
	public string m_currentMarketForce;
	
	public void Clear()
	{
		m_currentMarketForce = null;
		m_blockMarketInfo.Clear();
	}
}

[Serializable]
public class GameState_Following
{
	public List<int> m_followingIds = new List<int>();
}

[Serializable]
public class GameState_PathUndoEntry
{
	public int m_roadId;
	public int m_changeType; // 0 - change, 1 - add, 2 - remove
	public PathManager.Path m_path;
}
[Serializable]
public class GameState_PathUndo
{
	public List<GameState_PathUndoEntry> m_entries = new();
	public int m_head = 0;
	public int Count => m_entries.Count;
	public void RemoveEnd() => m_entries.RemoveAt(m_entries.Count - 1);
	public void PushEntry(int _index, PathManager.Path _path, int _changeType)
	{
		m_entries.Add(new GameState_PathUndoEntry
		{
			m_roadId = _index,
			m_changeType = _changeType,
			m_path = _path
		});
	}

	public void Clear()
	{
		m_entries.Clear();
		m_head = 0;
	}
}

[Serializable]
public class GameState {
	const int c_header = 0x1e6acef;
	const int c_minimumVersion = 0x00010002;
	const int c_latestVersion = 0x00010004;
	const int c_seedVersion = 1;
	//
	public int m_header = c_header;
	public int m_version = c_latestVersion;
	public string m_terrainVersion;
	public int m_seedVersion = c_seedVersion;
	public string m_balanceSet;
	private static int s_currentSDictionaryBaseValidIncrementer = 0;
	public static int CurrentSDictionaryBaseValidIncrementer => s_currentSDictionaryBaseValidIncrementer;
	public static void InvalidateAllSDictionaryBases() => ++s_currentSDictionaryBaseValidIncrementer;

    public GameState_GameTime m_gameTime = new GameState_GameTime();
    public GameState_InputRecorder m_inputRecording = new();
    public List<GameState_RoadSegment> m_roadPieces = new List<GameState_RoadSegment>();
	public List<GameState_Building> m_buildings = new List<GameState_Building>();
	[SerializeReference] public List<GameState_Person> m_people = new List<GameState_Person>();
	[SerializeReference] public List<GameState_Character> m_minorCharacterTypes = new List<GameState_Character>();
	public List<GameState_CreatureBase> m_creatures = new List<GameState_CreatureBase>();
	public List<GameState_Hero> m_heroes = new List<GameState_Hero>();
	public List<GameState_MAVehicle> m_vehicles = new List<GameState_MAVehicle>();
	public int m_creaturesDefeated = 0;

	public List<(string name, bool seen)> m_uniqueCreaturesSpawned = new();
	public List<string> m_nightNewCreaturesSeen = new();

	public List<GameState_Product> m_products = new List<GameState_Product>();
    public List<GameState_Pickup> m_pickups = new List<GameState_Pickup>();
    public List<PathManager.Path> m_paths = new();
    public List<GameState_Lock> m_locks = new();
    public GameState_DesignTable m_designTableDetails = new();
    public List<GameState_KeyContainer> m_keyContainers = new();
    public GameState_TerrainPopulation m_terrainPopulationState = new GameState_TerrainPopulation();
	public SDictionary<string, int> m_unlocks = new SDictionary<string, int>();
	public Vector3 m_cameraPosition, m_cameraEulers;
	public Vector3 m_cameraControlFocus;
	public float m_cameraControlStage, m_cameraControlDirection;
	public SaveContainers.SaveCountryside m_oldStyleData = new SaveContainers.SaveCountryside();
	public GameState_DesignFitness m_fitnessData = new GameState_DesignFitness();
	public NGBusinessDecisionManager.NewGameType m_saveStartPoint = NGBusinessDecisionManager.NewGameType.None;
	public List<GameState_Deccoration> m_decorations = new List<GameState_Deccoration>();
    public GameState_Info m_gameInfo = new GameState_Info();
    public bool m_haveFinishedGame;
	public SDictionary<long, SaveContainers.SaveMABuildingComponent> m_maComponentData = new ();
	public long m_highestComponentId = 0;
	public string m_spawnPointData;
	public List<GameState_NamedPoint> m_namedPoints = new();
	public List<GameState_ManaBall> m_manaBalls = new();
	public List<GameState_TreasurePit> m_treasurePits = new();
	public List<GameState_TreasureChest> m_treasureChests = new();
	public List<string> m_openedSubSceneTreasureChests = new();
	public List<GameState_Grotesque> m_grotesques = new();
	public GameState_CollectableManager gameState_CollectableManager = new();
	public GameState_Stats m_gameStats = new();
	public List<GameState_PathBreak> m_pathBreaks = new();
	public List<GameState_BuildHelper> m_buildHelpers = new();
	public List<Vector3> m_gateStates = new();
	public List<GameState_MovementBlocker> m_movementBlockers = new();
	public SDictionary<string, MAParserManager.ParserCommand> m_parserCommands = new();
	public int m_highestOrderId = 0;
	public List<MAOrder> m_orderDataHistory = new();
	public SDictionary<int, MAOrder> m_orders = new SDictionary<int, MAOrder>();
	public SDictionary<string, int> m_akTriggers = new();
	public GameState_SubScene m_subSceneState = new();
	public string m_globalOrderData;
	public TavernSequence m_tavernSequence = new();
	public GameState_Rings m_ringsState = new();
	public List<GameState_ExplodeInteraction> m_explodeInteractions = new();
	public List<GameState_Bridge> m_bridgeStates = new();
	public SDictionary<int, GameState_Following> m_following = new();
	public GameState_PathUndo m_pathUndo = new();
	public List<MASpawnPointManager.GameState_DaySpawn> m_currentSpawnWaveThisDayByLocation = new();
	public List<GameState_WildBlock> m_wildBlocks = new();
	public SDictionary<string, bool> m_lockedGenericSubScenes = new();
	public GameState_Alignment m_alignment = new();
	public GameState_MarketForces m_marketForces = new();
	public SDictionary<string, GameState_OrderGiver> m_orderGivers = new();
	public List<string> m_disabledPlotExcluders = new();
	public List<string> m_keyUnlockableUnlockedKeys = new();
	public int m_nextWildBlockId = 1;
	public int[] m_uniquePeopleDressed;
	
	public GameState_ChallengeIcons m_challengeIcons = new ();
	
	public float m_powerMana;
	public float m_handScarAmount = 0.0f;
	public float m_handScarColourAmount = 0.0f;

	public List<string> m_debug = new();
	
    //***************** Tutorial Fields
    public float m_tutorialTimer = -1;
    public float m_parserTimer = -1;
    public float m_tutorialCounter = -1;
    public float m_tutorialResearchLabTimer = -1;
	public float m_tutorialConditionalTimer = -1;
	public int m_tutorialMasterIndex;
    // public List<NGTutorial.SaveTutorial> m_tutorialMasterStates;
    // public List<NGTutorial.SaveTutorial> m_tutorialTriggerStates;
    // public List<NGTutorial.SaveTutorial> m_tutorialDialogStates;
    public string m_interactions;

    //***************** End tutorial fields
    public string m_questSaveData;
	// RW-04-JUL-25: Unity 6.1 defines a load of colours by name, but we don't have that yet. This is orange.
	public Color m_spawnMarkerColour = new Color(1f, 0.4f, 0f, 1f);
	public bool m_nightChallegeDialogActive = false;

	public string m_lipSyncSaveData;

	public List<Vector3> specialLockedGates = new();

	public void Invalidate() { m_header = 0; m_version = 0; }
	public bool Confirm() { return m_header == c_header && m_version >= c_minimumVersion; }

	public void Prepare()
	{
		m_header = c_header;
		m_version = c_latestVersion;
		m_terrainVersion = GlobalData.Me.TerrainAsset;
		m_balanceSet = NGKnack.BalanceSet;
		GameStateTimer.Me.UpdateSecsSpentInSaveGame();
		PreSave();
	}

	public void PreSaveBuildings(bool _clearComponentDictionary = false, bool _includeOutOfRange = false)
	{
		if(_clearComponentDictionary)
			m_maComponentData.Clear();
		MABuilding.SaveAllComponents(m_maComponentData, _includeOutOfRange);
	}
	
	void PreSave() {
		for (int i = 0; i < m_pickups.Count; ++i) m_pickups[i].PreSave();
		for(int i = 0; i < NGManager.Me.MAVehicles.Count; ++i) NGManager.Me.MAVehicles[i].PreSave();
		foreach (var c in NGManager.Me.m_NGCommanderList)
		{
			if (c == null || c.m_stateData == null)
			{
				continue;
			}
			c.m_stateData.m_typeSpecificData = c.GetReactProto()?.m_typeSpecificData;
			c.m_stateData.m_name = c.Name;
		}
		
		foreach (var c in NGManager.Me.m_NGCommanderListOutOfRange)
		{
			c.m_stateData.m_name = c.Name;
		}
		
		//NGTutorialManager.Me.PreSave();
		RoadManager.Me.PreSave();
		MACharacterInteract.SaveAll();
		NamedPoint.SaveAll();
		foreach(var hero in NGManager.Me.m_MAHeroList) hero.Save();
		foreach(var creature in NGManager.Me.m_MACreatureList) creature.Save();
		MAOrderDataManager.Me.SaveAll();
		PreSaveBuildings();
		m_spawnPointData = MASpawnPoint.SaveAll();
		m_questSaveData = MAQuestManager.Me.SaveAll();
		m_lipSyncSaveData = LipSyncManager.Me.SaveAll();
	}

	public void PostSave()
	{
	//	NGTutorialManager.Me.PostSave();
		RoadManager.Me.PostSave();
	}

	public void Debug(string _s)
	{
#if UNITY_EDITOR || DEVELOPMENT_BUILD
		m_debug.Add($"{DateTime.UtcNow.Ticks} [{m_gameTime.m_gameTime} {m_gameTime.m_secondsActive}] {_s}");
#endif
	}

	public void PostLoad()
	{
		if (m_version == 0x00010003)
		{
			// building directions were in 90 degree increments rather than degrees
			for (int i = 0; i < m_buildings.Count; ++i)
			{
				m_buildings[i].m_direction *= 90;
			}
		}
		
		for (int i = m_paths.Count - 1; i >= 0; --i)
			if (m_paths[i].m_path.Count < 2)
				m_paths.RemoveAt(i);

		InvalidateAllSDictionaryBases();
		
		//NGTutorialManager.Me.PostLoad();
	}
}
