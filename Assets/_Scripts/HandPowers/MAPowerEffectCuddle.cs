using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MAPowerEffectCuddle : MAPowerEffectBase
{
    protected override bool NeedsTarget => true;

    public string[] m_resultAnimations;
    public Vector3 m_characterOffset = new Vector3(0, -.5f, 0);
    public Vector3 m_characterRotation = new Vector3(0, 180, 0);
    public float m_holdEncourageLevel = .1f;
    public float m_strokeEncourageLevel = .1f;
    public float m_patEncourageLevel = .1f;
    
    protected override GameObject audioFocus => gameObject;
    
    private float m_strokeHeight = 0;
    private enum EState
    {
        Idle,
        Pat,
        Stroke
    }
    private EState m_state = EState.Idle;
    private float m_timeInState = 0;
    private MACharacterBase m_character;
    private bool m_puttingDown = false;
    private bool m_pickingUp = true;
    private float m_encourageLevel = 0;

    MACharacterBase GetCharacter()
    {
        if (m_targets.Count == 0)
            return null;
        var target = m_targets[0];
        var chr = target.GetComponent<MACharacterBase>();
        return chr;
    }
    
    void Start()
    {
        m_character = GetCharacter();
        if (m_character == null)
        {
            Destroy(gameObject);
            return;
        }
        PlayerHandManager.Me.SetPowerActivateAnimationState(true);
        PlayerHandManager.Me.SetCuddleDropPoint(m_character.transform, () => {
            m_character.m_nav.Pause(true, true);
            if (m_character is MAWorker)
                m_character.SetState(NGMovingObject.STATE.HELD_BY_PLAYER);
            else
                MACharacterStateFactory.ApplyCharacterState(CharacterStates.HeldByPlayer, m_character);
            m_character.m_ragdollController.StartAnimatedState();
            m_pickingUp = false;
        });
        m_encourageLevel = m_holdEncourageLevel;
    }
    
    void LateUpdate()
    {
        if (m_pickingUp) return;
        
        m_character.transform.rotation = PlayerHandManager.Me.Holder.transform.rotation * Quaternion.Euler(m_characterRotation);
        m_character.transform.position = PlayerHandManager.Me.Holder.transform.position +
                                         m_character.transform.up * m_characterOffset.y + m_character.transform.forward * m_characterOffset.z + m_character.transform.right * m_characterOffset.x;
        if (m_character.m_ragdollController.IsAnimated == false)
            m_character.m_ragdollController.StartAnimatedState();

        if (m_puttingDown == false)
        {
            if (Input.GetMouseButton(0) == false)
                PutDown();
            UpdateControls();
        }
    }

    void PutDown()
    {
        GameManager.Me.RaycastAtPoint(Utility.InputPos, out var hit, GameManager.c_layerTerrainBit);
        m_puttingDown = true;
        PlayerHandManager.Me.SetCuddleDropPoint(hit.point, () => {
            Destroy(gameObject);
            m_character.Encourage(Mathf.Min(1, m_encourageLevel));
            m_character.transform.rotation = Quaternion.identity;
            m_character.m_nav.Unpause();
            m_character.FinallyDroppedByPlayer((NGCommanderBase)null, null);
            m_character.SetCollisionStyle(NGMovingObject.COLLISIONSTYLE.DEFAULT);
        });
        /*RagdollHelper.ThrowObjectRagdoll(m_character.gameObject, hit.point, RagdollHelper.Type.Humanoid, null, 30, () =>
        {
            m_character.m_nav.Unpause();
            m_character.FinallyDroppedByPlayer(null, PickupAction.None);
            m_character.SetCollisionStyle(NGMovingObject.COLLISIONSTYLE.DEFAULT);
        });
        Destroy(gameObject);*/
    }

    void OnDestroy()
    {
        PlayerHandManager.Me.SetPowerActivateAnimationState(false);
    }

    void UpdateControls()
    {
        if (Input.GetKey(KeyCode.UpArrow)) m_strokeHeight = Mathf.Clamp(m_strokeHeight - Time.deltaTime, -1f, 1f);
        if (Input.GetKey(KeyCode.DownArrow)) m_strokeHeight = Mathf.Clamp(m_strokeHeight + Time.deltaTime, -1f, 1f);

        const float c_strokeSize = .1f;
        float stroke = m_strokeHeight * (1 - c_strokeSize), prod = 0;
        switch (m_state)
        {
            case EState.Idle:
                if (Utility.GetKeyDown(KeyCode.I))
                {
                    m_state = EState.Pat;
                    m_timeInState = 0;
                }
                if (Utility.GetKeyDown(KeyCode.K))
                {
                    m_state = EState.Stroke;
                    m_timeInState = 0;
                }
                break;
            case EState.Pat:
                m_timeInState += Time.deltaTime;
                prod = Mathf.Abs(Mathf.Sin(m_timeInState * Mathf.PI * 2));
                if (m_timeInState >= .5f) m_state = EState.Idle;
                PlayResultAnimation();
                m_encourageLevel += m_patEncourageLevel;
                break;
            case EState.Stroke:
                m_timeInState += Time.deltaTime;
                var sinT = Mathf.Sin(m_timeInState * Mathf.PI * 2);
                stroke += sinT * c_strokeSize;
                if (m_timeInState >= 1f) m_state = EState.Idle;
                PlayResultAnimation();
                m_encourageLevel += m_strokeEncourageLevel;
                break;
        }
        PlayerHandManager.Me.SetAnimatorValue("PettingHeight", stroke);
        PlayerHandManager.Me.SetAnimatorValue("PettingProd", prod);
    }

    void PlayResultAnimation()
    {
        var height = Mathf.FloorToInt((m_strokeHeight * .5f + .5f) * 2.99999f);
        if (m_resultAnimations != null && height < m_resultAnimations.Length)
            m_character.PlaySingleAnimation(m_resultAnimations[height], null);
    }
}
