using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MAPowerEffectDig : MAPowerEffectBase
{
    public ParticleSystem m_particles;
    public int m_splatChannel = -1;
    public float m_splatIntensity = .3f;
    public float m_heightAdjust = -1f;
    public float m_radius = 3;
    private bool m_created = false;
    private bool m_ended = false;
    
    protected override bool NeedsTarget => false;

    protected override GameObject audioFocus => gameObject;
    
    private MATreasurePit m_pit;

    void Start()
    {
        float bestD2 = 5 * 5;
        m_pit = null;
        var pos = m_source.position;
        foreach (var pit in GameManager.Me.m_state.m_treasurePits)
        {
            var d2 = (pit.m_position - pos).xzSqrMagnitude();
            if (d2 < bestD2)
            {
                bestD2 = d2;
                m_pit = pit.Object;
            }
        }
        if (m_pit != null)
            transform.position = m_pit.transform.position.GroundPosition();
        else
            transform.position = m_source.position.GroundPosition();
    }

    const float c_noDigDuration = .5f;
    void LateUpdate()
    {
        TickTime();
        if (m_pit == null)
        {
            if (m_created == false)
                Begin(false);
            if (m_time > c_noDigDuration)
                Finish(false);
            if (m_ended && m_particles.particleCount == 0)
                Destroy(gameObject);
            return;
        }
        
        if (m_ended)
        {
            if (m_particles.particleCount == 0)
                Destroy(gameObject);
            return;
        }
        if (m_created == false)
            Begin(true);
        if (Input.GetMouseButton(0))
        {
            int updates = Tick();
            int digsComplete = 0;
            for (int i = 0; i < updates; ++i)
            {
                if (SpendMana() == false)
                {
                    Finish(true);
                    break;
                }
                ++digsComplete;
            }
            if (digsComplete > 0)
            {
                float fractionApplied = .01f * digsComplete;
                float damage = m_info.m_baseDamage * MATreasurePit.c_digDepthToUnearth * -fractionApplied;
                float splat = m_splatIntensity * fractionApplied;
                DigAtPoint(m_particles.transform.position, damage, m_splatChannel, splat, m_radius);
                if (m_pit.Dig())
                    Finish(true); // dig complete
            }
        }
        else
            Finish(true);
    }
    
    public static void DigAtPoint(Vector3 _position, float _depth, int _splatChannel, float _splatIntensity, float _radius)
    {
        int splat = _splatChannel;
        if (splat < 0) splat = CameraRenderSettings.Me.BedrockAtPoint(_position);
        var min = _position - Vector3.one * _radius;
        var max = _position + Vector3.one * _radius;
        var dirtyMin = new Vector3(GlobalData.TerrainXf(min.x), 0, GlobalData.TerrainZf(min.z));
        var dirtyMax = new Vector3(GlobalData.TerrainXf(max.x), 0, GlobalData.TerrainZf(max.z));
        
        CameraRenderSettings.Me.BeginSplatOperations(min, max);
        GlobalData.Me.BeginBatchTerrainOperations();
        CameraRenderSettings.Me.SetSplatCircle(_position, _radius, splat, _splatIntensity);
        GlobalData.Me.AdjustHeightsRadial(_position, 0, _radius, _depth, dirtyMin, dirtyMax);
        GlobalData.Me.EndBatchTerrainOperations();
        CameraRenderSettings.Me.EndSplatOperations();
    }

    void Begin(bool _isRealDig)
    {
        m_created = true;
        m_particles.Play();
        PlayFireAudio();
        PlayerHandManager.Me.SetHandState("Dig", true);
        if (_isRealDig) RegisterSuccessfulTrigger();
    }

    void Finish(bool _isRealDig)
    {
        m_particles.Stop();
        m_ended = true;
        PlayFinishAudio();
        PlayerHandManager.Me.SetHandState("Dig", false);
    }
}
