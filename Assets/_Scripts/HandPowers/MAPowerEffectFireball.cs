using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MAPowerEffectFireball : MAPowerEffectBase
{
    public ParticleSystem m_system;
    public ParticleSystem m_impactSystem;
    public float m_scaleBase = 1.75f;
    public float m_scalePerLevel = .25f;
    public float m_floatDuration = .5f;
    public float m_floatHeight = 5;
    public float m_floatForward = 2;
    private bool m_finished;
    private bool m_impactSystemStarted;
    private Vector3 m_lastDirection;
    private Vector3 m_hoverBase;
    private float m_lightIntensity = 0;

    protected override GameObject audioFocus => m_system.gameObject;
    
    void Start()
    {
        if (SpendMana() == false)
            m_couldNotAfford = true;
        
        UpdateTarget();
        var scale = m_level * m_scalePerLevel + m_scaleBase;
        m_system.transform.localScale = Vector3.one * scale;
        m_impactSystem.transform.localScale = Vector3.one * scale;
        m_hoverBase = m_source.position;
        
        StopCasterAnimation();
    }

    float ChainTime => m_floatDuration * 1.1f;

    void LateUpdate()
    {
        TickTime();
        bool isDead = !HasTarget && m_time > m_floatDuration;
        if (isDead)
        {
            m_system.Stop();
            m_finished = true;
        }
        if (HasTarget && m_time > ChainTime) FireNext(false);
        UpdateTarget();
        if (m_finished && m_system.particleCount == 0 && m_impactSystem.particleCount == 0 && m_lightIntensity < .001f)
            Destroy(gameObject);

        if (m_light != null)
        {
            float targetIntensity = m_finished ? 0 : 1;
            m_lightIntensity = Mathf.Lerp(m_lightIntensity, targetIntensity, .2f);
            m_light.transform.position = m_system.transform.position;
            m_light.SetIntensity(m_lightIntensity * m_lightIntensityBase);
        }
    }
    void UpdateTarget()
    {
        float floatDuration = m_floatDuration * (m_couldNotAfford ? .5f : 1f);
        PlayerHandManager.Me.SetPowerActivateAnimationState(m_time < floatDuration);
        if (m_time < floatDuration)
        {
            var t = m_time / floatDuration;
            t = Mathf.Min(1, t * 3);
            t = t * t * (3 - t - t);
            var toTarget = Vector3.zero;
            var floatMod = m_couldNotAfford ? .4f : 1f; 
            if (GameManager.Me.IsPossessing) floatMod = .2f;
            if (HasTarget) toTarget = (m_targets[m_baseIndex].position - m_source.position).normalized;
            m_system.transform.position = m_hoverBase + (Vector3.up * m_floatHeight + toTarget * m_floatForward) * t * floatMod;
        }
        else if (HasTarget && !m_finished)
        {
            PlayFireAudio();
            var target = m_targets[m_baseIndex].position + Vector3.up * 1;
            m_lastDirection = target - m_system.transform.position;
            var move = m_lastDirection;
            const float c_speed = 20;
            float maxDistance = c_speed * Time.deltaTime;
            if (move.sqrMagnitude > maxDistance * maxDistance)
                move = move.normalized * maxDistance;
            m_system.transform.position += move;
            if ((m_system.transform.position - target).sqrMagnitude < .1f * .1f)
            {
                CreateExplosionAtPoint(IDamageReceiver.DamageSource.HandPower, target.GroundPosition(), m_info.m_aoeRadius, 200f + 50f * m_level, this, m_lastDirection.normalized);
                m_system.Stop();
                m_impactSystem.transform.position = target;
                m_impactSystem.Play();
                PlayImpactAudio(0);
                m_finished = true;
            }
        }
    }
}
