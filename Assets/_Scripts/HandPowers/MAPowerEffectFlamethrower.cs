using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MAPowerEffectFlamethrower : MAPowerEffectBase
{
    public ParticleSystem m_system;
    public ParticleSystem m_targetSystem;
    private bool m_finished;
    private bool m_targetSystemStarted;
    private float m_burnTime = 1;
    private float m_lightIntensity = 0;

    void Start()
    {
        UpdateTarget();
        var scale = m_level * .25f + .75f; 
        m_system.transform.localScale = Vector3.one * scale;
        m_targetSystem.transform.localScale = Vector3.one * scale;
        if (SpendMana() == false)
            m_couldNotAfford = true;
    }

    void LateUpdate()
    {
        TickTime();
        const float c_noTargetDuration = .25f;
        bool isDead = HasTarget == false && m_time > c_noTargetDuration;

        if (SpendManaContinuous(Time.deltaTime) == false)
            isDead = true;

        PlayerHandManager.Me.SetPowerActivateAnimationState(!m_finished);

        if (m_targets.Count > 0) PlayFireAudio();
        if (m_finished)
        {
            m_burnTime -= Time.deltaTime;
            if (m_burnTime <= 0 && m_targetSystem.isPlaying)
                m_targetSystem.Stop();
            if (m_system.particleCount == 0 && m_targetSystem.particleCount == 0 && m_damagedLatestTime.Count == 0 && m_lightIntensity < .001f)
                Destroy(gameObject);
        }
        else if (isDead || IsPowerContinuing == false)
        {
            StopCasterAnimation();
            PlayFinishAudio();
            m_system.Stop();
            m_finished = true;
        }
        UpdateTarget();
        CheckDamage();
    }

    HashSet<NGMovingObject> m_damagedThisFrame = new();
    Dictionary<NGMovingObject, float> m_damagedLatestTime = new();  
    private void CheckDamage()
    {
        if (string.IsNullOrEmpty(m_targetAnimation)) return;
        if (m_finished == false)
        {
            m_damagedThisFrame.Clear();
            var sys = m_system.transform;
            var hits = Physics.OverlapCapsule(sys.position, sys.position + sys.forward * m_info.m_attackLength, m_info.m_attackWidth * .5f);
            foreach (var hit in hits)
            {
                var obj = hit.GetComponentInParent<NGMovingObject>();
                if (obj == null) continue;
                if (GameManager.Me.PossessedObject == obj.gameObject) continue;
                if (obj.Health <= 0) continue;
                if (m_damagedThisFrame.Contains(obj)) continue;
                m_damagedThisFrame.Add(obj);
                if (m_damagedLatestTime.ContainsKey(obj) == false)
                    StartCharacterDamage(obj);
                ApplyDamage(IDamageReceiver.DamageSource.HandPower, obj, true);
                m_damagedLatestTime[obj] = Time.time;
            }
        }
        const float c_burnTime = 1.0f;
        var toRemove = new List<NGMovingObject>();
        foreach (var kvp in m_damagedLatestTime)
        {
            if (kvp.Value + c_burnTime < Time.time || kvp.Key.Health <= 0)
            {
                StopCharacterDamage(kvp.Key);
                toRemove.Add(kvp.Key);
            }
        }
        foreach (var obj in toRemove)
            m_damagedLatestTime.Remove(obj);
    }

    private Dictionary<NGMovingObject, ParticleSystem> m_damageParticleSystems = new();
    private void StartCharacterDamage(NGMovingObject _obj)
    {
        PlayImpactAudioOnObject(_obj);
        var psys = Instantiate(m_targetSystem);
        psys.transform.position = _obj.transform.position + Vector3.up * 1.5f;
        var sysMain = psys.main;
        sysMain.stopAction = ParticleSystemStopAction.Destroy;
        psys.Play();
        m_damageParticleSystems[_obj] = psys;
        StartTargetAnimationLoop(_obj);
    }
    private void StopCharacterDamage(NGMovingObject _obj)
    {
        m_damageParticleSystems[_obj].Stop();
        m_damageParticleSystems.Remove(_obj);
        StopTargetAnimationLoop(_obj);
    }


    void UpdateTarget()
    {
        m_system.transform.position = m_source.position;
        var targetPos = m_targets.Count == 0 ? m_source.position + Vector3.up * 5 + m_source.forward * 5 : m_targets[0].position + Vector3.up * 2;
        m_system.transform.LookAt(targetPos, Vector3.up);
        if (m_targets.Count > 0)
            m_targetSystem.transform.position = m_targets[0].position + Vector3.up * 1.5f;

        if (m_light != null)
        {
            const float c_maxLightProgressTime = .5f;
            float lightProgress = Mathf.Min(1, m_time / c_maxLightProgressTime);
            m_lightIntensity = Mathf.Lerp(m_lightIntensity, m_finished ? 0 : 1, .3f); 
            m_light.transform.position = Vector3.Lerp(m_system.transform.position, targetPos, lightProgress * .5f);
            m_light.SetIntensity(m_lightIntensityBase * m_lightIntensity * UnityEngine.Random.Range(.8f, 1.2f));
        }
    }
}
