using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MAPowerEffectWhip : MAPowerEffectBase
{
    public ParticleSystem m_hitEffect;
    public float m_uncoilTime = 3.0f;
    public float m_hitTime = 1.0f;


    protected override GameObject impactAudioFocus => m_targets.Count > m_baseIndex ? m_targets[m_baseIndex].gameObject : gameObject;
    
    protected override bool NeedsTarget => true;

    bool m_started = false;
    
    void Update()
    {
        if (m_started == false)
        {
            m_started = true;
            if (m_targets.Count > 0 && SpendMana() == false)
            {
                PlayFireAudio();
                Destroy(gameObject);
                return;
            }
            var target = m_targets.Count > m_baseIndex ? m_targets[m_baseIndex] : null;
            PlayerHandManager.Me.SnapWhipAt(target, m_uncoilTime, m_hitTime, () => {
                PlayImpactAudio(0);
                if (m_targets.Count > m_baseIndex)
                {
                    var movingObj = m_targets[m_baseIndex].GetComponent<NGMovingObject>();
                    PlayImpactAudioOnObject(movingObj);
                    if (m_hitEffect != null)
                    {
                        m_hitEffect.transform.position = m_targets[m_baseIndex].transform.position + Vector3.up * 1.5f;
                        m_hitEffect.Play();
                    }
                    PlayTargetAnimation();
                    movingObj?.Whip();
                }
            }, () => PlayFinishAudio());
        }
        TickTime();
        bool effectActive = m_time < (m_uncoilTime + m_hitTime + 0.5f);
        
        PlayerHandManager.Me.SetPowerActivateAnimationState(effectActive);
        if (effectActive == false)
            Destroy(gameObject);
    }
}
