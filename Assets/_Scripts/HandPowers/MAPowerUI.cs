using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class MAPowerUI : MonoBehaviour
{
    public TMPro.TextMeshProUGUI m_powerLabel;
    public Image m_powerManaBar;
    public Image m_powerManaBarSmoothed;
    public Canvas m_canvas;
    
    void Start() => m_canvas = GetComponent<Canvas>();
    
    public void SetPowerLabel(string _label)
    {
        m_powerLabel.text = _label;
    }
    public void SetManaBar(float _value)
    {
    }
    public void Show(bool _show)
    {
        float target = _show ? 1 : 0;
        var canvasGroup = GetComponent<CanvasGroup>();
        canvasGroup.alpha = Mathf.Lerp(canvasGroup.alpha, target, .2f);
        bool finalShow = canvasGroup.alpha > .01f;
        if (finalShow != gameObject.activeSelf)
            gameObject.SetActive(finalShow);
    }

    void Update()
    {
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
        m_powerManaBar.fillAmount = GameManager.Me.m_state.m_powerMana / MAUnlocks.Me.MaxPowerMana;
#else
        m_powerManaBar.fillAmount = GameManager.Me.m_state.m_powerMana / CombatTesting.MaxMana;
#endif //!(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
        m_powerManaBarSmoothed.fillAmount = Mathf.Lerp(m_powerManaBarSmoothed.fillAmount, m_powerManaBar.fillAmount, .1f);
    }
}
