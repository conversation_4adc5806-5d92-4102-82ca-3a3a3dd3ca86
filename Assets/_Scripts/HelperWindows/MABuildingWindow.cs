#if UNITY_EDITOR
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

public class MABuildingWindow : EditorWindow
{
    private Vector2 m_scrollPosition;
    int m_selectPopup = 0;
    GameObject m_cameraFollowing = null;
    [MenuItem("22Cans/Debug Windows/Buildings")] 
    public static void ShowWindow()
    {
        var window = GetWindow<MABuildingWindow>("Show Buildings");
        
        window.titleContent = new GUIContent("Buildings");
        window.Show();
    }

    private void OnGUI()
    {
        if (GlobalData.Me == null || NGManager.Me == null)
        {
            EditorGUILayout.LabelField("No Buildings:", EditorStyles.boldLabel);
            return;
        }

        var buildings = NGManager.Me.m_maBuildings;
        if (buildings.Count == 0)
        {
            EditorGUILayout.LabelField("No Buildings Found", EditorStyles.boldLabel);
            return;
        }

        if (m_cameraFollowing)
        {
            if (GUILayout.Button("Stop Camera Following"))
            {
                GameManager.Me.StopCameraTrackingObject();
                m_cameraFollowing = null;
            }
        }

        EditorGUILayout.LabelField($"Buildings List[{buildings.Count}]:", EditorStyles.boldLabel);
        m_scrollPosition = EditorGUILayout.BeginScrollView(m_scrollPosition);

        GUIStyle boldFoldoutStyle = new GUIStyle(EditorStyles.foldout) {fontStyle = FontStyle.Bold};
        GUIStyle normalFoldoutStyle = new GUIStyle(EditorStyles.foldout);
        GUIStyle colourFoldoutStyle = new GUIStyle(EditorStyles.foldout);
        var labelStyle = new GUIStyle(EditorStyles.textArea){richText = true};
        foreach (var b in buildings)
        {
            var displayString = $"{b.name}";
            b.m_debugSelectedForWindow = EditorGUILayout.Foldout(b.m_debugSelectedForWindow, displayString, b.m_debugSelectedForWindow, normalFoldoutStyle);
            if (b.m_debugSelectedForWindow)
            {
                EditorGUI.indentLevel++; 
                EditorGUILayout.LabelField($"Name: ", b.Name, labelStyle);
                EditorGUILayout.LabelField($"DoorPosInner: ", b.DoorPosInner.ToString(), labelStyle);
                EditorGUILayout.LabelField($"DoorPosOuter: ", b.DoorPosOuter.ToString(), labelStyle);
                EditorGUILayout.LabelField($"IsBeingConstructed: ", b.IsBeingConstructed.ToString(), labelStyle);

                b.m_debugShowComponets = EditorGUILayout.Foldout(b.m_debugShowComponets, $"Componets[{b.m_components.Count}]", true, normalFoldoutStyle);
                if (b.m_debugShowComponets)
                {
                    EditorGUI.indentLevel++;
                    var countDict = new Dictionary<Type, List<BCBase>>();
                    foreach (var c in b.m_components)
                        if (countDict.ContainsKey(c.GetType()))
                            countDict[c.GetType()].Add(c);
                        else
                            countDict.Add(c.GetType(), new List<BCBase> {c});
                    foreach (var c in countDict)
                    { 
                        var dString = $"{c.Key} x {countDict[c.Key].Count}";
                        c.Value[0].m_debugShowComponetDetails = EditorGUILayout.Foldout(c.Value[0].m_debugShowComponetDetails, dString, true, normalFoldoutStyle);
                        if (c.Value[0].m_debugShowComponetDetails)
                        {
                            EditorGUI.indentLevel++; 

                            foreach (var c2 in c.Value)
                            {
                                c2.DebugShowGUIDetails(labelStyle);
                            }
                            EditorGUI.indentLevel--; 

                        }
                    }
                    EditorGUI.indentLevel--;
                }

                EditorGUI.indentLevel--; 

            }
        }
        EditorGUILayout.EndScrollView(); // End scroll view
    }
    void OnInspectorUpdate()
    {
        Repaint();
    }
}
#endif
