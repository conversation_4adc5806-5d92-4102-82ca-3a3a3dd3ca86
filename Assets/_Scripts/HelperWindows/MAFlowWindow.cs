#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Reflection;

#if OLDFlow
public class MAFlowWindow : EditorWindow
{
    class FlowBlocks
    {
        public string m_blockName;
        public List<NGBusinessFlow> m_flows;
        public bool m_selected;
    }
    List<FlowBlocks> m_flowBlocks = new List<FlowBlocks>();
    private Vector2 m_scrollPosition;

    private List<NGBusinessFlow> m_flows;
    
    private int m_selectedBlockIndex = -1;
    private int m_selectedFlowIndex = -1;
    bool m_setup = false;
    private bool m_showJustActive = true;
    int m_selectPopup = 0;

    [MenuItem("22Cans/Debug Windows/Game Flows")]
    public static void ShowWindow()
    {
        var window = GetWindow<MAFlowWindow>("Show Game Flows");
        
        window.titleContent = new GUIContent("Game Flows");
        window.Show();
    }

    private void OnGUI()
    {
        if (NGBusinessFlow.s_flows == null || NGBusinessFlow.s_flows.Count == 0)
        {
            EditorGUILayout.LabelField("MAGameFlow Empty:", EditorStyles.boldLabel);
            m_setup = false;
            return;
        }
        GUILayout.BeginHorizontal();
        GUIStyle boldButtonStyle = new GUIStyle(GUI.skin.button){fontStyle = FontStyle.Bold};
        var normalButtonStyle = new GUIStyle(GUI.skin.button);
        
        if (GUILayout.Button((MAGameFlow.m_pauseAllFlows) ? "UnPause Flows":"Pause Flows", (MAGameFlow.m_pauseAllFlows) ? boldButtonStyle : normalButtonStyle))
        {
            m_showJustActive = true;
            m_setup= false;
        }       
        if (GUILayout.Button("Show Active", (m_showJustActive == true) ? boldButtonStyle : normalButtonStyle))
        {
            m_showJustActive = true;
            m_setup= false;
        }       
        if (GUILayout.Button("Show All", (m_showJustActive == false) ? boldButtonStyle : normalButtonStyle))
        {
            m_showJustActive = false;
            m_setup= false;
        }
 
        GUILayout.EndHorizontal();
        if (m_setup == false || m_flowBlocks.Count == 0)
        {
            m_setup = true;
            m_flowBlocks = new();
            if (m_showJustActive == false)
            {
                foreach (var block in NGBusinessFlow.s_flowsDict)
                {
                    var flowBlock = new FlowBlocks()
                    {
                        m_blockName = block.Key,
                        m_flows = block.Value,
                        m_selected = false
                    };
                    if (block.Key == "Main")
                        m_flowBlocks.Insert(0, flowBlock);
                    else
                        m_flowBlocks.Add(flowBlock);
                }
            }
            else
            {
             
            }
        }
        //ShowTriggers
        if (MAGameInterface.Me)
        {
            var properties = typeof(MAGameInterface).GetProperties(BindingFlags.Static | BindingFlags.Public);
            if (properties.Length > 0)
            {
                EditorGUILayout.LabelField($"Triggers[{properties.Length}]:", EditorStyles.boldLabel);
                foreach (var prop in properties)
                {
                    bool val = (bool) prop.GetValue(null);
                    var newVal = EditorGUILayout.Toggle($"{prop.Name}", val);
                    if (val != newVal)
                        prop.SetValue(null, newVal);
                }
            }
        }
        if (m_showJustActive)
        {
            bool refresh = true;
            for (var index = m_flowBlocks.Count-1; index >= 0; index--)
            {
                var fb = m_flowBlocks[index];
                if (MAGameFlow.m_activeFlowss.ContainsKey(fb.m_blockName) == false)
                {
                    m_flowBlocks.RemoveAt(index);
                    refresh = false;
                }
            }
            
            foreach (var af in MAGameFlow.m_activeFlowss)
            {
                if (m_flowBlocks.Find(o => o.m_blockName == af.Key) == null)
                {
                    var flowBlock2 = new FlowBlocks()
                    {
                        m_blockName = af.Key,
                        m_flows = af.Value,
                        m_selected = false
                    };
                    m_flowBlocks.Add(flowBlock2);
                }
            }
        }
        EditorGUILayout.LabelField($"MAGameFlow List[{m_flowBlocks.Count}]:", EditorStyles.boldLabel);
        
        m_scrollPosition = EditorGUILayout.BeginScrollView(m_scrollPosition); // Start scroll view
        GUIStyle boldFoldoutStyle = new GUIStyle(EditorStyles.foldout) {fontStyle = FontStyle.Bold};;
        GUIStyle normalFoldoutStyle = new GUIStyle(EditorStyles.foldout);
        GUIStyle colourFoldoutStyle = new GUIStyle(EditorStyles.foldout);
        colourFoldoutStyle.normal.textColor = Color.yellow;
        foreach (var block in m_flowBlocks)
        {
            var activeFlow = MAGameFlow.m_activeFlows.Find(o => o.m_blockName == block.m_blockName);
            bool selected = block.m_selected;

            if (activeFlow != null)
            {
                var displayString = $"{block.m_blockName} : [{activeFlow.m_blockIndex}] {activeFlow.State} {activeFlow.m_flow.m_comment}";
                switch (activeFlow.State)
                {
                    case MAGameFlow.GameFlowState.WaitingForDecision:
                        displayString += $" -> {activeFlow.m_flow.m_businessDecision}";
                        break;
                    case MAGameFlow.GameFlowState.WaitingForOtherFlow:
                        displayString += $" -> {activeFlow.m_waitingForOtherFlow}";
                        break;
                }

                selected = EditorGUILayout.Foldout(selected, displayString, block.m_selected, boldFoldoutStyle);
            }
            else
            {
                var style = normalFoldoutStyle;
                if (block.m_flows.Count > 0 && block.m_flows[0].m_flowType.Contains("Trigger"))
                    style = colourFoldoutStyle;
                selected = EditorGUILayout.Foldout(selected, block.m_blockName, block.m_selected, style);
            }

            if (selected)
            {
                block.m_selected = true;
            }
            else
            {
                block.m_selected = false;
            }

            if (block.m_selected)
            {
                var style = new GUIStyle(EditorStyles.textArea);
                style.richText = true;
                EditorGUI.indentLevel++;
                foreach (var flow in block.m_flows)
                {
                    var pausedText = (flow.m_pauseThisFlow) ? "PAUSED" : "";
                    if (activeFlow != null && activeFlow.m_blockIndex == flow.m_blockIndex)
                    {
                        flow.m_debugWindowSelected = EditorGUILayout.Foldout(flow.m_debugWindowSelected, $"{flow.m_blockName}:{flow.m_blockIndex} {activeFlow.State} {activeFlow.m_flow.m_comment} {pausedText}", flow.m_debugWindowSelected, boldFoldoutStyle);
                    }
                    else
                    {
                        flow.m_debugWindowSelected = EditorGUILayout.Foldout(flow.m_debugWindowSelected, $"{flow.m_blockName}:{flow.m_blockIndex} {flow.m_comment} {pausedText}", flow.m_debugWindowSelected);
                    }

                    if (flow.m_debugWindowSelected)
                    {
                        EditorGUI.indentLevel++;
                        if (flow.m_message.IsNullOrWhiteSpace() == false)
                        {
                            EditorGUILayout.LabelField($"Message:", flow.m_message, style);
                            EditorGUILayout.LabelField($"MessageType:", flow.m_messageType, style);
                            EditorGUILayout.LabelField("StaticPose:", flow.m_staticPose, style);
                        }

                        if (flow.m_audioID.IsNullOrWhiteSpace() == false)
                            EditorGUILayout.LabelField("AudioID:", flow.m_audioID, style);
                        if (flow.m_enterWaitForTrigger.IsNullOrWhiteSpace() == false)
                            EditorGUILayout.LabelField("EnterTrigger:", flow.m_enterWaitForTrigger, style);
                        if (flow.m_enterFunctions.IsNullOrWhiteSpace() == false)
                            EditorGUILayout.LabelField("EnterFunctions:", flow.m_enterFunctions, style);
                        if (flow.m_exitFunctions.IsNullOrWhiteSpace() == false)
                            EditorGUILayout.LabelField("ExitFunction:", flow.m_exitFunctions, style);
                        if (flow.m_businessDecision.IsNullOrWhiteSpace() == false)
                        {
                            EditorGUILayout.LabelField("Decision:", flow.m_businessDecision, style);
                            EditorGUILayout.LabelField("Decision Multiplier:", flow.m_decisionValueMultiplier.ToString(), style);
                        }

                        if (flow.m_takeAllGifts.IsNullOrWhiteSpace() == false)
                            EditorGUILayout.LabelField("Take Gifts:", flow.m_takeAllGifts, style);
                        if (flow.m_chooseGifts.IsNullOrWhiteSpace() == false)
                            EditorGUILayout.LabelField("Choose Gifts:", flow.m_chooseGifts, style);

                        if (flow.m_takeAllGifts.IsNullOrWhiteSpace() == false || flow.m_chooseGifts.IsNullOrWhiteSpace() == false)
                            EditorGUILayout.LabelField("Gift Multiplier:", flow.m_giftCostMultiplier.ToString(), style);
                        if (flow.m_businessAdvisor.IsNullOrWhiteSpace() == false)
                            EditorGUILayout.LabelField("Advisor:", flow.m_businessAdvisor, style);
                        if (flow.m_flowType.IsNullOrWhiteSpace() == false)
                            EditorGUILayout.LabelField("FlowType:", flow.m_flowType, style);
                        flow.m_pauseThisFlow = EditorGUILayout.Toggle("Pause This Flow", flow.m_pauseThisFlow);
                        flow.m_debugBreakHere = EditorGUILayout.Toggle("Force Breakpoint", flow.m_debugBreakHere);
                        if (activeFlow != null)
                        {
                            m_selectPopup = EditorGUILayout.IntPopup($"Actions:", m_selectPopup, new string[] {"None", "Skip", "Redo"}, new int[] {0, 1, 2});
                        }
                        else
                        {
                            m_selectPopup = EditorGUILayout.IntPopup($"Actions:", m_selectPopup, new string[] {"None", "Skip"}, new int[] {0, 1});
                        }

                        switch (m_selectPopup)
                        {
                            case 0:
                                break;
                            case 1:
                                MAGameFlow.SkipFlow($"{flow.m_blockName}:{flow.m_blockIndex}");
                                m_selectPopup = 0;
                                break;
                            case 3:
                                activeFlow.Abort();
                                new MAGameFlow(activeFlow.m_flow);
                                m_selectPopup = 0;
                                break;
                        }

                        EditorGUI.indentLevel--;
                    }
                }

                EditorGUI.indentLevel--;
            }
        }


        EditorGUILayout.EndScrollView(); // End scroll view
    }
    private void OnGUIOld()
    {

        // Display header
        if (NGBusinessFlow.s_flows == null || NGBusinessFlow.s_flows.Count == 0)
        {
            EditorGUILayout.LabelField("NGBusinessFlow Empty:", EditorStyles.boldLabel);
            return;
        }

        EditorGUILayout.LabelField($"NGBusinessFlow List[{NGBusinessFlow.s_flows.Count}]:", EditorStyles.boldLabel);

        var flows = new List<NGBusinessFlow>();
        foreach (var flow in NGBusinessFlow.s_flows)
        {
            flows.Add(flow);
        }

        flows.Sort((x, y) =>
        {
            var xx = x.m_blockName + x.m_blockIndex;
            var yy = y.m_blockName + y.m_blockIndex;
            return xx.CompareTo(yy);
        });
        m_scrollPosition = EditorGUILayout.BeginScrollView(m_scrollPosition); // Start scroll view

        // Display each flow as a selectable label
        for (int i = 0; i < flows.Count; i++)
        {
            NGBusinessFlow flow = flows[i];
            //bool selected = EditorGUILayout.ToggleLeft(flow.m_blockName + "[" + flow.m_blockIndex + "]", _selectedFlowIndex == i);
            var activeFlow = MAGameFlow.m_activeFlows.Find(o => o.m_blockName == flow.m_blockName && o.m_blockIndex == flow.m_blockIndex);
            bool selected = m_selectedFlowIndex == i;
            if (activeFlow != null)
            {
                GUIStyle boldFoldoutStyle = new GUIStyle(EditorStyles.foldout);
                boldFoldoutStyle.fontStyle = FontStyle.Bold;
                selected = EditorGUILayout.Foldout(selected, flow.m_blockName + "[" + flow.m_blockIndex + $"] {activeFlow.State}", m_selectedFlowIndex == i, boldFoldoutStyle);
            }
            else
            {
                selected = EditorGUILayout.Foldout(selected, flow.m_blockName + "[" + flow.m_blockIndex + "]", m_selectedFlowIndex == i);
            }

            if (selected)
            {
                // if(m_selectedFlowIndex == i)
                // {
                //     m_selectedFlowIndex = -1;
                // }
                // else
                {
                    m_selectedFlowIndex = i;
                }
            }

            // Display message for selected flow (indented)
            if (m_selectedFlowIndex == i)
            {
                var style = new GUIStyle(EditorStyles.textArea);
                style.richText = true;
                EditorGUI.indentLevel++;
                //       EditorGUILayout.LabelField("Message:", EditorStyles.boldLabel);
                if (flow.m_message.IsNullOrWhiteSpace() == false)
                {
                    EditorGUILayout.LabelField($"<b>Message: </b>{flow.m_message}", style);
                    EditorGUILayout.LabelField($"<b>MessageType: </b>{flow.m_messageType}", style);
                    EditorGUILayout.LabelField($"<b>StaticPose: </b>{flow.m_staticPose}", style);

                }
                if(flow.m_audioID.IsNullOrWhiteSpace() == false)
                    EditorGUILayout.LabelField($"<b>AudioID: </b>{flow.m_audioID}", style);
                if (flow.m_enterWaitForTrigger.IsNullOrWhiteSpace() == false)
                    EditorGUILayout.LabelField($"<b>Trigger: </b>{flow.m_enterWaitForTrigger}", style);
                if (flow.m_businessDecision.IsNullOrWhiteSpace() == false)
                {
                    EditorGUILayout.LabelField($"<b>Decision: </b>{flow.m_businessDecision}", style);
                    EditorGUILayout.LabelField($"<b>Decision Multiplier: </b>{flow.m_decisionValueMultiplier}", style);
                }
                if (flow.m_takeAllGifts.IsNullOrWhiteSpace() == false)
                    EditorGUILayout.LabelField($"<b>Take Gifts: </b>{flow.m_takeAllGifts}", style);
                if (flow.m_chooseGifts.IsNullOrWhiteSpace() == false)
                    EditorGUILayout.LabelField($"<b>Choose Gifts: </b>{flow.m_chooseGifts}", style);
                if (flow.m_takeAllGifts.IsNullOrWhiteSpace() == false || flow.m_chooseGifts.IsNullOrWhiteSpace() == false)
                    EditorGUILayout.LabelField($"<b>Gift Multiplier: </b>{flow.m_giftCostMultiplier}", style);
                if (flow.m_businessAdvisor.IsNullOrWhiteSpace() == false)
                    EditorGUILayout.LabelField($"<b>Advisor: </b>{flow.m_businessAdvisor}", style);
                if (flow.m_tutorialPhase.IsNullOrWhiteSpace() == false)
                    EditorGUILayout.LabelField($"<b>Tutorial Phase: </b>{flow.m_tutorialPhase}", style);
                if (flow.m_exitFunctions.IsNullOrWhiteSpace() == false)
                    EditorGUILayout.LabelField($"<b>ExitFunction: </b>{flow.m_exitFunctions}", style);

                if(activeFlow != null)
                {
                    m_selectPopup = EditorGUILayout.IntPopup($"Actions:", m_selectPopup, new string[] { "None", "Skip", "Restart", "Redo" }, new int[] { 0, 1, 2, 3 });
                }
                else
                {
                    m_selectPopup = EditorGUILayout.IntPopup($"Actions:", m_selectPopup, new string[] { "None", "Skip", "Restart"}, new int[] { 0, 1, 2});
                }
                switch (m_selectPopup)
                {
                    case 0:
                        break;
                    case 1:
                        MAGameFlow.SkipFlow($"{flow.m_blockName}:{flow.m_blockIndex}");
                        m_selectPopup = 0;
                        break;
                    case 2:
                        MAGameFlow.Reset(flow.m_blockName);
                        m_selectPopup = 0;
                        break;
                    case 3:
                        activeFlow.Abort();
                        new MAGameFlow(activeFlow.m_flow);
                        m_selectPopup = 0;
                        break;
                        
                }
                EditorGUI.indentLevel--;
                EditorGUILayout.Space(); // Add some space between flows
            }
        }
        EditorGUILayout.EndScrollView(); // End scroll view

    }

    void OnInspectorUpdate()
    {
        Repaint();
    }
}
#endif
#endif
