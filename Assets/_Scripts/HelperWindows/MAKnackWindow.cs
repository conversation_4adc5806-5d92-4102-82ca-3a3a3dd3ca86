#if UNITY_EDITOR
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;
using File = UnityEngine.Windows.File;

public class MAKnackWindow: EditorWindow
{
    private Vector2 _scrollPosition;
    private int m_selectedFlowIndex = -1;
    int m_selectPopup = 0;

    [MenuItem("22Cans/Debug Windows/Knack Window")]
    public static void ShowWindow()
    {
        var window = GetWindow<MAKnackWindow>("Knack Window");
        window.minSize = new Vector2(400, 300);
        window.titleContent = new GUIContent("Knack Window");
        window.Show(); 
    }
    private void OnGUI()
    {
        EditorGUILayout.LabelField("Knack Window", EditorStyles.boldLabel);
        NGKnack.SetEditorMe();
        if (NGKnack.s_Me == null )
        {
            EditorGUILayout.LabelField("NGKnack.Me is null", EditorStyles.boldLabel);
            return;
        }
        _scrollPosition = EditorGUILayout.BeginScrollView(_scrollPosition);
        var knackList = NGKnack.s_Me.m_importKnacks;
        knackList.Sort((a, b) => a.m_jsonName.CompareTo(b.m_jsonName));
        foreach(var knack in knackList)
        {
            var foldoutText = knack.m_jsonName;
            var fileName = knack.JsonFileName;
            if(File.Exists(fileName) == false)
            { 
                foldoutText+= " <color=red>File not found</color>";
            }
            else
            {
                var fileInfo = new FileInfo(fileName);
                var lastModifiedTime = fileInfo.LastWriteTime;

                var formattedTime = lastModifiedTime.ToString("yyyy-MM-dd HH:mm:ss");
                var oneHour = TimeSpan.FromHours(1);
                var isRecent = lastModifiedTime > DateTime.Now.Subtract(oneHour);

                if (isRecent)
                {
                    foldoutText += $" <color=green> Last Modified: {formattedTime}</color>";
                }
                else
                {
                    foldoutText += $" <color=#006334ff> Last Modified: {formattedTime}</color>";
                }
            }

            GUIStyle richFoldoutStyle = new GUIStyle(EditorStyles.foldout){richText = true};
            
            GUILayout.BeginHorizontal();
            if(GUILayout.Button("\u2b07\ufe0f", GUILayout.Width(25))) NGKnack.CacheKnack(knack);
            knack.m_debugWindowSelected = EditorGUILayout.Foldout(knack.m_debugWindowSelected, foldoutText, knack.m_debugWindowSelected, richFoldoutStyle);
            GUILayout.EndHorizontal();
  
            if (knack.m_debugWindowSelected)
            {
                var style = new GUIStyle(EditorStyles.textArea){richText = true};
                EditorGUI.indentLevel++;
                EditorGUILayout.LabelField($"tJsonName:",knack.m_jsonName.ToString(), style);
                EditorGUILayout.LabelField($"Class:",knack.m_class.ToString(), style);

                EditorGUILayout.LabelField($"KnackTable:",knack.m_knackTable.ToString(), style);
                EditorGUILayout.LabelField($"KnackPagesScene:",knack.m_knackPagesScene.ToString(), style);

                EditorGUILayout.LabelField($"KnackPagesView:", knack.m_knackPagesView.ToString(), style);
                EditorGUILayout.LabelField($"IsView:",knack.m_isView.ToString(), style);
                EditorGUILayout.LabelField($"JsonFileLocation:",knack.m_jsonFileLocation.ToString(), style);
                EditorGUILayout.LabelField($"CSVFileLocation:",knack.m_csvFileLocation.ToString(), style);
                m_selectPopup = EditorGUILayout.IntPopup($"Actions:", m_selectPopup, new string[] { "None", "Cache", "Import"}, new int[] { 0, 1, 2 });
                switch (m_selectPopup)
                {
                    case 0:
                        break;
                    case 1:
                        NGKnack.CacheKnack(knack);
                        
                        m_selectPopup = 0;
                        break;
                    case 2:
                        NGKnack.CacheKnack(knack);

                        var c = knack.GetClassType.GetMethod("LoadInfo");
                        if (c != null)
                            c.Invoke(null, null);
                        m_selectPopup = 0;
                        break;
                }
                EditorGUI.indentLevel--;
            }
        }
        EditorGUILayout.EndScrollView(); // End scroll view

    }
}
#endif
