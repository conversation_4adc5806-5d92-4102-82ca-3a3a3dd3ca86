#if UNITY_EDITOR
using UnityEngine;

using System;
using System.Collections;
using System.Collections.Generic;
using System.Reflection;
using UnityEditor;

public class MAListerWindow : EditorWindow
{
    private IList dataList;
    private Type dataType;
    private FieldInfo[] fields;

    private Vector2 scrollPosition;

    public void Initialize(IList list)
    {
        dataList = list;
        dataType = list.GetType().GetGenericArguments()[0];
        fields = dataType.GetFields();
    }
    private int m_changeCount = 0;

    private void OnGUI()
    {
        if (dataList == null)
            return;

        if (GUILayout.Button($"Write Changes [{m_changeCount}]"))
        {
            WriteChanges();
        }

        if (GUILayout.Button("Add New"))
        {
            var obj = Activator.CreateInstance(dataType);
            dataList.Add(obj);
            m_changeCount++;
        }

        List<(FieldInfo field, float width)> filteredFields = new List<(FieldInfo, float)>();
        scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
        GUILayout.BeginHorizontal();

        foreach (var field in fields)
        {
            if (field.Name.StartsWith("m_"))
            {
                float width = GetColumnWidth(field);
                filteredFields.Add((field, width));
                GUILayout.Label(field.Name, EditorStyles.boldLabel, GUILayout.Width(width));
            }
        }

        HandleColumnResizing(filteredFields);

        GUILayout.EndHorizontal();
        if (filteredFields.Count == 0)
            return;


        foreach (var item in dataList)
        {
            // GUILayout.BeginHorizontal(EditorStyles.helpBox);
            GUILayout.BeginHorizontal();

            foreach ((FieldInfo field, float width) in filteredFields)
            {
                EditorGUI.BeginChangeCheck();
                var value = field.GetValue(item);
                object changeValue = null;
                if (field.FieldType.IsEnum)
                {
                    Rect fieldRect = GUILayoutUtility.GetRect(width, EditorGUIUtility.singleLineHeight);
                    changeValue = EditorGUI.EnumPopup(fieldRect, (Enum)field.GetValue(item));
                }
                else
                {
                    changeValue = GUILayout.TextField(value?.ToString() ?? "null", GUILayout.Width(width));
                }
                if (EditorGUI.EndChangeCheck())
                {
                    m_changeCount++;
                    try
                    {
                        field.SetValue(item, changeValue);
                    }
                    catch (FormatException)
                    {
                        Debug.LogWarning($"Invalid input for field: {field.Name}");
                    }
                    //field.SetValue(item, Convert.ChangeType(changeValue, field.FieldType));
                }
            }

            GUILayout.EndHorizontal();
        }

        EditorGUILayout.EndScrollView();
    }
    private int resizingColumnIndex = -1;
    private Vector2 resizeStartMousePos;
    private void HandleColumnResizing(List<(FieldInfo field, float width)> fields)
    {
        var e = Event.current;
        if (e.type == EventType.MouseDown)
        {
            // Check if mouse is over a column divider
            for (int i = 0; i < fields.Count; i++)
            {
                Rect columnRect = GUILayoutUtility.GetLastRect();
                float dividerX = columnRect.xMax - 2; // Adjust for divider width
                if (e.mousePosition.x > dividerX && e.mousePosition.x < dividerX + 4)
                {
                    resizingColumnIndex = i;
                    resizeStartMousePos = e.mousePosition;
                    e.Use();
                    break;
                }
            }
        }
        else if (e.type == EventType.MouseDrag && resizingColumnIndex >= 0)
        {
            // Resize the column
            float deltaX = e.mousePosition.x - resizeStartMousePos.x;
            fields[resizingColumnIndex] = (fields[resizingColumnIndex].field, fields[resizingColumnIndex].width + deltaX);
            Repaint();
        }
        else if (e.type == EventType.MouseUp)
        {
            resizingColumnIndex = -1;
        }
    }

    // Function to calculate column width based on type
    private float GetColumnWidth(FieldInfo field)
    {
        switch (field.FieldType.ToString())
        {
            case "System.Boolean":
                return 50f;
            case "System.Int32":
            case "System.Single":
                return 100f;
            case "System.String":
                return 200f;
            default:
                return 150f; // Default width for other types
        }
    }

    void WriteChanges()
    {
        
    }
    static public void Create(IList _list, string _title)
    {
        var list = _list; 

        var window = GetWindow<MAListerWindow>();
        window.titleContent = new GUIContent($"List '{_title}' Editor");
        window.position = new Rect(100, 100, 1000, 600);
        window.Initialize(list);
        window.Show();
    }
}
#endif
