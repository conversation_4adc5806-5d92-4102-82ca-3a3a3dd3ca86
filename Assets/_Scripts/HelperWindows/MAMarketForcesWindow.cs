#if UNITY_EDITOR
using UnityEditor;
using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

public class MAMarketForcesWindow : EditorWindow
{
    private Vector2 m_scrollPosition;
    
    [MenuItem("22Cans/Debug Windows/Market Forces")] 
    public static void ShowWindow()
    {
        var window = GetWindow<MAMarketForcesWindow>("MarketForces");
        
        window.titleContent = new GUIContent("Market Forces");
        window.Show();
    }

    private void OnInspectorUpdate()
    {
        Repaint();
    }

    private void DestroyUIObject(Graphic _toDestroy)
    {
        if (_toDestroy != null)
        {
            Destroy(_toDestroy.gameObject);
        }
    }
    
    
    private string[] Groups
    {
        get
        {
            if(m_groups == null || MAMarketForce.s_allForces.Count != m_groups.Length)
            {
                List<string> groups = new();
                foreach(var market in MAMarketForce.s_allForces)
                {
                    if(groups.Contains(market.m_groupName)) continue;
                    groups.Add(market.m_groupName);
                }
                m_groups = groups.ToArray();
            }
            return m_groups;
        }
    }
    private string[] m_groups = null;
    private int m_selectedGroup;
    
    void OnGUI()
    {
        if (GlobalData.Me == null || NGManager.Me == null || GameManager.Me == null || GameManager.Me.LoadComplete == false)
        {
            EditorGUILayout.LabelField("Game Not Loaded", EditorStyles.boldLabel);
            return;
        }
        
        GUIStyle style = new GUIStyle ();
        
        style.normal.textColor = Color.white;
        style.richText = true;
        
        EditorGUILayout.LabelField("<b>Market Forces</b>", style);
        EditorGUILayout.LabelField($"Current: {GameManager.Me.m_state.m_marketForces.m_currentMarketForce}", style);
        
        if (GUILayout.Button("Clear"))
        {
            MAMarketForcesManager.Me.ClearMarketForces();
        }
        
        GUILayout.BeginHorizontal();
        m_selectedGroup = EditorGUILayout.Popup(m_selectedGroup, Groups);
        if (GUILayout.Button("Apply"))
        {
            MAMarketForcesManager.Me.ApplyMarketForces(Groups[m_selectedGroup]);
        }
        GUILayout.EndHorizontal();
    }
}
#endif
