#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using UnityEditor;
using UnityEngine;

public class MAParserFlowWindow : EditorWindow
{
    private Vector2 m_scrollPosition;

    bool m_setup = false;
    private bool m_showJustActive = true;
    private int m_selectPopup = 0;
    private int m_selectPopupLoad = 0;
    private int m_selectPopupSkip = 0;
    private int m_selectedAdvisor = 0;
    private int m_selectedTab = 0;
    private string m_executeSection = "";
    private GUIStyle boldButtonStyle;
    private GUIStyle normalButtonStyle;
    private GUIStyle whiteBoldFoldoutStyle;
    private GUIStyle yellowBoldFoldoutStyle;
    private bool m_toolsSelected = false;
    private bool m_spawnsSelected = false;
    private bool m_showChecksSelected = false;

    [MenuItem("22Cans/Debug Windows/Parser Flows")]
    public static void ShowWindow()
    {
        var window = GetWindow<MAParserFlowWindow>("Show Parser Flows");
        
        window.titleContent = new GUIContent("Parser Flows");
        window.Show();
    }

    void DisplayChecks(MAParserSection _section)
    {
        m_showChecksSelected = EditorGUILayout.Foldout(m_showChecksSelected, $"Checks[{_section.m_checks.Count}]", true, whiteBoldFoldoutStyle);
        if (m_showChecksSelected)
        {
            foreach (var c in _section.m_checks)
            {
                EditorGUILayout.LabelField($"{c.m_check} :: {c.m_triggered}");
            }            
        }
    }
    void DisplaySpawns()
    {
        if (DayNight.Me == null) return;
        var day = DayNight.Me.CurrentWorkingDay;
        var spawns = MASpawnByDayInfo.s_creatureSpawnByDayInfos.FindAll(o => o.m_dayNum == day);
        m_spawnsSelected = EditorGUILayout.Foldout(m_spawnsSelected, $"Spawns[{spawns.Count}]", true, whiteBoldFoldoutStyle);
        if (m_spawnsSelected)
        {
            foreach (var spawn in spawns)
            {
                var txt = $"{spawn.m_spawnLocation}:{spawn.m_creature}[{spawn.m_spawnTimeSeconds}]:{spawn.m_count}";
                EditorGUILayout.LabelField(txt);
            }            
        }
    }

    void ActivateStyles()
    {
        normalButtonStyle = new GUIStyle(GUI.skin.button)
        {
            fontStyle = FontStyle.Normal,
            fontSize = 12,
            alignment = TextAnchor.MiddleCenter,
            fixedHeight = 20 // Inspector-looking buttons are short
        };

        boldButtonStyle = new GUIStyle(GUI.skin.button)
        {
            fontStyle = FontStyle.Bold,
            fontSize = 12,
            alignment = TextAnchor.MiddleCenter,
            fixedHeight = 20
        };
        whiteBoldFoldoutStyle = new GUIStyle(EditorStyles.foldout)
        {
            fontStyle = FontStyle.Bold,
            richText = true,
            normal = { textColor = new Color(1f, 1f, 1f) }  // Custom light yellow color
        };
        yellowBoldFoldoutStyle = new GUIStyle(EditorStyles.foldout)
        {
            fontStyle = FontStyle.Bold,
            normal = { textColor = new Color(1f, 1f, 0.6f) }  // Custom light yellow color
        };
    }
    private void OnGUI()
    {
        string[] tabs = { "Active", "Chapter1", "Chapter2" };
        ActivateStyles();
        switch (m_selectedTab)
        {
            case 0:
                ActivateFlows();
                break;
            case 1:
                break;
        }
    }
    private void ActivateFlows()
    {
        DisplayTools();
        if(MAParserManager.Me == null)
        {
            EditorGUILayout.LabelField("No MAParserFile found");
            return;
        }

        var files = MAParserManager.GetAllFlowFiles();
        if (files.Count > 1)
        {
            var options = new int[files.Count];
            for(int i = 0; i < files.Count; i++)
                options[i] = i;
            m_selectPopupLoad = EditorGUILayout.IntPopup($"Execute:", m_selectPopupLoad, files.ToArray(), options);
            if(m_selectPopupLoad != 0)
            {
                MAParserManager.Me.ExecuteSection(files[m_selectPopupLoad]);
                m_selectPopupLoad = 0;
            }
            AllowSkip(files);

        }
        if(MAParserManager.Me.m_sections.Count == 0)
        {
            EditorGUILayout.LabelField("No sections found");
            return;
        }

        
        
        GUIStyle whiteBoldFoldoutStyle = new GUIStyle(EditorStyles.foldout) {fontStyle = FontStyle.Bold};
        GUIStyle yellowBoldFoldoutStyle = new GUIStyle(EditorStyles.foldout)
        {
            fontStyle = FontStyle.Bold,
            normal = { textColor = new Color(1f, 1f, 0.6f) }  // Custom light yellow color
        };
        GUIStyle boldLabelStyle = new GUIStyle(EditorStyles.label) {fontStyle = FontStyle.Bold};
        GUIStyle normalFoldoutStyle = new GUIStyle(EditorStyles.foldout);
        GUIStyle colourFoldoutStyle = new GUIStyle(EditorStyles.foldout);
        m_scrollPosition = EditorGUILayout.BeginScrollView(m_scrollPosition);
        DisplayTOD();
        DisplaySpawns();
        string sectionPrefix = "";
        var dName = MAParserManager.Me.m_whichFlow.ToString();
        EditorGUILayout.LabelField($"USING {dName.ToUpper()}", boldLabelStyle);
        sectionPrefix = $"{dName}/";
 
        foreach (var section in MAParserManager.Me.m_sections)
        {
            section.m_debugSelected = EditorGUILayout.Foldout(section.m_debugSelected, $"{sectionPrefix}{section.m_name}:{section.m_currentLine}/{section.Lines.Count}", true, whiteBoldFoldoutStyle);
            if (section.m_debugSelected)
            {
                EditorGUI.indentLevel++;
                DisplayChecks(section);
                /*if(section.m_currentTrackFloat.Count > 0)
                {
                    EditorGUILayout.LabelField("m_currentTrackFloat:", boldLabelStyle);
                    EditorGUI.indentLevel++;
                    foreach ((string id, float value) trackFloat in section.m_currentTrackFloat)
                    {
                        EditorGUILayout.LabelField($"{trackFloat.id}:{trackFloat.value}", boldLabelStyle);
                    }
                    EditorGUI.indentLevel--;
                }
                if(section.m_checks.Count > 0)
                {
                    EditorGUILayout.LabelField("m_checks:", boldLabelStyle);
                    EditorGUI.indentLevel++;
                    foreach (var check in section.m_checks)
                    {
                        EditorGUILayout.LabelField($"{check}", boldLabelStyle);
                    }
                    EditorGUI.indentLevel--;
                }*/
                GUILayout.BeginHorizontal();
                if (section.m_isStepping)
                {
                    if (GUILayout.Button("Run", boldButtonStyle))
                    {
                        section.m_isStepping = false;
                        section.m_nextStep = false;

                        break;
                    }
                    if (GUILayout.Button("Next", boldButtonStyle))
                    {
                        section.m_nextStep = true;
                    }
                }
                else
                {
                    if (GUILayout.Button("Step", boldButtonStyle))
                    {
                        section.m_isStepping = true;
                        break;
                    }
                }
                

                if (GUILayout.Button("Reload", boldButtonStyle))
                {
                    var fname = section.m_fileName;
                    var lineNo = section.m_currentLine;
                    var stepping = section.m_isStepping;
                    
                    MAParserManager.Me.m_sections.Remove(section);
                    var newSection = MAParserManager.Me.ExecuteSection(fname, lineNo);
                    newSection.m_isStepping = stepping;
                    foreach (var s in MAParserManager.Me.m_sections)
                    {
                        if(s.m_currentCall == section)
                            s.m_currentCall = newSection;
                    }
                    break;
                }
                if (GUILayout.Button("Kill", boldButtonStyle))
                {
                    section.ClearCurrents();
                    MAParserManager.Me.m_sections.Remove(section);
                    break;
                }
                GUILayout.EndHorizontal();
                int braceCount = 0;
                for (var i = 0; i < section.Lines.Count; i++)
                {
                    var line = section.Lines[i];
                    if(line.m_line == "") continue;

                    if(line.m_line == "}" && braceCount > 0)
                        braceCount--;
                    var s = $"{line.m_lineNumber}:{line.m_line}";
                    string changedString = s;
                    EditorGUI.indentLevel+=braceCount;
                    
                    GUILayout.BeginHorizontal();
                    if(GUILayout.Button("->", GUILayout.Width(25))) section.SkipToLine(i);
                    line.DebugSelected = EditorGUILayout.Foldout(line.DebugSelected, s, true, (i == section.m_currentLine) ? yellowBoldFoldoutStyle : normalFoldoutStyle);
                    GUILayout.EndHorizontal();
                    
                    if (line.DebugSelected) 
                    {
                        EditorGUI.indentLevel++;
                        /*m_selectPopup = EditorGUILayout.IntPopup($"Actions:", m_selectPopup, new string[] {"None", "Skip"}, new int[] {0, 1,});
                        switch (m_selectPopup)
                        {
                            case 0:
                                break;
                            case 1:
                                section.SkipToLine(i);
                                m_selectPopup = 0;
                                break;

                        }*/
                        //EditorGUILayout.LabelField("Line:", boldLabelStyle);
                        GUIStyle textAreaStyle = new GUIStyle(EditorStyles.textArea);
                        textAreaStyle.alignment = TextAnchor.UpperLeft; // Left align and top justify the text
                        textAreaStyle.wordWrap = true; // Enable word wrap if needed
                        textAreaStyle.padding = new RectOffset(5, 5, 5, 5);
                        GUILayout.BeginVertical(EditorStyles.helpBox);
                        changedString = EditorGUILayout.TextArea(line.m_line, boldLabelStyle, GUILayout.Height(75),GUILayout.Width(75), GUILayout.ExpandWidth(true));
                        //changedString = EditorGUILayout.TextArea(line.m_line, textAreaStyle, GUILayout.Height(75),GUILayout.Width(75));
                        GUILayout.EndVertical();
                        //changedString = EditorGUILayout.TextField("Line:",line.m_line, boldLabelStyle);
                        if (changedString != line.m_line)
                        {
                            section.ChangeLine(line, changedString);
                        }
                        var changedInt = EditorGUILayout.IntField("Line Number:", line.m_lineNumber, boldLabelStyle);
                        if(changedInt != line.m_lineNumber)
                            line.m_lineNumber = changedInt;
                        

                        EditorGUI.indentLevel--;
                    }
                    EditorGUI.indentLevel-=braceCount;
                    if(line.m_line == "{")
                        braceCount++;
                }
                EditorGUI.indentLevel--;
            }
            

        }
        EditorGUILayout.EndScrollView(); // End scroll view
    }

    void OnInspectorUpdate()
    {
        Repaint();
    }
    void DisplayTools()
    {
        m_toolsSelected = EditorGUILayout.Foldout(m_toolsSelected, $"Tools", true, whiteBoldFoldoutStyle);
        if (m_toolsSelected)
        {
            GUILayout.Space(10);

            EditorGUILayout.BeginVertical("box");
            EditorGUI.indentLevel+=0;

            if (GUILayout.Button((MAParserManager.m_pauseAllFlows) ? "UnPause Flows" : "Pause Flows", (MAParserManager.m_pauseAllFlows) ? boldButtonStyle : normalButtonStyle))
            {
                MAParserManager.m_pauseAllFlows = !MAParserManager.m_pauseAllFlows;
            }
            if(NGBusinessAdvisor.s_advisors.Count == 0)
                NGBusinessAdvisor.LoadInfo();
            var advisors = new List<string>() {"All"};
            for (var i = 0; i < NGBusinessAdvisor.s_advisors.Count; i++)
            {
                var advisor = NGBusinessAdvisor.s_advisors[i];
                if (advisor == null) continue;
                if (advisor.m_name.IsNullOrWhiteSpace()) continue;
                advisors.Add(advisor.m_name);
            }
            int[] index = new int[advisors.Count];
            for (var i = 0; i < advisors.Count; i++) index[i] = i;
            m_selectedAdvisor = EditorGUILayout.IntPopup($"Advisor:", m_selectedAdvisor, advisors.ToArray(), index);
            string selectedAdvisor = advisors[0];
            if (m_selectedAdvisor != 0)
                selectedAdvisor = advisors[m_selectedAdvisor];
            if (GUILayout.Button($"Output/{selectedAdvisor}Dialog.csv"))
            {
                if(m_selectedAdvisor == 0)
                    MAParserManager.WriteDialogToFile("csv", null);
                else
                    MAParserManager.WriteDialogToFile("csv", advisors[m_selectedAdvisor]);
            }
            //GUILayout.BeginHorizontal();
;
            if (GUILayout.Button($"Output/{selectedAdvisor}Script.rtf"))
            {
                if(m_selectedAdvisor == 0)
                    MAParserManager.WriteDialogToFile("script", null);
                else
                    MAParserManager.WriteDialogToFile("script", advisors[m_selectedAdvisor]);
            }
            if(GUILayout.Button("Import Audio Into Scripts"))
            {
                string path = EditorUtility.OpenFilePanel(
                    "Select CSV File", 
                    "Downloads", 
                    "tsv" // file extension filter
                );

                if (path.IsNullOrWhiteSpace() == false)
                {
                    Debug.Log($"Selected file: {path}");
                    ProcessScripts(path);
                }
            }
            //GUILayout.EndHorizontal();
            EditorGUILayout.EndVertical();
            GUILayout.Space(10);

            EditorGUI.indentLevel-=0;
        }

    }
    void AllowSkip(List<string> files)
    {
        const string directory = "Chapter1V2/Day";
        var days = files.FindAll(o=> o.Contains(directory));
        if (days.Count == 0) return;
        for (var i = 0; i < days.Count; i++)
        {
            days[i] = days[i].Replace(directory, "");
        }

        days.Insert(0, "Select day to skip to:");
        var options = new int[days.Count];
        for(int i = 0; i < days.Count; i++)
            options[i] = i;
        m_selectPopupSkip = EditorGUILayout.IntPopup($"Skip To Day:", m_selectPopupSkip, days.ToArray(), options);
        if(m_selectPopupSkip != 0)
        {
            var split = days[m_selectPopupSkip].Split('_');
            if(int.TryParse(split[0], out var dayNumber))
            {
                MAParserManager.SkipToDay(dayNumber);
            }
            m_selectPopupSkip = 0;
        }
    }
    private void DisplayTOD()
    {
        if (DebugTOD.Me == null)
        {
            EditorGUILayout.LabelField("Time of Day not available");
            return;
        }
        DayNight.Me.GetDHM(out int days, out int hours, out int minutes);
        EditorGUILayout.Space();
        if(GameManager.Me)
            EditorGUILayout.LabelField(GameManager.Me.GetFPSString(), EditorStyles.boldLabel);
        if (DayNight.Me && DayNight.Me.CurrentWorkingDay >= 0)
        {
            var calenderInfo = MACalenderInfo.GetInfo(DayNight.Me.CurrentWorkingDay);
            if(calenderInfo != null)
                EditorGUILayout.LabelField($"Day Length:{calenderInfo.m_dayLength:F0} Night Length:{calenderInfo.m_nightLength:F0}", EditorStyles.boldLabel);
        }
        
        GUILayout.BeginHorizontal();
        EditorGUILayout.LabelField("Time of Day:", EditorStyles.boldLabel);
        bool newFrozen = GUILayout.Toggle(DebugTOD.Me.IsFrozen, " Freeze Time", "Button");

        if (newFrozen != DebugTOD.Me.IsFrozen)
        {
            DebugTOD.Me.IsFrozen = newFrozen;
        }
        GUILayout.EndHorizontal();

        GUILayout.BeginHorizontal();
        if (GUILayout.Button("-", GUILayout.Width(25)))
        {
            DebugTOD.Me.OnDaysDec();
        }

        EditorGUILayout.LabelField($"Days: {days}", GUILayout.Width(100));

        if (GUILayout.Button("+", GUILayout.Width(25)))
        {
            DebugTOD.Me.OnDaysInc();
        }
        GUILayout.EndHorizontal();

        GUILayout.BeginHorizontal();
        if (GUILayout.Button("-", GUILayout.Width(25)))
        {
            DebugTOD.Me.OnHoursDec();
        }
        EditorGUILayout.LabelField($"Hour: {hours}", GUILayout.Width(100));

        if (GUILayout.Button("+", GUILayout.Width(25)))
        {
            DebugTOD.Me.OnHoursInc();
        }
        GUILayout.EndHorizontal();

        GUILayout.BeginHorizontal();
        if (GUILayout.Button("-", GUILayout.Width(25)))
        {
            DebugTOD.Me.OnMinutesDec();
        }

        EditorGUILayout.LabelField($"Minute: {minutes}", GUILayout.Width(100));

        if (GUILayout.Button("+", GUILayout.Width(25)))
        {
            DebugTOD.Me.OnMinutesInc();
        }
        GUILayout.EndHorizontal();

    }
    [System.Serializable] public class AudioEntry
    {
        public string Folder;
        public string ScriptName;
        public string Character;
        public string Message;
        public string AudioID;
    }
    public string c_flowFolder = "Flows";

    List<AudioEntry> ParseCsv(string csv)
    {
        var lines = csv.Split('\n');
        var entries = new List<AudioEntry>();

        for (int i = 2; i < lines.Length; i++) // Skip header
        {
            var line = lines[i].Trim();
            if (string.IsNullOrWhiteSpace(line)) continue;

            var parts = line.Split('\t'); // Assumes TSV format
            if (parts.Length < 5) continue;

            entries.Add(new AudioEntry
            {
                Folder = parts[0],
                ScriptName = parts[1],
                Character = parts[2],
                Message = parts[3],
                AudioID = parts[4]
            });
        }

        return entries;
    }

    void ProcessScripts(string CSVLocation)
    {
        
        var csvTable = Resources.Load<TextAsset>(CSVLocation);
        List<AudioEntry> entries = null;
        if (csvTable == null)
        {
            var table = File.ReadAllText(CSVLocation);
            entries = ParseCsv(table);
        }
        else
        {
            entries = ParseCsv(csvTable.text);
            
        }
        var groupedByScript = entries.GroupBy(e => (e.Folder, e.ScriptName));

        foreach (var group in groupedByScript)
        {
            if(group.Key.Folder.IsNullOrWhiteSpace() || group.Key.ScriptName.IsNullOrWhiteSpace())
                continue;
            
            string path = $"{c_flowFolder}/{group.Key.Folder}/{group.Key.ScriptName}";
            string resourcePathd = path;
            string resourcePath = Path.Combine(group.Key.Folder, group.Key.ScriptName).Replace(".MOA", ""); // no extension for Resources.Load
            TextAsset scriptAsset = Resources.Load<TextAsset>($"{c_flowFolder}/{resourcePath}");
//Flows/Chapter1V2/Chapter1Flow
            if (scriptAsset == null)
            {
                Debug.LogWarning($"Script not found at: {resourcePath}/{group.Key.ScriptName}");
                continue;
            }

            string[] lines = scriptAsset.text.Split('\n').ToArray();
            var modifiedLines = new List<string>();

            foreach (var line in lines)
            {
                var newLine = line;
               // modifiedLines.Add(line);

                foreach (var entry in group)
                {
                    if (line.Contains(entry.Message) && line.Contains("Message"))
                    {
                        var leadingWhitespace = "";
                        foreach (char c in line)
                        {
                            if (char.IsWhiteSpace(c) == false) break;
                            leadingWhitespace += c;
                        }
                        var parsedLine = MAParserSupport.ParseString(line); 
                        if (parsedLine == null || parsedLine.Count == 0)
                        {
                            Debug.LogWarning($"Line not found at: {resourcePath}");
                            continue;
                        }
                        var p = parsedLine[0];
                        
                        newLine = $"{leadingWhitespace}{p.m_functionName.Trim()}(";
                        switch (p.m_args.Count)
                        {
                            case 3:
                                newLine+= $"{p.m_args[0]},"; // Advisor
                                newLine += $"\"{p.m_args[1]}\","; // Message
                                newLine += $" {entry.AudioID})";
                                break;
                            case 5:
                                newLine += $"{p.m_args[0]},"; // Advisor
                                newLine += $"{p.m_args[1]},"; // Popup
                                newLine += $"{p.m_args[2]},"; // Sprite
                                newLine += $"\"{p.m_args[3]}\","; // Message
                                newLine += $" {entry.AudioID})";
                                break;
                            default:
                                Debug.LogWarning($"Unexpected number of arguments in line: {line}");
                                newLine = line;
                                continue;
                        }
                        break;
                    }
                }
                modifiedLines.Add(newLine);
            }
 
            string outputPath = Path.Combine(Application.dataPath, $"Resources/Flows/{group.Key.Folder}", group.Key.ScriptName);
            //File.WriteAllLines(outputPath, modifiedLines);
            Debug.Log($"Modified script saved to: {outputPath}");
        }
    }
    
    
}
#endif
