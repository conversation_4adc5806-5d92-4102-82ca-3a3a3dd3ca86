#if UNITY_EDITOR
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;


public class MAQuestWindow : EditorWindow
{
    private Vector2 m_scrollPosition;
    int m_selectPopup = 0;
    private string[] m_questSpawnPointInfoNames;
    private int[] m_questSpawnPointInfoInts;
    private string[] m_advisorInfoNames;
    private int[] m_advisorInfoInts;
    private string[] m_questNames;
    private int[] m_questNamesInts;
    private bool m_newSpawnPointBool = false;
    private bool m_showSpawnPoints = false;
    private bool m_setup = false;
    
    [MenuItem("22Cans/Debug Windows/Quests Window ")] 
    public static void ShowWindow()
    {
        var window = GetWindow<MAQuestWindow>("Quests");
        
        window.titleContent = new GUIContent("Quests");
        window.Show();
        window.m_showSpawnPoints = false;
    }
    void SetupOnGUI()
    {
        m_questSpawnPointInfoNames = new string[MAQuestSpawnInfo.s_questSpawnInfo.Count+1];
        m_questSpawnPointInfoInts = new int[MAQuestSpawnInfo.s_questSpawnInfo.Count+1];
        m_questSpawnPointInfoNames[0] = "None";
        for (var index = 1; index < MAQuestSpawnInfo.s_questSpawnInfo.Count+1; index++)
        {
            m_questSpawnPointInfoNames[index] = MAQuestSpawnInfo.s_questSpawnInfo[index-1].m_name;
            m_questSpawnPointInfoInts[index] = index;
        }
        m_advisorInfoNames = new string[NGBusinessAdvisor.s_advisors.Count+1];
        m_advisorInfoInts = new int[NGBusinessAdvisor.s_advisors.Count+1];
        m_advisorInfoNames[0]= "None";
        for (var index = 1; index < NGBusinessAdvisor.s_advisors.Count + 1; index++)
        {
            m_advisorInfoNames[index] = NGBusinessAdvisor.s_advisors[index-1].m_name;
            m_advisorInfoInts[index] = index;
        }
        m_questNames = new string[NGBusinessFlow.s_flowsDict.Count+1];
        m_questNamesInts = new int[NGBusinessFlow.s_flowsDict.Count+1];
        m_questNames[0] = "None";
        var fIndex = 1;
        foreach(var d in NGBusinessFlow.s_flowsDict)
        {
            m_questNames[fIndex] = d.Key;
            m_questNamesInts[fIndex] = fIndex++;
        }
        m_showSpawnPoints = false;
        m_newSpawnPointBool = false;
        m_setup = true;
    }
    private void OnGUI()
    {
        // if (GlobalData.Me == null || MAQuestManager.Me == null || GameManager.Me.LoadComplete == false)
        // {
        //     EditorGUILayout.LabelField("No Quest Spawn Points:", EditorStyles.boldLabel);
        //     m_setup = false;
        //     return;
        // }
        var quests = new List<MAQuestBase>();
        if (MAQuestManager.Me == null)
        {
            MAQuestManager.SetEditorMe();
        }
        quests = MAQuestManager.Me.m_activeQuests;
        if(quests.Count == 0)
        {
            var questsChildren = MAQuestManager.Me.m_questHolder.GetComponentsInChildren<MAQuestBase>();
            foreach (var q in questsChildren)
            {
                quests.Add(q);
            }
        }
//        if(m_setup == false)
//            SetupOnGUI();
 
        GUIStyle boldFoldoutStyle = new GUIStyle(EditorStyles.foldout) {fontStyle = FontStyle.Bold};
        m_scrollPosition = EditorGUILayout.BeginScrollView(m_scrollPosition);
        foreach (var q in quests)
        {
            var label =$"{q.m_id} Status={q.Status}";
            q.m_debugWindowBool = EditorGUILayout.Foldout(q.m_debugWindowBool, label, true, boldFoldoutStyle);
            if(q.m_debugWindowBool)
            {
                q.DebugShowWindow();
            }
        }
        EditorGUILayout.EndScrollView();
    }

}
#endif
