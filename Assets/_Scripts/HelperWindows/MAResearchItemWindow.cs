#if UNITY_EDITOR
using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

public class MAResearchItemWindow : EditorWindow
{
    static public MAResearchItemUI m_item;
    public Vector3 m_position;
    private Vector2 m_scrollPosition;
   
    [MenuItem("CONTEXT/Transform/Open Editor Window")]
    public static MAResearchItemWindow Create(MAResearchItemUI _item, Vector3 _pos)
    {
        Vector2 mousePos = Input.mousePosition;
        MAResearchItemWindow window = GetWindow<MAResearchItemWindow>();
        m_item = _item;

        window.position = new Rect(mousePos.x, mousePos.y, 400, 300);
        return window;
    }

    void ShowAndUpdateText(string _name, ref string _text, GUIStyle _style)
    {
        var newText = _text;
        newText = EditorGUILayout.TextField(_name, newText, _style);
        if(newText != _text)
        {
            _text = newText;
            m_item.m_info.m_changed = true;
        }

    }
    void ShowAndUpdateInt(string _name, ref int _num, GUIStyle _style)
    {
        var newNum = _num;
        newNum = EditorGUILayout.IntField(_name, newNum, _style);
        if(newNum != _num)
        {
            _num = newNum;
            m_item.m_info.m_changed = true;
        }

    }
    public void OnGUI()
    {
        if (m_item== null || m_item.m_info == null)
            return;
        GUIStyle boldFoldoutStyle = new GUIStyle(EditorStyles.foldout){fontStyle = FontStyle.Bold};
        GUIStyle normalFoldoutStyle = new GUIStyle(EditorStyles.foldout);
        var labelStyle = new GUIStyle(EditorStyles.textArea) {richText = true};
       
        var info = m_item.m_info;
        var newText = "";
        m_scrollPosition = EditorGUILayout.BeginScrollView(m_scrollPosition);
        if (info.ShowWindowGUI(labelStyle))
        {
            m_item.Refresh();
        }
        EditorGUILayout.EndScrollView(); 
    }
}
#endif
