#if UNITY_EDITOR
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;


public class MAResearchWindow: EditorWindow
{
    private Vector2 m_scrollPosition;
    int m_selectPopup = 0;
    int m_statePopup = 0;
    public GameObject m_cameraFollowing = null;
    Dictionary<MAFactionInfo.FactionType, ReseachDict> reseachDict = new Dictionary<MAFactionInfo.FactionType, ReseachDict>();

    [MenuItem("22Cans/Debug Windows/Research")]
    public static void ShowWindow()
    {
        var window = GetWindow<MAResearchWindow>("Show Research");
        
        window.titleContent = new GUIContent("Research");
        window.Show();
    }

    public class ReseachDict
    {
        public List<MAResearchInfo> m_reseachItems = new List<MAResearchInfo>();
        public bool m_foldOutFlag;
    }

    void RefreshResearchDict()
    {
        bool isValid = true;
        reseachDict.Clear();
        foreach (var c in MAResearchInfo.s_factionResearchDict)
        {
            var t = c.Key;
            if(reseachDict.ContainsKey(t) == false)
                reseachDict.Add(t, new ReseachDict {m_reseachItems = new List<MAResearchInfo>(c.Value)});
        }
    }
    private void OnGUI()
    {
        if (GlobalData.Me == null || MAResearchInfo.s_factionResearchDict == null || MAResearchInfo.s_factionResearchDict.Count == 0)
        {
            EditorGUILayout.LabelField("No Research:", EditorStyles.boldLabel);
            return;
        }
        EditorGUILayout.LabelField($"Research List[{MAResearchInfo.s_factionResearchDict.Count}]:", EditorStyles.boldLabel);
        if(reseachDict.Count == 0)
            RefreshResearchDict();
        GUIStyle boldFoldoutStyle = new GUIStyle(EditorStyles.foldout){fontStyle = FontStyle.Bold};
        GUIStyle normalFoldoutStyle = new GUIStyle(EditorStyles.foldout);
        var labelStyle = new GUIStyle(EditorStyles.textArea) {richText = true};
  
        if (GUILayout.Button("Write Changes"))
        {
            bool anyChanged = false;
            foreach (var ri in MAResearchInfo.s_researchInfos)
            {
                if (ri.m_changed)
                {
                    ri.m_linkToString = "";
                    ri.m_linkToList.ForEach(o=>ri.m_linkToString+=$"{o.m_key}|");
                    ri.m_linkToString = ri.m_linkToString.TrimEnd('|');
                    ri.m_updateTime  = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                    NGKnack.GetKnackPutWholeRecord(ri);
                    anyChanged = true;
                }
            }
            NGKnack.CacheKnack(typeof(MAResearchInfo));
        }

        m_scrollPosition = EditorGUILayout.BeginScrollView(m_scrollPosition);
        foreach (var d in reseachDict)
        { 
            if (GUILayout.Button($"Load {d.Key} Background"))
            {
                if(MAResearchManagerUI.Me == null)
                    MAResearchManagerUI.Create(NGManager.Me.m_centreScreenHolder, d.Key);
                MAResearchManagerUI.Me.ShowBackground(d.Key);
            }
            if(GUILayout.Button($"Setup {d.Key} Info"))
            {
                if(MAResearchManagerUI.Me == null)
                    MAResearchManagerUI.Create(NGManager.Me.m_centreScreenHolder, d.Key);
                MAResearchManagerUI.Me.SetupInfo(d.Key);
            }
            if(GUILayout.Button("Refresh"))
            {
                MAResearchManagerUI.Me.Refresh();
            }
            d.Value.m_foldOutFlag = EditorGUILayout.Foldout(d.Value.m_foldOutFlag, $"{d.Key.ToString()}[{d.Value.m_reseachItems.Count}]", true, boldFoldoutStyle);
            if (d.Value.m_foldOutFlag)
            {
                foreach (var c in d.Value.m_reseachItems)
                {
                    var inListDontShow = false;
                    foreach (var checkLink in d.Value.m_reseachItems)
                    {
                        if (checkLink.m_linkToList.Contains(c))
                        {
                            inListDontShow = true;
                            break;
                        }
                    }
                    if (inListDontShow)
                        continue;
                    ShowParentAndChildren(c);
                }
            }
        }
        EditorGUILayout.EndScrollView(); 
    }

    void ShowParentAndChildren(MAResearchInfo _info)
    {
        if (_info.m_linkToList.Count == 0)
            return;
        EditorGUI.indentLevel++;
        EditorGUILayout.BeginHorizontal();
        var name = $"{_info.m_name} ${_info.m_dollarCost} 🪙{_info.m_factionCost}";

        _info.m_debugSelectedForWindow = EditorGUILayout.Foldout(_info.m_debugSelectedForWindow, name, _info.m_debugSelectedForWindow);
        if (MAResearchManagerUI.Me && MAResearchManagerUI.Me.HasEmptyItem())
        {
            if (MAResearchManagerUI.Me.IsInfoAssigned(_info))
            {
                if (GUILayout.Button("Clear"))
                {
                    
                }

            }
            else if (GUILayout.Button("Assign"))
            {
                var item =MAResearchManagerUI.Me.AssignResearch(_info);
                
                _info.m_changed = true;
            }
            
        }

        EditorGUILayout.EndHorizontal();
        if (_info.m_debugSelectedForWindow)
        {

            foreach (var c in _info.m_linkToList)
            {
                ShowParentAndChildren(c);
            }
        }
        EditorGUI.indentLevel--;
    }

    void OnInspectorUpdate()
    {
        Repaint();
    }
}
#endif
