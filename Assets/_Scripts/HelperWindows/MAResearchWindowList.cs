#if UNITY_EDITOR
using UnityEditor;
using UnityEngine;

public class MAResearchWindowList : EditorWindow
{
    private static MAFactionInfo.FactionType m_faction;
    private static MAResearchItemUI m_item;
    
    private Vector2 m_scrollPosition;

    
    public static MAResearchWindowList Create(MAFactionInfo.FactionType _faction, MAResearchItemUI _item, Vector3 _pos)
    {
        m_faction = _faction;
        m_item = _item;
        Vector2 mousePos = Input.mousePosition;
        MAResearchWindowList window = GetWindow<MAResearchWindowList>();
            
        window.position = new Rect(mousePos.x, mousePos.y, 400, 300);
        return window;
    }

    public void OnGUI()
    {
        GUIStyle boldFoldoutStyle = new GUIStyle(EditorStyles.foldout) {fontStyle = FontStyle.Bold};
        GUIStyle normalFoldoutStyle = new GUIStyle(EditorStyles.foldout);
        var labelStyle = new GUIStyle(EditorStyles.textArea) {richText = true};
        
        m_scrollPosition = EditorGUILayout.BeginScrollView(m_scrollPosition);
        if (GUILayout.Button("Delete"))
        {
            m_item.DestroyMe();
            focusedWindow.Close();
        }
        
        foreach (var item in MAResearchInfo.s_factionResearchDict[m_faction])
        {
            EditorGUILayout.BeginHorizontal();
            var name = item.m_name;
            item.m_debugSelectedForWindow = EditorGUILayout.Foldout(item.m_debugSelectedForWindow, name, item.m_debugSelectedForWindow);
            if(MAResearchManagerUI.Me.IsInfoAssigned(item))
            {
                EditorGUILayout.LabelField("Assigned");
            }
            else
            {
                if (GUILayout.Button("Assign"))
                {
                    m_item.AssignResearchItem(item);
                    focusedWindow.Close();
                }
            }
            
            EditorGUILayout.EndHorizontal();
            if(item.m_debugSelectedForWindow)
            {
                EditorGUILayout.IntField($"Dollar Cost: ", item.m_dollarCost, labelStyle);
                EditorGUILayout.IntField($"Faction Cost: ", item.m_factionCost, labelStyle);
                var newIcon = EditorGUILayout.TextField($"Icon Name: ", item.m_icon, labelStyle);
            }
        }
        
        EditorGUILayout.EndScrollView();
    }
}
#endif
