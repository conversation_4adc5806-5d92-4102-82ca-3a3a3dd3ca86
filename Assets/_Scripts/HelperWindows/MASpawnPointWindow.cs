#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using System.IO;
using TMPro;
using UnityEditor;
using UnityEngine;
using UnityEngine.UI;

public class MASpawnPointWindow : EditorWindow
{
    private Image m_editorCrossHairImage = null;
    private TextMeshProUGUI m_editorTitleText = null;
    private Vector2 m_scrollPosition;
    int m_selectPopup = 0;
    private string[] m_spawnPointInfoNames;
    private int[] m_spawnPointInfoInts;
    private string[] m_linkedBuildingsNames;
    private int[] m_linkedBuildingsInts;
    private bool m_newSpawnPointBool = false;
    private MASpawnPoint m_newSpawnPoint;
    private bool m_showSpawnPoints;
    private bool m_setup = false;
    [MenuItem("22Cans/Debug Windows/Creature Spawn Points")] 
    public static void ShowWindow()
    {
        var window = GetWindow<MASpawnPointWindow>("Creature Spawn Points");
        
        window.titleContent = new GUIContent("Creature Spawn Points");
        window.Show();
        window.m_showSpawnPoints = false;
    }
    void SetupOnGUI()
    {
        m_spawnPointInfoNames = new string[MACreatureSpawnInfo.s_creatureSpawnInfos.Count+1];
        m_spawnPointInfoInts = new int[MACreatureSpawnInfo.s_creatureSpawnInfos.Count+1];
        m_spawnPointInfoNames[0] = "None";
        for (var index = 1; index < MACreatureSpawnInfo.s_creatureSpawnInfos.Count+1; index++)
        {
            m_spawnPointInfoNames[index] = MACreatureSpawnInfo.s_creatureSpawnInfos[index-1].m_name;
            m_spawnPointInfoInts[index] = index;
        }

        var buildings = NGManager.Me.m_maBuildings;
        m_linkedBuildingsNames = new string[buildings.Count+1];
        m_linkedBuildingsInts = new int[buildings.Count+1];
        m_linkedBuildingsNames[0] = "None";
        for(int index = 1; index < buildings.Count; index++)
        {
            m_linkedBuildingsNames[index] = buildings[index-1].name;
            m_linkedBuildingsInts[index] = index;
        }
        
        var spawnPointHolder = GlobalData.Me.m_spawnPointHolder;
        List<MASpawnPoint> spawnPoints = new();
        spawnPointHolder.GetComponentsInChildren<MASpawnPoint>(true, spawnPoints);
        
        m_showSpawnPoints = false;
        UpdateShowHideSpawnPointHelpers(true, spawnPoints);
        
        m_newSpawnPointBool = false;
        m_setup = true;
    }

    private void OnInspectorUpdate()
    {
        Repaint();
    }

    private void OnLostFocus()
    {
        DestroyUIObject(m_editorCrossHairImage);
        DestroyUIObject(m_editorTitleText);
    }

    private void DestroyUIObject(Graphic _toDestroy)
    {
        if (_toDestroy != null)
        {
            Destroy(_toDestroy.gameObject);
        }
    }

    private void UpdateShowHideSpawnPointHelpers(bool _show, List<MASpawnPoint> spawnPoints)
    {
        if (_show != m_showSpawnPoints)
        {
            m_showSpawnPoints = _show;
            foreach(var sp in spawnPoints)
            {   
                sp.ShowDebug(m_showSpawnPoints);
            }
        }
        
        if (m_showSpawnPoints)
        {
            if (m_editorCrossHairImage == null)
            {
                GameObject crossHair =
                    Resources.Load("_Prefabs/Spawns/Editor/EditorCrossHair", typeof(GameObject)) as GameObject;
                crossHair.name = "EditorCrossHair";
                m_editorCrossHairImage = Instantiate(crossHair, GameManager.Me.m_fullScreenCanvas).GetComponent<Image>();
                RectTransform rt = m_editorCrossHairImage.GetComponent<RectTransform>();
                rt.anchoredPosition = Vector2.zero;//Vector2.one * .5f;
                m_editorCrossHairImage.raycastTarget = false;
            }
            
            // if (m_editorTitleText == null)
            // {
            //     GameObject titleText = new GameObject("EditorSpawnInterfaceTitleText", typeof(RectTransform), typeof(TextMeshProUGUI));
            //     titleText.transform.parent = GameManager.Me.m_fullScreenCanvas;
            //     m_editorTitleText = titleText.GetComponent<TextMeshProUGUI>();
            //     RectTransform rt = m_editorTitleText.GetComponent<RectTransform>();
            //     rt.anchorMax = new Vector2(0.5f, 0.9f);
            //     rt.anchorMin = new Vector2(0.5f, 0.9f);
            //     m_editorTitleText.enableAutoSizing = true;
            //     m_editorTitleText.autoSizeTextContainer = true;
            //     m_editorTitleText.text = "Spawn Point Editor";
            //     m_editorTitleText.raycastTarget = false;
            //     m_editorTitleText.enabled = true;
            // }
        }
        else
        {
            DestroyUIObject(m_editorCrossHairImage);
            DestroyUIObject(m_editorTitleText);
        }
    }
    
    bool m_spawnsPaused = false;
    void OnGUI()
    {
        if (GlobalData.Me == null || NGManager.Me == null || GameManager.Me == null || GameManager.Me.LoadComplete == false)
        {
            EditorGUILayout.LabelField("No Spawn Points:", EditorStyles.boldLabel);
            m_setup = false;
            return;
        }
        if (m_setup == false)
            SetupOnGUI();
 
        GUIStyle boldFoldoutStyle = new GUIStyle(EditorStyles.foldout) {fontStyle = FontStyle.Bold};

        var spawnPointHolder = GlobalData.Me.m_spawnPointHolder;
        List<MASpawnPoint> spawnPoints = new();
        spawnPointHolder.GetComponentsInChildren<MASpawnPoint>(true, spawnPoints);
        var seedSaveState = "";
        if (File.Exists(MASpawnPoint.SeedSavePath))
        {
            var fileInfo = new FileInfo(MASpawnPoint.SeedSavePath);
            var lastModifiedTime = fileInfo.LastWriteTime;
            var oneHour = TimeSpan.FromHours(1);
            var isRecent = lastModifiedTime > DateTime.Now.Subtract(oneHour);
            var formattedTime = lastModifiedTime.ToString("yyyy-MM-dd HH:mm:ss");

            seedSaveState = $"<color=white> Write Seed Save[{NGManager.Me.m_MACreatureSpawnPoints.Count}]:</color>";
            if (isRecent)
            {
                seedSaveState += $" <color=#A0DB8E> Last Modified: {formattedTime}</color>";
            }
            else
            {
                seedSaveState += $" <color=#006334ff> Last Modified: {formattedTime}</color>";
            }
        }
        else
        {
            seedSaveState += $" <color=red>Seed Save Not Found</color>";
        }
        
        GUIStyle seedSaveButtonStyle = new GUIStyle(GUI.skin.button);
        seedSaveButtonStyle.richText = true;
        if (GUILayout.Button(seedSaveState, seedSaveButtonStyle))
        {
            MASpawnPoint.WriteSeedSave();
        }
        
        if (GUILayout.Button($"{(m_spawnsPaused ? "UnPause" : "Pause")} All Existing SpawnPoints"))
        {
            m_spawnsPaused = !m_spawnsPaused;
            MAParser.MAPauseAllSpawnPoints(m_spawnsPaused);
        }

        GUIStyle spawnPointInterfaceOnOff = new GUIStyle(GUI.skin.button);
        bool show = m_showSpawnPoints;
        var showHide = m_showSpawnPoints ? "Hide" : "Show";
        spawnPointInterfaceOnOff.normal.textColor = m_showSpawnPoints ? Color.red : Color.green;;
        if (GUILayout.Button($"{showHide} In-game Spawn Point Interface"))
        {
            show = !show;
        }
        UpdateShowHideSpawnPointHelpers(show, spawnPoints);

        m_newSpawnPointBool = EditorGUILayout.Foldout(m_newSpawnPointBool, "New SpawnPoint", true, boldFoldoutStyle);
        if (m_newSpawnPointBool && MACreatureSpawnInfo.s_creatureSpawnInfos.Count > 0)
        {
            if (m_newSpawnPoint == null)
            {
                m_newSpawnPoint = MASpawnPoint.Create("New SpawnPoint", MACreatureSpawnInfo.s_creatureSpawnInfos[0], null,Utility.GetRaycastCameraPosition(), 5f, _dontAddToMASpawnPoints:true, _showDebug:m_showSpawnPoints);
            }
            m_newSpawnPoint.ShowSpawnPointGUI(m_spawnPointInfoNames, m_spawnPointInfoInts, m_linkedBuildingsNames, m_linkedBuildingsInts, m_showSpawnPoints);
            
            EditorGUILayout.Space();
            
            GUIStyle addButtonStyle = new GUIStyle(GUI.skin.button);
            addButtonStyle.normal.textColor = Mathf.RoundToInt(Time.realtimeSinceStartup) % 2 == 0 ? Color.red : Color.yellow;
            addButtonStyle.onHover.textColor = Color.green;
            //addButtonStyle.fixedHeight *= 2;
            addButtonStyle.border = new RectOffset(15, 15, 10, 10);
            addButtonStyle.fontSize *= 2;
            
            EditorGUILayout.Space();
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Save this Spawn Point!", addButtonStyle))
            {
                m_newSpawnPoint.transform.SetParent(spawnPointHolder);
                m_newSpawnPoint.Enable();
                m_newSpawnPointBool = false;
                m_newSpawnPoint = null;
                EditorGUILayout.EndHorizontal();
            }
        }

        if (m_newSpawnPoint != null)
        {
            if (GUILayout.Button("Cancel New Spawn Point"))
            {
                m_newSpawnPointBool = false;
                m_newSpawnPoint.DestroyMe();
                m_newSpawnPoint = null;
            }
            EditorGUILayout.EndHorizontal();
        }
            
        EditorGUILayout.Space();

        m_scrollPosition = EditorGUILayout.BeginScrollView(m_scrollPosition, true, true);
        EditorGUILayout.LabelField("Existing Spawn Points", EditorStyles.boldLabel);
        foreach (var sp in spawnPoints)
        {
            if (sp.enabled == false) continue;
            var displayString = $"{sp.m_name} {(sp.HasBeenTriggered ? (sp.IsSpawnPhaseValid ? "x" : "!") : "")} => {""/*(sp.MACreatureSpawnInfo == null?"NoInfo":sp.MACreatureSpawnInfo.m_name)*/}[{sp.transform.position.x:F2},{sp.transform.position.y:F2}]";
            boldFoldoutStyle.normal.textColor = sp.HasBeenTriggered ? (sp.IsSpawnPhaseValid ? Color.yellow : Color.red) : Color.white;
            sp.m_debugSelectedForWindow = EditorGUILayout.Foldout(sp.m_debugSelectedForWindow, displayString, true, boldFoldoutStyle);
            if (sp.m_debugSelectedForWindow)
            {
                sp.ShowSpawnPointGUI(m_spawnPointInfoNames, m_spawnPointInfoInts, m_linkedBuildingsNames, m_linkedBuildingsInts, m_showSpawnPoints);
            }
        }
        EditorGUILayout.EndScrollView();
    }
}
#endif
