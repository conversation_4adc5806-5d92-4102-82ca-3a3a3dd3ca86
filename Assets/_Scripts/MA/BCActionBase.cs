using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class BuildingComponentsState
{
    public int ProductsBeingBuilt;
    
    public void Clear()
    {
        ProductsBeingBuilt = 0;
    }
}

public class BCActionBase : BCWorkerBase
{
    public float PreviousPowerUsed => m_previousPowerUsed;
    private float m_powerUsed;
    private float m_previousPowerUsed;
    
    public override List<MACharacterBase> GetWorkersAllocated() => m_workersAllocated;
    public override List<MACharacterBase> GetWorkersPresent() => m_workersPresent;
    virtual public int CantMakePriority => 0;
    override public bool IsAction => true;
    
    override public (string id, Func<BCUIPanel> create) GetUIPanelInfo() => (BCGeneralInfoPanel.PanelID, () => new BCGeneralInfoPanel());
    
    public enum OrderDisplayedType { All, Unassigned, Assigned }

    public virtual string GetCantMakeReason() => $"";
    
    virtual public NGStock GetInputStock() => null;
    
    void OnEnable()
    {
        AudioClipManager.Me.SetRegionNameForObject(gameObject);
    }

    protected override void Deactivate(MABuilding _previousOwner, int _quantityRemainingInBuilding, BlockDragAction _action)
    {
        SetCharacterVisualsEnabled(null);
        base.Deactivate(_previousOwner, _quantityRemainingInBuilding, _action);
    }
    
    public List<BCActionBase> m_outputDestinations = new();
    public List<NGCarriableResource> m_externalResourcesRequired = new();
    public override bool OutputHasDestination => m_outputDestinations.Count > 0;
    
    public override bool Allocate(MACharacterBase _worker)
    {
        if(base.Allocate(_worker))
        {
            if(this is BCBedroom == false)
            {
                _worker.Job = this;
            }
            return true;
        }
        return false;
    }

    public override bool Arrive(MACharacterBase _worker)
    {
        if(base.Arrive(_worker))
        {
            _worker.NGSetAsWorking(false);
            return true;
        }
        return false;
    }
    
    override public MACharacterBase GetAvailableWorker()
    {
        if (Time.time < Building.m_nextDeliverTime)
            return null;
            
        foreach(var worker in m_workersPresent)
        {
            if(worker.IsTimeToHangout || worker.IsTimeToGoHome) 
                continue;
            return worker;
        }
        return null; 
    }
    
    public void UpdateExternalResourcesRequired(List<NGCarriableResource> _externalResourcesRequired)
    {
        m_externalResourcesRequired.Clear();
        
        var stockRequired = GetInputStock();
        if(stockRequired == null)
            return;
        
        foreach(var requirment in stockRequired.Items)
        {
            m_externalResourcesRequired.Add(requirment.Resource);
        }
        
        foreach(var action in Building.ActionComponents)
        {
            if(m_externalResourcesRequired.Count == 0)
                break;
                
            if(action == this)
                continue;
            
            foreach(var stock in action.GetStock().Items)
            {
                if(m_externalResourcesRequired.Contains(stock.Resource))
                {
                    m_externalResourcesRequired.Remove(stock.Resource);
                }
            }
        }
        
        foreach(var requirement in m_externalResourcesRequired)
        {
            if(_externalResourcesRequired.Contains(requirement)) continue;
            _externalResourcesRequired.Add(requirement);
        }
    }
    
    public void UpdateOutputDestinations()
    {
        NGCarriableResource outputResource = null;
        foreach(var item in m_stock.Items)
        {
            if(item.m_neededToProduce <= 0) continue;
            
            outputResource = item.Resource;
            break;
        }
        
        m_outputDestinations.Clear();
        
        if(outputResource == null)
            return;
        
        // Check which blocks consume my output
        foreach(var action in Building.ActionComponents)
        {
            if(action == this) continue;
            
            var stock = action.GetInputStock();
            if(stock == null) continue;
            
            if(action is BCActionOrderBase)
            {
                if((action as BCActionOrderBase).CanAcceptResource(outputResource))
                {
                    m_outputDestinations.Add(action);
                }
            }
            else if(stock.GetNeededToProduce(outputResource) > 0)
            {
                m_outputDestinations.Add(action);
            }
            else if(outputResource.IsTopLevelResource && action is BCFactory)
            {
                m_outputDestinations.Add(action);
            }
        }
    }

    override public void UpdateInternal(BuildingComponentsState _state)
    {
        base.UpdateInternal(_state);
        UpdateWorkers();
    }
    
    public override void LateUpdateInternal()
    {
        AnimateBlock(m_powerUsed);
        m_previousPowerUsed = m_powerUsed;
        m_powerUsed = 0f;
           
        base.LateUpdateInternal();
    }
    
    protected virtual void UpdateWorkers()
    {
        var count = m_workersPresent.Count;
        
        if(m_workersPresent.Count == 0)
        {
            SetCharacterVisualsEnabled(null, false);
            return;
        }
        
        SetCharacterVisualsEnabled(m_workersPresent[0], false);
        
        Building.m_isInhabited = true; 
        
        for (var i = m_workersPresent.Count - 1; i >= 0; i--)
        {
            var worker = m_workersPresent[i];

            if(worker.IsTimeToHangout && worker.SendToHangOut())
            {
                Leave(worker);
            }
            else if(worker.IsTimeToGoHome || worker.IsTimeToHangout)
            {
                if(m_building.WorkerArrivesToRest(worker))
                {
                }
                else if(worker.Home != null)
                {
                    worker.SetMoveToComponent(worker.Home, PeepActions.ReturnToRest);
                }
                else
                {
                    worker.SetMoveToPosition(m_building ? m_building.GetDoorPos() : GetDoorPos(), false, PeepActions.WaitingForHome);   
                }
                Leave(worker);
            }
        }
    }
    
    public bool SendOutToDeliver(MABuilding.BestStockDestinationCheck _destination, MACharacterBase _character)
    {
        if(_destination.Empty) return false;
        
        var pickup = m_building.TakeItemForDelivery(_destination.m_supplyWhat, (o) => {
            var item = o.GetComponent<ReactPickupPersistent>();
            item.m_intendedDestination = _destination.m_building;
            item.AssignToCarrier(_character); // do this after SetMoveToBuilding so the object is enabled and animator is valid
        });
                
        if(pickup != null)
        {
            Building.m_nextDeliverTime = Time.time + NGManager.Me.m_workerLeavesDelay;
            _character.SetMoveToBuilding(_destination.m_building, PeepActions.DeliverToInput);
            Leave(_character);
            return true;
        }
        return false;
    }
    
    override public bool ConsumeStock(NGCarriableResource _resource)
    {
        bool consumed = m_stock.Consume(_resource);
        if(consumed)
            ClearStockVisuals(Block.m_outputStockVisuals);
        return consumed;
    }

    protected void ClearStockVisuals(GameObject[] _list)
    {
        for(int i = 0; i < _list.Length; ++i)
        {
            var item = _list[i];
            if(item == null)
            {
                Debug.LogError("Null stock visual found");
                continue;
            }
            
            // Move to parent and destroy
            foreach(Transform t in item.transform)
            {
                t.SetParent(item.transform.parent);
                Destroy(t.gameObject);
            }
        }
    }
    
    protected void RemoveStockFromVisuals(GameObject[] _visuals, NGCarriableResource _resource)
    {
        if(_resource == null || _visuals == null) return;
        
        int shiftDownFrom = _visuals.Length;
        
        for(int i = _visuals.Length-1; i >= 0; i--)
        {
            var item = _visuals[i];
            if(item == null || item.transform.childCount == 0)
                continue;
            
            var obj = item.transform.GetChild(0).gameObject;
            var pickup = obj.GetComponent<ReactPickup>();
            if(pickup != null && pickup.Contents == _resource)
            {
                foreach(Transform t in item.transform)
                {
                    t.SetParent(item.transform.parent);
                    Destroy(t.gameObject);
                }
                shiftDownFrom = i;
                break;
            }
        }
        
        for(int i = shiftDownFrom; i < (_visuals.Length-1); ++i)
        {
            var from = _visuals[i+1];
            var to = _visuals[i];
            if(from == null || to == null)
                continue;
                
            foreach(Transform t in from.transform)
            {
                t.SetParent(to.transform);
            }
        }
    }
    
    protected void AddStockToVisuals(GameObject[] _visuals, NGCarriableResource _resource)
    {
        if(_resource == null || _visuals == null) return;
        
        for(int i = 0; i < _visuals.Length; i++)
        {
            if(_visuals[i] == null || _visuals[i].transform.childCount > 0)
                continue;
                
            var pickup = MABuildingSupport.CreatePickupFromResource(m_building, _resource, null, true);
            pickup.transform.SetParent(_visuals[i].transform);
            pickup.transform.localPosition = Vector3.zero;
            pickup.transform.localScale = Vector3.one;
            break;
        }
    }

    public override ReactPickup TakePickup(NGCarriableResource _item, bool _killPickup, Action<GameObject> _onComplete = null)
    {
        var pickup = base.TakePickup(_item, _killPickup, _onComplete);
        
        if(pickup != null)
            ClearStockVisuals(Block.m_outputStockVisuals);
        
        return pickup;
    }

    override public bool AddResource(NGCarriableResource _resource)
    {
        if(_resource == null) return false;
     
        var inputStock = GetInputStock();
        if(inputStock == null)
            return false;
        
        var item = inputStock.Find(_resource);
        if(item == null || item.Needed <= 0) return false;

        AddStockToVisuals(Block. m_inputStockVisuals, _resource);
        inputStock.AddOrCreateStock(_resource, 1);

        return true;
    }

    protected void AddToEnergyUsed(float _amount, bool _showSmoke = false)
    {
        if(_showSmoke && _amount > 0) ShowChimneySmoke();
        m_building.m_workThisUpdate += _amount;
        m_powerUsed += _amount;
    }
}

public class SpecialHandlingAction
{
    public static SpecialHandlingAction AssignHome = new("Assigning Home", "Bedroom", " color=#000000ff");
    public static SpecialHandlingAction AssignJob = new("Assigning Job", "Worker", " color=#000000ff");
    public static SpecialHandlingAction GuildHealing = new("Heal Hero");
    public static SpecialHandlingAction GuildTraining = new("Train Hero");
    public static SpecialHandlingAction CollectWeapon = new("Collect Weapon");
    public static SpecialHandlingAction CollectArmour = new("Collect Armour");
    public static SpecialHandlingAction CollectClothing = new("Collect Clothing");
    public static SpecialHandlingAction ReturnCard = new("Return Card");
    public static SpecialHandlingAction AssignToStocks = new("Punishment via Pillory");
    public static SpecialHandlingAction AssignToGallows = new("Condemn to Death");
    public static SpecialHandlingAction AssignToTableSaw = new("Set Prisoner Free");

    public string m_action;
    public string m_tmpIcon;
    public string m_tmpPrefix;
    
    public SpecialHandlingAction(string _action) { m_action = _action; }
    public SpecialHandlingAction(string _action, string _tmpIcon) { m_action = _action; m_tmpIcon = _tmpIcon; }
    public SpecialHandlingAction(string _action, string _tmpIcon, string _tmpPrefix) { m_action = _action; m_tmpIcon = _tmpIcon; m_tmpPrefix = _tmpPrefix; }
}