using UnityEngine;

public class BCActionWeaponsmith : BCActionShop
{
    public override bool ClearOrderDesignOnReturn => true;
    public override bool ResetOrderQuantityOnReturn => true;
    protected override string OrderInfoBlockName => "WeaponOrder";
    
    public override SpecialHandlingAction GetSpecialHandlingAction(NGMovingObject _obj, SpecialHandlingAction _restrictedAction)
    {
        MAHeroBase hero = _obj as MAHeroBase;
        if(hero != null)
        {
            if(_restrictedAction == null || _restrictedAction == SpecialHandlingAction.CollectWeapon)
                if(HasCollectable(hero))
                    return SpecialHandlingAction.CollectWeapon;
        }
        return null;
    }
    
    protected override void OnItemAdded()
    {
        GameManager.Me.m_state.m_gameInfo.m_numWeaponsDelivered++;
        base.OnItemAdded();
    }

    override public bool Collect(NGMovingObject _o)
    {
        var collected = base.Collect(_o);
        
        if(collected)
        {
            GameManager.Me.m_state.m_gameInfo.m_numWeaponsCollected++;
        }
        return collected;
    }
    
    override public void SetDesign(MACharacterBase _character, GameState_Design _design)
    {
        _character.SetWeaponDesign(_design);
    }
}
