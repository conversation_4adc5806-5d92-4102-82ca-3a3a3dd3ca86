using System;
using System.Collections.Generic;
using SaveContainers;
#if UNITY_EDITOR
using UnityEditor;
#endif
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.Serialization;
using Color = UnityEngine.Color;
using Random = UnityEngine.Random;

public enum BlockDragAction
{
   None,
   ToWild,
   Destroy
}

public class BCBase : MonoBehaviour
{
   public static float s_forceBuildingAnimation = 0;
   private static DebugConsole.Command s_forcebuildinganimationcmd = new("buildinganim", _s => float.TryParse(_s, out s_forceBuildingAnimation));
   
   public List<Collider> GetDamageInteractionAreas(MACharacterBase _attacker)
   {
      Transform interacts = transform.Find("Interacts");
      return interacts != null ? InteractTransform.FindDamageAreas(interacts.gameObject) : new();
   }
   
   public bool ContainsDamageInteractionArea(MACharacterBase _attacker, out MADamageArea damageAreaOut)
   {
      damageAreaOut = null;
      var cols = InteractTransform.FindDamageAreas(gameObject);
      bool containsAny = false;
      foreach (var col in cols)
      {
         MADamageArea damageArea = col.GetComponent<MADamageArea>();
         if (damageArea != null)
         {
            containsAny = true;
            if(damageArea.IsAllowed(_attacker)) //see if you can get all or the closest one
            {
               damageAreaOut = damageArea;
               return true;
            }
         }
      }
      return containsAny;
   }

   virtual public bool ShowWarning => m_isValid == false;
   public virtual bool ClearOrderDesignOnReturn => false;
   public virtual bool ResetOrderQuantityOnReturn => false;
   public virtual bool AllowMultipleSegmentsOnCardHolder => false;
   public virtual bool AllowSave => true;
   public long m_uid;
   protected MABuilding m_building;
   public MABuilding Building => m_building;

   // Updated component system parameters
   [SerializeField][Save] protected NGStock m_stock = new ();
   
   [HideInInspector] public bool m_debugShowComponetDetails;
   private float m_defenseValueCurrent = 1f;//knack
   private float m_defenseValueMax = 1f;//knack
   public float DefenseValueMax => m_defenseValueMax;
   public float DefenseValueCurrent => m_defenseValueCurrent;
   [NonSerialized]public MAComponentInfo m_info;
   public bool IsValid => m_isValid;
   public bool m_isValid;
   protected Block m_block = null;
   public Block Block => m_block;
   
   virtual public void UpdateIsValid()
   {
      m_isValid = IsValidInBuilding();
      
      /*if(m_isValid && m_info != null && m_info.m_requiredComponents.Count > 0)
      {
         foreach (var requiredComponent in m_info.m_requiredComponents)
         {
            var componentsOfType = m_building.BuildingComponents(new[] { requiredComponent.m_classType });
            foreach(var c in componentsOfType)
            {
               c.m_isValid = true;
            }
         }  
      }*/
   }

   virtual public bool IsValidInBuilding()
   {
      if (m_info == null || m_info.m_requiredComponents.Count == 0)
         return true;

      foreach (var requiredComponent in m_info.m_requiredComponents)
      {
         if(m_building.GetBuildingComponentCount(requiredComponent, m_info.m_useRequiredClassName) == 0)
            return false;
      }

      return true;
   }
   
   public void GetMissingComponents(List<MAComponentInfo> _list)
   {
      if(m_info == null || m_info.m_requiredComponents.Count == 0 || _list == null)
         return;
      
      foreach (var requiredComponent in m_info.m_requiredComponents)
      {
         if (m_building.GetBuildingComponentCount(requiredComponent, m_info.m_useRequiredClassName) == 0 && _list.Contains(requiredComponent) == false) 
            _list.Add(requiredComponent);
      }
   }
   
   virtual public bool IsAction => false;
#if UNITY_EDITOR   
   virtual public void DebugShowGUIDetails(GUIStyle _labelStyle, bool _showBase = true)
   {
      EditorGUILayout.LabelField($"m_defenseValueCurrent: ", m_defenseValueCurrent.ToString(), _labelStyle);
      EditorGUILayout.LabelField($"m_defenseValueMax: ", m_defenseValueMax.ToString(), _labelStyle);
   }
#endif
   virtual protected void Awake() 
   { 
      m_block = GetComponent<Block>();
      enabled = false; 
      SetCharacterVisualsEnabled(null);
   }

   virtual protected void Start()
   {
      
   }

   public void Setup(MAComponentInfo _info)
   {
      m_info = _info;
      foreach (var fv in _info.m_fields)
      {
         ReactReflection.SetAnyFieldOrProperty(this, $"{fv.t_field}={fv.t_value}", typeof(KnackField));
      }
   }
   
   virtual public bool SetOwner(MABuilding _building, int _quantityInBuilding, BlockDragAction _action)
   {
      if(GameManager.Me.LoadComplete)
         TrySetUID(m_uid);
         
      var previousOwner = m_building;
      bool changed = _building != previousOwner;
      m_building = _building;
      enabled = _building != null;
      
      if(_building == null) 
         Deactivate(previousOwner, _quantityInBuilding, _action);

      return changed;
   }
   
   virtual public void OnBuildingMoved() {}
   virtual public void OnBuildingDesignChanged() {}
   
   virtual public void InitialiseWildBlockVisuals() {}
   
   // Called when a component is assigned to a building, always after Load/PostLoad
   virtual public void Activate(int _indexOfComponentType, int _quantityInBuilding) { SetupStockRequirements(); }
   // Called when a component is removed from a building
   virtual protected void Deactivate(MABuilding _previousOwner, int _quantityRemainingInBuilding, BlockDragAction _action)
   {
      ControlBlockAudio(false, _previousOwner);
      SetCharacterVisualsEnabled(null);
   }
   
   // Called when a components save data has been loaded
   virtual protected void PostLoad() { }

   public MAComponentInfo GetInfo() => (m_info != null) ? m_info : MAComponentInfo.GetInfoByClass(GetType());

   void OnDestroy()
   {
      if(Utility.IsShuttingDown) return;
      
      OnDestroyCleanup();
      
      if (m_building != null)
         m_building.RemoveComponent(this, BlockDragAction.None);
   }
   
   public void TrySetUID()
   {
      TrySetUID(m_uid);
   }
   private void TrySetUID(long _uid)
   {
      if(GameManager.Me.m_state.m_highestComponentId <= 0)
      {
         foreach(var key in GameManager.Me.m_state.m_maComponentData.Keys)
         {
            if(key > GameManager.Me.m_state.m_highestComponentId)
               GameManager.Me.m_state.m_highestComponentId = key;
         }
      }
      
      if(!AllowSave)
      {
         m_uid = 0;
      }
      else if(_uid <= 0)
      {
         m_uid = ++GameManager.Me.m_state.m_highestComponentId;
      }
      else
      {
         m_uid = _uid;
      }
   }
   
   [KnackField] public float m_animationSpeedMultiplier = 1f;
    
   private float m_animationSpeed = 0;
   private int m_blockAudioHandle, m_buildingAudioHandle;
   
   private float m_nextSwitchTime = 0;
   private int m_characterVisualID = -1;
   private int m_characterVisualIteration = -1;
   
   private GameObject m_characterVisual;
   
   private static Dictionary<string, Transform> s_boneMap = new ();
   void GetBoneMapRecursive(Transform _current, Dictionary<string, Transform> _map)
   {
      if (!_map.ContainsKey(_current.name))
         _map[_current.name] = _current;

      foreach (Transform child in _current)
         GetBoneMapRecursive(child, _map);
   }
   
   private void CopyAndRebindSMR(SkinnedMeshRenderer _sourceSMR, GameObject _targetObject, Transform _newRootBone)
   {
      SkinnedMeshRenderer newSMR = _targetObject.AddComponent<SkinnedMeshRenderer>();

      // Map bone names under new root
      s_boneMap.Clear();
      GetBoneMapRecursive(_newRootBone, s_boneMap);

      // Map bones by name
      Transform[] newBones = new Transform[_sourceSMR.bones.Length];
      for (int i = 0; i < _sourceSMR.bones.Length; i++)
      {
         string boneName = _sourceSMR.bones[i].name;
         if (!s_boneMap.TryGetValue(boneName, out newBones[i]))
         {
            Debug.LogError($"Bone '{boneName}' not found in new skeleton.");
         }
      }

      // Assign mesh and materials
      newSMR.sharedMesh = _sourceSMR.sharedMesh;
      newSMR.sharedMaterials = _sourceSMR.sharedMaterials;

      // Assign bones and root
      newSMR.bones = newBones;
      newSMR.rootBone = _newRootBone;
   }
   
   protected void CreateNewCharacterVisuals(MACharacterBase _character)
   {
      if(m_characterVisual != null)
      {
         Destroy(m_characterVisual);
         m_characterVisual = null;
         m_characterVisualID = -1;
         m_characterVisualIteration = -1;
      }
      
      if(_character == null)
         return;
      
      if(Block.m_workerVisual != null)
      {
         var blockCharacter = Block.m_workerVisual;
         if(blockCharacter == null)
            return;
         var targetSMR = blockCharacter.GetComponent<SkinnedMeshRenderer>();
         if(targetSMR == null)
            return;
         var parent = blockCharacter.transform.parent;
         var realWorkerSMR = _character.ContentRoot.GetComponentInChildren<SkinnedMeshRenderer>();
         if(realWorkerSMR == null) return;
         m_characterVisual = new GameObject("ClothedWorkerVisual");
         m_characterVisual.transform.SetParent(parent);
         m_characterVisual.transform.localPosition = Vector3.zero;
         m_characterVisualID = _character.m_ID;
         m_characterVisualIteration = _character.m_visualIteration;
         CopyAndRebindSMR(realWorkerSMR, m_characterVisual, targetSMR.rootBone);
      }
      else if(Block.m_characterHolder != null)
      {
         m_characterVisual = Instantiate(_character.ContentRoot, Block.m_characterHolder).gameObject;
         var weapon = m_characterVisual.GetComponentInChildren<MAWeapon>();
         if(weapon != null) Destroy(weapon.gameObject);
         var sendPos = m_characterVisual.GetComponentInChildren<SendPosToParent>();
         if(sendPos != null) Destroy(sendPos);
         
         m_characterVisual.transform.localPosition = Vector3.zero;
         m_characterVisualID = _character.m_ID;
         m_characterVisualIteration = _character.m_visualIteration;
      }
   }
   
   protected void SetCharacterVisualsEnabled(MACharacterBase _character, bool _force = true)
   {
      bool showWorker = _character != null;
      if(Block == null) return;
      if(Block.m_workerVisual != null && Block.m_workerVisual.activeSelf) Block.m_workerVisual.SetActive(false);
      
      if(_character != null && (m_characterVisualID != _character.m_ID || _character.m_visualIteration != m_characterVisualIteration))
      {
         CreateNewCharacterVisuals(_character);
      }
      
      if(m_characterVisual == null) return;
      if(m_characterVisual.activeSelf == showWorker) return;
      
      if(m_nextSwitchTime < 0 && showWorker)
         m_nextSwitchTime = Time.time + 0.5f;
      
      if(m_nextSwitchTime > Time.time && _force == false) return;
      
      m_nextSwitchTime = -1;
      m_characterVisual.SetActive(showWorker);
   }
   
   public void AnimateBlock(float _powerUsed)
   {
      if(m_block == null) return;
        
      if(_powerUsed > 0)
         m_animationSpeed = Mathf.Min(3f, m_animationSpeed + _powerUsed * m_animationSpeedMultiplier);
      else 
         m_animationSpeed = Mathf.Lerp(m_animationSpeed, 0f, 0.5f * Time.deltaTime);
      
      if (s_forceBuildingAnimation > 0) m_animationSpeed = s_forceBuildingAnimation;
      
      bool playAudio = m_animationSpeed > 0.1f;
        
      if (m_block.m_visualSwitcher != null)
      {
         // Cheaky check until we have fully moved to the new system
         if(m_block.m_workerVisual != null)
         {
            m_block.m_visualSwitcher.ManualControl(GetProductScore());
            //SetWorkerVisualsEnabled(m_block.m_visualSwitcher.HasReachedManualTarget() == false && Building.GetWorkersWorking() > 0);
            //SetWorkerVisualsEnabled(Building.GetWorkersWorking() > 0, _powerUsed > 0);
         }
         else
         {
            m_block.m_visualSwitcher.Animate(m_animationSpeed);  
         }
      }
      
      ControlBlockAudio(playAudio, m_building);
      if (playAudio)
      {
         m_block.m_animatedAudioSpeed.SetValue(gameObject, m_animationSpeed);
         m_block.m_animatedAudioSpeed.SetValue(m_building.gameObject, m_animationSpeed);
      }
   }
    
   private void ControlBlockAudio(bool _active, MABuilding _building)
   {
      if (m_block == null) return;
      PlayAudio(_active, m_block.m_animatedAudio, gameObject, ref m_blockAudioHandle, m_block.m_animatedAudioOrientationOffset);
      PlayAudio(_active, m_block.m_animatedBuildingAudio, _building?.gameObject, ref m_buildingAudioHandle, m_block.m_animatedAudioOrientationOffset);
   }
   
   public static void PlayAudio(bool _active, AkEventHolder _hook, GameObject _go, ref int _handle, float _orientationOffset = 0f)
   {
      if(_active && _handle > 0) return;
      if(_active == false && _handle == 0) return; 
      
      if (_hook.IsValid())
      {
         var transformOverride = _go.GetOrAddComponent<AudioTransformOverride>();
         transformOverride.m_rotationAdjust = Vector3.up * (180 + _orientationOffset);
         if (_active)
         {
            _handle = _hook.Play(_go);
            return;
         }
      }
      
      if (_handle != 0)
      {
         AudioClipManager.Me.StopSound(_handle, _go);
         _handle = 0;
      }
   }
   
   virtual protected void OnDestroyCleanup() {}

   virtual public void PreUpdate(BuildingComponentsState _state) {}

   private void LateUpdate()
   {
      if(!GameManager.Me.DataReady || m_building == null)
         return;
         
      LateUpdateInternal();
   }
   
   virtual public void UpdateInternal(BuildingComponentsState _state) { }
   virtual public void LateUpdateInternal() { }
   
   public void Save(SDictionary<long,SaveMABuildingComponent> _sComponents)
   {
      if(m_uid <= 0) return;
      _sComponents[m_uid] = Save();
   }
   
   virtual public SaveMABuildingComponent Save()
   {
      var save = new SaveMABuildingComponent();
      save.m_uid = m_uid;
      save.m_blockIndex = m_block?.m_indexInDesign ?? 0;
      save.m_componentType = GetType().ToString();
      save.m_blockName = m_block?.BlockID ?? "";
      save.m_saveString = SSerializer.Serialize(this);
      save.m_loadSeedData = m_building?.m_stateData.m_shouldLoadSeedData ?? false;
      return save;
   }
   
   public void Load(SaveMABuildingComponent _save)
   {
      if(m_uid > 0)
      {
         Debug.LogError($"Trying to load a component that already has a UID set -> Old: {m_uid} New: {_save.m_uid}");
      }
      
      TrySetUID(_save.m_uid);
      
      if(m_info != null)
         Setup(m_info);
         
      if(GameManager.HasLoadedFromSeed == false || _save.m_loadSeedData || GameManager.Me.LoadComplete)
      {
         if(SSerializer.IsSSerialized(_save.m_saveString))
            SSerializer.DeserializeInPlace(this, _save.m_saveString);
      }
      
      PostLoad();
   }
   
   protected void ShowChimneySmoke()
   {
      m_building.m_chimneySmokeThisUpdate = true;
   }
   
   virtual public SpecialHandlingAction GetSpecialHandlingAction(NGMovingObject _o, SpecialHandlingAction _restrictedAction) => null;

   virtual public void OnBeginDrag(PointerEventData _eventData) { }
   //Worker Overloads

   virtual public int GetFreeSlots() => 0;
   virtual public int GetMaxSlots() => 0;
   
   virtual public float GetRestMultiplier() { return 1f; }
   
   virtual public MACharacterBase GetAvailableWorker() => null;
   virtual public float GetWorkerPower() => 0;
   virtual public bool Arrive(MACharacterBase _worker) => false;
   virtual public bool IsAllocated(MACharacterBase _worker) => false;
   virtual public bool Leave(MACharacterBase _worker) => false;
   virtual public bool Allocate(MACharacterBase _worker) => false;
   virtual public bool Deallocate(MACharacterBase _worker) => false;
   virtual public List<MACharacterBase> GetWorkersAllocated() => new();
   virtual public List<MACharacterBase> GetWorkersPresent() => new();
   virtual public List<MACharacterBase> GetHeroesAllocated() => new();
   virtual public List<MACharacterBase> GetHeroesPresent() => new();

   virtual public MAWorker GetIdleWorker() => null;
   //Entrance Overloads
   
   virtual public Vector3 GetDoorPos()
   {
      var bounds = ManagedBlock.GetTotalVisualBounds(gameObject);
      var direction = Random.onUnitSphere.GetXZNorm() * Mathf.Max(bounds.extents.x, bounds.extents.z) * Random.Range(1.05f, 1.15f);
      return transform.position + direction;
   }

   virtual public Vector3 GetQueuePos(int _index) => GetDoorPos();

   //Action overloads
   
   virtual public float GetRestScore() => 0f;
   
   virtual public bool UpdateTapWork() => false;
   virtual public string Title => m_info == null ? "" : m_info.m_title;
   
   public virtual bool OutputHasDestination => false;
   
   //Stock Overloads
   public bool AddPickup(ReactPickup _what)
   {
      if(_what == null) return false;
      return AddResource(_what.Contents);
   }
   virtual public bool AddResource(NGCarriableResource _what) => false;
   
   virtual public ReactPickup TakePickup(NGCarriableResource _item, bool _killPickup, Action<GameObject> _onComplete = null)
   {
      if(ConsumeStock(_item) == false)
         return null;
      
      return MABuildingSupport.CreatePickupFromResource(m_building, _item, GlobalData.Me.m_pickupsHolder, _killPickup, (o) => 
         { 
            if(_onComplete != null)
               _onComplete(o);
         });
   }
   
   public void RemoveCompletedOrderStock()
   {
      foreach(var item in m_stock.Items)
      {
         if(item.Stock <= 0 || item.Resource.IsFromCompletedOrder() == false) continue;
         
         int remove = item.Stock;
         
         for(int i = 0; i < remove; i++)
         {
            ConsumeStock(item.Resource);
         }
      }
   }
   
   virtual protected void SetupStockRequirements() { if(m_building != null) { m_building.StockRefreshRequired = true; } }
   virtual public float GetResourceRequirement(NGCarriableResource _resource, bool _isTest = false) => 0f; // > 0 signals that this item is required

   virtual public bool ConsumeStock(NGCarriableResource _resource)
   {
      return m_stock.Consume(_resource);
   }
   
   virtual public NGCarriableResource ConsumeOrderProduct(BCActionOrderBase _orderDestination) => null;
   virtual public NGCarriableResource ConsumeForStockOut() => null;
   public NGStock GetStock() => m_stock; // stock available to take
   
   virtual public int GetStockSpace() => 0;
   
   //virtual public GameState_Product GetProduct() => null;
   //virtual public bool ReceiveOrder(MAOrder _maOrder) => false;
   //virtual public bool RemoveOrder(MAOrder _orderToRemove) => false;
   //virtual public MAOrder GetOrder() => null;
   //virtual public bool SetProduct(GameState_Product _product) => false;
   //virtual public int GetOrderFreeSlots() => 0;
   virtual public void DoDamage(float _damageDone, out float OUTdamageDone)
   {
      float defenseValueBefore = DefenseValueCurrent;
      float val = defenseValueBefore - _damageDone;
      m_defenseValueCurrent = 0 > val ? 0 : val;
      OUTdamageDone = defenseValueBefore - m_defenseValueCurrent;
   }

   //Tap & Drag
   virtual public void OnTap(PointerEventData _eventData) { }
   virtual public bool HasDragContent() => false;
   virtual public GameObject GetDragContent() => null;
   virtual public void OnBeginLongPress(PointerEventData _eventData) { }
   virtual public void OnUpdateLongPress(PointerEventData _eventData, float _longPressTime) { }
   virtual public void OnEndLongPress(PointerEventData _eventData) { }
   virtual public float GetProductScore() => 0f;
   virtual public string GetDebugInfo() => "";
   
   virtual public NGProductInfo GetProductLineInfo() => null;
   
   public virtual void Highlight(bool _moveCamera = true)
   {
      if(Block == null) return;
      
      if(_moveCamera)
         MAParser.MoveCamera(transform.position, 25f);
      BuildingComponentVisualHelpers.HighlightBlock(Block);
      NGBuildingInfoGUI.DestroyCurrent();
   }
   
   public static void HighlightRange(List<BCBase> _components)
   {
      if(_components == null) return;
      
      var averagePos = Vector3.zero;
      int count = 0;
      BuildingComponentVisualHelpers.ClearHighlights();
      foreach(var c in _components)
      {
         if(c.Block == null) continue;
         
         averagePos += c.transform.position;
         count++;
         
         BuildingComponentVisualHelpers.AddToHighlightedBlocks(c.Block);
      }
      
      if(count == 0) return;
      averagePos /= count;
      
      
      MAParser.MoveCamera(averagePos, 25f);
      NGBuildingInfoGUI.DestroyCurrent();
   }
      
   
   public class CombinedStat
   {
      public enum ValueDisplay
      {
         Default,
         BuildingTip,
         BuildingTipOnlyFreeSlots,
         NewBlockInfo,
         InfoPanel,
      }
      
      public int m_componentCount = 0;
      public virtual string GetValue(ValueDisplay _display = ValueDisplay.Default, bool _verbose = false) => null;
   }
   
   virtual public void GetCombinedValue(ref CombinedStat _value) {}
   
   public string GetGUIText()
   {
      CombinedStat stat = null;
      GetCombinedValue(ref stat);
      return stat?.GetValue() ?? "";
   }
   
   virtual public bool CombineGUI => true;
   
   virtual public (string id, Func<BCUIPanel> create) GetUIPanelInfo() => (m_info?.id, null);
      
   public void GetOrCreatePanel(List<BCUIPanel> _panels) 
   {
      var panelInfo = GetUIPanelInfo();
      if(panelInfo.id.IsNullOrWhiteSpace() || panelInfo.create == null) return;
      
      BCUIPanel existingPanel = null;
      if(CombineGUI)
      {
         foreach(var panel in _panels)
         {
            if(panel.UID != panelInfo.id) continue;
            existingPanel = panel;
            break;
         }
      }
      
      if(existingPanel == null)
      {
         var newPanel = panelInfo.create();
         if(newPanel != null)
         {
            _panels.Add(newPanel);
            AddToPanel(newPanel);
         }
      }
      else
      {
         AddToPanel(existingPanel);
      }
   }
   
   public void AddToPanel(BCUIPanel _panel) 
   {
      if(_panel == null) return;
      
      _panel.AddComponent(this);
   }
   
   virtual public void DisplayBuildingInfo(Transform _holder, List<BCBase> _components) { }
   
   private const float c_hangOutDistanceFromDoorPosOuter = 5f;
   private const float c_hangOutDistanceExtraMargin = 4.5f;
   private bool hangOutAreaLeft = false;
   
   public Vector3 GetRandomPosInHangOutArea()
   {
      UnityEngine.Random.InitState(DateTime.Now.Millisecond);
        
      var dir = m_building.DoorPosOuter - m_building.DoorPosInner;
      float rotY;
      if (hangOutAreaLeft)
         rotY = UnityEngine.Random.Range(-45f, -10f);
      else
         rotY = UnityEngine.Random.Range(10f, 45f);
      hangOutAreaLeft = !hangOutAreaLeft;
      dir = Quaternion.Euler(0f, rotY, 0f) * dir;
      var areaCenter = m_building.DoorPosOuter + (dir.normalized * c_hangOutDistanceFromDoorPosOuter);
      var dest = areaCenter + (UnityEngine.Random.insideUnitCircle.V3XZ() * UnityEngine.Random.Range(1f, c_hangOutDistanceExtraMargin));

      return dest;
   }

   public static bool IsInHangOutArea(MABuilding _building, Vector3 _pos)
   {
      float distSq = (_building.DoorPosOuter - _pos).xzSqrMagnitude();
      float maxDist = c_hangOutDistanceFromDoorPosOuter + c_hangOutDistanceExtraMargin + 2f;
      return maxDist * maxDist > distSq;
   }
}

public class BCGeneralInfoPanel : BCUIPanel
{
   public override int Priority => 0;
   public const string PanelID = "generalinfo";
   public Dictionary<MAComponentInfo, List<BCBase>> m_lookup = new();

   public BCGeneralInfoPanel() : base(PanelID, "Additional Features") { }
    
   //public override string GetDescription() => "";

   public override void AddComponent(BCBase _cmp)
   {
      var key = _cmp.GetInfo();
      if (key == null)
         return;
         
      if (m_lookup.ContainsKey(key))
         m_lookup[key].Add(_cmp);
      else
         m_lookup[key] = new List<BCBase>() { _cmp };
         
      m_all.Add(_cmp);
   }

   override public IEnumerable<Action> CreateTableLines(Transform _holder)
   {
      foreach (var d in m_lookup)
      {
         BCBase.CombinedStat stat = null;
         foreach (var c in d.Value)
         {
            c.GetCombinedValue(ref stat);
         }
         if(stat == null)
            yield return () => MADesignInfoSheetLine.Create(_holder, $"{d.Key.m_title} x {d.Value.Count}", d.Key.m_description, true);
         else
            yield return () => MADesignInfoSheetLine.Create(_holder, $"{d.Key.m_title} x {d.Value.Count}", stat.GetValue(), true);
      }
   }
}

public class WarningInfoPanel : BCUIPanel
{
   public override int Priority => 0;
   public const string PanelID = "warnings";
   public MABuilding m_building;
   public override bool ShowWarning => true;

   public WarningInfoPanel(MABuilding _building) : base(PanelID, "Missing Blocks") { m_building = _building; }

   public override string GetPrimaryText()
   {
      return "Your building requires the following blocks for it to function fully.";
   }

   override public IEnumerable<Action> CreateTableLines(Transform _holder)
   {
      m_disablePanel = true;
      if(m_building == null)
      {
         yield break;
      }
      
      bool hasEntrance = false;
      List<MAComponentInfo> missing = new();
      foreach(var c in m_building.m_components)
      {
         hasEntrance |= c is BCEntrance;
         c.GetMissingComponents(missing);
      }
      
      m_disablePanel = missing.Count == 0;
      yield return () => MADesignInfoSheetLine.Create(_holder, "<b>Block Name</b>", "<b>Description</b>", true);
      foreach(var m in missing)
      {
         yield return () => MADesignInfoSheetLine.Create(_holder, m.m_title, m.m_description, true); 
      }
      
      if(hasEntrance)
      { 
         var nav = m_building.GetComponent<BuildingNav>(); 
         if(nav != null && nav.HasDoor == false)
         {
            yield return () => MADesignInfoSheetLine.Create(_holder, "Entrance", "Missing accessible entrance block. Add or reposition an existing one", true);
            m_disablePanel = false;
         }
      }
   }
}

public class BCBlockInfoPanel : BCUIPanel
{
   public override int Priority => 0;
   public const string PanelID = "blocks";
   public MABuilding m_building;
   
   public BCBlockInfoPanel(MABuilding _building) : base(PanelID, "Breakdown of Blocks") { m_building = _building; }
  
   override public IEnumerable<Action> CreateTableLines(Transform _holder)
   {
      if(m_building == null)
      {
         yield return () => MADesignInfoSheetLine.Create(_holder, $"None", null, true);
      }
      else
      {
         var lines = m_building.GetBlockData();
         foreach(var line in lines)
         {
            yield return () => MADesignInfoSheetLine.Create(_holder, line.Item1, line.Item2, true);
         }
      }
   }
}
public class BCUIPanel
{
   public virtual int Priority => 10;
   public string UID => m_uid;
   public virtual bool ShowWarning => false;
   public virtual bool ExpandByDefault => ShowWarning;
   public virtual string Title =>  GetTitle();
   public virtual bool ForceDisableQuantityDisplay => false;
   public bool m_forceUpdateTableLines = false;
   public virtual bool m_forceDisableHighlightButton => false;
   
   public virtual Action GetHighlightAction()
   {
      if(m_forceDisableHighlightButton || m_all.Count == 0) return null;
      
      return () => BCBase.HighlightRange(m_all);
   }
   
   public virtual Action GetShowRangeAction() => null;
   
   private string GetTitle()
   {
      var value = ShowWarning ? MAMessageManager.GetTMPString("Warning") + " " : "";
      value +=  (m_quantity < 2 || ForceDisableQuantityDisplay) ? m_title : $"{m_title} x {m_quantity}";
      return value;
   }
   
   public int Quantity => m_quantity;
   public bool DisablePanel => m_disablePanel;
   public virtual string GetDescription() => null;
   public virtual string SpriteBlockID => null;
   public virtual string GetPrimaryText() => null;
   public virtual string TableHeading => "";
   public int TableSpacing => 0; 
   
   private string m_title;
   private string m_uid;
   protected int m_quantity;
   public string m_tipMessage;
   protected bool m_disablePanel = false;
   protected List<BCBase> m_all = new ();
   // Maybe to collect generic appended info here?
   
   public BCUIPanel(MAComponentInfo _info) 
   {
      m_uid = _info.id;
      m_title = _info.m_title;
   }
   public BCUIPanel(string _uid, string _title) 
   {
      m_uid = _uid;
      m_title = _title;
   }
   
   virtual public void AddComponent(BCBase _component)
   {
      m_quantity++;
      m_all.Add(_component);
   }
   
   public static string GetModifiedValue(string _name, string _value, float _modification = 1f, bool _isNewLine = true)
   {
      // currently assumes all modifications are good
      string modTxt = "";
      if(Mathf.Approximately(_modification, 1f) == false)
      {
         modTxt = $" <color={MAGUIBase.GreenColor}>({(_modification > 1f ? "+" : "")}{(_modification-1f):P0})</color>";
      }
      return $"{(_isNewLine ? "\n" : "")}<b>{_name}:</b>  {_value}{modTxt}";
   }
   
   virtual public bool RebuildTableLines() => true;
   
   virtual public IEnumerable<Action> CreateTableLines(Transform _holder)
   {
      m_forceUpdateTableLines = false;
      yield break;
   }
}

public static class BuildingComponentVisualHelpers
{
   private static TerrainSectionMesh s_rangeArea = null;
   private static Material s_greenMat;
   private static float s_disableRangeAreaTime;
   private static List<Block> s_highlightedBlocks = new();
   
   public static TerrainSectionMesh DrawTerrainArea(Vector3 _position, float _minRadius, float _maxRadius, Material _mat, float _raise = 0.15f)
   {
      var mesh = TerrainSectionMesh.Create("TurretRangeArea", _position, _maxRadius * 2f * Vector3.one, _raise, _mat, _minRadius, _maxRadius);
      //plot.m_uvRotate = dir;
      var goMesh = mesh.gameObject;
      goMesh.transform.SetParent(GlobalData.Me.m_terrainCircleSelectionHolder, true);
      goMesh.name = "TurretRangeArea";
      return mesh;
   }
   
   public static void ClearHighlights()
   {
      if(s_rangeArea) s_rangeArea.gameObject.SetActive(false);  
      s_highlightedBlocks.Clear();
   }
   
   public static void AddToHighlightedBlocks(Block _block, float _displayTime = 4f)
   {
      if(s_rangeArea) s_rangeArea.gameObject.SetActive(false);
      s_highlightedBlocks.Add(_block);
      s_disableRangeAreaTime = Time.time + _displayTime;
   }
   
   public static void HighlightBlock(Block _block, float _displayTime = 4f)
   {
      if(s_rangeArea) s_rangeArea.gameObject.SetActive(false);  
      s_highlightedBlocks.Clear();
      s_highlightedBlocks.Add(_block);
      s_disableRangeAreaTime = Time.time + _displayTime;
   }
   
   private const float c_maxAlpha = 0.5f;
   private static void SetAlpha(float _value)
   {
      if(s_greenMat == null) return;
      
      var color = s_greenMat.color;
      color.a = Mathf.Clamp(_value, 0, c_maxAlpha);
      s_greenMat.color = color;
   }
   
   public static void AddGroundArea(Vector3 _position, float _minRadius, float _maxRadius, float _displayTime = 4f)
   {
      if(s_greenMat == null)
      {
         s_greenMat = new Material(GlobalData.Me.m_terrainCircleProjectionMaterial);
         s_greenMat.SetColor("_Color", new Color(0,1,0,0));
      }
      
      if(s_rangeArea == null) s_rangeArea = DrawTerrainArea(_position, _minRadius, _maxRadius, s_greenMat, 0.3f);
      
      s_rangeArea.SetPosition(_position);
      s_rangeArea.SetMinMaxRadius(_minRadius, _maxRadius);
      s_rangeArea.Initialise();
      s_rangeArea.Update();
      s_rangeArea.gameObject.SetActive(true);
      
      s_disableRangeAreaTime = Time.time + _displayTime;
   }
    
   private const float c_pulseSpeed = 9f;
   private const float c_pulseAmount = 0.15f;
   public static void UpdateHighlights()
   {
      if(Time.time >= s_disableRangeAreaTime)
      {
         if(s_rangeArea != null && s_rangeArea.gameObject.activeInHierarchy)
         {
            var a = s_greenMat.color.a;
            if(a > 0.01f)
            {
               a -= Time.deltaTime;
               SetAlpha(a);
            }
            else
            {
               s_rangeArea.gameObject.SetActive(false);
            }
         }
         s_highlightedBlocks.Clear();
      }
      else
      {
         if(s_rangeArea != null &&  s_greenMat.color.a < c_maxAlpha)
         {
            var a = s_greenMat.color.a;
            a += Time.deltaTime;
            SetAlpha(a);
         }
            
         if (s_highlightedBlocks.Count > 0)
         {
            var pulse = Mathf.Sin(Time.time * c_pulseSpeed) * c_pulseAmount;
            foreach(var block in s_highlightedBlocks)
            {
               if(block.GetComponent<BaseBlock>() == null)
               {
                  MABuilding.SetRendererJolt(block, new Vector3(0, pulse, 0));
               }
            }
         }   
      }
   }
}
