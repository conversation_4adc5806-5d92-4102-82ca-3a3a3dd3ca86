using System;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

public class BCChopObject : MonoBehaviour
{
    [Serializable]
    public class ChopObjectSaveData
    {
        public float chopLeft = 1f;
        public float resourceScale = -1f;
        public int regrowDay = -1;
        public float regrowDelay;
        //public float regrowTime = -1f;
        //public float regrowStartTime = -1f; // not used
    }

    private const float c_minScale =  0.0001f;
    private const float c_growDuration = 5f;
    
    public static SDictionary<int, ChopObjectSaveData> s_choppedObjects = new SDictionary<int, ChopObjectSaveData>();
    
    public MACharacterBase m_worker = null;
    
    private ChopObjectSaveData m_data = new();
    private float m_maxHeightScale = 1f;
    private float m_maxWidthScale = 1f;
    private float m_rotation;
    private float m_uprootStartTime = 0;
    private TreeHolder m_treeHolder = null;
    public MACollectableResource m_collectableResource = null;
    
    public TreeHolder TreeHolder
    {
        get
        {
            if (m_treeHolder == null) m_treeHolder = GetComponentInChildren<TreeHolder>();
            return m_treeHolder;
        }
    }
    
    public float ChopLeft => m_data.chopLeft;
    public bool IsChoppedDown => m_data.chopLeft <= 0f;
    public bool IsChopObjectValid => m_collectableResource != null;

    public static void RestartDay()
    {
        RegrowAllResources(true);
    }
    
    // _dawnPercent: 1 == whole dawn, 0 == instant
    public static void RegrowResourceType(string _type, bool forceRegrow = false)
    {
        if(TreeHolder.AllTreesByType.TryGetValue(_type, out var list))
        {
            foreach(var item in list)
            {
                if(item.IsLocked) continue;
                
                var chopObj = item.ChopObject;
                
                if(chopObj == null || chopObj.ChopLeft >= 1f) continue;
                
                chopObj.SetRegrowDay(forceRegrow ? -100 : 0);
            }
        }
    }
    
    public static void RegrowAllResources(bool forceRegrow = false)
    {
        RegrowResourceType(TreeHolder.c_cotton, forceRegrow);
        RegrowResourceType(TreeHolder.c_wheat, forceRegrow);
        RegrowResourceType(TreeHolder.c_metal, forceRegrow);
        RegrowResourceType(TreeHolder.c_trees, forceRegrow);
    }

    public void Awake()
    {
        if(GameManager.Me != null && GameManager.Me.DataReady)
            SetupTreeObject();
    }

    public void Update()
    {
        UpdateUproot();
        UpdateRegrow();
    }

    public void SetupTreeObject(bool _loadFromSave = false)
    {
        if (m_collectableResource)
            return;

        var th = TreeHolder;
        if (th == null || th.gameObject.activeSelf == false)
            return;

        if(_loadFromSave == false)
            s_choppedObjects[th.m_treeIndex] = m_data;
        
        m_maxHeightScale = Mathf.Max(c_minScale, TreeHolder.GetInstanceHeightScale(th.m_treeIndex));
        
        if(th.IsWheat || th.IsCotton || th.IsMetal)
        {
            var origionalHeightScale = m_maxHeightScale;
            m_maxHeightScale = 1f + UnityEngine.Random.Range(-0.1f, 0.1f); // Regrown wheat reaches full size (with variation)
            
            if(_loadFromSave == false)
                m_data.chopLeft = Mathf.Clamp01(origionalHeightScale / m_maxHeightScale);
        }

        m_maxWidthScale = Mathf.Max(c_minScale, TreeHolder.GetInstanceWidthScale(th.m_treeIndex));
        m_rotation = TreeHolder.GetInstanceRotation(th.m_treeIndex);
        
        var prefab = th.GetPrefab();

        if (prefab != null)
        {
            var treeObject = Instantiate(prefab, transform);
            treeObject.transform.localPosition = Vector3.zero;
            treeObject.transform.rotation = Quaternion.Euler(0f, m_rotation * Mathf.Rad2Deg, 0f);
            treeObject.transform.localScale = Vector3.one;
            treeObject.name = th.m_treeName + "_" + th.m_treeIndex;

            m_collectableResource = treeObject.GetComponentInChildren<MACollectableResource>();
        }
        else
        {
            m_collectableResource = GetComponentInChildren<MACollectableResource>();
            gameObject.transform.localPosition = Vector3.zero;
            gameObject.transform.rotation = Quaternion.Euler(0f, m_rotation * Mathf.Rad2Deg, 0f);
            gameObject.transform.localScale = Vector3.one;
            gameObject.name = th.m_treeName + "_" + th.m_treeIndex;
        }
        
        if(m_collectableResource)
        {
            float scale = m_data.resourceScale < 0 ? m_data.chopLeft : m_data.resourceScale;
            m_collectableResource.transform.localScale = new Vector3(m_maxWidthScale, Mathf.Max(c_minScale, scale * m_maxHeightScale), m_maxWidthScale);
        }
        else Debug.LogError($"Trying to setup BCChopObject for treeholder: {m_treeHolder.m_treeName} but no TreePrefab exists for this type!");
        
        RefreshTreeObjectActive();
        
        TreeHolder.SetInstanceHeightScale(th.m_treeIndex, 0); // Hide the terrain version
    }

    private void UpdateRegrow()
    {
        bool regrowAtDusk = !GameManager.m_cropRegenerationAtDawn;
        int regrowDay = m_data.regrowDay;
        if (regrowAtDusk && (regrowDay > 0))
            regrowDay -= 1;
        
        bool forceRegrow = regrowDay == -100;
        if ((!forceRegrow && (regrowDay < 0)) || DayNight.Me.CurrentWorkingDay < regrowDay)
            return;
        
        if (!forceRegrow && regrowAtDusk && (DayNight.Me.m_timeStage < DayNight.c_timeStageDayEnd))
            return;

        if(m_data.regrowDelay > 0f)
        {
            m_data.regrowDelay -= Time.deltaTime;
            return;
        }
        
        var th = TreeHolder;
        if (th == null)
            return;

        if(m_choppedDownVisual)
        {
            Destroy(m_choppedDownVisual);
            m_choppedDownVisual = null;
        }
            
        m_data.chopLeft = Mathf.Clamp01(m_data.chopLeft + (1f / c_growDuration) * Time.deltaTime);
        m_data.resourceScale = m_data.chopLeft;

        float regrowHeight = m_maxHeightScale * m_data.chopLeft;
        float regrowWidth = m_maxWidthScale;// * m_data.chopLeft;

        if (m_data.chopLeft >= 1f)
        {
            m_data.regrowDay = -1;
            RefreshTreeObjectActive();
        }

        if(m_collectableResource) m_collectableResource.Regrow(regrowHeight, regrowWidth);
    }
    
    /*private void UpdateRegrow()
    {
        if (m_data.regrowTime < 0f)
            return;

        var th = TreeHolder;
        if (th == null)
            return;

        float gameTime = GameManager.Me.m_state.m_gameTime.m_gameTime;
        if (gameTime < m_data.regrowTime) return;

        if(m_choppedDownVisual)
        {
            Destroy(m_choppedDownVisual);
            m_choppedDownVisual = null;
        }
            
        m_data.chopLeft = Mathf.Clamp01(m_data.chopLeft + (1f / c_growDuration) * Time.deltaTime);

        float regrowHeight = m_maxHeightScale * m_data.chopLeft;
        float regrowWidth = m_maxWidthScale * m_data.chopLeft;

        if (m_data.chopLeft >= 1f)
        {
            m_data.regrowTime = -1f;
            RefreshTreeObjectActive();
        }

        if(m_collectableResource) m_collectableResource.Regrow(regrowHeight, regrowWidth);
    }*/

    private void EnableCollider(bool _enabled)
    {
        var cll = GetComponent<BoxCollider>();
        if (cll != null) cll.enabled = _enabled;
    }

    private GameObject m_choppedDownVisual = null;
    public void OnChoppedDown(int _daysDelay)
    {
        var th = TreeHolder;
        if (th == null) return;

        m_data.chopLeft = 0;
        m_data.resourceScale = 0f;
        
        if (m_collectableResource)
        {
            if (m_collectableResource.WaitForChopDownFallingAnimation == false)
            {
                m_collectableResource.Regrow(m_maxHeightScale * m_data.chopLeft, m_maxWidthScale);
                RefreshTreeObjectActive();
            }
            else
            {
                m_collectableResource.m_onTreeFallAnimationFinished += () =>
                {
                    m_collectableResource.Regrow(m_maxHeightScale * m_data.chopLeft, m_maxWidthScale);
                    if (th.IsTree && m_choppedDownVisual == null)
                        m_choppedDownVisual = Instantiate(NGManager.Me.m_treeStumpPrefab, transform.position,
                            Quaternion.Euler(0f, UnityEngine.Random.Range(0f, 360f), 0f), transform.parent);
                    m_collectableResource.m_onTreeFallAnimationFinished = null;
                    RefreshTreeObjectActive();
                };
            }
        }
        
        AddRegrowDays(_daysDelay);
        
        if(m_treeHolder.IsTree)
            AlignmentManager.Me.ApplyAction("TreeChoppedDown", -0.001f, "");
    }

    private void AddRegrowDays(int _daysDelay)
    {
        if (_daysDelay < 0)
        {      
            m_data.regrowDay = Int32.MaxValue;
            m_data.regrowDelay = Int32.MaxValue;
            return;
        }
        int regrowDays = DayNight.Me.CurrentWorkingDay + _daysDelay;
        bool regrowAtDusk = !GameManager.m_cropRegenerationAtDawn;
        if (regrowAtDusk && (DayNight.Me.m_timeStage >= DayNight.c_timeStageDayEnd))
            regrowDays += 1;

        SetRegrowDay(regrowDays);
    }
    
    private void SetRegrowDay(int _day, float _maxDelay = 3f)
    {
        m_data.regrowDay = _day;
        m_data.regrowDelay = UnityEngine.Random.Range(0, _maxDelay);
    }
    
    /*private void CalculateRegrowTimeFromDawns(float regrowDawns, float startTime)
    {
        if (regrowDawns < 0)
        {
            m_data.regrowTime = -1f;
            return;
        }
        
        float regrowMidnight = Mathf.Floor(startTime + regrowDawns);
        float regrowHour = UnityEngine.Random.Range(NGManager.Me.m_regrowHourMin, NGManager.Me.m_regrowHourMax);
        
        m_data.regrowTime = regrowMidnight + DayNight.ClockToFraction(regrowHour);
    }*/
    
    public static NGCarriableResource TypeToCarriableResource(string _treeType)
    {
        if(_treeType == "Trees") return NGCarriableResource.GetInfo("Wood");
        if(_treeType == "Wheat") return NGCarriableResource.GetInfo("Wheat");
        if(_treeType == "Metal") return NGCarriableResource.GetInfo("Ore");
        if(_treeType == "Cotton") return NGCarriableResource.GetInfo("Cotton");
        return NGCarriableResource.GetInfo("");
    }
    
    public NGCarriableResource GetCarriableResourceProduced()
    {
        var th = TreeHolder;
        if (th != null)
            return TypeToCarriableResource(TreeHolder.m_treeType); 
        return NGCarriableResource.GetInfo("");
    }
    
    public float HitsUntilHarvest()
    {
        var th = TreeHolder;
        if (th == null)
            return 0f;
            
        var totalResources = th.TotalResourceProduced;
        var stepsBetweenLevels = 1f / totalResources;
        int nextLevel = (int)Math.Floor(ChopLeft*totalResources-0.001f);
        float nextChopThreshold = nextLevel * stepsBetweenLevels;
        
        return ChopLeft - nextChopThreshold;
    }
    
    public int ResourcesRemaining()
    {
        var totalResources = TreeHolder.TotalResourceProduced;
        return (int)Math.Floor(ChopLeft*totalResources-0.001f) + 1;
    }
    
    public bool HitObject(float _hitAmount, bool _scheduleRegrow = true)
    {
        var th = TreeHolder;
        if (th == null)
            return false;
        
        if (ChopLeft <= 0f)
            return false;
        
        bool generatedResource = false;
        var totalResources = th.TotalResourceProduced;
        int nextLevel = (int)Math.Floor(ChopLeft*totalResources-0.001f);
        var stepsBetweenLevels = 1f / totalResources;
        float nextChopThreshold = nextLevel * stepsBetweenLevels;
        
        m_data.chopLeft -= _hitAmount;
        AddRegrowDays(1);
        
        if((ChopLeft <= 0f) || (ChopLeft <= nextChopThreshold))
        {
            generatedResource = true;
            m_data.chopLeft = Mathf.Clamp01(nextChopThreshold);
        }
        
        m_data.chopLeft = Mathf.Clamp01(ChopLeft);
        if(m_collectableResource) m_collectableResource.ApplyHit(ChopLeft);
        
        if(_scheduleRegrow && IsChoppedDown)
        {
            OnChoppedDown(1);
        }
        return generatedResource;
    }

    public string GetChopIntroAnimation()
    {
        var th = TreeHolder;
        if (th == null) return null;
        
        if(th.IsWheat) return "WorkerScytheInto";
        if(th.IsCotton) return "WorkerScytheInto";
        return null;
    }

    public string GetChopLoopAnimation()
    {
        var th = TreeHolder;
        if (th == null) return null;
            
        if(th.IsTree) return "WorkerChop";
        if(th.IsWheat) return "WorkerScytheLoop";
        if(th.IsCotton) return "WorkerScytheLoop";
        if(th.IsMetal) return "WorkerChop";
            
        return null;
    }

    public string GetChopOutroAnimation()
    {
        var th = TreeHolder;
        if (th == null)
            return null;
        
        if(th.IsWheat) return "WorkerScytheOut";
        if(th.IsCotton) return "WorkerScytheOut";
            
        return null;
    }

    public void RefreshTreeObjectActive()
    {
        if (m_collectableResource)
        {
            m_collectableResource.gameObject.SetActive(IsChoppedDown == false);
            if(m_collectableResource.gameObject.activeSelf == false)
            {
                m_collectableResource.transform.rotation = Quaternion.Euler(0f, m_rotation * Mathf.Rad2Deg, 0f);
                m_collectableResource.StopUprootAnimation();
            }
        }
            
        EnableCollider(IsChoppedDown == false && TreeHolder.IsTreePickupMode);
    }
    

    public static bool CanUproot(TreeHolder _th)
    {
        return _th.IsTree;
    }

    private string GetUprootedVisual()
    {
        if (TreeHolder.IsTree) return "MA_Barrel"; // TODO - change to uprooted tree visual
        return null;
    }

    public void StartUproot()
    {
        m_uprootStartTime = Time.time;
        if (m_collectableResource) m_collectableResource.StartUprootAnimation();
    }
    
    [NonSerialized]
    public int m_dawnsUntilUprootedTreeRegrowth = 1;
    private void UpdateUproot()
    {
        if (m_uprootStartTime == 0) return;
        const float c_uprootDuration = 1f;
        if (Time.time > m_uprootStartTime + c_uprootDuration)
        {
            if (m_collectableResource) m_collectableResource.PlayUprootedAnimation();
            
            // Should probably create a number of resources here instead
            // Scale tree down too?
            
            
            /*// create pickup and start drag
            var uprootedVisual = GetUprootedVisual();
            if (uprootedVisual != null)
            {
                var info = NGDecorationInfoManager.GetInfo(uprootedVisual);
                var instance = Instantiate(info.m_prefab, NGDecorationInfoManager.Me.m_decorationHolder);
                instance.transform.position = transform.position;
                GlobalData.Me.DoNextFrame(() =>
                {
                    var drag = instance.GetComponent<NGDecorationInputHandler>();
                    drag.StartDrag(true, 0);
                    drag.BeginDragging(null, null, true);
                });
            }*/
            m_uprootStartTime = 0;
            OnChoppedDown(m_dawnsUntilUprootedTreeRegrowth);
        }
        else if (GameManager.GetMouseButton(0) == false)
        {
            /*var th = TreeHolder;
            TreeHolder.SetInstanceHeightScale(th.m_treeIndex, 1);
            if (m_collectableResource)
            {
                m_collectableResource.StopUprootAnimation();
                Destroy(m_collectableResource.gameObject);
            }
            Destroy(this);*/
        }
    }

    public static void SaveAll(ref string _s)
    {
        var result = JsonUtility.ToJson(s_choppedObjects);
        _s = result;
    }
    
    public static void LoadAll(string _l)
    {
        if (_l.IsNullOrWhiteSpace()) return;

        s_choppedObjects = JsonUtility.FromJson<SDictionary<int, ChopObjectSaveData>>(_l);
        if (s_choppedObjects == null) return;

        List<int> removeLockedIds = new();
        foreach(var kvp in s_choppedObjects.Base)
        {
            int treeIndex = kvp.Key;
            var data = kvp.Value;
            
            var go = TreeHolder.GetTreeObject(treeIndex);
            
            if(go == null)
            {
                removeLockedIds.Add(treeIndex);
                Debug.LogError($"BCChopObject: TreeHolder with id {treeIndex} not found!");
                continue;
            }
            
            // Check if should be locked
            var holder = go.GetComponent<TreeHolder>();
            if(holder == null || holder.ShouldBeLocked)
            {
                removeLockedIds.Add(treeIndex);
                continue; 
            }
            
            var co = go.GetComponent<BCChopObject>();
            if (co == null)
                co = go.AddComponent<BCChopObject>();
                
            co.m_data = data;
            co.SetupTreeObject(true);
        }
        
        foreach(var treeId in removeLockedIds)
        {
            s_choppedObjects.RemoveKey(treeId);
        }
    }
}
