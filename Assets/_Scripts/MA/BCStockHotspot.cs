using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class BCStockHotspot : BCStockIn
{
    private Transform m_cropHolder = null;
    public string m_hotspotResource = NGCarriableResource.c_none;
    public NGCarriableResource HotspotResource => NGCarriableResource.GetInfo(m_hotspotResource);
    [Save] public float m_refreshStockTime = 60*10f;
    [Save] public float m_refreshStockTimer;
    private int[] m_hotspotStocksMax;
    private int[] m_hotspotStocks;

  
    override public void Activate(int _indexOfComponentType, int _quantityInBuilding)
    {
        base.Activate(_indexOfComponentType, _quantityInBuilding);
        m_stock.Clear();
        m_hotspotStocks = null;
        m_hotspotStocksMax = null;
        ActivateCrops();
        var maHotspotStock = GetComponentInChildren<MAHotspotStock>();
        if (maHotspotStock && maHotspotStock.m_resource.IsNullOrWhiteSpace() == false && m_hotspotResource.IsNullOrWhiteSpace())
        {
            m_hotspotResource = maHotspotStock.m_resource;
        }
        m_stock.AddOrCreateStock(HotspotResource, m_maxStock, 0);
    }

    void ActivateCrops()
    {
        m_cropHolder = transform.Find("Visuals/Crops");
        if (m_cropHolder == null || m_cropHolder.childCount == 0) return;

        if (m_cropHolder)
        {
            m_hotspotStocksMax = new int [m_cropHolder.childCount];
            m_hotspotStocks = new int [m_cropHolder.childCount];
            var perStock = (float)m_maxStock / (float)m_cropHolder.childCount;
            var remainder = 0f;
            for (int i = 0; i < m_cropHolder.childCount; i++)
            {
                m_hotspotStocksMax[i] = m_hotspotStocks[i] = (int)perStock;
                remainder += perStock - m_hotspotStocksMax[i];
            }

            for (int i = 0; i < remainder && i < m_cropHolder.childCount; i++)
            {
                m_hotspotStocks[i]++;
                m_hotspotStocksMax[i]++;
            }
        }
    }

    override public void UpdateInternal(BuildingComponentsState _state)
    {
        if (m_refreshStockTime != -1f)
        {
            if (Time.time >= m_refreshStockTimer)
            {
                m_refreshStockTimer = m_refreshStockTime + Time.time;
                RegrowCrops();
            }
        }
    }

    bool RegrowCrops()
    {
        
        if (m_cropHolder == null || m_cropHolder.childCount == 0) return false;
        for(int i = 0; i < m_cropHolder.childCount;i++)
        {
            var t = m_cropHolder.GetChild(i);
            if (t.gameObject.activeSelf == false)
            {
                m_hotspotStocks[i] = m_hotspotStocksMax[i];
                t.gameObject.SetActive(true);
                return true;
            }
        }

        return false;
    }

    /*override public void DestroyStockBlock(NGCarriableResource _resource, int _count = 1)
    {
        if (m_cropHolder == null || m_cropHolder.childCount == 0) return;
        for (int i = 0; i < _count; i++)
        {
            for (int j = 0; j < m_cropHolder.childCount; j++)
            {
                if (m_hotspotStocks[j] > 0)
                {
                    m_hotspotStocks[j]--;
                    if (m_hotspotStocks[j] == 0)
                        m_cropHolder.GetChild(j).gameObject.SetActive(false);
                    break;
                }
            }
        }
    }*/

    /*override public List<NGCarriableResource> GetInputItems()
    {
        var result = new List<NGCarriableResource>();
        if (m_hotspotResource == null || m_hotspotResource == NGCarriableResource.None)
            return result;
        result.Add(HotspotResource);
        return result;
    }*/
}
