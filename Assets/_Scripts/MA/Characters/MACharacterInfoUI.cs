using System;
using TMPro;
using UnityEngine;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using UnityEngine.UI;

public class MACharacterInfoUI : NGBaseInfoGUI
{
    public GameObject m_possessButton;
    public GameObject m_recallButton;
    public GameObject m_levelupButton;
    public Image m_newLevelup;
    public GameObject m_editNameButton;
    
    public TMP_Text m_recallText;
    public Transform m_infoPanel;
    public Transform m_combatPanel;
    public Transform m_historyPanel;
    public TMP_Text m_title = null;
    
    [SerializeField]
    private float m_refreshTime = 0.5f;

    protected MATimer m_timer = null;
    protected MACharacterBase m_character = null;

    private List<MACharacterInfoElement> m_elements = new();

    private void Update()
    {
        if(m_character == null)
        {
            ClickedClose();
        }
        if (m_timer.IsFinished)
        {
            UpdateInfo();
        }
    }
    
    private void DestroyElements()
    {
        foreach(var field in m_elements)
        {
            Destroy(field.gameObject);
        }
        m_elements.Clear();
    }
    
    public void OnZoom()
    {
        if(m_character == null) return;
        
        MAParser.MoveCamera(m_character.transform.position, 20f);
        ClickedClose();
    }
    
    public void OnEditName()
    {
        if(m_character == null || NGRename.Me != null) return;
        
        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
        NGRename.Create(m_character.Name, GotNewName);
    }
    
    public void GotNewName(string _newName)
    {
        if(m_character == null) return;
        
        m_character.Name = _newName;
    }
    
    public void OnRecall()
    {
        var action = MABuildingWorkerPanelLine.GetWorkerActionButton(m_character);
        
        if(action.action == null) return;
        
        action.action();
    }
    
    public void OnPossess()
    {
        if(GameManager.Me.CanPossess(m_character) == false) return;
        
        GameManager.Me.PossessObject(m_character);
        ClickedClose();
    }
    public void OnLevelup()
    {
        ClickedClose();
        MAHeroLevelupGUI.Create(m_character as MAHeroBase);
    }

    void Activate(MACharacterBase _character)
    {
        m_timer = new(m_refreshTime);
        
        base.Activate("");
        
        if (_character != null)
        {
            DestroyElements();
            
            m_character = _character;
            
            Setup();
                        
            UpdateInfo();
        }
    }
    
    private void AddInfo(string _name, Func<string> _value, Action _onClick = null) { m_elements.Add(MACharacterInfoElement.Create(m_infoPanel, _name, _value, _onClick)); }
    private void AddCombat(string _name, Func<string> _value, Action _onClick = null) { m_elements.Add(MACharacterInfoElement.Create(m_combatPanel, _name, _value, _onClick)); }
    private void AddHistory(string _name, Func<string> _value, Action _onClick = null) { m_elements.Add(MACharacterInfoElement.Create(m_historyPanel, _name, _value, _onClick)); }
    
    private string GetAllocatedBuilding(BCBase _base)
    {
        if(_base == null)
            return "None";
        if(_base.Building) return _base.Building.GetBuildingTitle();
        return _base.Title;
    }
    private string GetAllocatedJob(BCBase _base)
    {
        if(_base == null)
            return "None";
        return _base.m_info.GetJobTitle();
    }
    
    private Action GetBuildingZoomAction(BCBase _base)
    {
        if(_base == null) return null;
        return () => { _base.Highlight(); this.ClickedClose(); };
    }
    
    public static string GetStateName(MACharacterBase _character)
    {
        string state = "None";
        if(_character is MAWorker)
        {
            state = _character.PeepAction.ToString();
        }
        else if(_character.CharacterUpdateState != null)
        {
            state = _character.CharacterUpdateState.State;
        }
        return Regex.Replace(state, "([A-Z])", " $1").Trim();
    }
    
    private void Setup()
    {
        string characterType = "";
        
        var gameState = m_character.CharacterGameState;

        bool isDog = m_character is MADog;

        if (isDog)
        {
            AddInfo("Type", () => /*characterType*/"Husky Dog");
            AddInfo("State", () => /*m_character.PeepAction.ToString()*/"Panting");
            AddInfo("Speed", () => m_character.SpeedDescription);
            AddHistory("Possessed Time", () => gameState.m_timePossessed.FormatTime());
            AddHistory("Distance Travelled", () => $"{gameState.m_distanceTravelled:F2}m");
            AddHistory("Items Found", () => gameState.m_itemsCollected.ToString());
        }
        else
        {
            AddInfo("Type", () => characterType);
            AddInfo("State", () => GetStateName(m_character));
            AddInfo("Level", () => m_character.GetCharacterLevelName());
            AddInfo("Speed", () => m_character.SpeedDescription);
            AddInfo("Experience", () => $"{m_character.Experience:N0}/{m_character.GetExperienceRequiredForNextLevel():N0}");
            AddInfo("Health", () => m_character.HealthDescription);
            if(MACharacterBase.c_useEnergy)
                AddInfo("Energy", () => $"{m_character.Energy:F1}/{m_character.MaxEnergy:F1}");

            AddInfo("Home", () => GetAllocatedBuilding(m_character.Home), GetBuildingZoomAction(m_character.Home));
            
            if(m_character is MAWorker)
                AddInfo("Job", () => GetAllocatedJob(m_character.Job), GetBuildingZoomAction(m_character.Job));
            
            AddCombat("Weapon", () => (gameState.m_weaponDesign != null && gameState.m_weaponDesign.HasDesign) ? gameState.m_weaponDesign.GetDominantMaterial() : "None");
            AddCombat("Armour", () => (gameState.m_armourDesign != null && gameState.m_armourDesign.HasDesign) ? gameState.m_armourDesign.GetDominantMaterial() : "None");
            AddCombat("Defense", () => $"{m_character.ArmourPoints:F0}/{m_character.MaxArmour:F0}");
            AddCombat("Attack", () => ((gameState.m_weaponDesign != null && gameState.m_weaponDesign.HasDesign) ? gameState.m_weaponDesign.m_attackScore : 0).ToString());
            
            if(m_character is MAHeroBase)
                AddCombat("Max Followers", () => (m_character as MAHeroBase).HeroGameState.m_maxTags.ToString());

            AddHistory("Kills", () => gameState.m_kills.ToString());
            AddHistory("Knocked Down", () => gameState.m_timesKnockedDown.ToString());

            // Only show if we hired this character
            if (gameState.m_dayHired >= 0)
                AddHistory("Hired", () => $"Day { gameState.m_dayHired }");
            AddHistory("Possessed Time", () => gameState.m_timePossessed.FormatTime());
            AddHistory("Freewill Time", () => gameState.m_timeUnpossessed.FormatTime());
        }
    }
    
    private void UpdateInfo()
    {
        bool canPossess = GameManager.Me.CanPossess(m_character);
        m_possessButton.SetActive(canPossess);
        var hero = m_character as MAHeroBase;
        m_levelupButton.SetActive(hero != null);
        if (hero)
        {
            m_newLevelup.enabled = hero.HeroGameState.m_characterExperience.CanLevelup();
        }
        if (m_character == null)
        {
            ClickedClose();
            return;
        }

        bool isDog = m_character is MADog;
        bool isQuestDog = isDog && m_character.Name == "QuestDog";

        // We should move this to either knack or have a virtual function in MACharacterBase
        bool canRecall = m_character is MAWorker && m_character is not MAQuestGiver && m_character is not MATourist;
        bool canRename = m_character is not MACreatureBase && m_character is not MAQuestGiver && m_character is not MATourist && !isQuestDog;
        
        var action = MABuildingWorkerPanelLine.GetWorkerActionButton(m_character);
        
        if(action.description.IsNullOrWhiteSpace() == false)
        {
            m_recallText.text = action.description;
        }
        m_recallButton.SetActive(action.description.IsNullOrWhiteSpace() == false);
        m_editNameButton.SetActive(canRename);
        m_combatPanel.gameObject.SetActive(!isDog);
        
        foreach(var field in m_elements)
        {
            field.UpdateValue();
        }
        
        m_title.text = isQuestDog ? "Old Bane" : m_character.Name;
        
        m_timer.Set(m_refreshTime);
    }
    
    public static MACharacterInfoUI Create(MACharacterBase _character)
    {
        if(_character == null) return null;
        
        if(s_infoShowing != null)
        {
            s_infoShowing.DestroyMe();
            s_infoShowing = null;
        }
        
        var go = Instantiate(NGManager.Me.m_MACharacterInfoGUIPrefab, NGManager.Me.NGInfoGUIHolder);
        var big = go.GetComponent<MACharacterInfoUI>();
        big.Activate(_character);
        s_infoShowing = big;
        return big;
    }
}