using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class MAAdvisorDialog : NGBaseInfoGUI//MAGUIBaseSingleton<MAAdvisorDialog>
{
    public Image m_advisorImage;
    public TMP_Text m_advisorName;
    public TMP_Text m_advisorTitle;
    public TMP_Text m_advisorExplainer;    
    public TMP_Text m_advisorMessage;
    
    void Activate(NGBusinessAdvisor _advisor, string _message)
    {
        base.Activate("");
        if (_advisor != null)
        {
            m_advisorName.text = $"{_advisor.m_firstName} {_advisor.m_givenName}";
            m_advisorTitle.text = _advisor.m_title;
            m_advisorExplainer.text = _advisor.m_info;
            m_advisorImage.sprite = _advisor.PortaitSprite;
        }
        if(_message.IsNullOrWhiteSpace() == false)
            m_advisorMessage.text = _message.ReplaceInputTokens();
        else
        {
            m_advisorMessage.text = "";
        }
    }
    
    public static MAAdvisorDialog Create(NGBusinessAdvisor _advisor, string _message)
    {
        if(s_infoShowing != null)
        {
            s_infoShowing.DestroyMe();
            s_infoShowing = null;
        }
        
        DecisionGUIHolder.Me?.ForceCollapse();
        var go = Instantiate(NGBusinessDecisionManager.Me.m_MAAdvisorDialogPrefab, NGManager.Me.NGInfoGUIHolder);
        var ad = go.GetComponent<MAAdvisorDialog>();
        ad.Activate(_advisor, _message);
        s_infoShowing = ad;
        return ad;
    }
}
