using UnityEngine;

public class MABanditMelee : MACreatureBase
{
	//[Header("MABanditMelee")]
	override public string HumanoidType => "BanditMelee";
	public override EDeathType DeathCountType => EDeathType.Enemy;

	[SerializeField]
	override protected string m_defaultWeaponStrikeType => c_weaponType_Club;
	[SerializeField]
	override protected string m_defaultWeaponDesign => m_banditWeaponDesign;
	protected override bool GetsDisabledBasedOnProximity => true;

	string m_banditWeaponDesign;
	
	protected override void Awake()
	{
		base.Awake();
	}

	public override void Activate(MACreatureInfo _creatureInfo, MABuilding _optionalOwner, bool _init, Vector3 _position, Vector3 _rotation, bool _addToCharacterLists = true)
	{
		base.Activate(_creatureInfo, _optionalOwner, _init, _position, _rotation, _addToCharacterLists);

		float r = Random.value;
		if (r >= 0.5f)
		{
			 m_banditWeaponDesign = "2|Weapon_Handle_B@|Weapon_Blade_Dagger_A@|1|1.1.0.0.0|";
		}
		else
		{
			m_banditWeaponDesign = "3|Weapon_Pummel_C@|Weapon_Handle_A@|Weapon_Blade_Sword_A@|2|1.1.0.0.0|2.1.1.0.0|";
		}
		/*else
		{
			m_banditWeaponDesign = "2|Weapon_Handle_B@|MA_Bread_LongLoaf@|1|1.2.0.0.310|";
		}*/
	}

	public override void DestroyedTarget()
	{
		base.DestroyedTarget();
	}

	public override void DoPossessedAction()
	{
		
	}
}
