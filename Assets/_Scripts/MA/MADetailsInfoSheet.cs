using TMPro;
using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

public class MADetailsInfoSheet : MAGUIBase
{
    public TMP_Text m_title;
    public TMP_Text m_description;
    public ScrollRect m_scrollRect;
    public Transform m_lineHolder;
    private NGDesignInterface.DesignScoreInterface m_designInterface;
    public MABuilding m_focusedBuilding;
    public Block m_draggingBlock;
    public float m_nextRefreshTime = 0;
    
    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        
    }
    
    public void Update()
    {
        RefreshView();
    }
    
    public void RefreshView()
    {
        var draggingBlock = DesignTableManager.Me.GrabbedBlock?.GetComponent<Block>();
        bool dsiChanged = m_designInterface != DesignTableManager.Me.DesignInterface; 
        bool focusedBuildingChanged = m_focusedBuilding != DesignTableManager.Me.m_designGloballyFocusBuilding;
        bool blockChanged = draggingBlock != m_draggingBlock;
        
        m_designInterface = DesignTableManager.Me.DesignInterface;
        m_focusedBuilding = DesignTableManager.Me.m_designGloballyFocusBuilding as MABuilding;
        m_draggingBlock = draggingBlock;
        
        if(dsiChanged || focusedBuildingChanged || blockChanged)// || m_nextRefreshTime < Time.time)
        {
            DesignUIManager.Me.m_detailsPanelText.text = "Details";
            m_nextRefreshTime = Time.time + 1f;
            m_description.gameObject.SetActive(false);
            m_lineHolder.DestroyChildren();
            
            if(m_draggingBlock != null)
            {
                RefreshDraggingPart();
            }
            else
            {
                RefreshDesignDetails();
            }
        }
    }
    
    public void RefreshDesignDetails()
    {
        if(m_designInterface.IsProduct)
        {
            m_title.text = MADesignPriceSheet.GetProductString(m_designInterface);
            
            var totalCost = m_designInterface.TotalCost();
            var profit = m_designInterface.GetProfit();
            string materialsRequired = "";
            foreach(var mat in m_designInterface.MaterialsRequired)
            {
                materialsRequired += $"{mat.Value:F0}x<size=150%>{mat.Key.TextSprite}</size> ";
            }
            materialsRequired.TrimEnd();
            
            MADesignInfoSheetLine.Create(m_lineHolder, "<b>Parts</b>", m_designInterface.Parts.Count.ToString(), true);
            MADesignInfoSheetLine.Create(m_lineHolder, "<b>Resource Required</b>", materialsRequired, true);
            MADesignInfoSheetLine.Create(m_lineHolder, $"<b>{BlockInfoPanelV2.c_costToManufacture}</b>", $"{GlobalData.CurrencySymbol}{totalCost:F2}", true);
            MADesignInfoSheetLine.Create(m_lineHolder, "<b>Sells For</b>", $"{GlobalData.CurrencySymbol}{m_designInterface.SellingPrice:F2}", true);
            MADesignInfoSheetLine.Create(m_lineHolder, "<b>Profit</b>", $"<color={(profit >= 0 ? MAGUIBase.GreenColor : MAGUIBase.RedColor)}>{GlobalData.CurrencySymbol}{profit:F2}", true);
        }
        else if(m_focusedBuilding != null)
        {
            m_title.text = m_focusedBuilding.GetBuildingTitle();
            List<MAComponentInfo> missing = new();
            
            var status = $"<color={MAGUIBase.GreenColor}>Functional</color>";
            if(m_focusedBuilding.ActionComponents.Count == 0)
            {
                status = $"<color={MAGUIBase.RedColor}>Requires Action Block</color>";
                DesignUIManager.Me.m_detailsPanelText.text = MAMessageManager.GetTMPString("Warning") + " Details";
            }
            else
            {
                bool hasEntrance = false;
                foreach(var c in m_focusedBuilding.m_components)
                {
                    hasEntrance |= c is BCEntrance;
                    c.GetMissingComponents(missing);
                }
                if(missing.Count > 0)
                {
                    status = "<color=red>Missing Blocks</color>";
                    DesignUIManager.Me.m_detailsPanelText.text = MAMessageManager.GetTMPString("Warning") + " Details";
                }
                else if(hasEntrance)
                {
                    var nav = m_focusedBuilding.GetComponent<BuildingNav>(); 
                    if(nav != null && nav.HasDoor == false)
                    {
                        status = "<color=red>Inaccessible Entrance</color>";
                        DesignUIManager.Me.m_detailsPanelText.text = MAMessageManager.GetTMPString("Warning") + " Details";
                    }
                }
            }
            
            var actions = "";
            foreach(var list in m_focusedBuilding.m_componentsDict.Values)
            {
                if(list.Count == 0) continue;
                var first = list[0];
                if(first.m_info.m_showInInfo == false || first as BCActionBase == null) continue;
                
                if(actions.IsNullOrWhiteSpace() == false)
                    actions += ", "; 
                actions += $"{list.Count}x{first.Title}";
            }
            
            MADesignInfoSheetLine.Create(m_lineHolder, "<b>Actions<b>", actions, true);
            MADesignInfoSheetLine.Create(m_lineHolder, "<b>Status</b>", status, true);
            
            if(missing.Count > 0)
            {
                var missingStatus = "";
                foreach(var m in missing)
                {
                    if(missingStatus.IsNullOrWhiteSpace() == false)
                        missingStatus += ", ";
                    missingStatus += m.m_title;
                }
                MADesignInfoSheetLine.Create(m_lineHolder, "<b>Missing</b>", missingStatus, true);
            }
        }
        LayoutRebuilder.ForceRebuildLayoutImmediate(m_lineHolder as RectTransform);
    }
    
    public void RefreshDraggingPart()
    {
        var blockInfo = m_draggingBlock.BlockInfo;
        m_title.text = m_draggingBlock.BlockInfo.m_displayName;
        
        m_description.gameObject.SetActive(true);
        m_description.text = blockInfo.m_description;
        
        if(m_designInterface.IsProduct)
        {
            var scoreType = MADesignGuage.GetGaugeType(m_designInterface.ProductLineName);
            (string,string) scoreValue = ("","");
            switch(scoreType)
            {
                case MADesignGuage.GaugeType.Attack: scoreValue = blockInfo.GetAttackDetails(); break;
                case MADesignGuage.GaugeType.Defense: scoreValue = blockInfo.GetDefenseDetails(); break;
                case MADesignGuage.GaugeType.Nutrition: scoreValue = blockInfo.GetNurtitionalDetails(); break;
                default: scoreValue = blockInfo.GetAsetheticDetails(); break;
                                
            }
           
            MADesignInfoSheetLine.Create(m_lineHolder, $"<b>{scoreValue.Item1}</b>", scoreValue.Item2, true);
            MADesignInfoSheetLine.Create(m_lineHolder, "<b>Adds To Selling Price</b>", $"{GlobalData.CurrencySymbol}{blockInfo.SellingPrice:F2}", true);
        }
        else
        {
            var function = BlockLabel.GetFunctionString(gameObject, blockInfo, m_designInterface);
            var cmps = blockInfo.GetComponentInfos();
            foreach(var c in cmps)
            {
                if(c.m_showInInfo == false) continue;
                if(function.IsNullOrWhiteSpace() == false) function += ", ";
                function += c.m_title;
            }
            MADesignInfoSheetLine.Create(m_lineHolder, "<b>Function</b>", function, true);
        }
        
        
        MADesignInfoSheetLine.Create(m_lineHolder, $"<b>{BlockInfoPanelV2.c_costToManufacture}</b>", $"{GlobalData.CurrencySymbol}{m_designInterface.GetPriceOfBlock(blockInfo):F2}", true);
        MADesignInfoSheetLine.Create(m_lineHolder, "<b>Times Used In Design Before Penalty</b>", blockInfo.m_usageCap.ToString(), true);
        
        LayoutRebuilder.ForceRebuildLayoutImmediate(m_lineHolder as RectTransform);
        LayoutRebuilder.ForceRebuildLayoutImmediate(transform as RectTransform);
    }

    public void Activate()
    {
        base.Activate();
        RefreshView();
    }
    
    public static MADetailsInfoSheet s_current;
    public static MADetailsInfoSheet Create(Transform _holder)
    {
        var prefab = Resources.Load<MADetailsInfoSheet>("_Prefabs/Dialogs/MADetailsInfoSheet");
        s_current = Instantiate(prefab, _holder);
        s_current.Activate();
        return s_current;
    }
}
