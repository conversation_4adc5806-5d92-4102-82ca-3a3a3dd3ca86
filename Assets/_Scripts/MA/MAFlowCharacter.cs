#if UNITY_EDITOR
using UnityEditor;
#endif
using System;
using MACharacterStates;
using UnityEngine;

public class MA<PERSON><PERSON><PERSON>haracter : MATourist, ICharacterObjectInteract
{
    public MAParserSection m_session;
    
    public MABuilding m_optionalInteractionBuilding = null;
    
    private MAQuestScroll QuestScroll
    {
        get
        {
            var scroll = GetComponentInChildren<MAQuestScroll>(); 
            return scroll == null ? null : scroll;
        }
    }
    
    protected override GameState_Person CreateNewGameState() { return new GameState_FlowCharacter(); }
    public string InteractType => null;
    public bool CanInteract(NGMovingObject _chr) => QuestScroll?.CanInteract(_chr) ?? false;
    public string GetInteractLabel(NGMovingObject _chr) => MAQuestScroll.InteractLabel;
    public void DoInteract(NGMovingObject _chr) => QuestScroll?.DoInteract(_chr);
    public bool RunInteractSequence(System.Collections.Generic.List<FollowChild> _chr) => false;
    public void EnableInteractionTriggers(bool _b) {}
    public float AutoInteractTime => 0;
    
    public override bool CanPickup() { return GameState.m_canPickupOverride ? GameState.m_canPickupValue : false; }

    public override string GetDefaultDisplayName()
    {
        return m_workerInfo?.m_displayName ?? "";
    }


    protected override void Awake()
    {
        m_initialWorkerState = STATE.MA_DECIDE_WHAT_TO_DO;
        base.Awake();
    }

    override protected void InitState()
    {
        SetState(STATE.IDLE);
        if (m_currentDecision != null)
        {
            switch(m_currentDecision.m_decisionType)
            {
                case Decisions.DecisionType.VisitBuilding:
                    SetState(STATE.MA_DECIDE_WHAT_TO_DO);
                    break;
                case Decisions.DecisionType.VisitDecoration:
                    SetState(STATE.MA_DECIDE_WHAT_TO_DO);
                    break;
            }
        }
    }
    override protected void UpdateState()
    {
        var threatNearby = QuestCharacterCheckForCreatures();
        
        switch (m_state)
        {
            case STATE.MA_LEAVING:
                break;
            case STATE.MA_DECIDE_WHAT_TO_DO:
                break;
            case STATE.MA_WAITING_FOR_ANIMATION:
                break;
            default:
                base.UpdateState();
                break;
        }
    }

    override public void MASetAsPetrified(MACharacterBase _attacker, bool _dropCarried = true, bool _rotateToThreat = true)
    {
        base.MASetAsPetrified(_attacker, false, false);
    }

    protected override void StatePetrified()
    {
        QuestCharacterPetrified();
    }
    
    protected void ActivateFlowCharacter(MAParserSection _session)
    {
        m_session = _session;
        InitState();
    }

    public override void Activate(MACreatureInfo _creatureInfo, MABuilding _optionalOwner, bool _init, Vector3 _position, Vector3 _rotation,
        bool _addToCharacterLists = true)
    {
        base.Activate(_creatureInfo, _optionalOwner, _init, _position, _rotation, _addToCharacterLists);
        IsQuestCharacter = true;
    }

    protected override void OnDestroy()
    {
        base.OnDestroy();
    }
    
    public override float GetDesiredSpeed()
    {
        float speedFactor = PeepAction == PeepActions.Flee || PeepAction == PeepActions.DespawnRun ? m_runMultiplier : 1f;
        return m_gameState.m_walkSpeed * speedFactor;
    }

    public void SectionMoveToPosition(Vector3 _pos)
    {
        SetMoveToPosition(_pos);
    }
    public void SectionMoveToBuilding(MABuilding _building)
    {
        SetMoveToBuilding(_building);
    }
    
    public static MAFlowCharacter FindCharacter(string _name)
    {
        var foundWorker = NGManager.Me.m_MAHumanList.Find(x => x is MAFlowCharacter flowChar && flowChar.Name == _name);
        
        if(foundWorker == null)
        {
            foundWorker = NGManager.Me.m_MAHumanList.Find(x => x is MAFlowCharacter flowChar && flowChar.GameStatePerson.m_workerInfo == _name);
        }
      
        return foundWorker as MAFlowCharacter;
    }
    
    public static MAFlowCharacter Create(string _name, MAWorkerInfo _workerInfo, Vector3 _pos, MAParserSection _session, Quaternion? _rot = null)
    {
        var flowCharacter = MAWorker.Create(_workerInfo, _pos, true, _rot) as MAFlowCharacter;
        _pos = _pos.GroundPosition();
        flowCharacter.ActivateFlowCharacter(_session);
        flowCharacter.Name = _name;
        return flowCharacter;
    }

    public static MAFlowCharacter Load(GameState_FlowCharacter _stateData)
    {
        var flowCharacter = MAWorker.Load(_stateData) as MAFlowCharacter;
        flowCharacter.m_leavePos = _stateData.m_leavePos;
        return flowCharacter;
    }
    
    protected override bool WorkplaceHasStockSpace()
    {
        if (m_optionalInteractionBuilding != null)
        {
            return BuildingHasStockSpaceForDestinationObject(m_optionalInteractionBuilding);
        }
        return base.WorkplaceHasStockSpace();
    }

    public override MABuilding GetResourceDestination() => base.GetResourceDestination() ?? m_optionalInteractionBuilding;
    
}


#if UNITY_EDITOR
[CustomEditor(typeof(MAFlowCharacter))]
public class MAFlowCharacterEditor : MATouristEditor
{
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();
    }
}
#endif