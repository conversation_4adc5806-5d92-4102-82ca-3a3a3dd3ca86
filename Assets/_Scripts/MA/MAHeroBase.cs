using System;
using System.Collections.Generic;
using UnityEngine;
using Random = UnityEngine.Random;

#if UNITY_EDITOR
using System.Linq;
using UnityEditor;
#endif

public class MAHeroBase : MACharacterBase, IDamager
{
	public override Dictionary<string, Func<MACharacterBase, CharacterBaseState>> StateLibrary() { return MAHeroStateLibrary.StateLibrary; }
	public override EDeathType DeathCountType => EDeathType.Hero;
	
	private TerrainSectionMesh m_area = null;
	private MeshRenderer m_areaMesh = null;
	private CharacterPickupBehaviour m_pickupBehaviour = null;
	private float m_firstShowInteractionTime = 0;

	private string m_cachedOverrideRuneVFXStr = null;
	private bool m_cachedBranchingComboStatus = false;
	
	public override bool IsActivelyHealing
	{
		get
		{
			if(BCGuildHealing.RequiresHealing(this) == false)
				return false;
				
        	MABuilding buildingInside = m_insideMABuilding as MABuilding;
            if (buildingInside == null || Home == null || Home.Building != buildingInside)
				return false;
            
            return true;
		}
	}
		
	/******** non-override properties *******/
	public GameState_Hero HeroGameState { get { return m_gameState as GameState_Hero; } }
	
	public override float GetBezierDistanceOverride(SpecialHandlingAction _action)
	{
		if(_action == SpecialHandlingAction.AssignHome) return -1;
		if(IsUnconscious() == Consciousness.Conscious)
			return 15f;
		return 10f;
	}
	override public string HumanoidType => IsMale ? "HeroMale" : "HeroFemale";
	public bool IsMale => true; // TODO - fill this in
	
	override protected string m_defaultWeaponStrikeType => c_weaponType_Axe;
	override protected string m_defaultWeaponDesign => "3|Weapon_Handle_B@|Weapon_WoodGrip_B@|Weapon_AxeHead_D@|2|*******.0|*******.0|";

	public override float Experience => m_gameState.m_characterExperience.m_experience;
	public override int Level => m_gameState.m_characterExperience.m_level;
	public override float ExperienceMultiplier => m_gameState.m_characterExperience.m_experienceMultiplier;

	public override float HealthBoostPerLevel => 0.15f;
	public override float DamageBoostPerLevel => 0.15f;

	public override bool HasBlockAbility => true;

	// public override void AddExperienceOld(float _xpToAdd, string _reason)
	// {
	// 	float levelBefore = Level;
	// 	m_gameState.m_characterExperience.AddExperience(_xpToAdd, _reason);
	// 	float levelNow = Level;       
	// 	if (levelBefore < levelNow)
	// 	{
	// 		OnLevelUp();
	// 	}
	// }
	
	public override bool HasReachedMaxExperience()
	{
		var levels = MAHeroLevelInfo.GetInfoList(CreatureInfo.m_name);
		return CharacterGameState.CharacterLevel >= levels.Count-1;
	}
	
	public override float GetDesiredSpeed()
	{
		var state = IsUnconscious();
		var speed = base.GetDesiredSpeed();
		if(state == Consciousness.Conscious)
			return speed;
		return speed * 0.7f;
	}
	
	public override void AddExperience(float _xpToAdd, string _reason)
	{
		if(_xpToAdd <= 0f) return;
		var xpToAdd = _xpToAdd*m_gameState.m_characterExperience.m_experienceMultiplier;
		//m_gameState.m_characterExperience.AddExperience(_xpToAdd, _reason);
		m_gameState.m_characterExperience.LogExperience(xpToAdd, _reason);
		var levels = MAHeroLevelInfo.GetInfoList(CreatureInfo.m_name);
		var newExp = m_gameState.m_characterExperience.m_experience + xpToAdd;
		var currentlevel = m_gameState.m_characterExperience.m_level;
		int newLevel = 0;
		for (int i = levels.Count - 1; i >= 0; i--)
		{
			if (newExp >= levels[i].m_experienceRequired)
			{
				newLevel = levels[i].m_level;
				break;
			}
		}
		CheckLevelUp();
		m_gameState.m_characterExperience.m_experience = newExp;
		if (newLevel != currentlevel)
		{
			m_gameState.m_characterExperience.m_level = newLevel;
		}
	}

	protected void CheckLevelUp()
	{
		if (m_specialInteraction == null && HeroGameState.m_characterExperience.CanLevelup())
		{
			MAHeroLevelupGUI.CreateHeroLevelupIcon(this);
			m_specialInteraction = () =>
			{
				if (GameManager.Me.IsPossessed(this)) return;
				MAHeroLevelupGUI.Create(this);
				if (HeroGameState.m_characterExperience.CanLevelup() == false)
				{
					m_specialInteraction = null;
					MAHeroLevelupGUI.DestroyHeroLevelupIcon(this);
				}
			};
		}
	}
	protected override void SetHumanoidType()
	{
		AudioClipManager.Me.SetHumanoidType(this, HumanoidType, Level);
	}

	public override string GetFallbackInteractionLabel()
	{
		var creatures = PlayerHandManager.GetNearestObjects(false, true, false, false, false, false, false, 1, 4f, transform.position, _direction: transform.forward);
		if (creatures.Count == 1)
		{
			if (m_firstShowInteractionTime == 0)
				m_firstShowInteractionTime = Time.time;
			const float c_showAttackInteractionForSeconds = 3;
			if (Time.time - m_firstShowInteractionTime < c_showAttackInteractionForSeconds)
			return "Attack";
		}
		return null;
	}

	override public float MaxHealth
	{
		get => m_creatureInfo.m_health * HeroGameState.m_healthMultiplier;
	}

	public override bool ShowHealthBar
	{
		get
		{
			if(m_state == STATE.MA_MOVE_TO_INSIDE_BUILDING || m_state == STATE.MA_MOVE_TO_OUTSIDE_BUILDING)
				return false; 
			return NGManager.Me.m_alwaysShowHeroHealthBars || base.ShowHealthBar || Health < MaxHealth
	                                      || ((Armour != null) && (ArmourPoints < MaxArmour));
		}
	}

	public override bool IsAllowedToGuardOutsideDistrict => false; // TODO - fill this in

	/******** Unity *******/
	protected override void Awake()
	{
		base.Awake();
		m_pickupBehaviour = GetComponent<CharacterPickupBehaviour>();
	}
	
	protected override void Update()
	{
		base.Update();

		if (m_cachedBranchingComboStatus != GameManager.BranchingCombosEnabled)
		{
			MAAttackCombo comboToUse = GameManager.BranchingCombosEnabled ? MAAttackCombo.GetByID("HeroBranchingCombo") : m_creatureInfo.m_attackComboObj;
			m_meleeComboState = new MAAttackComboState(this, comboToUse);
			s_overrideRuneVFX = GameManager.BranchingCombosEnabled  ? "Fire Rune;Lightning Rune" : null;
			m_cachedBranchingComboStatus = GameManager.BranchingCombosEnabled;
		}
		if (m_cachedOverrideRuneVFXStr != s_overrideRuneVFX)
		{
			UpdateWeapon();
			SetupRuneComboState();
			m_cachedOverrideRuneVFXStr = s_overrideRuneVFX;
		}
		
		if (IsHeld && TryGetGuardArea(out var guardArea))
		{
			if (m_area == null) m_area = MAAreaSelectionCircle.DrawTerrainArea(guardArea.pos, guardArea.radius);
			else m_area.SetSize(guardArea.radius * 2 * Vector3.one).SetPosition(guardArea.pos);
		}
		else if (m_area != null)
		{
			Destroy(m_area.gameObject);
			m_area = null;
		}
		CheckLevelUp();
	}
	
	/******** Init *******/
	protected override void ApplyInitialCharacterState()
	{	
		MACharacterStateFactory.ApplyInitialState(HeroGameState.m_savedUpdateState, this);
	}
		
	public override MAWorkerInfo.WorkerGender Gender
	{
		get
		{
			var gsHero = GameState as GameState_Hero;
			if(gsHero == null || gsHero.m_isMale)
				return MAWorkerInfo.WorkerGender.Male;
			return MAWorkerInfo.WorkerGender.Female;
		}
	}

	protected override void InitialiseGameState()
	{
		base.InitialiseGameState();

		SetGameStateSaveData(new GameState_Hero());
		
		GameState_Hero heroGameState = m_gameState as GameState_Hero;
		GameManager.Me.m_state.m_heroes.Add(heroGameState);
		
		Transform tr = transform;
		Vector3 position = tr.position;
		Quaternion rotation = tr.rotation;

		heroGameState.m_creatureInfoName = m_creatureInfo.m_name;
		heroGameState.m_id = m_ID;
		heroGameState.m_type = GetType().Name;
		heroGameState.m_bodyType = FindBodyType();
		heroGameState.m_isMale = m_creatureInfo.IsMale;
		//heroGameState.m_subtype = FindNewSubType().ToString();
		heroGameState.m_savedUpdateState = InitialState;
		heroGameState.m_pos = position;
		heroGameState.m_rotation = rotation.eulerAngles;
		heroGameState.m_targetPos = position;
		heroGameState.m_aliveDuration = 0f;
		heroGameState.m_health = m_creatureInfo.m_health;
		heroGameState.m_walkSpeed = m_creatureInfo.GetNewWalkingSpeed();
		heroGameState.m_attackSpeed = m_creatureInfo.GetNewAttackSpeed();
		heroGameState.m_spawnPos = position;
		heroGameState.m_characterExperience = new CharacterExperience();
		heroGameState.m_characterExperience.m_level = m_creatureInfo.m_initialLevel;
	}
	
	public override void SetTargetObj(TargetResult _obj)
	{
		base.SetTargetObj(_obj);
	}
	
	protected override void GetInteractionPoint(TargetResult _bestTarget)
	{
		if ((IsRoamingFreely || (IsOnPatrol && IsWithinGuardRadius()))
		    && NormalizedHealth < CharacterSettings.m_healAtHomeHealthThreshold &&
		    IsReadyToHealAtHome() == false)
		{
			var magicLog = SpawnMagicLogAndInteract();
			if (magicLog != null)
			{
				var dist = GetDistanceScore(transform.position, magicLog.transform.position);
				if(dist.Item1 > _bestTarget.m_distanceScore)
					_bestTarget.Set(dist, magicLog, TargetObject.TargetType.InteractionPointType);
			}
		}
		
		if (Random.Range(0f, 1) < m_gameState.m_wantsToPlay)
		{
			Vector3 pos = transform.position;

			if (Random.value > 0.5f)
			{
				float toHomeRand = Random.value;
				float toHomeTime = 0f;
				float toHome = 0f;
				Transform homeTransform = Home == null ? null : Home.transform;
				
				if(Home && Home.Building)
				{
					homeTransform = Home.Building.transform;
				}
				
				if (homeTransform)
				{
					toHome = toHomeRand;
					toHomeTime = GetTravelTime(homeTransform.position);
					toHome += 1 - Mathf.Clamp01(toHomeTime / 45f);
					toHome /= 2f;
				}

				List<MABuilding> taverns = MABuilding.FindBuildingsByName("tavern");

				float dist = Single.MaxValue;

				MABuilding closestTavern = null;
				foreach (var tavern in taverns)
				{
					float newDist = (pos - tavern.transform.position).xzMagnitude();
					if (newDist < dist)
					{
						dist = newDist;
						closestTavern = tavern;
					}
				}

				float toTavern = 0;
				if (closestTavern != null)
				{
					toTavern = 1 - toHomeRand;
					float toTavernTime = GetTravelTime(closestTavern.transform.position);
					toTavern += 1 - Mathf.Clamp01(toTavernTime / 45f);
					toTavern /= 2f;
				}

				if (toHome > toTavern)
				{
					if (homeTransform != null)
					{
						var distScore = GetDistanceScore(transform.position, homeTransform.position);
						if(distScore.Item1 > _bestTarget.m_distanceScore)
						{
							_bestTarget.Set(distScore, homeTransform, TargetObject.TargetType.InteractionPointType);
						}
					}
				}
				else if (closestTavern != null)
				{
					var distScore = GetDistanceScore(transform.position, closestTavern.transform.position);
					if(distScore.Item1 > _bestTarget.m_distanceScore)
					{
						_bestTarget.Set(distScore, closestTavern.transform, TargetObject.TargetType.InteractionPointType);
					}
				}
			}
			else
			{
				bool isGuarding = ObjectiveWaypoint != null && CharacterGameState.m_guardObjectiveWaypoint;
#if UNITY_EDITOR
				List<MACharacterInteract> m_interactionPoints = new();
#endif
				foreach (var s in MACharacterInteract.SaveData)
				{
					CheckInteract(_bestTarget, s.m_interact, isGuarding);
					
#if UNITY_EDITOR
					if (s.m_interact != null && m_interactionPoints.Contains(s.m_interact))
						Debug.LogError($"Duplicate interaction point found! '{s.m_interact.name}'");
#endif
				}

				foreach (var animal in NGManager.Me.m_MAAnimalList)
				{
					var interact = animal.GetComponent<MACharacterInteract>();
					CheckInteract(_bestTarget, interact, isGuarding);
#if UNITY_EDITOR
					if (interact != null && m_interactionPoints.Find(x => x.m_saveData == interact.m_saveData))
						Debug.LogError($"Duplicate interaction point found! '{interact.name}'");
#endif
				}

				Transform magicLogInteract = SpawnMagicLogAndInteract();
				MACharacterInteract characterInteract = magicLogInteract?.GetComponent<MACharacterInteract>();
				CheckInteract(_bestTarget, characterInteract, isGuarding);
			}
		}
	}
	
	private bool CheckInteract(TargetResult _bestTarget, MACharacterInteract _interact, bool _isGuarding)
	{
		if(_interact == null) return false;
		if(_interact.HasNewSpaceForCharacter(this) == false) return false;
		if (_isGuarding && IsWithinGuardRadius(_interact.transform.position, m_bodyToBodyCollider.radius) == false) return false;
		if(_interact.UsedRecently(this)) return false;
		
		var dist = GetDistanceScore(ObjectiveWaypoint ?? transform.position, _interact.transform.position);
		dist.Item1 *= m_gameState.m_wantsToPlay;
		
		if(dist.Item1 > _bestTarget.m_distanceScore)
		{
			_bestTarget.Set(dist, _interact.transform, TargetObject.TargetType.InteractionPointType);
			return true;
		}
		return false;
	}
	
	private Transform SpawnMagicLogAndInteract()
	{
		float innerRadius = m_bodyToBodyCollider.radius;
		float outerRadius = Mathf.Clamp(GuardRadius, 0, CreatureInfo.m_visionRadius);
		outerRadius = outerRadius - innerRadius <= 0f ? CreatureInfo.m_visionRadius * 0.5f : outerRadius;
		Vector3 referencePos = ObjectiveWaypoint == null ? transform.position : ObjectiveWaypoint.Value;
		Vector3? newPos = ScanForNavigableRandomPos(referencePos, innerRadius, outerRadius, false);
		if (newPos != null)
		{
			Vector3 restLogPosition = ((Vector3)newPos).GroundPosition();
			NGDecoration sittingLogPrefab = Resources.Load<NGDecoration>("_Art/Decorations/MA_Decorations/MA_HeroLog");
			var sittingLogInstance = Instantiate(sittingLogPrefab, NGDecorationInfoManager.Me.m_decorationHolder);
			Transform sittingLogTr = sittingLogInstance.transform;
			sittingLogTr.position = restLogPosition;
			sittingLogTr.localEulerAngles = Random.Range(0, 360) * Vector3.up;
			sittingLogTr.localScale = Vector3.one;
				
			return sittingLogTr;
		}

		return null;
	}

	public override bool IsTargetAliveAndValid(TargetObject _target)
	{
		if (_target == null)
			return false;
		
		GameObject targetGo = _target.gameObject;
		bool isActive = targetGo.activeInHierarchy;
		
		switch (_target.TargetObjectType)
		{
			case TargetObject.TargetType.CharacterType:					
				bool isValid = isActive
				               && _target.GetHealth() > 0
				               && _target.IsInState(STATE.MA_DEAD) == false
				               && _target.IsInState(STATE.HELD_BY_PLAYER) == false
				               && _target.IsInAllowedTargetState
				               && _target.IsAnyDeadState == false;
				if (isValid && IsPossessed == false && _target.IsQuestGiver) // _target.Name is "The Lost Boy" or "Garrick") allow questgiver targeting only when possessed! Also see knack entry for valid targets
				{
					isValid = false;
				}
				return isValid;
		}
		return false;
	}

	public override bool HitByCart(Collision _collision)
	{
		if (base.HitByCart(_collision))
		{
			// m_lastHitTime = Time.time;
			if (!InState(CharacterStates.KnockedDown))
				MACharacterStateFactory.ApplyCharacterState(CharacterStates.KnockedDown, this);
			
			return true;
		}

		return false;
	}
	
	public (int level,string name) GetLevelInfo()
	{
		var heroLU = MAHeroLevelInfo.GetInfoList(m_creatureInfo.m_name);
		if (heroLU == null)
		{
			Debug.LogError($"No hero level info found for {m_creatureInfo.m_name}");
			return (-1,"Error");
		}
		var level = Mathf.Min(HeroGameState.m_characterExperience.m_level, heroLU.Count-1);
		return (level,heroLU[level].m_levelName);
	}
	
	public override string GetCharacterLevelName()
	{
		var info = GetLevelInfo();
		if(info.level < 0) return info.name;
		return $"{info.level} - {info.name}";
	}
	public override float GetExperienceRequiredForNextLevel()
	{
		return ExperienceRequiredForLevel(HeroGameState.m_characterExperience.m_level+1);
	}
	public override float ExperienceRequiredForLevel(int _level)
	{
		var heroLU = MAHeroLevelInfo.GetInfoList(m_creatureInfo.m_name);
		if (heroLU == null)
		{
			Debug.LogError($"No hero level info found for {m_creatureInfo.m_name}");
			return 0;
		}
		if(_level >= heroLU.Count)
		{
			Debug.LogError($"Level {_level} is out of range of {heroLU.Count} for {m_creatureInfo.m_name}");
		}
		_level = Mathf.Min(_level, heroLU.Count-1);
		return heroLU[_level].m_experienceRequired;

	}
	override public bool SetupHeldByPlayer(NGCommanderBase _originator)
	{
		CharacterUpdateState.ApplyState(CharacterStates.HeldByPlayer);
		return true;
	}
	
	override public void FinallyDroppedByPlayer(NGCommanderBase _destination, SpecialHandlingAction _restrictedAction)
	{
		if(RagdollHelper.WillThrowObject(GetComponent<Pickup>().SmoothedDragDelta))
		{
			m_ragdollController.PauseNextStateChange = true;
		}
		
		var consciousState = IsUnconscious();
		if(Home != null && Home.Building == _destination && _restrictedAction == SpecialHandlingAction.GuildHealing)
			consciousState = Consciousness.Conscious;
			
		switch (consciousState)
		{
			case Consciousness.Dead:
				CharacterUpdateState.ApplyState(CharacterStates.Dead);
				break;
			case Consciousness.Unconscious:
				CharacterUpdateState.ApplyState(CharacterStates.Unconscious);
				break;
			case Consciousness.UnconsciousDead:
				CharacterUpdateState.ApplyState(CharacterStates.UnconsciousDead);
				break;
			default:
				// m_ragdollController.PauseNextStateChange = false;
				CharacterUpdateState.ApplyState(CharacterStates.StandUp);
				if (_destination != null && _destination.ApplySpecialDropHandling(this, _restrictedAction))
				{
					// When the Hero is first puchased and thrown by the player
					// ApplySpecialDropHandling sets it to GoingHome state, bypassing the StandUp state and breaking the flow.
					// This check prevents the hero from being stuck without passing through StandUp
					if (ScheduledState.State != CharacterStates.StandUp)
						m_ragdollController.PauseNextStateChange = false;
				}
				break;
		}
	}

	override public void SetupPossessed(bool _possessedOn)
	{
		m_firstShowInteractionTime = 0;
		base.SetupPossessed(_possessedOn);
	}

	public override float GetDamageMultiplier()
	{
		float weaponDesignDamageMult = 0;
		if (Weapon != null)
		{
			// RW-14-MAY-25: Doing the different multipliers (experience level, weapon design) 
			// additively, so if the multiplier is 0.8, I need to use -0.2 for my calculation.
			weaponDesignDamageMult = Weapon.DamageMultiplier-1f;
		}

		return base.GetDamageMultiplier() + weaponDesignDamageMult;
	}

	public override void Activate(MACreatureInfo _heroInfo, MABuilding _optionalOwner, bool _init, Vector3 _position, Vector3 _rotation, bool _addToCharacterLists = true)
	{
		base.Activate(_heroInfo, _optionalOwner, _init, _position, _rotation, _addToCharacterLists);
		
		if (_init)
		{
		}
		if(_addToCharacterLists)
			NGManager.Me.AddHero(this);
	}

	override public void DestroyMe()
	{
		HeroGameState.m_aliveDuration = -1f;

		base.DestroyMe();

		GameManager.Me.m_state.m_heroes.Remove(HeroGameState);
		NGManager.Me.RemoveHero(this);
	}

	protected override void OnDestroy()
	{
		if (GameManager.Me == null) return; // Shutting down
		
		base.OnDestroy();
		
		if (m_area != null)
		{
			Destroy(m_area.gameObject);
			m_area = null;
		}

		if (NGManager.Me != null)
		{
			NGManager.Me.RemoveHero(this);
		}
		
		GameManager.Me.m_state.m_heroes.Remove(HeroGameState);
	}
}

#if UNITY_EDITOR
[CustomEditor(typeof(MAHeroBase))]
public class MAHeroBaseEditor : MACharacterBaseEditor
{
	public override void OnInspectorGUI()
	{
		base.OnInspectorGUI();
	}
}
#endif