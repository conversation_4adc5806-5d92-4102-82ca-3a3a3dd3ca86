using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

    public class MAMessage
    {
        private static MAMessage s_showingMessage;
        private static bool s_isPlayingAudio;
        
        private NGTutorialMessageBase m_showingMessage;
        public const string PoseLocation = "_Art/Characters/Advisors/";
        public MAMessageType m_type;
        public string m_message;
        public string m_audioID;
        public string m_pose;
        public Sprite m_poseSprite = null;
        public string m_advisor;
        private float m_audioRepeatTime;
        private float m_lastAudioPlayedTime = -1;
        public bool m_holdOnScreen;
        private int m_lastPlayedTime;
        private int m_frameCountDisplayed = 0;
        private bool m_hasFinished = false;
        
        private static void DestroyShowingMessage()
        {
            s_isPlayingAudio = false;
            if(s_showingMessage == null)
                return;
            
            s_showingMessage.m_hasFinished = true;
                
            if(s_showingMessage.m_holdOnScreen)
                return;

            MAQuestManager.Me.OnDestroyMessage(s_showingMessage);
            LipSyncManager.Me.OnDestroyMessage(s_showingMessage);
            
            s_showingMessage?.Destroy();
        }
        
        public void Destroy()
        {
            if(m_showingMessage != null)
            {
                m_showingMessage.DestroyMe();
                m_showingMessage = null;
            }
            if(s_showingMessage == this)
            {
                s_showingMessage = null;
            }
        }
        
        public static bool IsReadyToDisplay()
        {
            return (s_showingMessage == null || s_showingMessage.m_hasFinished) && s_isPlayingAudio == false;
        }
        
        public void Display()
        {
            if(Time.frameCount - m_frameCountDisplayed < 10)
                return;
            
            // Don't replay until we have exceeded audioRepeatTime
            if(m_audioRepeatTime > 0 && m_lastAudioPlayedTime > 0 && (Time.time - m_lastAudioPlayedTime) < m_audioRepeatTime)
                return;

            if(s_showingMessage != this)
                DestroyShowingMessage();

            m_hasFinished = false;
            m_frameCountDisplayed = Time.frameCount;
            
            if(m_showingMessage == null)
                m_showingMessage = NGTutorialMessageBase.Create(this);
            else
                m_showingMessage.Reactivate(this);
                
            s_showingMessage = this;
            MAQuestManager.Me.OnDisplayMessage(this);
            LipSyncManager.Me.OnDisplayMessage(this);
            
            switch (m_type.m_type)
            {
                case "QuestDialog":
                    new MAMessageManager.WaitForEvent(MAGameFlow.UpdatingControlObject as MAQuestBase, () => DestroyShowingMessage());
                    break;
                case "TopRightTextOnly":
                    var timeToDisplay = 0.0f;
                    if (m_audioID.IsValidTimeValue(out timeToDisplay))
                    {
                        new MAMessageManager.WaitForEvent(null, () => DestroyShowingMessage(), GetAudioDirection(), timeToDisplay);
                    }
                    break;
                default:
                    s_isPlayingAudio = true;
                    if (m_audioID.IsNullOrWhiteSpace() == false)
                    {
                        m_lastAudioPlayedTime = Time.time;
                        new MAMessageManager.WaitForEvent(m_audioID, () => DestroyShowingMessage(), GetAudioDirection());
                    }
                    else
                    {
                        float timeToRead = -1;
                        if (m_message.ToLower().StartsWith("DisplayFor"))
                        {
                            var mSplit = m_message.Split(':');
                            if(mSplit.Length > 1)
                            {
                                if (float.TryParse(mSplit[1].Trim(), out var timeLength))
                                {
                                    timeToRead = timeLength;
                                    m_message = "!";
                                }
                            }
                        }
                        new MAMessageManager.WaitForEvent(m_advisor, m_message, () => DestroyShowingMessage(), GetAudioDirection(), timeToRead);
                    }
                    break;
            }
        }
        
        private int GetAudioDirection()
        {
            int direction = 0;
            if (m_type.m_type == "BottomLeft") direction = -1;
            else if (m_type.m_type == "BottomRight") direction = 1;
            return direction;
        }

        public static float GetTimeToRead(string _sentance)
        {
            // Add time for pauses
            float extraTime = _sentance.Count(f => f == '.' || f ==',' || f == '?' || f == '!' || f == '–' || f == '…') * 1f;
            int AverageReadingSpeedWordsPerMinute = 220;
            int minimumTimeToRead = 3;
            var wordCount = _sentance.Split().Length;
            var timeToRead = Mathf.Max(minimumTimeToRead, (wordCount / (float)AverageReadingSpeedWordsPerMinute * 60f)+extraTime);
            return timeToRead;
        }

        public void Reactivate(string _message, string _audioID, string _pose, float _audioRepeatTime, string _advisor, bool _stayOnScreen = false)
        {
            m_message = _message;
            m_audioID = _audioID;
            m_advisor = _advisor;
            m_pose = _pose;
            m_holdOnScreen = _stayOnScreen;
            m_audioRepeatTime = _audioRepeatTime;
            
            // Reset values
            m_audioRepeatTime = 0;
            m_lastAudioPlayedTime = -1;
            m_lastPlayedTime = 0;
            m_frameCountDisplayed = 0;
        }
        
        public static MAMessage Create(string _type, string _message, string _audioID, string _pose, float _audioRepeatTime, string _advisor, bool _stayOnScreen = false)
        {
            var message =  new MAMessage()
            {
                m_type = MAMessageType.GetInfo(_type),
                m_message = _message,
                m_audioID = _audioID,
                m_pose = _pose,
                m_audioRepeatTime = _audioRepeatTime,
                m_advisor = _advisor,
                m_holdOnScreen = _stayOnScreen
            };
            if (_pose.IsNullOrWhiteSpace() == false)
            {
                if(_pose.Contains("/"))
                    message.m_poseSprite = Resources.Load<Sprite>(_pose);
                else
                    message.m_poseSprite = Resources.Load<Sprite>($"{PoseLocation}{_pose}");
            }
            return message;
        }
        
        
    }