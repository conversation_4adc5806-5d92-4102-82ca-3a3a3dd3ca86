using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MAMovingInfoBase
{
		// RW-10-MAR-25: The first string is what comes in from Knack, this
		// may have multiple possible drops concatenated. This is then split
		// into m_deathDropOptions, which is what should be used at runtime.
    [SerializeField] protected string m_deathDropFavour;
    [SerializeField] public float m_deathAlignment;
    [SerializeField] public float m_positiveInteractionAlignment;
    [SerializeField] public float m_negativeInteractionAlignment;
    
    public string[] m_deathDropOptions;
    public float m_deathDropChance;
    
    [ScanField] public string m_allowedInteractTypes;
    private HashSet<string> m_allowedInteractTypesCache;
    public bool IsInteractTypeAllowed(string _s)
    {
        if (m_allowedInteractTypesCache == null)
        {
            var types = m_allowedInteractTypes.Split(';', '|', '\n');
            foreach (var type in types)
                m_allowedInteractTypesCache.Add(type);
        }
        return m_allowedInteractTypesCache.Contains(_s);
    }


    public virtual HashSet<MAMovingInfoBase> GetMovingInfoTargets()
    {
        return new HashSet<MAMovingInfoBase>();
    }
    
    virtual public float PossessWalkSpeed { 
        get=> 0;
    }
    virtual public float PossessRunSpeed { 
        get=> 0;
    }
    virtual public float DeathMana => 0;
    
    virtual public float GetDamageModifier(string _damgeType) => 1;
}
