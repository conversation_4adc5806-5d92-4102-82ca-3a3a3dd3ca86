using System;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class MAResearchItemInfo : MonoSingleton<MAResearchItemInfo>
{
    private MAResearchItemUI m_item;
    private MAResearchInfo m_info;
    public TMP_Text m_title;
    public TMP_Text m_description;
    public Image m_buyImage;
    public ContextMenuButton m_buyButton;
    public CanvasGroup m_buyButtonGroup;
    public Transform m_detailLineHolder;
    public GameObject m_detailLinePrefab;
    public void ClickedDetails()
    {
        Debug.Log("Clicked");
    }
    public void ClickedBuy()
    {
        if(m_info.CanPlayerAfford() == false)
            return;
        if (m_info.BuyItem() == false)
            return;
        MAResearchManagerUI.s_purchasedItems.AddUnique(m_info.m_name);
        AudioClipManager.Me.PlaySound("PlaySound_Arcadium_ITEM_PURCHASE", GameManager.Me.gameObject);
        MAResearchManagerUI.Me.Refresh();
        DestroyMe();
    }
    
    public void ClickedClose()
    {
        AudioClipManager.Me.PlaySound("PlaySound_Arcadium_ITEMINFO_CLOSE", GameManager.Me.gameObject);
        DestroyMe();
    }
    public void DestroyMe()
    {
        Destroy(gameObject);
    }
    
    void Activate(MAResearchItemUI _item)
    {
        AudioClipManager.Me.PlaySound("PlaySound_Arcadium_ITEMINFO_OPEN", GameManager.Me.gameObject);
        m_item = _item;
        m_info = _item.m_info;
        m_title.text = m_info.m_newDisplayName;
        
        bool isUnlocked = m_info.IsUnlocked();
        bool canAfford = m_info.CanPlayerAfford();
        bool acquired = m_info.IsAcquired;
        bool interactable = canAfford & isUnlocked & !acquired;
        
        // if(m_info.m_isLocked)
        // {
        //     m_description.text = "Locked";
        // }
        // else
        // {
            GenerateNewDescription();
        //}
        var txt = GetBuyText();
        if (txt.IsNullOrWhiteSpace())
        {
            txt = "Free";
        }

        m_buyButton.SetText(txt);
        m_buyButton.SetButtonInteractable(interactable);
        m_buyButton.gameObject.SetActive(!m_info.IsAcquired);
        m_buyButtonGroup.alpha = interactable ? 1f : 0.4f;
    }

    void GenerateNewDescription()
    {
        var displayText = m_info.m_newDescription;
        if(m_info.m_newDetails.IsNullOrWhiteSpace() == false)
        {
            displayText += $"\n\n<b>Details: </b>{m_info.m_newDetails}";
        }

        if (m_info.m_newUsage.IsNullOrWhiteSpace() == false)
        {
            displayText += $"\n\n<b>Usage: </b>{m_info.m_newUsage}";
        }
        if (m_info.m_newHint.IsNullOrWhiteSpace() == false)
        {
            displayText += $"\n\n<b>Hint: </b>{m_info.m_newHint}";
        }
        if (m_info.m_newWarning.IsNullOrWhiteSpace() == false)
        {
            displayText += $"\n\n<b>Warning: </b>{m_info.m_newWarning}";
        }
        if (m_info.m_newLore.IsNullOrWhiteSpace() == false)
        {
            displayText += $"\n\n<b>Lore: </b>{m_info.m_newLore}";
        }
        m_description.text = displayText;
        if (m_info.m_newDetailsLine.IsNullOrWhiteSpace() == false)
        {
            GenerateNewDetailsLines(m_info.m_newDetailsLine);
        }
        else
        {
            m_detailLineHolder.gameObject.SetActive(false);
        }
    }

    void GenerateNewDetailsLines(string _line)
    {
        m_detailLineHolder.DestroyChildren();
        m_detailLineHolder.gameObject.SetActive(true);
        var lines = _line.Split('\n');
        foreach (var line in lines)
        {
            if (line.IsNullOrWhiteSpace()) continue;
            var cSplit = line.Split(':');
            if (cSplit.Length < 2) continue;
            (TMP_Text title, TMP_Text details) = GetDetailsLine();
            title.text = cSplit[0];
            var decode = cSplit[1];
            var sbSplit = decode.Split('[', ']');
            if (sbSplit.Length < 3)
            {
                details.text = decode;
                continue;
            }
            var dClass = sbSplit[0].Trim();
            var dIndex = sbSplit[1].Trim();
            var dField = sbSplit[2].Trim().TrimStart('.');
            var type = Type.GetType(dClass);
            var methord = type?.GetMethod("GetInfo");
            var obj = methord?.Invoke(methord, new object[] { dIndex });
            object value = null;
            if (dField.Contains('('))
            {
                var brackets = dField.Split('(',')');
                value = type?.GetMethod(brackets[0])?.Invoke(obj, new object[] { brackets[1] });
            }
            else if (dField.Contains("m_"))
            {
                value = type?.GetField(dField)?.GetValue(obj);
            }
            else
            {
                value = type?.GetProperty(dField)?.GetValue(obj);
            }
            if (value != null)
            {
                details.text = value.ToString();
            }
            else
            {
                details.text = "Value not found";
            }
        }
    }
    public (TMP_Text title, TMP_Text details) GetDetailsLine()
    {
        var line = Instantiate(m_detailLinePrefab, m_detailLineHolder);
        line.SetActive(true);
        var title = line.transform.Find("Title").GetComponent<TMP_Text>();
        var details = line.transform.Find("Details").GetComponent<TMP_Text>();
        return (title, details);
    }
    
    void ActivateOld(MAResearchItemUI _item)
    {
        AudioClipManager.Me.PlaySound("PlaySound_Arcadium_ITEMINFO_OPEN", GameManager.Me.gameObject);
        m_item = _item;
        m_info = _item.m_info;
        m_title.text = m_info.m_title;
        
        bool isUnlocked = m_info.IsUnlocked();
        bool canAfford = m_info.CanPlayerAfford();
        bool acquired = m_info.IsAcquired;
        bool interactable = canAfford & isUnlocked & !acquired;
        
        if(m_info.m_isLocked)
        {
            m_description.text = "Locked";
        }
        else
        {
            m_description.text = m_info.m_description;
        }
        
        m_buyButton.SetText(GetBuyText());
        m_buyButton.SetButtonInteractable(interactable);
        m_buyButton.gameObject.SetActive(!m_info.IsAcquired);
        m_buyButtonGroup.alpha = interactable ? 1f : 0.4f;
    }
    
    private string GetBuyText()
    {
        if(m_info.IsUnlocked() == false)
            return "Buy";
        return m_info.GetBuyText();
        /*var buyText = "";
        if (m_info.m_factionCost > 0)
        {
            buyText+= $"<sprite={(int)m_info.m_faction+1}> {m_info.m_factionCost:N0} ";
        }
        var additionalCost = m_info.GetAdditionalFactionCost();
        if(additionalCost.factionType != MAFactionInfo.FactionType.None && additionalCost.amount > 0)
        {
            buyText+= $"<sprite={(int)additionalCost.factionType+1}> {additionalCost.amount:N0} ";
        }

        if (m_info.m_dollarCost > 0)
        {
            buyText+= $" {GlobalData.CurrencySymbol}{m_info.m_dollarCost:N0} ";
        }
        
        return buyText.Trim();*/
    }
    
    public static MAResearchItemInfo Create(MAResearchItemUI _info)
    {
        if (Me)
        {
            Me.Activate(_info);
            return Me;
        }
        var prefab = Resources.Load<MAResearchItemInfo>("_Prefabs/Research/MAResearchItemInfoNew");
        var instance = Instantiate(prefab, NGManager.Me.m_centreScreenHolder);
        instance.Activate(_info);
        return instance;
    }
}
