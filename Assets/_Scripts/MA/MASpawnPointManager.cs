using System.Collections;
using System.Collections.Generic;
#if UNITY_EDITOR
using UnityEditor;
#endif
using UnityEngine;

public class MASpawnPointManager : MonoBehaviour
{
    [SerializeField] 
    private bool m_enableEditorDebugDataTextField = false;

    private string m_locationString = "";
    
    public List<CharIdMapping> m_charactersSpawnedThisDay = new();
    private Dictionary<int, List<MASpawnByDayInfo>> m_wavesToday = new Dictionary<int, List<MASpawnByDayInfo>>();
    private List<int> m_spawnTimesToday = new List<int>();
    private int m_repeatModeDelay;
    private int m_repeatModeWaveOriginalTime;
    private Coroutine m_spawningNowCR = null;
    
    private bool m_loaded = false;

    private bool isNonEnemySpawner = false;
    
    [System.Serializable]
    public class GameState_DaySpawn
    {
        public string m_location;
        public int m_currentSpawnDay = 0;
        public float m_secondsIntoNight = 0;
        // RW-23-JUN-25: Keeping both the spawn time idx (into m_spawnTimesToday) and the raw next spawn time 
        // because the former is useful before we reach the end of the defined spawn times, the latter once we're 
        // into "repeat" mode.
        public int m_nextSpawnTimeIdx = 0;
        public int m_nextSpawnTime = 0;
        public List<MASpawnByDayInfo> m_oneShotSpawnInfosCompleted = new List<MASpawnByDayInfo>();
		public List<MASpawnByDayInfo> m_dailyOneShotSpawnInfosSpawnedToday = new List<MASpawnByDayInfo>();
        public List<GameState_GradualSpawnSchedule> m_leftOverCharacterSpawns = new();
        public List<int> m_characterIdsSpawnedThisDay = new();
        public bool m_dayChangedFlag = false;
        
        public void ClearData()
        {
            m_location = "";
            m_currentSpawnDay = 0;
            m_dailyOneShotSpawnInfosSpawnedToday = new List<MASpawnByDayInfo>();
            m_leftOverCharacterSpawns = new();
            m_characterIdsSpawnedThisDay = new();
            m_dayChangedFlag = false;
        }
    
        public override string ToString()
        {
            string location = m_location;
            string day = m_currentSpawnDay.ToString();
            string dayChangedToday = m_dayChangedFlag.ToString();
            string spawnTime = m_secondsIntoNight.ToString();
            string[] characterIds = m_characterIdsSpawnedThisDay.ConvertAll(x => x.ToString()).ToArray();
            string charIds = string.Join(", ", characterIds);
            string[] leftOverCharacters = m_leftOverCharacterSpawns.ConvertAll(x => $"{x.m_countLeft.ToString()}, {x.m_spawnByDayInfo.m_dayNum.ToString()}, {x.m_spawnByDayInfo.m_spawnTimeSeconds.ToString()}, {x.m_groupId.ToString()}").ToArray();
            string leftOverChars = string.Join(", ", leftOverCharacters);
            string[] oneShotsCompleted = m_oneShotSpawnInfosCompleted.ConvertAll(x => x.m_creature).ToArray();
            string oneShotsCompWave = string.Join(", ", oneShotsCompleted);
            return $"Location: {location}\n Day: {day}\ndayChangedToday: {dayChangedToday.ToString()}\nSpawnTime: {spawnTime}\nCharacterIds: {charIds}\nLeftOverCharacters: {leftOverChars}\nOneShotsCompletedThisWave: {oneShotsCompWave}\n";
        }
    }

    public GameState_DaySpawn m_dayData = null;
    public GameState_DaySpawn DayData
    {
        get
        {
            if (m_dayData == null)
            {
                m_dayData = GameManager.Me.m_state.m_currentSpawnWaveThisDayByLocation.Find(x =>
                    x.m_location == m_locationString) ?? new();
            }
            return m_dayData;
        }
        private set
        {
            if (m_dayData == null && value != null)
            {
                m_dayData = GameManager.Me.m_state.m_currentSpawnWaveThisDayByLocation.Find(x =>
                    x.m_location == m_locationString) ?? new();
            }
            m_dayData = value;
        }
    }

    public List<int> CharacterIdsSpawnedThisDay
    {
        get => DayData.m_characterIdsSpawnedThisDay;
        private set => DayData.m_characterIdsSpawnedThisDay = value;
    }
    
    public List<GameState_GradualSpawnSchedule> LeftOverCharacterSpawns
    {
        get => DayData.m_leftOverCharacterSpawns;
        private set => DayData.m_leftOverCharacterSpawns = value;
    }

    private bool DayChangedFlag
    {
        get => DayData.m_dayChangedFlag;
        set => DayData.m_dayChangedFlag = value;
    }

    public static List<MASpawnPointManager> s_spawnPointManagers = new ();
    public static List<MASpawnPointManager> LoadAllSpawnPointLocations()
    {
        MASpawnArea.LoadAll();
        LoadSpawnPointLocations(MASpawnByDayInfo.s_spawnByDayInfosSortedByLocation);
        LoadSpawnPointLocations(MASpawnByDayInfo.s_spawnByDayInfosDailyOneShotDayAndLocationGrouped);

        return s_spawnPointManagers;
    }

    static void LoadSpawnPointLocations(List<List<MASpawnByDayInfo>> _spawnByDayInfos)
    {
        foreach (List<MASpawnByDayInfo> maSpawnByDayInfos in _spawnByDayInfos)
        {
            if(maSpawnByDayInfos.Count == 0) continue;
            if (s_spawnPointManagers.Find(x => x.m_locationString == maSpawnByDayInfos[0].m_spawnLocation) != null) continue;
            if (InputUtilities.GetObjectFromLocationString(maSpawnByDayInfos[0].m_spawnLocation, out object _outPos1))
            {
                MABuilding spawnBuilding = _outPos1 as MABuilding;
                //MABuilding spawnDecoration = _outPos2 as MABuilding;
                Vector3? spawnPos = _outPos1 as Vector3?;
                NamedPoint spawnNamedPoint = _outPos1 as NamedPoint;
            
                if ((spawnBuilding != null || spawnPos != null || spawnNamedPoint != null))
                {
                    var newSpawnManager = new GameObject($"Spawn Point - {maSpawnByDayInfos[0].m_spawnLocation}");
                    newSpawnManager.transform.SetParent(GlobalData.Me.m_spawnPointHolder, true);
                    var spawnPointManager = newSpawnManager.AddComponent<MASpawnPointManager>();
                    spawnPointManager.m_locationString = maSpawnByDayInfos[0].m_spawnLocation;
                    s_spawnPointManagers.Add(spawnPointManager);
                    if (spawnBuilding != null)
                    {
                        newSpawnManager.transform.position = spawnBuilding.transform.position;
                    }
                    else if (spawnPos != null)
                    {
                        newSpawnManager.transform.position = (Vector3)spawnPos;
                    }
                    else if (spawnNamedPoint != null)
                    {
                        newSpawnManager.transform.position = spawnNamedPoint.transform.position;
                    }
                    spawnPointManager.LoadAll();
                }
            }
        }
    }

    public void LoadAll()
    {
		//MASpawnArea.LoadAll();
        if (m_locationString.IsNullOrWhiteSpace() == false)
        {
            var dayData = GameManager.Me.m_state.m_currentSpawnWaveThisDayByLocation?.Find(x => x.m_location == m_locationString);
            if (dayData != null)
            {
                m_dayData = dayData;
            }
            else
            {
                m_dayData = new();
                m_dayData.m_location = m_locationString;
                GameManager.Me.m_state.m_currentSpawnWaveThisDayByLocation.Add(m_dayData);
            }
        }

        for(int i = CharacterIdsSpawnedThisDay.Count -1; i >= 0; i--)
        {
            MACharacterBase charBase = NGManager.Me.FindCharacterByID(CharacterIdsSpawnedThisDay[i]);
            if(charBase != null)
                m_charactersSpawnedThisDay.Add(new(CharacterIdsSpawnedThisDay[i], NGManager.Me.FindCharacterByID(CharacterIdsSpawnedThisDay[i])));
            else
            {
                CharacterIdsSpawnedThisDay.RemoveAt(i);
            }
        }
        
		DayNight.Me.RegisterRestartDayCallback(OnRestardDay);
        
        m_loaded = true;
    }

    public void OnRestardDay()
    {
        DayData.m_currentSpawnDay = -1;
    }

    [System.Serializable] //save struct used to save info about characters that are gradually spawned
    //during a gradual spawn sequence ('spawning over time' or 'spawn when space available') we may quit the game.
    //We use this save data to remember how many spawns are left in our sequence.
    public class GameState_GradualSpawnSchedule
    {
        public MASpawnByDayInfo m_spawnByDayInfo;
        public int m_countLeft;
        public int m_groupId;
    }

    [System.Serializable]
    public class CharIdMapping
    {
        public int id;
        public MACharacterBase character;

        public CharIdMapping(int _id, MACharacterBase _charBase)
        {
            id = _id;
            character = _charBase;
        }
    }
    
    private IEnumerator SpawnInSpace(List<GameState_GradualSpawnSchedule> _byDaySpawnInfos)
    {
        var group = new List<MACharacterGroupBehaviour.GroupCharacterInfo>();
        NamedPoint spawnNamedPoint = null;
        float groupPatrolRadius = 0f;
        MACharacterGroupBehaviour cgb = null;
        foreach (var spawnSchedule in _byDaySpawnInfos)
        {
            MASpawnByDayInfo spawnByDayInfo = spawnSchedule.m_spawnByDayInfo;

            // spawnByDayInfo.m_spawnLocation = spawnByDayInfo.m_spawnLocation.Replace("OakridgeRoadCrypt", "OakridgeCrypt");
            // spawnByDayInfo.m_spawnLocation = spawnByDayInfo.m_spawnLocation.Replace("OakridgeGraveyard", "OakridgeCrypt");
            if (InputUtilities.GetObjectFromLocationString(spawnByDayInfo.m_spawnLocation, out object _outPos1) == false)
            {
                Debug.LogError($"{GetType().Name} - Failed to Parse spawnByDayInfo.m_spawnLocation: {spawnByDayInfo.m_spawnLocation}");
            }

            // spawnByDayInfo.m_destination = spawnByDayInfo.m_destination.Replace("OakridgeRoadCrypt", "OakridgeCrypt");
            // spawnByDayInfo.m_destination = spawnByDayInfo.m_destination.Replace("OakridgeGraveyard", "OakridgeCrypt");
            // RW-06-MAR-25: The DailyOneShot type doesn't have a destination - these guys just wander around in the wilderness.
            object _outPos2 = null;
            if (!spawnByDayInfo.IsAmbientSpawn() && InputUtilities.GetObjectFromLocationString(spawnByDayInfo.m_destination, out _outPos2) == false)
            {
                Debug.LogError($"{GetType().Name} - Failed to Parse spawnByDayInfo.m_destination: {spawnByDayInfo.m_destination}");
            }

            MABuilding spawnBuilding = _outPos1 as MABuilding;
            //MABuilding spawnDecoration = _outPos2 as MABuilding;
            Vector3? spawnPos = _outPos1 as Vector3?;
            spawnNamedPoint = _outPos1 as NamedPoint;
            if (cgb != null)
            {
                spawnSchedule.m_groupId = cgb.ID;
                if (spawnNamedPoint != null)
                    spawnNamedPoint.SetSpawnedGroup(cgb);
            }

            MABuilding destBuilding = _outPos2 as MABuilding;
            NGDecoration destDecoration = _outPos2 as NGDecoration;
            Vector3? destPos = _outPos2 as Vector3?;

            if (cgb == null)
            {
                if (MACharacterGroupBehaviour.s_groups.ContainsKey(spawnSchedule.m_groupId))
                {
                    cgb = MACharacterGroupBehaviour.s_groups[spawnSchedule.m_groupId];
                    if (spawnNamedPoint != null)
                        spawnNamedPoint.SetSpawnedGroup(cgb);
                }
            }

            if ((spawnBuilding != null || spawnPos != null || spawnNamedPoint != null) &&
                (destBuilding != null || destDecoration != null || destPos != null || spawnByDayInfo.IsAmbientSpawn()))
            {
                int stationaryCount = spawnByDayInfo.m_numStationary;
                bool spaceAvailable = true;
                while (spawnSchedule.m_countLeft > 0 && spaceAvailable)
                {
                    spaceAvailable = false;
                    MACreatureInfo creatureInfo = MACreatureInfo.GetInfo(spawnByDayInfo.m_creature);
                    MACharacterBase ch = null;
                    if (spawnBuilding != null)
                    {
                        if (MACreatureControl.Me.SpawnCreatureIfSpaceExists(creatureInfo, spawnBuilding, out ch))
                        {
                            spaceAvailable = true;
                        }
                    }
                    else if (spawnPos != null)
                    {
                        if (MACreatureControl.Me.SpawnCreatureIfSpaceExists(creatureInfo, spawnPos.Value, out ch))
                        {
                            spaceAvailable = true;
                        }
                    }
                    else if (spawnNamedPoint != null)
                    {
                        if (MACreatureControl.Me.SpawnCreatureIfSpaceExists(creatureInfo, spawnNamedPoint, spawnSchedule.m_countLeft, out ch))
                        {
                            spaceAvailable = true;
                        }
                    }

                    if (ch != null)
                    {
                        // character.Name = m_thisDay.ToString() + " " + m_charactersSpawnedThisDay.Count;
                        ch.SetToGuard(spawnByDayInfo.m_destination,
                            ch.CharacterSettings.m_guardModeOverrideRadius);
                        // character.SetDespawnPosition(decorationDest.transform.position);

                        var escortee = ch as MAEscortee;
                        if (escortee != null)
                        {
                            escortee.SetupWaypoints(MASpawnByDayInfo.DecodeWaypoints(spawnByDayInfo.m_waypoints));
                        }

                        // RW-10-MAR-25: These lists are used to track if anyone from the nightly waves is still alive.
                        // The Bandits in the wilderness shouldn't be getting entered into this.
                        if (!spawnByDayInfo.IsAmbientSpawn())
                        {
                            m_charactersSpawnedThisDay.Add(new(ch.m_ID, ch));
                            CharacterIdsSpawnedThisDay.Add(ch.m_ID);
                        }
                        spawnSchedule.m_countLeft--;

                        if (spawnByDayInfo.m_creatureSpawnType == MASpawnByDayInfo.CreatureSpawnType.OneShot)
                        {
                            DayData.m_oneShotSpawnInfosCompleted.Add(spawnByDayInfo);
                        }

                        bool shouldSetPatrol = false;
                        if (creatureInfo.m_groupPatrolRadius > 0f)
                        {
                            if (creatureInfo.m_groupPatrolRadius > groupPatrolRadius)
                            {
                                shouldSetPatrol = true;
                                groupPatrolRadius = creatureInfo.m_groupPatrolRadius;
                            }
                        }
                        if (creatureInfo.m_isGroup)
                        {
                            var stationary = Vector3.zero;
                            if (stationaryCount > 0)
                            {
                                stationary = ch.transform.position;
                                --stationaryCount;
                            }

                            var chInfo = new MACharacterGroupBehaviour.GroupCharacterInfo
                                {
                                    character = ch,
                                    stationaryPos = stationary
                                };
                            if (cgb != null)
                            {
                                if (shouldSetPatrol)
                                {
                                    var pos = ch.transform.position;
                                    if (spawnNamedPoint != null)
                                        pos = spawnNamedPoint.transform.position.GroundPosition();
                                    var patrollingInfo = new MACharacterGroupBehaviour.GroupPatrollingInfo
                                        {
                                            centerPos = pos,
                                            radius = groupPatrolRadius
                                        };
                                    cgb.SetPatrollingInfo(patrollingInfo);
                                }
                                cgb.AddCharacter(chInfo);
                            }
                            else
                            {
                                var prefab = Resources.Load<GameObject>("_Prefabs/Characters/CharacterGroupBehaviour");
                                var go = Instantiate(prefab, Vector3.zero, Quaternion.identity, GlobalData.Me.m_characterHolder);
                                cgb = go.GetComponent<MACharacterGroupBehaviour>();
                                bool inSubScene = spawnNamedPoint?.transform.GetComponentInParent<GenericSubScene>() != null;
                                cgb.SetupGroup(creatureInfo.m_creatureType, _unlockOnDeath: spawnByDayInfo.m_unlockOnDeath, _inSubScene: inSubScene);
                                if (shouldSetPatrol)
                                {
                                    var pos = ch.transform.position;
                                    if (spawnNamedPoint != null)
                                        pos = spawnNamedPoint.transform.position.GroundPosition();
                                    var patrollingInfo = new MACharacterGroupBehaviour.GroupPatrollingInfo
                                        {
                                            centerPos = pos,
                                            radius = groupPatrolRadius
                                        };
                                    cgb.SetPatrollingInfo(patrollingInfo);
                                }
                                cgb.AddCharacter(chInfo);
                                cgb.StartGroup();
                                spawnSchedule.m_groupId = cgb.ID;
                                if (spawnNamedPoint != null)
                                    spawnNamedPoint.SetSpawnedGroup(cgb);
                            }
                        }

                        // RW-14-APR-25: I'm adding a delay after successful spawns so that the creatures aren't too in-sync in their animation.
                        // Note this only affects them at spawn-time, not at load-time, when they may still be synced to some degree, but I think
                        // it'll be less obvious than it is currently with the spawn anims.
                        yield return new WaitForSeconds(0.1f);
                    }
                    else
                    {
                        yield return new WaitForEndOfFrame();
                    }
                }
            }
            else
            {
                if (spawnBuilding == null || destBuilding == null)
                {
                    Debug.LogError($"MASpawnByDayInfo Day '{spawnByDayInfo.m_dayNum}' Cannot spawn: LocationStr {spawnByDayInfo.m_spawnLocation} && DestinationStr: {spawnByDayInfo.m_destination}");
                    yield break;
                }
            }
        }

        for (int i = _byDaySpawnInfos.Count - 1; i >= 0; i--)
            {
                if (_byDaySpawnInfos[i].m_countLeft == 0)
                    _byDaySpawnInfos.RemoveAt(i);
            }
        m_spawningNowCR = null;
        yield break;
    }

    /*public static int GetCurrentHighestSpawnWave
    {
        get
        {
            int highestWave = 0;
            foreach (MASpawnPointManager maSpawnPointManager in s_spawnPointManagers)
            {
               if (maSpawnPointManager.DayData.m_currentSpawnWaveThisDay > highestWave)
               {
                   highestWave = maSpawnPointManager.DayData.m_currentSpawnWaveThisDay;
               }
            }
            return  highestWave;
        }
    }*/

    // RW-25-JUN-25: We should show the overlay once the spawn point has spawned something tonight,
    // for the rest of the night.
    public bool ShowOverlay()
    {
        return DayData.m_nextSpawnTimeIdx > 0 && !isNonEnemySpawner;
    }

    public Dictionary<(Vector3 pos, Vector3 dest), int> GetRemainingSpawns()//bool _includeThisWave = false)
    {
        var res = new Dictionary<(Vector3, Vector3), int>();
        foreach (var kvp in m_wavesToday)
        {
            bool valid = DayData.m_nextSpawnTimeIdx < m_spawnTimesToday.Count &&  kvp.Key >= m_spawnTimesToday[DayData.m_nextSpawnTimeIdx];
            foreach (var waveInfo in kvp.Value)
            {
                if (valid || waveInfo.m_creatureSpawnType == MASpawnByDayInfo.CreatureSpawnType.Respawn)
                {
                    var pos = waveInfo.SpawnLocationPos;
                    var dest = waveInfo.DestinationPos;
                    int count = waveInfo.m_count;
                    if (res.TryGetValue((pos, dest), out var existingCount)) count += existingCount;
                    res[(pos, dest)] = count;
                }
            }
        }
        return res;
    }

    bool HasRepeatMode()
    {
        return m_repeatModeDelay > 0;
    }

    bool SpawningInRepeatMode()
    {
        return DayData.m_nextSpawnTimeIdx >= m_spawnTimesToday.Count;
    }

    private void Update()
    {
        if (m_loaded && GameManager.Me.LoadComplete && IntroControl.IsInIntro == false && FailSequenceController.Me.IsActive == false)
        {
            DayChangedFlag = DayData.m_currentSpawnDay != DayNight.Me.CurrentWorkingDay;

            if (DayNight.Me.m_isFullNight)
            {
                DayData.m_secondsIntoNight += Time.deltaTime;
            }
            else
            {
                DayData.m_secondsIntoNight = 0;
                DayData.m_nextSpawnTimeIdx = 0;
                DayData.m_nextSpawnTime = 0;
                if (m_spawningNowCR != null)
                {
                    StopCoroutine(m_spawningNowCR);
                    m_spawningNowCR = null;
                }
                LeftOverCharacterSpawns.Clear();
            }

            m_wavesToday.Clear();
            m_spawnTimesToday.Clear();
            
            if (MASpawnByDayInfo.s_spawnByDayInfosSortedByDay.TryGetValue(DayNight.Me.CurrentWorkingDay, out var dayInfos))
            {
                isNonEnemySpawner = true;
                foreach (var info in dayInfos)
                {
                    if (info.m_spawnLocation != m_locationString) continue;
                    if (m_wavesToday.TryGetValue(info.m_spawnTimeSeconds, out var inf))
                    {
                        inf.Add(info);
                    }
                    else
                    {
                        var infos = new List<MASpawnByDayInfo>();
                        m_wavesToday.Add(info.m_spawnTimeSeconds, infos);
                        m_spawnTimesToday.Add(info.m_spawnTimeSeconds);
                        infos.Add(info);
                    }

                    if (info.m_repeatDelaySeconds > 0)
                    {
                        m_repeatModeDelay = info.m_repeatDelaySeconds;
                        // RW-23-JUN-25: Not necessarily the most intuitive way to do things, but store the original time of the wave we want to repeat 
                        // once all defined wave times have elapsed, so we can retrieve it from m_wavesToday (which is a map of times to wave data) easily.
                        m_repeatModeWaveOriginalTime = info.m_spawnTimeSeconds;
                    }

                    if (info.m_creature != "Escortee")
                        isNonEnemySpawner = false;
                }
                m_spawnTimesToday.Sort();

                // RW-24-JUN-25: Ensure m_nextSpawnTime is correctly initialised.
                if (m_spawnTimesToday.Count > 0 && DayData.m_nextSpawnTime < m_spawnTimesToday[0])
                {
                    DayData.m_nextSpawnTime = m_spawnTimesToday[0];
                }
            }

						for (int i=0; i<MASpawnByDayInfo.s_spawnByDayInfosDailyOneShotDayAndLocationGrouped.Count; i++)
						{
							var dayAndLocationGroup = MASpawnByDayInfo.s_spawnByDayInfosDailyOneShotDayAndLocationGrouped[i];
							var firstMember = dayAndLocationGroup[0];
							if (m_locationString != firstMember.m_spawnLocation)
							{
								continue;
							}

							if (firstMember.m_creatureSpawnType == MASpawnByDayInfo.CreatureSpawnType.OneShot && DayData.m_oneShotSpawnInfosCompleted.Contains(firstMember))
							{
								continue;
							}

							bool alreadySpawnedToday = DayData.m_dailyOneShotSpawnInfosSpawnedToday.Find(o => 
							{ 
									return o.m_dayNum == firstMember.m_dayNum && o.m_spawnLocation == firstMember.m_spawnLocation;
							}) != null;

							bool canSpawn = false;
							if (InputUtilities.GetObjectFromLocationString(firstMember.m_spawnLocation, out object o))
							{
								NamedPoint np = o as NamedPoint;
								if (np != null)
								{
									canSpawn = np.CanSpawn();
								}
							}

							if (firstMember.m_dayNum <= DayData.m_currentSpawnDay && !alreadySpawnedToday && canSpawn  && MACreatureControl.Me.IsValidTimeOfDay(MACreatureInfo.GetInfo(firstMember.m_creature)))
							{
								List<GameState_GradualSpawnSchedule> spawnSchedules = new();
								for (int j=0; j<dayAndLocationGroup.Count; j++)
								{
									var info = dayAndLocationGroup[j];
									spawnSchedules.Add(new GameState_GradualSpawnSchedule()
                                    {
                                        m_spawnByDayInfo = info,
                                        m_countLeft = info.m_count,
                                        m_groupId = -1
									});
								}
								StartCoroutine(SpawnInSpace(spawnSchedules));
								DayData.m_dailyOneShotSpawnInfosSpawnedToday.Add(firstMember);
							}
						}
            
            if (m_wavesToday.Count > 0 && (!SpawningInRepeatMode() || HasRepeatMode()) && DayData.m_secondsIntoNight >= DayData.m_nextSpawnTime)
            {
                var spawnTime = SpawningInRepeatMode() ? m_repeatModeWaveOriginalTime : m_spawnTimesToday[DayData.m_nextSpawnTimeIdx];
                List<MASpawnByDayInfo> thisDayWaveInfos = m_wavesToday[spawnTime];
                if (/*(m_charactersSpawnedThisDay.Count == 0 || LeftOverCharacterSpawns.Count > 0) &&*/
                    m_spawningNowCR == null && MACreatureControl.Me.IsValidTimeOfDay(MACreatureInfo.GetInfo(thisDayWaveInfos[0].m_creature)))
                {
                    // RW-25-JUN-25: If this is the first thing we're spawning tonight, regenerate the overlay as we'll be displaying the marker for this 
                    // spawn point when we weren't previously.
                    if (DayData.m_nextSpawnTimeIdx == 0)
                    {
                        TowerDefenseVisualsManager.Me.DestroyOverlay(true);
                    }
                    DayData.m_nextSpawnTimeIdx++;
                    if (!SpawningInRepeatMode())
                    {
                        DayData.m_nextSpawnTime = m_spawnTimesToday[DayData.m_nextSpawnTimeIdx];
                    }
                    else
                    {
                        DayData.m_nextSpawnTime += m_repeatModeDelay;
                    }

                    if (LeftOverCharacterSpawns.Count > 0)
                    {
                        LeftOverCharacterSpawns.RemoveAll(x =>
                            DayData.m_oneShotSpawnInfosCompleted.Contains(x.m_spawnByDayInfo));
                        //Debug.Log($"{GetType().Name} - Day [{DayNight.Me.CurrentWorkingDay}] Leftover Spawn initiated {LeftOverCharacterSpawns.ConvertAll(x=> x.m_spawnByDayInfo.m_creature)} ");
                        m_spawningNowCR = StartCoroutine(SpawnInSpace(LeftOverCharacterSpawns));
#if UNITY_EDITOR
                        m_spawnInfo = DayData.ToString();
#endif
                    }
                    else
                    {
                        Debug.Log($"{GetType().Name} - Day [{DayNight.Me.CurrentWorkingDay}] Spawn initiated ");
                        List<GameState_GradualSpawnSchedule> spawnSchedules = new();
                        foreach (var spawnInfo in thisDayWaveInfos)
                        {
                            if(DayData.m_oneShotSpawnInfosCompleted.Contains(spawnInfo))
                            {
                                //Debug.Log($"{GetType().Name} - Omitting OneShot Spawn info [{spawnInfo.DebugDisplayName}]");
                                continue;
                            }   
                            spawnSchedules.Add(new GameState_GradualSpawnSchedule() 
                            { 
                                m_spawnByDayInfo = spawnInfo,
                                m_countLeft = spawnInfo.m_count,
                                m_groupId = -1
                            });
                        }

                        LeftOverCharacterSpawns = spawnSchedules;
                        m_spawningNowCR = StartCoroutine(SpawnInSpace(spawnSchedules));
                    }
                }
            }

            if (m_charactersSpawnedThisDay.Count > 0)
            {
                List<CharIdMapping> keysToRemove = new();
                foreach(var charBase in m_charactersSpawnedThisDay)
                {
                    if (/*charBase == null || */charBase.character == null || charBase.character.CharacterUpdateState.State == CharacterStates.Despawn)
                    {
                        keysToRemove.Add(charBase);
                    }
                    else if (charBase.character.CharacterGameState.IsAlive == false ||
                             charBase.character.CharacterUpdateState.State == CharacterStates.Dead ||
                             charBase.character.CharacterUpdateState.State == CharacterStates.Dying)
                    {
                        keysToRemove.Add(charBase);
                    }
                }

                foreach(var keyToRemove in keysToRemove)
                {
                    CharacterIdsSpawnedThisDay.Remove(keyToRemove.id);
                    m_charactersSpawnedThisDay.Remove(keyToRemove);
                    if (m_charactersSpawnedThisDay.Count == 0)
                    {
                        if (DayData.m_currentSpawnDay == DayNight.Me.CurrentWorkingDay) 
                        {
                            // RW-25-JUN-25; If all creatures from the spawn point are killed, we update the timer to make the next wave spawn
                            // immediately. Updating the timer this way ensures that the intervals to subsequent spawns after this immediate one
                            // remain consistent.
                            DayData.m_secondsIntoNight = DayData.m_nextSpawnTime;
                        }
                    }
                }
            }
                
            if (DayChangedFlag && m_charactersSpawnedThisDay.Count == 0)
            {
								DayData.m_dailyOneShotSpawnInfosSpawnedToday.Clear();
                DayData.m_currentSpawnDay = DayNight.Me.CurrentWorkingDay;
                DayData.m_location = m_locationString;
                DayData.m_secondsIntoNight = 0;
                DayData.m_nextSpawnTimeIdx = 0;
                DayData.m_nextSpawnTime = 0;
                LeftOverCharacterSpawns.Clear();
                DayChangedFlag = false;
            }
            
#if UNITY_EDITOR
            if (m_enableEditorDebugDataTextField)
            {
                m_spawnInfo = DayData.ToString();
            }
            else
            {
                m_spawnInfo = "";
            }
#endif
        }
    }
    
    [TextArea(1, 10)] public string m_spawnInfo = "";
}

#if UNITY_EDITOR
[CustomEditor(typeof(MASpawnPointManager))]
public class MASpawnPointManagerEditor : Editor
{
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();
        MASpawnPointManager spawnPointManager = (MASpawnPointManager)target;

        if(GUILayout.Button($"Clear CharacterIds"))
        {
            spawnPointManager.CharacterIdsSpawnedThisDay.Clear();
        }
        if(GUILayout.Button($"Clear this Save Data"))
        {
            spawnPointManager.DayData.ClearData();
        }
        
        if(GUILayout.Button($"Clear All Save Data"))
        {
            foreach (MASpawnPointManager.GameState_DaySpawn gameStateDaySpawn in GameManager.Me.m_state.m_currentSpawnWaveThisDayByLocation)
            {
                gameStateDaySpawn.ClearData();
            }
            GameManager.Me.m_state.m_currentSpawnWaveThisDayByLocation.Clear();
        }
    }
}
#endif