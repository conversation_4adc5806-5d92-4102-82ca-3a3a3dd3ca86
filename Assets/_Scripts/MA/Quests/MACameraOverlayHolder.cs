using System.Collections.Generic;
using UnityEngine;

public class MACameraOverlayHolder : MonoSingleton<MACameraOverlayHolder>
{
    private Dictionary<MACameraOverlay.OverlayType, MACameraOverlay> m_overlays = new Dictionary<MACameraOverlay.OverlayType, MACameraOverlay>();

    override protected void Awake()
    {
        base.Awake();

        var overlays = GetComponentsInChildren<MACameraOverlay>(true);

        foreach(var overlay in overlays)
        {
            m_overlays[overlay.m_type] = overlay;
        }

    }

    public bool Show(MACameraOverlay.OverlayType _type)
    {
        if(m_overlays.ContainsKey(_type))
        {
            m_overlays[_type].Show();
            return true;
        }

        return false;
    }

    public bool Hide(MACameraOverlay.OverlayType _type)
    {
        if (m_overlays.ContainsKey(_type))
        {
            m_overlays[_type].Hide();
            return true;
        }

        return false;
    }

    public bool IsShowing(MACameraOverlay.OverlayType _type)
    {
        if (m_overlays.ContainsKey(_type))
        {
            return m_overlays[_type].IsShowing;
        }

        return false;
    }
}
