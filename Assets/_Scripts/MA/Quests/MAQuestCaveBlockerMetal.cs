
using UnityEngine;

public class MAQuestCaveBlockerMetal : MAQuestBase
{
    public int m_swordRackUID;
    public BuildingNavBlocker m_caveEntranceBlocker;
    public BoxCollider m_caveEntranceColliderBox;
    public GameObject m_caveGate;
    
    private MABuilding m_swordRack;
    private MA<PERSON><PERSON><PERSON>haracter m_caveExplorer;

    private bool m_hasReturnedToQuestGiver = false;
    private bool m_gateOpened = false;

    protected override void OnPostLoad()
    {
        InitReferences();
    }
    
    private void InitReferences()
    {
        m_caveEntranceBlocker = GameObject.Find("MoAVisuals/Caves/Cave_Mine_Entrance/MANavBlocker").GetComponent<BuildingNavBlocker>();
        m_caveEntranceColliderBox = GameObject.Find("MoAVisuals/Caves/Cave_Mine_Entrance/Colliders/Collier (2)").GetComponent<BoxCollider>();
        
        if (m_caveExplorer == null)
        {
            m_caveExplorer =
                NGManager.Me.m_MACharacterList.Find(x => x.GetTypeInfo() == "CaveBlockerMetal") as MAFlowCharacter;
        }
        
        if (m_caveExplorer != null)
        {
            LipSyncManager.Me.AddLipSyncer("CaveBlockerMetal", m_caveExplorer, -18.0f);
        }

        if (m_gateOpened)
        {
            OpenGate();
        }
    }

    private void SetSwordRack()
    {
        m_swordRack = MABuilding.FindBuilding(m_swordRackUID.ToString());
        if (m_swordRack == null)
        {
            Debug.Log("MAQuestCaveBlockerMetal - Could not find swordRack");
        }
    }
    

    private void AttachSwordToQuestGiver()
    {
        SetSwordRack();
        // SetQuestCharacter();
            
        if (m_swordRack != null && m_caveExplorer != null)
        {
            BCQuestStockIn stockIn = m_swordRack.GetComponentInChildren<BCQuestStockIn>();
            NGCarriableResource weapon = stockIn.GetResource();
            if (weapon != null)
            {
                m_caveExplorer.SetWeaponDesign(weapon.GetProduct().m_design);
                stockIn.DestroyStock();
            }
        }
    }

    private void OpenGate()
    {
        Animator animator = m_caveGate.GetComponent<Animator>();
        animator.SetTrigger("open");
        m_gateOpened = true;
    }

    private void SetGateOpen()
    {
        Animator animator = m_caveGate.GetComponent<Animator>();
        animator.SetBool("Unlocked", true);
    }
    
    private void EnableCaveEntranceBlocker(bool _isActive)
    {
        PathBlock pathBlock = m_caveEntranceBlocker.GetComponent<PathBlock>();

        if(pathBlock != null)
        {
            pathBlock.m_isActive = _isActive;
        }
        
        m_caveEntranceColliderBox.gameObject.SetActive(_isActive);
    }
    
    public override float QuestObjectiveValue(string _objective)
    {
        if (_objective.Contains("ReturnedToQuestGiver"))
        {
            return m_hasReturnedToQuestGiver ? 0.0f : 1.0f;
        }
        return 1.0f;
    }
    
    public override void TriggerQuestEvent(string _event)
    {
        base.TriggerQuestEvent(_event);

        var split = _event.Split(';', '|', ':', '\n');

        if (split[0].Contains("AttachSwordToQuestGiver"))
        {
            AttachSwordToQuestGiver();
        }
        else if (split[0].Contains("EnableCaveEntranceBlocker"))
        {
            bool enabled = false;

            if (bool.TryParse(split[1], out enabled))
            {
                EnableCaveEntranceBlocker(enabled);
            }
        }
        else if (split[0].Contains("InitReferences"))
        {
            InitReferences();
        }
        else if (split[0].Contains("ReturnToQuestGiver"))
        {
            m_hasReturnedToQuestGiver = true;
        }
        else if (split[0].Contains("OpenGate"))
        {
            OpenGate();
        }
        else if (split[0].Contains("SetGateOpen"))
        {
            SetGateOpen();
        }
    }
    
    
    public class SaveLoadQuestCaveBlockerContainer : SaveLoadQuestBaseContainer
    {
        public SaveLoadQuestCaveBlockerContainer() : base() { }
        public SaveLoadQuestCaveBlockerContainer(MAQuestBase _base) : base(_base) { }
        [Save] public int m_gateOpened;
    }

    public override SaveLoadQuestBaseContainer Save()
    {
        var saveContainer = new SaveLoadQuestCaveBlockerContainer(this);
        saveContainer.m_gateOpened = m_gateOpened ? 1 : 0;

        return saveContainer;
    }

    public override void Load(SaveLoadQuestBaseContainer _l)
    {
        base.Load(_l);

        var saveContainer = _l as SaveLoadQuestCaveBlockerContainer;
        if (saveContainer != null)
        {
            m_gateOpened = saveContainer.m_gateOpened == 1;
        }
    }
    
}
