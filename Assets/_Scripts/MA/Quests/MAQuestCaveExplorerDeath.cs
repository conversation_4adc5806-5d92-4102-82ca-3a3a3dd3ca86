
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Serialization;

public class MAQuestCaveExplorerDeath : MAQuestBase
{
    public MAQuestInteraction m_interaction;
    private bool m_hasInteracted = false;
    public GameObject m_swordPrefab;
    private GameObject m_swordInstance;
    private bool m_isDead = false;
    
    public MAQuestInteraction m_pullSwordInteraction;
    
    private Vector3 m_swordStartPos;
    private Vector3 m_swordEndPos;
    private float m_swordLerpTime = 1.0f;
    private float m_swordLerpValue = 0f;
    private float m_swordPullDistance = 1.3f;
    private SwordState m_swordState = SwordState.Idle;
    
    private Transform m_bloodTarget;
    
    private BloodEmitter.BloodEmitterSettings m_bloodEmitterSettingsPullSword = new()
    {
        m_playOnStart = true,
        m_destroyOnFinish = true,
        m_destroyOnlyComponent = true,
        m_splatsPerSecond = 2.7f,
        m_emitCountPerSplat = 1,
        m_totalBleedTime = 0.65f
    };

    public enum SwordState
    {
        Idle,
        Pulling,
        Dropped
    }

    protected override void OnPostLoad()
    {
        InitReferences();
    }

    private void InitReferences()
    {
        if (m_questGiver == null)
        {
            var workerInfo = MAWorkerInfo.GetInfo(m_questWorkerInfoName);
            if (workerInfo != null)
            {
                m_questGiver = MAQuestGiver.Create(workerInfo, m_questGiverHolder.position);
                m_questGiver.CharacterGameState.m_immortal = true;
                
                MACharacterBase chr = m_questGiver.GetComponent<MACharacterBase>();
                chr.SetSubScene("Cave1");
            }
        }

        if (m_questGiver != null)
        {
            m_questGiver.transform.position = m_questGiverHolder.position;
        }
        
        if (m_questGiver != null)
        {
            LipSyncManager.Me.AddLipSyncer("CaveBlockerMetal", m_questGiver, -18.0f);
            
            if (!m_isDead)
            {
                m_questGiver.PlayLoopAnimation("WorkerSlumpedIdle", null); 
            }
            else
            {
                m_questGiver.PlayLoopAnimation("WorkerSlumpedDead", null);
                DisableHeadTracking();
            }
            
            if (m_swordState != SwordState.Dropped)
            {
                AttachSword();
                AddBloodTarget();
            }
        }

        if (m_interaction != null && !m_interaction.HasInteracted())
        {
            m_interaction.SetInteractive(true);
        }
        
    }

    private void Update()
    {
        UpdateSword();
    }

    private void UpdateSword()
    {
        if (m_swordState == SwordState.Pulling)
        {
            m_swordLerpValue += Time.deltaTime;

            if (m_swordLerpValue <= m_swordLerpTime)
            {
                Vector3 lerpPos = Vector3.Lerp(m_swordStartPos, m_swordEndPos, m_swordLerpValue);
                m_swordInstance.transform.localPosition = lerpPos;
            }
            else
            {
                DropSword();
            }
        }
    }
    
    public void OnPullSword()
    {
        m_swordInstance.transform.SetParent(null);
        m_swordStartPos = m_swordEndPos = m_swordInstance.transform.localPosition;
        m_swordEndPos.z += m_swordPullDistance;
        m_swordState = SwordState.Pulling;
        m_pullSwordInteraction.SetInteractive(false);
        m_pullSwordInteraction.SetInteracted(true);

        EmmitBlood();
    }

    public void DropSword()
    {
        Rigidbody rb = m_swordInstance.GetComponent<Rigidbody>();

        if (rb != null)
        {
            rb.isKinematic = false;
        }
        m_swordState = SwordState.Dropped;
    }

    public override float QuestObjectiveValue(string _objective)
    {
        if (_objective.Contains("HasInteracted"))
        {
            return m_hasInteracted ? 0.0f : 1.0f;
        }
        return 1.0f;
    }

    public override void TriggerQuestEvent(string _event)
    {
        base.TriggerQuestEvent(_event);

        var split = _event.Split(';', '|', ':', '\n');
        
        if (split[0].Contains("InitReferences"))
        {
            InitReferences();
        }
        else if (split[0].Contains("KillExplorer"))
        {
            KillExplorer();
        }
    }

    public override bool WaitForQuestEvent(string _event)
    {
        var split = _event.Split(';', '|', ':', '\n');

        if (split[0].Contains("SwordPulled"))
        {
            return m_swordState == SwordState.Dropped;
        }
        return false;
    }

    private void DisableHeadTracking()
    {
        HeadTracker headtracker = m_questGiver.GetComponent<HeadTracker>();
        if (headtracker != null)
        {
            headtracker.ForceUseAnimation(true);
        }
        else
        {
            Debug.LogWarning("DisableHeadTracking - HeadTracker component not found in " + m_questGiver.gameObject.name);
        }
    }

    private void KillExplorer()
    {
        StartCoroutine(Co_ExplorerDead());
    }

    private IEnumerator Co_ExplorerDead()
    {
        DisableHeadTracking();
        yield return new WaitForSeconds(0.5f);
        
        m_questGiver.PlaySingleAnimation("WorkerSlumpedDie",
            (b) => { m_questGiver.PlayLoopAnimation("WorkerSlumpedDead", null);});
        
        m_isDead = true;
        
        yield return new WaitForSeconds(1.5f);
        m_pullSwordInteraction.SetInteractive(true);
    }

    private void AttachSword()
    {
        if (m_questGiver != null && m_swordPrefab != null && m_swordInstance == null)
        {
            Transform spine = m_questGiver.transform.FindChildRecursiveByName("mixamorig:Spine2");
            
            m_swordInstance = Instantiate(m_swordPrefab, spine, false); 
            m_swordInstance.SetActive(true);
        }
    }

    private void AddBloodTarget()
    {
        if (m_questGiver != null)
        {
            Transform spine = m_questGiver.transform.FindChildRecursiveByName("mixamorig:Spine2");
            if (spine != null)
            {
                GameObject bloodTarget = new GameObject("BloodTarget");
                m_bloodTarget = bloodTarget.transform;
                m_bloodTarget.SetParent(spine, false);
                m_bloodTarget.localPosition = new Vector3(-0.094f, 0.058f, 0.196f);
            }
        }
    }

    private void EmmitBlood()
    {
        var bloodEmitter = m_bloodTarget.gameObject.AddComponent<BloodEmitter>();
        bloodEmitter.Play(m_bloodEmitterSettingsPullSword);
    }
    
    
    public void OnInteract()
    {
        m_hasInteracted = true;
        m_interaction.SetInteractive(false);
        m_interaction.SetInteracted(true);
    }
    
    public class SaveLoadQuestCaveDeathContainer : SaveLoadQuestBaseContainer
    {
        public SaveLoadQuestCaveDeathContainer() : base() { }
        public SaveLoadQuestCaveDeathContainer(MAQuestBase _base) : base(_base) { }
        [Save] public List<MAQuestInteraction.SaveState> m_interactionSaveStates;
        [Save] public int m_hasInteracted;
        [Save] public int m_isDead;
        [Save] public int m_swordState;
    }
    public override SaveLoadQuestBaseContainer Save()
    {
        var saveContainer = new SaveLoadQuestCaveDeathContainer(this);
        
        saveContainer.m_interactionSaveStates = GetInteractionSaveStates();
        saveContainer.m_hasInteracted = m_hasInteracted ? 1 : 0;
        saveContainer.m_isDead = m_isDead ? 1 : 0;
        saveContainer.m_swordState = (int)m_swordState;

        return saveContainer;
    }

    public override void Load(SaveLoadQuestBaseContainer _l)
    {
        base.Load(_l);

        var saveContainer = _l as SaveLoadQuestCaveDeathContainer;
        if (saveContainer != null)
        {
            SetInteractionSaveStates(saveContainer.m_interactionSaveStates);
            m_hasInteracted = saveContainer.m_hasInteracted == 1;
            m_isDead = saveContainer.m_isDead == 1;
            m_swordState = (SwordState)saveContainer.m_swordState;
        }
    }
    
    private List<MAQuestInteraction.SaveState> GetInteractionSaveStates()
    {
        List<MAQuestInteraction.SaveState> interactionSaveStates = new List<MAQuestInteraction.SaveState>();

        interactionSaveStates.Add(m_interaction.GetSaveState());
        interactionSaveStates.Add(m_pullSwordInteraction.GetSaveState());

        return interactionSaveStates;
    }

    private void SetInteractionSaveStates(List<MAQuestInteraction.SaveState> _interactionSaveStates)
    {
        if (_interactionSaveStates != null)
        {
            int i = 0;
            {
                if (i < _interactionSaveStates.Count)
                {
                    m_interaction.SetSaveState(_interactionSaveStates[i]);
                    m_pullSwordInteraction.SetSaveState(_interactionSaveStates[i]);
                }
            }
        }
    }
}
