
using System.Collections.Generic;
using UnityEngine;

public class MAQuestMessageInBottle : MAQuestBase
{
    public List<MAQuestInteraction> m_pickupBottles;
    private int m_bottlesCollectedCount = 0;
    private bool m_isBottleCollected = false;
    private bool m_messageDelivered = false;

    public AkEventHolder m_bottleCollected = new ();
    
    protected override void OnPostLoad()
    {
        // do not call base OnPostLoad(), .MOA file will create quest giver
    }

    public void OnPickupBottle(int _bottleIndex)
    {
        m_pickupBottles[_bottleIndex].SetInteractive(false);
        m_pickupBottles[_bottleIndex].SetCollidersEnable(false);
        m_pickupBottles[_bottleIndex].SetGlintEnable(false);

        if (Camera.main != null)
        {
            var pickupHolder = Camera.main.transform.GetComponentInChildren<MAPickupHolder>();

            if (pickupHolder != null)
            {
                Animator animator = m_pickupBottles[_bottleIndex].gameObject.GetComponentInChildren<Animator>();
                pickupHolder.Pickup(m_pickupBottles[_bottleIndex].gameObject, () => OnPickupComplete(_bottleIndex), animator);
                m_bottlesCollectedCount++;

                EnableBottleInteraction(false);
                
                m_bottleCollected.Play(m_pickupBottles[_bottleIndex].gameObject);

                // StartCoroutine(DelayedAction(() => 
                // {
                // }, m_pickUpMessageDelay));
                return;
            }
        }

        OnPickupComplete(_bottleIndex);
    }

    public void OnPickupComplete(int _bottleIndex)
    {
        m_pickupBottles[_bottleIndex].gameObject.SetActive(false);
        m_pickupBottles[_bottleIndex].SetInteracted(true);
        m_isBottleCollected = true;
    }

    private void EnableBottleInteraction(bool _enable)
    {
        foreach (var b in m_pickupBottles)
        {
            b.SetInteractive(_enable, null);
        }
    }

    private void EnableBottles(bool _enable)
    {
        foreach (var b in m_pickupBottles)
        {
            b.gameObject.SetActive(_enable);
        }
    }

    public override void QuestObjectiveActivate(string _objective)
    {
        if (_objective.Contains("CollectMessageBottle"))
        {
            m_isBottleCollected = false;
        }
    }

    public override float QuestObjectiveValue(string _objective)
    {
        if (_objective.Contains("CollectMessageBottle"))
        {
            return m_isBottleCollected ? 0.0f : 1.0f;
        }

        if (_objective.Contains("DeliverMessage"))
        {
            return m_messageDelivered ? 0.0f : 1.0f;
        }
        return 1.0f;
    }
    
    public override void OnDestroyMessage(MAMessage _message)
    {
        base.OnDestroyMessage(_message);
        EnableBottleInteraction(true);
    }
    
    public override void TriggerQuestEvent(string _event)
    {
        base.TriggerQuestEvent(_event);

        var split = _event.Split(';', '|', ':', '\n');

        if (split[0].Contains("DeliverMessage"))
        {
            DeliverMessage();
        }
        
        if (split[0].Contains("ActivateBottles"))
        {
            ActivateBottles();
        }
    }

    public void DeliverMessage()
    {
        m_messageDelivered = true;
    }
    
    public void ActivateBottles()
    {
        EnableBottles(true);
        EnableBottleInteraction(true);
    }
    
    public class SaveLoadQuestMessageInBottleContainer : SaveLoadQuestBaseContainer
    {
        public SaveLoadQuestMessageInBottleContainer() : base() { }
        public SaveLoadQuestMessageInBottleContainer(MAQuestBase _base) : base(_base) { }
        [Save] public int m_bottlesCollectedCount;
        [Save] public List<int> m_pickupBottles = new List<int>();
    }
    public override SaveLoadQuestBaseContainer Save()
    {
        var saveContainer = new SaveLoadQuestMessageInBottleContainer(this);
        saveContainer.m_bottlesCollectedCount = m_bottlesCollectedCount;

        foreach (var b in m_pickupBottles)
        {
            saveContainer.m_pickupBottles.Add(b.gameObject.activeSelf ? 1 : 0);
        }
        return saveContainer;
    }

    public override void Load(SaveLoadQuestBaseContainer _l)
    {
        base.Load(_l);
        
        var saveContainer = _l as SaveLoadQuestMessageInBottleContainer;
        if (saveContainer != null)
        {
            m_bottlesCollectedCount = saveContainer.m_bottlesCollectedCount;

            if(saveContainer.m_pickupBottles != null)
            {
                for (int i = 0; i < saveContainer.m_pickupBottles.Count; i++)
                {
                    if (i < m_pickupBottles.Count)
                    {
                        m_pickupBottles[i].gameObject.SetActive(saveContainer.m_pickupBottles[i] > 0);
                        m_pickupBottles[i].SetInteractive(saveContainer.m_pickupBottles[i] > 0);
                    }
                }
            }
        }
    }
}
