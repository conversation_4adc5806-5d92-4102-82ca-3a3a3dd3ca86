
using System.Collections.Generic;
using UnityEngine;

public class MAQuestRumBarrels : MAQuestBase
{
    private List<ExplodeInteraction> m_barrels;
    public Transform m_barrelHolder;

    public MAQuestInteraction m_caveNotice;
    private bool m_noticeRead = false;
    
    private MAFlowCharacter m_pirateCaptain, m_pirateFirstMate, m_pirateSecondMate;

    public GameObject m_caveBarrier;

    
    protected override void Awake()
    {
        base.Awake();
        AddBarrels();
    }
    
    protected override void OnPostLoad()
    {
        // do not call base OnPostLoad(), .MOA file will create quest giver
        InitReferences();
    }

    private void InitReferences()
    {
        if (m_pirateCaptain == null)
        {
            m_pirateCaptain =
                NGManager.Me.m_MACharacterList.Find(x => x.GetTypeInfo() == "PirateCaptain") as MAFlowCharacter;
        }
        
        if (m_pirateFirstMate == null)
        {
            m_pirateFirstMate =
                NGManager.Me.m_MACharacterList.Find(x => x.GetTypeInfo() == "PirateFirstMate") as MAFlowCharacter;
        }
        
        if (m_pirateCaptain != null)
        {
            LipSyncManager.Me.AddLipSyncer("PirateCaptain", m_pirateCaptain, -18.0f);
            LipSyncManager.Me.SetGestureSet("PirateCaptain", "Default");
        }
        
        if (m_pirateFirstMate != null)
        {
            LipSyncManager.Me.AddLipSyncer("PirateFirstMate", m_pirateFirstMate, -18.0f);
            LipSyncManager.Me.SetGestureSet("PirateFirstMate", "Default");
        }
    }

    private void AddBarrels()
    {
        m_barrels = new List<ExplodeInteraction>();

        foreach (Transform child in m_barrelHolder)
        {
            ExplodeInteraction explodeInteraction = child.GetComponent<ExplodeInteraction>();
            if (explodeInteraction != null)
            {
                m_barrels.Add(explodeInteraction);
            }
        }
    }
    
    private int ExplodeBarrelCount()
    {
        int count = 0;
        foreach (var barrel in m_barrels)
        {
            if (barrel.isActiveAndEnabled) count++;
        }
        return count;
    }
    
    private void SetBarrelsAttackable(bool _isAttackable)
    {
        foreach (var barrel in m_barrels)
        {
            barrel.m_isAttackable = _isAttackable;
            
            Rigidbody rb = barrel.GetComponent<Rigidbody>();
            if (rb != null)
            {
                rb.isKinematic = false;
            }
        }
    }

    private void SetCaveNoteInteractable(bool _isInteractable)
    {
        m_caveNotice.SetInteractive(_isInteractable);
    }

    private void EnableCaveBarrier(bool _enabled)
    {
        m_caveBarrier.SetActive(_enabled);
    }


    
    public override float QuestObjectiveValue(string _objective)
    {
        if (_objective.Contains("SmashAll"))
        {
            return ExplodeBarrelCount();
        }
        if (_objective.Contains("ReadNotice"))
        {
            return m_noticeRead ? 0.0f : 1.0f;
        }
        return 1.0f;
    }

    public override void TriggerQuestEvent(string _event)
    {
        base.TriggerQuestEvent(_event);

        var split = _event.Split(';', '|', ':', '\n');
        
        if (split[0].Contains("InitReferences"))
        {
            InitReferences();
        }

        if (split[0].Contains("SetBarrelsAttackable"))
        {
            bool isAttackable = false;

            if (bool.TryParse(split[1], out isAttackable))
            {
                SetBarrelsAttackable(isAttackable);
            }
        }
        else if (split[0].Contains("SetCaveNoteInteractable"))
        {
            SetCaveNoteInteractable(true);
        }
        else if (split[0].Contains("EnableCaveBarrier"))
        {
            bool enable = false;

            if (bool.TryParse(split[1], out enable))
            {
                EnableCaveBarrier(enable);
            }
        }
    }
    
    public void OnReadCaveNotice()
    {
        m_noticeRead = true;
        SetCaveNoteInteractable(false);
        m_caveNotice.SetInteracted(true);
    }
    
    public class SaveLoadQuestRumBarrelsContainer : SaveLoadQuestBaseContainer
    {
        public SaveLoadQuestRumBarrelsContainer() : base() { }
        public SaveLoadQuestRumBarrelsContainer(MAQuestBase _base) : base(_base) { }
        [Save] public List<MAQuestInteraction.SaveState> m_interactionSaveStates;
        [Save] public int caveBarrierEnabled;
    }
    public override SaveLoadQuestBaseContainer Save()
    {
        var saveContainer = new SaveLoadQuestRumBarrelsContainer(this);
        
        saveContainer.m_interactionSaveStates = GetInteractionSaveStates();
        saveContainer.caveBarrierEnabled = m_caveBarrier.activeSelf ? 1 : 0;

        return saveContainer;
    }

    public override void Load(SaveLoadQuestBaseContainer _l)
    {
        base.Load(_l);

        var saveContainer = _l as SaveLoadQuestRumBarrelsContainer;
        if (saveContainer != null)
        {
            SetInteractionSaveStates(saveContainer.m_interactionSaveStates);
            EnableCaveBarrier(saveContainer.caveBarrierEnabled == 1);
        }
    }
    
    private List<MAQuestInteraction.SaveState> GetInteractionSaveStates()
    {
        List<MAQuestInteraction.SaveState> interactionSaveStates = new List<MAQuestInteraction.SaveState>();

        interactionSaveStates.Add(m_caveNotice.GetSaveState());

        return interactionSaveStates;
    }

    private void SetInteractionSaveStates(List<MAQuestInteraction.SaveState> _interactionSaveStates)
    {
        if (_interactionSaveStates != null)
        {
            int i = 0;
            {
                if (i < _interactionSaveStates.Count)
                {
                    m_caveNotice.SetSaveState(_interactionSaveStates[i]);
                }
            }
        }
    }
    
}
