using System.Collections;
using System.Collections.Generic;
using NUnit.Framework.Constraints;
using TMPro;
using UnityEngine;

public class MAQuestSimonStones : MAQuestBase
{

    [Header("MAQuestSimonStones")]
    public List<MAQuestSimonStone> m_stones = new List<MAQuestSimonStone>();
    public MAQuestCutscene m_completeCutscene;
    public MAQuestCutscene m_hippyLevitateCutscene;
    public Animator m_hippyLevitateAnimator;
    public ParticleSystem m_hippyLevitateParticle;
    public MAQuestCameraTransition m_dialogueCameraTransition;
    public AkSwitchHolder m_completeAudio = new();
    float m_playStoneDelay = 1.5f;
    Coroutine m_playStonesCoroutine;
    Coroutine m_stopGesturingCoroutine;
    Animator m_animator;
    int m_playTuneID;
    int m_playStonesID;
    int m_negativeID;
    int m_disturbID;
    int m_tapID;
    private bool m_wasSkipped = false;

    public enum QuestSimonState
    {
        Ready,
        PlayTune,
        Input,
        Complete,
        Failed,
        Levitated,
        Count
    }
    
    public enum HippyQuestGiverState
    {
        Idle = 0,
        Gesture = 1,
        Positive = 2,
        Celebrate = 3,
        Negative,
        Disturb,
        Count
    }

    // Tunes are defined by the note value 1 = C, 12 = B,
    static private byte[][] m_puzzleTunes =
    {
        new byte[] { 3, 6, 5 },
        new byte[] { 5, 6, 10, 8 },
        new byte[] { 6, 5, 8, 10, 12 },
        new byte[] { 8, 10, 12, 3, 5, 6 },
        new byte[] { 3, 6, 5, 8, 6, 10, 8, 12 },
        new byte[] { 12, 8, 10, 6, 8, 3, 5, 1 }
    };

    static private byte[] m_scaleSweep = { 1, 3, 5, 6, 8, 10, 12}; 
    
    
    private float m_puzzleTuneNoteLength = 0.65f;
    private float m_sweepNoteLength = 0.06f;
    
    private int m_puzzleTuneLevel = 0;
    private int m_puzzleTuneIndex = 0;
    private bool m_continuePuzzleOnDialogueComplete = false;
    private bool m_puzzleAttemptMade = false;
    private bool m_isFirstAttempt = true;
    
    private bool m_isEnabled = false;
    public List<GameObject> m_questElements = new List<GameObject>();
    
    QuestSimonState m_simonState;

    protected override void Awake()
    {
        base.Awake();
        m_animator = GetComponent<Animator>();
        m_playTuneID = Animator.StringToHash("PlayTune");
        m_playStonesID = Animator.StringToHash("PlayStones");      
        m_negativeID = Animator.StringToHash("Negative");
        m_disturbID = Animator.StringToHash("Disturb");
        m_tapID = Animator.StringToHash("Tap");
    }
    
    protected override void OnPostLoad()
    {
        base.OnPostLoad();
        if (m_questGiver != null)
        {
            m_questGiver.CharacterGameState.m_immortal = true;
        }
    }
    
    private void EnableQuestElements(bool _enable)
    {
        m_isEnabled = _enable;
        foreach (var item in m_questElements)
        {
            item.SetActive(m_isEnabled);
        }
    }
    
    // Used for end cutscene animation
    public void OnNoteEvent(int index)
    {
        var stone = m_stones.Find(x => x.m_stoneNum == index);
        if (stone)
        {
            stone.PlayAnim(MAQuestSimonStone.StonesAnimState.NoSound);
        }
    }

    void PlayNote(int index)
    {
        var stone = m_stones.Find(x => x.m_stoneNum == index);
        if (stone)
        {
            stone.PlayAnim(MAQuestSimonStone.StonesAnimState.Correct);
        }
    }

    void OnPlayCompleteAudioEvent()
    {
        // m_completeAudio.Play(gameObject, AkEventHolder.EBus.Music);
        //KW: seems to be some weirdness with this animation event when cutscene is skipped
        if (!m_wasSkipped)
        {
            m_completeAudio.SetAsOverride();
        }
    }

    void OnCompleteEndEvent()
    {
        SetQuestGiverState((int)HippyQuestGiverState.Idle);
    }

    IEnumerator StartPuzzleTune()
    {
        yield return new WaitForSeconds(m_playStoneDelay);
        
        m_simonState = QuestSimonState.PlayTune;
        m_puzzleTuneIndex = 0;
        
        SetQuestGiverState((int)HippyQuestGiverState.Idle);
        StartCoroutine(PlayPuzzleTune());
    }

    IEnumerator PlayCutscene()
    {
        SetQuestGiverState((int)HippyQuestGiverState.Positive);
        
        yield return new WaitForSeconds(1.0f);
        SetQuestGiverState((int)HippyQuestGiverState.Celebrate);

        m_wasSkipped = false;

        yield return m_completeCutscene.Co_Play(() =>
        {
            m_wasSkipped = true;
        });

        m_puzzleAttemptMade = true;
        m_simonState = QuestSimonState.Complete;
    }

    public MAQuestSimonStone.StonesAnimState ReceiveStoneTap(int stoneNum)
    {
        if (m_simonState == QuestSimonState.Input)
        {
            if (stoneNum == m_puzzleTunes[m_puzzleTuneLevel][m_puzzleTuneIndex])
            {
                // Note is correct
                SetQuestGiverState((int)HippyQuestGiverState.Positive);
                m_puzzleTuneIndex++;

                if (m_puzzleTuneIndex >= m_puzzleTunes[m_puzzleTuneLevel].Length)
                {
                    // Tune is complete
                    m_puzzleTuneLevel++;
                    m_simonState = QuestSimonState.Ready;
                    if (m_puzzleTuneLevel < m_puzzleTunes.Length)
                    {
                        // If this is the first "practice" tune, log the attempt and continue puzzle after dialogue
                        if (m_puzzleTuneLevel == 1)
                        {
                            m_puzzleAttemptMade = true;
                            m_continuePuzzleOnDialogueComplete = true;
                        }
                        // If not the first tune, move on to the next one
                        else
                        {
                            StartCoroutine(StartPuzzleTune());
                        }
                    }
                    // All tunes are completed
                    else
                    {

                        StartCoroutine(PlayCutscene());
                    }
                }

                return MAQuestSimonStone.StonesAnimState.Correct;

            }

            // Note is incorrect
            m_puzzleAttemptMade = true;
            SetQuestGiverState((int)HippyQuestGiverState.Negative);
            m_simonState = QuestSimonState.Failed;
            return MAQuestSimonStone.StonesAnimState.Wrong;
        }

        return MAQuestSimonStone.StonesAnimState.NoAnim;
    }

    IEnumerator PlayHippyLevitate()
    {
        m_questGiver.SetKinematic(true, false);
        m_questGiver.SetState(NGMovingObject.STATE.MA_ANIMATOR_CONTROLLED);
        SetQuestGiverState((int)HippyQuestGiverState.Celebrate);
        m_simonState = QuestSimonState.Levitated;
        if (m_hippyLevitateParticle != null)
        {
            m_hippyLevitateParticle.Play();
        }
        yield return m_hippyLevitateCutscene.Co_Play();
    }

    public void DeativateQuestGiver()
    {
        m_questGiverHolder.gameObject.SetActive(false);
    }
    
    IEnumerator PlayPuzzleTune()
    {
        // Iterate through each note and play it
        for (int i = 0; i < m_puzzleTunes[m_puzzleTuneLevel].Length; i++)
        {
            PlayNote(m_puzzleTunes[m_puzzleTuneLevel][i]);
            yield return new WaitForSeconds(m_puzzleTuneNoteLength); // Wait for the duration of the note
        }
        // enable input after the last note has finished
        m_simonState = QuestSimonState.Input;
    }

    IEnumerator PlayScaleSweep()
    {
        for (int i = 0; i < m_scaleSweep.Length; i++)
        {
            PlayNote(m_scaleSweep[i]);
            yield return new WaitForSeconds(m_sweepNoteLength); // Wait for the duration of the note
        }
    }
    
    override public void SetQuestGiverState(int _questGiverState)
    {
        if (m_questGiver != null && m_questGiver.m_anim != null)
        {
            switch((HippyQuestGiverState)_questGiverState)
            {
                case HippyQuestGiverState.Negative:
                    m_questGiver.m_anim.SetTrigger(m_negativeID);
                    break;
                case HippyQuestGiverState.Disturb:
                    m_questGiver.m_anim.SetTrigger(m_disturbID);
                    break;
                default:
                    base.SetQuestGiverState(_questGiverState);
                    break;
            }
        }
    }

    private void ResetQuest()
    {
        m_simonState = QuestSimonState.Ready;
        m_puzzleTuneLevel = 0;
        m_puzzleTuneIndex = 0;
        m_continuePuzzleOnDialogueComplete = false; 
        m_puzzleAttemptMade = false;
    }
    
    protected override void OnActivateQuest()
    {
        base.OnActivateQuest();
        ResetQuest();
        SetQuestGiverState((int)HippyQuestGiverState.Disturb);
        // m_dialogueCameraTransition.Transition();
    }
    
    public override void OnAcceptChallenge()
    {
        StartCoroutine(StartPuzzleTune());
        m_isFirstAttempt = false;
    }

    public override void OnDisplayMessage(MAMessage _message)
    {
        base.OnDisplayMessage(_message);

        if (_message.m_advisor == "Hippy")
        {
            // if (m_stopGesturingCoroutine != null)
            // {
            //     StopCoroutine(m_stopGesturingCoroutine);
            //     m_stopGesturingCoroutine = null;
            // }

            SetQuestGiverState((int)HippyQuestGiverState.Gesture);
        }
    }

    public override void OnDestroyMessage(MAMessage _message)
    {
        base.OnDestroyMessage(_message);

        if (m_questGiver != null && m_questGiver.m_anim.GetInteger(m_stateID) == (int)HippyQuestGiverState.Gesture && m_stopGesturingCoroutine == null)
        {
            m_stopGesturingCoroutine = StartCoroutine(Co_StopGesturing());
        }

        if (m_continuePuzzleOnDialogueComplete)
        {
            StartCoroutine(StartPuzzleTune());
            m_continuePuzzleOnDialogueComplete = false;
        }

        if (m_simonState == QuestSimonState.Failed)
        {
            OnCancelChallenge();
        }
        
        if(m_simonState == QuestSimonState.Complete)
        {
            StartCoroutine(PlayHippyLevitate());
        }
    }

    IEnumerator Co_StopGesturing()
    {
        yield return new WaitForSeconds(0.3f);
        if (m_simonState != QuestSimonState.Levitated)
        {
            SetQuestGiverState((int)HippyQuestGiverState.Idle);
        }
        m_stopGesturingCoroutine = null;
    }
    
    
    public override float QuestObjectiveValue(string _objective)
    {
        if (_objective.Contains("AttemptMade"))
        {
            float ret = m_puzzleAttemptMade ? 0.0f : 1.0f;
            if (m_puzzleAttemptMade)
                m_puzzleAttemptMade = false;
            return ret;
        }
        if (_objective.Contains("TuneCompleted"))
        {
            return m_puzzleTuneLevel > 0 ? 0.0f : 1.0f;
        }
        if (_objective.Contains("PuzzleCompleted"))
        {
            return m_simonState == QuestSimonState.Complete ? 0.0f : 1.0f;
        }
        if (_objective.Contains("IsFirstAttempt"))
        {
            return m_isFirstAttempt ? 0.0f : 1.0f;
        }

        return 1.0f;
    }
    
    public override void TriggerQuestEvent(string _event)
    {
        base.TriggerQuestEvent(_event);

        var split = _event.Split(';', '|', ':', '\n');

        if (split[0].Contains("HideQuestElements"))
        {
            EnableQuestElements(false);
        }
        else if (split[0].Contains("ShowQuestElements"))
        {
            EnableQuestElements(true);
        }
        else if (split[0].Contains("PlayScaleSweep"))
        {
            StartCoroutine(PlayScaleSweep());
        }
        else if (split[0].Contains("ContinuePuzzle"))
        {
            StartCoroutine(StartPuzzleTune());
        }
    }
    
    public class SaveLoadQuestSimonStonesContainer: SaveLoadQuestBaseContainer
    {
        public SaveLoadQuestSimonStonesContainer() : base() { }  
        public SaveLoadQuestSimonStonesContainer(MAQuestBase _base) : base(_base) { }
        [Save] public int m_questGiverIsActive;
        [Save] public int m_isEnabled;
    }
    public override SaveLoadQuestBaseContainer Save()
    {
        var saveContainer = new SaveLoadQuestSimonStonesContainer(this);
        if (m_questGiverHolder != null)
        {
            saveContainer.m_questGiverIsActive = m_questGiverHolder.gameObject.activeSelf && m_simonState != QuestSimonState.Levitated ? 1 : 0;
        }
        saveContainer.m_isEnabled = m_isEnabled  ? 1 : 0;
        return saveContainer;
    }
    
    public override void Load(SaveLoadQuestBaseContainer _l)
    {
        var saveContainer =_l as SaveLoadQuestSimonStonesContainer;
        base.Load(_l);
        if (saveContainer != null)
        {
            if (m_questGiverHolder != null)
            {
                m_questGiverHolder.gameObject.SetActive(saveContainer.m_questGiverIsActive > 0);
            }
            
            EnableQuestElements(saveContainer.m_isEnabled > 0);
        }
    }
}
