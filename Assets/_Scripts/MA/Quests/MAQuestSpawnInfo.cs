using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
[Serializable]
public class MAQuestSpawnInfo
{ 
    public static List<MAQuestSpawnInfo> s_questSpawnInfo = new List<MAQuestSpawnInfo>();
    public static List<MAQuestSpawnInfo> GetList=>s_questSpawnInfo;
    public string DebugDisplayName => m_name;

    public static IList GetTheList() => s_questSpawnInfo;

    public enum QuestTypeEnum
    {
        SideQuest,
        MainQuest,
        OneShot
    }
    public enum ActivationTypeEnum 
    {
        ByDistrict,
        ByCommand,
        AlwaysOn
    }

    public string id;
    public bool m_debugChanged;
    public string m_name;
    public string m_questType;
    public string m_activationType;
    public string m_activationCommand;
    [ScanField] public string m_character;
    [ScanField] public string m_advisor;
    
    public QuestTypeEnum QuestType => (QuestTypeEnum)Enum.Parse(typeof(QuestTypeEnum), m_questType);
    public ActivationTypeEnum ActivationType => (ActivationTypeEnum)Enum.Parse(typeof(ActivationTypeEnum), m_activationType);
    public MAWorkerInfo CharacterInfo => MAWorkerInfo.GetInfo(m_character);
    public NGBusinessAdvisor Advisor => NGBusinessAdvisor.GetInfo(m_advisor);
    public static bool PostImport(MAQuestSpawnInfo _what)
    {
        return true;
    }
    public static List<MAQuestSpawnInfo> LoadInfo()  // Must be loaded after blocks
    {
        s_questSpawnInfo = NGKnack.ImportKnackInto<MAQuestSpawnInfo>(PostImport);
        return s_questSpawnInfo;
    }

    public static MAQuestSpawnInfo GetInfo(string _name) => s_questSpawnInfo.Find(o => o.m_name.Equals(_name, StringComparison.OrdinalIgnoreCase));
}
