using UnityEngine;

public class MAQuestValmeysInterlude : MAQuestBase
{
    private float m_minFollowDistance = 30.0f;
    private float m_maxFollowDistance = 40.0f;
    private MAFlowCharacter m_valmey;
    private bool m_isFollowing = false;
    private bool m_hasTarget = false;
    
    public float m_followSpeedScale = 4.0f;

    override protected void OnPostLoad()
    {
        // do not call base OnPostLoad(), .MOA file will create quest giver
        InitReferences();
    }

    private void Update()
    {
        if(m_isFollowing)
        {
            UpdateFollowing();
        }
    }

    private void InitReferences()
    {
        if (m_valmey == null)
        {
            m_valmey = NGManager.Me.m_MACharacterList.Find(x => x.GetTypeInfo() == "Valmey") as MAFlowCharacter;
            
            if (m_valmey != null)
            {
                LipSyncManager.Me.AddLipSyncer("Valmey", m_valmey, -14.0f);
            }
        }
    }

    public override void TriggerQuestEvent(string _event)
    {
        base.TriggerQuestEvent(_event);

        var split = _event.Split(';', '|', ':', '\n');

        if (split[0].Contains("InitReferences"))
        {
            InitReferences();
        }

        if (split[0].Contains("SetFollowing"))
        {
            bool isFollowing = false;
            if (bool.TryParse(split[1], out isFollowing))
            {
                SetFollowing(isFollowing);
            }
        }
    }

    private void SetFollowing(bool _isFollowing)
    {
        m_isFollowing = _isFollowing;
        m_valmey.SetState(NGMovingObject.STATE.MA_DECIDE_WHAT_TO_DO);
        m_hasTarget = false;
    }

    private void UpdateFollowing()
    {
        if(m_valmey != null)
        {
            var possessed = GameManager.Me.PossessedCharacter;
            float possessedDistanceSqr = 0.0f;

            if(possessed != null)
            {
                possessedDistanceSqr = (possessed.transform.position - m_valmey.transform.position).xzSqrMagnitude();
            }

            if(m_hasTarget)
            {
                if(possessed == null || possessedDistanceSqr > (m_maxFollowDistance * m_maxFollowDistance))
                {
                    m_valmey.SetState(NGMovingObject.STATE.MA_DECIDE_WHAT_TO_DO);
                    m_hasTarget = false;
                }

                // KW: catch any unexpected states
                if(m_valmey.m_state == NGMovingObject.STATE.MA_DECIDE_WHAT_TO_DO)
                {
                    m_hasTarget = false;
                }
            }
            else
            {
                if(possessed != null && possessedDistanceSqr <= (m_minFollowDistance * m_minFollowDistance))
                {
                    MACharacterBase.TargetResult target = new MACharacterBase.TargetResult();
                    target.m_targetObject = TargetObject.Create(possessed.gameObject);
                    m_valmey.SetTargetObj(target);
                    float followSpeedMulti = 1 / (NavAgent.FinalSpeedMultiplier == 0 ? 1f : NavAgent.FinalSpeedMultiplier);
                    m_valmey.SetSpeed(GameManager.Me.PossessedCharacter.m_possessionFwdWalkSpeed * followSpeedMulti);
                    m_valmey.SetToChaseObject();
                    
                    m_hasTarget = true;
                }
            }
        }
    }

    public class SaveLoadQuestValmeysInterludeContainer : SaveLoadQuestBaseContainer
    {
        public SaveLoadQuestValmeysInterludeContainer() : base() { }
        public SaveLoadQuestValmeysInterludeContainer(MAQuestBase _base) : base(_base) { }
        [Save] public int m_isFollowing;
    }

    public override SaveLoadQuestBaseContainer Save()
    {
        var saveContainer = new SaveLoadQuestValmeysInterludeContainer(this);

        saveContainer.m_isFollowing = m_isFollowing ? 1 : 0;

        return saveContainer;
    }

    public override void Load(SaveLoadQuestBaseContainer _l)
    {
        base.Load(_l);

        var saveContainer = _l as SaveLoadQuestValmeysInterludeContainer;
        if (saveContainer != null)
        {
            m_isFollowing = saveContainer.m_isFollowing > 0;
        }
    }
}
