using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

public class MAQuestWyrmscarBridge : MAQuestBase
{
    [System.Serializable]
    public class BridgeSection
    {
        public Transform m_plankHolder, m_bridgeStartTransform;
        public NavStrut m_navStrut;

        [System.NonSerialized]
        public List<GameObject> m_missingPlanks = new List<GameObject>();
    }

    public BridgeSection[] m_bridgeSections;

    public Transform m_bridgeStartTransform, m_waitTransform;
    public BuildingNavBlocker m_navBlocker;
    public NavStrut m_finalNavStrut;

    // Constants
    private const int c_planksPerTimber = 5;
    private const int c_totalPlanks = 60;
    
    // settings to be made public
    private float m_plankBuildOffset = 2.0f;
    private float m_targetSqrDistanceThreshold = 0.5f;
    private float m_replacePlanksDelay = 0.2f;

    private MAFlowCharacter m_mineEngineer;
    private BCStockIn m_stockpile;
    private Transform m_faceTarget = null;

    // saved data
    private bool m_isStockpileEnabled = false;
    private int m_planksReplaced = 0;
    
    private int m_stockpileCount;
    private int m_timberRequired = c_totalPlanks / c_planksPerTimber;

    private int RemainingPlanks => Math.Max(TotalPlanks() - m_planksReplaced, 0);

    protected override void Awake()
    {
        base.Awake();
        InitPlanks();
        SetPlanksReplaced(0);
    }

    private void Update()
    {
        switch (m_status)
        {
            case QuestStatus.InProgress:
                UpdateEngineerState();
                UpdateStockCount();
                break;
        }
    }

    override protected void OnPostLoad()
    {
        // do not call base OnPostLoad(), .MOA file will create quest giver
        InitReferences();
    }

    private void InitPlanks()
    {
        foreach (var section in m_bridgeSections)
        {
            for (int i = 0; i < section.m_plankHolder.childCount; i++)
            {
                GameObject plank = section.m_plankHolder.GetChild(i).gameObject;

                if (!plank.activeSelf)
                {
                    section.m_missingPlanks.Add(plank);
                }
            }

            section.m_missingPlanks = section.m_missingPlanks.OrderBy(x => (x.transform.position - section.m_navStrut.LiveWorldStart).xzSqrMagnitude()).ToList();
        }
    }

    private void InitReferences()
    {
        if (m_mineEngineer == null)
        {
            m_mineEngineer = NGManager.Me.m_MACharacterList.Find(x => x.GetTypeInfo() == "MineEngineer") as MAFlowCharacter;
        }
        
        if (m_mineEngineer != null)
        {
            LipSyncManager.Me.AddLipSyncer("MineEngineer", m_mineEngineer, -24.0f);
        }

        if (m_stockpile == null)
        {
            MABuilding building = MABuilding.FindBuilding("QuestWyrmscarBridgeStockpile", true);

            if (building != null)
            {
                foreach (var stock in building.StockIns)
                {
                    if (stock != null)
                    {
                        m_stockpile = stock;
                        break;
                    }
                }
            }

            SetStockpileEnabled(m_isStockpileEnabled);

            UpdateTimberRequired();
        }
    }

    private void UpdateEngineerState()
    {
        if(m_mineEngineer != null && m_stockpile != null)
        {
            CheckResetMineEngineer();

            if (m_mineEngineer.IsDecideWhatToDo)
            {
                if (m_mineEngineer.Carrying != null)
                {
                    Vector3 targetPos = GetBuildPosition();

                    if ((targetPos - m_mineEngineer.transform.position).xzSqrMagnitude() < m_targetSqrDistanceThreshold)
                    {
                        m_mineEngineer.DestroyCarriedObject();

                        float delay = 0.0f;

                        int planksToReplace = Mathf.Min(c_planksPerTimber, CurrentSectionTotalPlanks() - m_planksReplaced);

                        for(int i = 0; i < planksToReplace; i++)
                        {
                            StartCoroutine(DelayedAction(() =>
                            {
                                ReplacePlanks(1);
                            }, delay));

                            delay += m_replacePlanksDelay;
                        }
                    }
                    else
                    {
                        m_mineEngineer.SetMoveToPosition(targetPos);
                        IncrementPathAttempts(0);
                        SetHeadTrackerTarget(m_mineEngineer, null);     
                    }
                }
                else
                {
                    int stockCount = m_stockpile.GetStock().GetStock(GetResource());

                    if ((m_waitTransform.position - m_mineEngineer.transform.position).xzSqrMagnitude() > m_targetSqrDistanceThreshold)
                    {
                        m_mineEngineer.SetMoveToPosition(m_waitTransform.position);
                        IncrementPathAttempts(1);
                        m_faceTarget = m_stockpile.Building.transform;      
                    }
                    else if (stockCount > 0 && RemainingPlanks > 0)
                    {
                        m_mineEngineer.SetMoveToBuilding(m_stockpile.Building, PeepActions.CollectPickup);
                        IncrementPathAttempts(2);
                        SetHeadTrackerTarget(m_mineEngineer, m_stockpile.Building.transform);       
                    }
                    else if (m_faceTarget != null && !m_mineEngineer.IsTurning)
                    {
                        SetHeadTrackerTarget(m_mineEngineer, m_faceTarget);
                        
                        if (!m_mineEngineer.IsFacing(m_faceTarget.position, 10.0f))
                        {
                            m_mineEngineer.LookAt(m_faceTarget.position, 120.0f);
                        }

                        m_faceTarget = null;
                        IncrementPathAttempts(-1);
                    }
                }
            }
        }
    }

    public override void TriggerQuestEvent(string _event)
    {
        base.TriggerQuestEvent(_event);

        var split = _event.Split(';', '|', ':', '\n');

        if (split[0].Contains("InitReferences"))
        {
            InitReferences();
        }

        if (split[0].Contains("EnableStockpile"))
        {
            bool enabled = false;

            if (bool.TryParse(split[1], out enabled))
            {
                SetStockpileEnabled(enabled);
            }
        }
    }

    public override bool WaitForQuestEvent(string _event)
    {
        var split = _event.Split(';', '|', ':', '\n');

        if (split[0].Contains("BridgeComplete"))
        {
            return RemainingPlanks == 0;
        }
        
        return false;
    }

    public override float QuestObjectiveValue(string _objective)
    {
        // if (_objective.Contains("BridgeComplete"))
        // {
        //     return Mathf.Max(TotalPlanks() - m_planksReplaced, 0.0f);
        // }
        if (_objective.Contains("TimberRequired"))
        {
            return m_timberRequired;
        }

        return 1.0f;
    }

    private void UpdateStockCount()
    {
        if (m_stockpile != null)
        {
            int stockpileCount = m_stockpile.GetStock().GetStock(GetResource());
            
            if (stockpileCount != m_stockpileCount)
            {
                m_stockpileCount = stockpileCount;
                UpdateTimberRequired();
            }
        }
    }

    private void UpdateTimberRequired()
    {
        if (m_mineEngineer != null)
        {
            // Carried planks
            int carriedStock = m_mineEngineer.Carrying != null ? 1 : 0;
            // Number of planks remaining to place
            int remainingPlanks = Math.Max(TotalPlanks() - m_planksReplaced, 0);

            m_timberRequired = (remainingPlanks / c_planksPerTimber) - m_stockpileCount - carriedStock;
            
            // Debug.Log($"timberRequired = {m_timberRequired}, stockCount = {m_stockpileCount}, carriedStock = {carriedStock}, remainingPlanks = {remainingPlanks}");
        }
    }

    private void PrintPlanks()
    {
        foreach(var section in m_bridgeSections)
        {
            Debug.Log($">>> section.m_missingPlanks.Count = {section.m_missingPlanks.Count}");
        }
    }

    private int TotalPlanks()
    {
        int totalPlanks = 0;

        foreach(var section in m_bridgeSections)
        {
            totalPlanks += section.m_missingPlanks.Count;
        }

        return totalPlanks;
    }

    private int CurrentSection()
    {
        int planksReplaced = m_planksReplaced;

        for(int i = 0; i < m_bridgeSections.Length; i++)
        {
            if(planksReplaced < m_bridgeSections[i].m_missingPlanks.Count)
            {
                return i;  
            }
            else
            {
                planksReplaced -= m_bridgeSections[i].m_missingPlanks.Count;
            }
        }

        return m_bridgeSections.Length - 1;
    }

    private int CurrentSectionTotalPlanks()
    {
        int totalPlanks = 0;

        foreach (var section in m_bridgeSections)
        {
            totalPlanks += section.m_missingPlanks.Count;

            if(totalPlanks > m_planksReplaced)
            {
                break;
            }
        }

        return totalPlanks;
    }

    private GameObject GetNextPlankToReplace()
    {
        int plankIndex = m_planksReplaced;

        foreach (var section in m_bridgeSections)
        {
            if(plankIndex < section.m_missingPlanks.Count)
            {
                return section.m_missingPlanks[plankIndex];   
            }
            else
            {
                plankIndex -= section.m_missingPlanks.Count;
            }
        }

        return null;
    }

    private void SetStockpileEnabled(bool _enabled)
    {
        if (m_stockpile != null)
        {
            m_stockpile.GetStock().RemoveEmptyStockAndClearNeededToProduce();

            if (_enabled)
            {
                m_stockpile.GetStock().AddOrCreateStock(GetResource(), 0, 1f);
            }
        }

        m_isStockpileEnabled = _enabled;
    }

    private void ReplacePlanks(int _count)
    {
        SetPlanksReplaced(Mathf.Min(TotalPlanks(), m_planksReplaced + _count));
    }

    private void SetPlanksReplaced(int _planksReplaced)
    {
        m_planksReplaced = _planksReplaced;

        int index = 0;
        int sectionPlanks = 0;

        for(int i = 0; i < m_bridgeSections.Length; i++)
        {
            BridgeSection section = m_bridgeSections[i];
            
            for (int j = 0; j < section.m_missingPlanks.Count; j++, index++)
            {
                section.m_missingPlanks[j].SetActive(index < m_planksReplaced);
            }

            section.m_navStrut.gameObject.SetActive(m_planksReplaced >= sectionPlanks);
            sectionPlanks += section.m_missingPlanks.Count;
        }

        //SetNavBlockerActive(m_planksReplaced < TotalPlanks());
        m_finalNavStrut.gameObject.SetActive(m_planksReplaced >= TotalPlanks());
    }

    private NGCarriableResource GetResource()
    {
        return NGCarriableResource.GetInfo("Timber");
    }

    private Vector3 GetBuildPosition()
    {
        var bs = m_bridgeSections[CurrentSection()];
        GameObject plank = GetNextPlankToReplace();

        if(plank == null)
        {
            return bs.m_bridgeStartTransform.position;
        }
        else
        {
            Vector3 toBridgeStart = (bs.m_bridgeStartTransform.position - plank.transform.position).normalized;
            Vector3 buildPos = plank.transform.position + toBridgeStart * m_plankBuildOffset;

            Vector3 strutPos1 = bs.m_navStrut.ClosestContainedPoint(buildPos, out bool overflow);
            var navStrut2 = bs.m_navStrut.GetComponentInChildren<NavStrut>();

            if(navStrut2 != null)
            {
                Vector3 strutPos2 = navStrut2.ClosestContainedPoint(buildPos, out overflow);
                return (strutPos1 - buildPos).xzSqrMagnitude() < (strutPos2 - buildPos).xzSqrMagnitude() ? strutPos1 : strutPos2;
            }
            else
            {
                return strutPos1;
            }
        }
    }

    private void SetNavBlockerActive(bool _isActive)
    {
        PathBlock pathBlock = m_navBlocker.GetComponent<PathBlock>();

        if(pathBlock != null)
        {
            pathBlock.m_isActive = _isActive;
        }
    }

    private float m_checkResetMineEngineerTime = 0.0f;
    private float m_lastMineEngineerNavTime = -1.0f;
    private int[] m_mineEngineerNavAttempts = new int[] { 0, 0, 0 };
    private const float m_checkResetMineEngineerInterval = 5.0f;
    private const float m_maxMineEngineerYOffset = 10.0f;
    private const float m_maxMineEngineerDistanceSqr = 40000.0f;
    private const float m_maxMineEngineerNavTime = 300.0f;
    private const int m_maxMineEngineerPathAttempts = 5;

    private void CheckResetMineEngineer()
    {
        m_checkResetMineEngineerTime -= Time.deltaTime;

        if(m_checkResetMineEngineerTime <= 0.0f)
        {
            m_checkResetMineEngineerTime = m_checkResetMineEngineerInterval;
            Vector3 offset = m_waitTransform.position - m_mineEngineer.transform.position;

            bool shouldReset = offset.y > m_maxMineEngineerYOffset || offset.sqrMagnitude > m_maxMineEngineerDistanceSqr;
            shouldReset |= m_lastMineEngineerNavTime >= 0.0f && (Time.time - m_lastMineEngineerNavTime) > m_maxMineEngineerNavTime;

            foreach(var attempts in m_mineEngineerNavAttempts)
            {
                shouldReset |= attempts > m_maxMineEngineerPathAttempts;
            }

            if (shouldReset)
            {
                m_mineEngineer.m_nav.Pause(true, true);
                m_mineEngineer.transform.position = m_waitTransform.position;
            }
        }
    }

    private void IncrementPathAttempts(int _index)
    {
        m_lastMineEngineerNavTime = _index >= 0 && _index < m_mineEngineerNavAttempts.Length ? Time.time : -1.0f;

        for(int i = 0; i < m_mineEngineerNavAttempts.Length; i++)
        {
            if(i == _index)
            {
                m_mineEngineerNavAttempts[i]++;
            }
            else
            {
                m_mineEngineerNavAttempts[i] = 0;
            }
        }
    }

    public class SaveLoadQuestWyrmscarBridgeContainer : SaveLoadQuestBaseContainer
    {
        public SaveLoadQuestWyrmscarBridgeContainer() : base() { }
        public SaveLoadQuestWyrmscarBridgeContainer(MAQuestBase _base) : base(_base) { }
        [Save] public int m_isStockpileEnabled;
        [Save] public int m_planksReplaced;
        [Save] public int m_stockpileCount;
    }

    public override SaveLoadQuestBaseContainer Save()
    {
        var saveContainer = new SaveLoadQuestWyrmscarBridgeContainer(this);

        saveContainer.m_isStockpileEnabled = m_isStockpileEnabled ? 1 : 0;
        saveContainer.m_planksReplaced = m_planksReplaced;
        saveContainer.m_stockpileCount = m_stockpileCount;

        return saveContainer;
    }

    public override void Load(SaveLoadQuestBaseContainer _l)
    {
        base.Load(_l);

        var saveContainer = _l as SaveLoadQuestWyrmscarBridgeContainer;
        if (saveContainer != null)
        {
            m_isStockpileEnabled = saveContainer.m_isStockpileEnabled > 0;
            SetPlanksReplaced(saveContainer.m_planksReplaced);
            m_stockpileCount = saveContainer.m_stockpileCount;
        }
    }
}
