#if CHOICES
using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using Random = UnityEngine.Random;

public class DesignGaugeScore : BaseUIController {
	[System.Serializable]
	public class DesignGaugeRows
	{
		public DesignGaugeRows(string _label, string _help, bool _isPie = false)
		{
			m_rowLabel = _label;
			m_helpText = _help;
			m_isPie = _isPie;
		}
		public string m_rowLabel;
		public string m_helpText;
		public bool m_isPie;
	}

	private List<DesignGaugeRows> m_gaugeRows = new List<DesignGaugeRows>()
	{
		{ new DesignGaugeRows("Part Score", "Part score is rewarded for having the required number parts in your design")},
		{ new DesignGaugeRows("Quality Score", "Quality score, the more parts you use with better rarity is better")},
		{ new DesignGaugeRows("Paint Score", "The more parts of your design that are painted the better this score will ")},
		{ new DesignGaugeRows("Sticker Score", "The more parts of your design that have stickers the better your score will be")},
		{ new DesignGaugeRows("Pattern Score", "The more parts of your design that have patterns the better your score will be")},
		{ new DesignGaugeRows("Business Level Score", "As you unlock the business flow so this score will increase")},
		{ new DesignGaugeRows("Uniqueness Score", "Every design you make will be compared to every other players design you score for uniqueness")},
		{ new DesignGaugeRows("Ranking Score", "The score this design gets is ranked with other players giving you a bonus if you design is high enough")},
		{ new DesignGaugeRows("Boredom Score", "If you use the same parts over and over your boredom score will go down")},
		{ new DesignGaugeRows("Compatibility Score", "Using just parts from this product line will give you a boost")},
		{ new DesignGaugeRows("Demand Score", "How much your demand for your product line", true)},
		//
		{ new DesignGaugeRows("Price Modifier", "How the number of parts in your product affects the price")},
	};
	public DesignGaugeScoreRow m_designGaugeScoreRowPrefab;
	public GameObject m_rowPrefab;
	public GameObject m_totalPrefab;
	public GameObject m_table;
	public TextMeshProUGUI m_title;
	[SerializeField] private TextMeshProUGUI m_designGaugePlayerLevelBonus = default;
	[SerializeField] private TextMeshProUGUI m_designGaugeScoreTotal = default;
	public enum Rows {
		Part, Paint, Sticker, Pattern, BusinessLevel, Boredom, Ranking/**/, Quality/**/, Uniqueness/**/, Compatibility/**/, Demand/**/, PriceModifier/**/,
		Count,
	};
	
	public readonly Dictionary<Rows, string> m_rowLabels = new() { 
		{ Rows.Part, "Part Score" }, { Rows.Paint,"Paint Score" }, { Rows.Sticker,"Sticker Score" }, 
		{ Rows.Pattern,"Pattern Score" }, { Rows.BusinessLevel, "Business Level Score" }, { Rows.Boredom, "Boredom" }, 
		{ Rows.Ranking, "Ranking" }, { Rows.Quality ,"Quality Score" }, { Rows.Uniqueness, "Uniqueness Score" },
		{ Rows.Compatibility, "Compatibility" }, { Rows.Demand,"Demand" }, { Rows.PriceModifier,"Price Modifier" }};

	void Init() {

		Debug.Assert(NGManager.Me.m_useOldScoreSystem == false, "DesignGaugeScore - Init called with NGManager.Me.m_useOldScoreSystem flag set to true");
		
		DesignGaugeScoreRow[] preExistingRows = m_table.transform.GetComponentsInChildren<DesignGaugeScoreRow>(true);
		
		m_title.text = DesignTableManager.Me.DesignModeTitle;

		List<Tuple<NGBlockInfo, float>> partData = DesignTableManager.Me.CurrentDesignIndividualPartScores;

		float total = 0;
		float totalWithoutBoredom = 0;
		int iRow = -1;
		if (partData.Count > 0)
		{
	//		total = DesignUtilities.GetRelevantTotalScoreFromList(DesignTableManager.Me.DesignIndividualScores);
			totalWithoutBoredom = total - DesignTableManager.Me.DesignIndividualScores[(int)Rows.Boredom];

			if (total > 0)
			{
				for (iRow = 0; iRow < partData.Count; iRow++)
				{
					TryCreateRow(partData[iRow].Item1.m_displayName, partData[iRow].Item2);
				}

				if (TryCreateRowByEnum(Rows.Paint)) iRow++;
				if (TryCreateRowByEnum(Rows.Sticker)) iRow++;
				if (TryCreateRowByEnum(Rows.Pattern)) iRow++;
        	
				m_designGaugePlayerLevelBonus.text = $"{(DesignTableManager.Me.DesignIndividualScores[(int)Rows.BusinessLevel] / totalWithoutBoredom) *100f:F1}%";
			}
		}

		ScrollRect r = GetComponentInChildren<ScrollRect>();
		r.vertical = iRow + 1 > preExistingRows.Length;
		
		m_designGaugeScoreTotal.text = $"{total*100:F2}";
		
		bool TryCreateRow(string rowLabel, float rowScore)
        {
            DesignGaugeScoreRow newRow = iRow < preExistingRows.Length ? preExistingRows[iRow] : null;
            
            if (rowScore > 0)
            {
                float value = rowScore / totalWithoutBoredom;
                if (newRow)
                {
                	newRow.Activate(rowLabel, value);
                }
                else
                {
                	DesignGaugeScoreRow.Create(m_designGaugeScoreRowPrefab, m_table.transform, rowLabel, value);
                }
                return true;
            }
            return false;
        }
		
		bool TryCreateRowByEnum(Rows row) { return TryCreateRow( m_rowLabels[row], DesignTableManager.Me.DesignIndividualScores[(int)row]); }
	}

	Func<string, string, List<PieChart.PieChartData>> m_pieFunc;

	public static PieChart.PieChartDataContainer GetPieChartData()
	{
		var dummyPieData = new List<PieChart.PieChartData>();
		foreach (var p in NGProductInfo.s_allProducts)
		{
			if (p.Value.IsActive && p.Value.m_lowMarket.IsZero() == false)
			{
				var hiRange = (p.Value.m_starterPack) ? 1000 : 200;
				dummyPieData.Add(new PieChart.PieChartData(p.Key, Random.Range(hiRange/100, hiRange)));
			}
		}

		var container = new PieChart.PieChartDataContainer()
		{
			m_title = "Product Demand",
			m_body = "Shows the demand for all products on the Legacy Market",
			m_data = dummyPieData
		};
		return container;
	}
	void SetRow(Transform _row, string _label, float _value) {
		_row.GetComponentInChildren<TextMeshProUGUI>(true).text = _label;
		_row.GetComponentInChildren<ProgressBar_v0>(true).fillAmount = _value;
	}
	void SetTotals(Transform _totals, string _value) {
		_totals.GetChild(1).GetComponent<TextMeshProUGUI>().text = _value;
	}
	
	public static DesignGaugeScore Open() {
		var dgs = InfoPlaqueManager.Me.LoadUI<DesignGaugeScore>();
		dgs.Init();
		dgs.Show();
		return dgs;
	}

	private System.Action m_refresh;
	protected override void Awake()
	{
		m_refresh = () => Init();
		DesignTableManager.OnRefreshInfo += m_refresh;
		base.Awake();
	}
	void OnDestroy()
	{
		DesignTableManager.OnRefreshInfo -= m_refresh;
	}
	public void ClickedClose()
	{
		HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
		Destroy(gameObject);
	}
}
#endif