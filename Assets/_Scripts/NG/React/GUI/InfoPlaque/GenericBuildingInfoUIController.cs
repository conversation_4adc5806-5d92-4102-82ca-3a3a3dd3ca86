using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class GenericBuildingInfoUIController : BaseBezierClosableUIController
{
	public UITextPro m_titleText;
	public UITextPro m_capacityText;
    [SerializeField]
	private ProgressBar_v0 capacityProgressBar;
	public UITextPro m_capacityNumber;
	public UITextPro m_statusText;
	public UITextPro m_workersGatheringText;
	public UITextPro m_spousesGatheringText;
	public UITextPro m_buildingDescription;
	public UIButton m_designButton;
	public UITextPro m_designButtonText;
	public UIButton m_ejectButton;
	public UITextPro m_ejectButtonText;

	private NGCommanderBase m_building;
	private ReactItemInfo.BuyItem m_item;
	private float m_toDoCapacity = 10f;

	private string m_todo = "To Do";

   	public void Setup(NGCommanderBase _building){
		Setup(_building.transform);
		m_building = _building;
		//m_item = ItemDetailsHelper.GetBuyItemByPrefabName(m_building.m_stateData.m_prefab) as ReactItemInfo.BuyItem;
		SetStaticText();
		UpdateUIStats(_building);
	}

	private void SetStaticText(){
		m_titleText.text = m_building.Name;
		m_capacityText.text = Localizer.Get(TERM.GUI_INFO_CAPACITY);
		m_buildingDescription.text = m_building.DescriptionText;
		m_ejectButtonText.text = Localizer.Get(TERM.GUI_EJECT);
		m_designButtonText.text = Localizer.Get(TERM.GUI_DESIGN);
	}

	private void UpdateUIStats(NGCommanderBase _building = null)
	{
	}

	protected override void Update() {
		base.Update();
		UpdateUIStats();
	}

	protected override void OnDestroy() {
		m_building = null;
		base.OnDestroy();
	}

}
