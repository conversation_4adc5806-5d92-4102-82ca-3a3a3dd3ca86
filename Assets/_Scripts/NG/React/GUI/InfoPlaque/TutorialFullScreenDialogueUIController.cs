
using UnityEngine;

public class TutorialFullScreenDialogueUIController : FullScreenUIController {
	public UITextPro Text;

	public void Setup (string _text) {
		Text.text = _text;
        if (GameManager.Me.IsOKToPlayUISound())
            AudioClipManager.Me.PlaySoundOld("PlaySound_WindowBubble_Large02", transform);
	}

    protected override void Close_Internal(bool _playCloseSound = true) {
		base.Close_Internal(_playCloseSound);
    }
}
