using System.Collections.Generic;
using UnityEngine;

[System.Serializable]
public class ReactDistrictTable 
{
	public const string ImagePath = "_Art/Districts/";
	public enum CostType
	{
		None,
		Money,
		Gems,
	}

	public bool m_debugChanged;
	public string id;
	public string m_districtID;
	public string m_districtName; 
	public string m_districtFlavourText; 
	public int m_moneyCost;
	public int m_legacyGemCost;
	public bool m_isDistrictLocked; // Is land purchasable by default.
	public string m_imageName;
	public string m_regionUnlockScript;
	public Sprite ImageSprite;
	public static List<ReactDistrictTable> s_records = new List<ReactDistrictTable>();
	public static List<ReactDistrictTable> GetList => s_records;
	public string DebugDisplayName => $"{m_districtName}[{m_districtID}]";
	public static ReactDistrictTable GetEntryByID( string ID ) {
		for( int i = 0; i < s_records.Count; ++i ) {
			var entry = s_records[i];
			if( entry.m_districtID == ID ) {
				return entry;
			}
		}
		
		Debug.LogError( $"District ID \"{ID}\" not found.");
		return default;
	}
	
	public static ReactDistrictTable GetInfo(string _name)
	{
		return s_records.Find(x => x.m_districtID.Equals(_name, System.StringComparison.OrdinalIgnoreCase));
	}

	public (CostType type, int cost) GetCost()
	{
		if (m_legacyGemCost != 0)
			return (CostType.Gems, m_legacyGemCost);
		if (m_moneyCost != 0)
			return (CostType.Money, m_moneyCost);
		return (CostType.None, 0);
	}
	
	public static bool PostImportARecord(ReactDistrictTable _what)
	{
		if (_what.m_districtID.IsNullOrWhiteSpace())
		{
			Debug.LogError($"ReactDistrictTable district has no id {_what.m_districtID} {_what.m_districtName}");
			return false;
		}
		if(_what.m_imageName.IsNullOrWhiteSpace() == false)
		{
			_what.ImageSprite = Resources.Load<Sprite>($"{ImagePath}{_what.m_imageName.Trim()}");
		}
		return true; 
	}
	public static List<ReactDistrictTable> LoadInfo()
	{
		//This will download (or read from cache) all records on Knack NB can be called without the PostImportAReacord.
		s_records = NGKnack.ImportKnackInto<ReactDistrictTable>(PostImportARecord);
		return s_records;
	}
}
