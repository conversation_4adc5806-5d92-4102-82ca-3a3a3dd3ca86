using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class BusinessFlowTutorialMessage : MAGUIBase
{
    public TMP_Text m_message;
    public Image m_advisor;
    
    private NGBusinessFlow m_flow;
    private System.Action m_tappedAction;

    public void ClickedMe()
    {
        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
        if (GameManager.Me.IsOKToPlayUISound())
            AudioClipManager.Me.PlaySoundOld("PlaySound_TextBoxClose", transform);
        if (m_tappedAction != null)
            m_tappedAction();
        DestroyMe();
    }

    public void DestroyMe()
    {
        if (s_lastTutorialMessage == this)
            s_lastTutorialMessage = null;
        Destroy(gameObject);
    }

    void Activate(NGBusinessFlow _flow, System.Action _tappedAction, string _message)
    {
        base.Activate();
        m_flow = _flow;
        m_tappedAction = _tappedAction;
        if (_message != null)
        {
            m_message.text = _message;
        }
        else
        {
            //TODO: PDM:Change Advisor sprite depending on the advisor
            
           // m_message.text = NGManager.AnalyseTextForHTML(m_flow.m_message, m_message.maxWidth);
            m_message.text = m_flow.m_message;
  
#if OldBusinessFlow
            switch (_flow.FlowType)
            {
                case    NGBusinessFlow.Type.Message:
                    //TODO:Implement FullImageSprite
//                    if (m_flow.BusinessAdvisor != null)
//                        m_advisor.sprite = m_flow.BusinessAdvisor.FullImageSprite;
                    break;
                case    NGBusinessFlow.Type.PopupMessage:
                    if (m_flow.BusinessAdvisor != null)
                        m_advisor.sprite = m_flow.BusinessAdvisor.PortaitSprite;
                    break;
            }
#endif
        }
    }

    public static BusinessFlowTutorialMessage s_lastTutorialMessage;
    public static BusinessFlowTutorialMessage Create(NGBusinessFlow _flow, System.Action _tappedAction = null, string _message = null)
    {
        var prefab = NGBusinessDecisionManager.Me.m_businessFlowTutorialMessageBig.gameObject;
        if (_flow != null)
        {
#if OldBusinessFlow
            switch (_flow.FlowType)
            {
                case    NGBusinessFlow.Type.Message:
                    break;
                case NGBusinessFlow.Type.PopupContinue:
                case NGBusinessFlow.Type.PopupMessage:
                    prefab = NGBusinessDecisionManager.Me.m_businessFlowTutorialMessagePopup.gameObject;
                    break;
            }
#endif
        }
        var go = Instantiate(prefab, NGBusinessDecisionManager.Me.m_businessFlowTutorialMessageBigHolder);
        var bftmb = go.GetComponent<BusinessFlowTutorialMessage>();
        bftmb.Activate(_flow, _tappedAction, _message);
        s_lastTutorialMessage = bftmb;
        return bftmb;
    }
}
