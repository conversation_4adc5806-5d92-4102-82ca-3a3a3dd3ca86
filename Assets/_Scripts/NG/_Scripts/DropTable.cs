using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[System.Serializable]
public class DropTable
{
	public string id;
	public bool m_debugChanged;
	public string m_rarity;
	public int m_dropChance;
	public int m_pityTimer;
	public float m_maxFitnessBonus;
	public float m_minFitnessBonus;
	public int m_partLifetime;
	public float m_duplicateAgeRecovery;
	public string m_localizationKey;
	
	public static List<DropTable> s_records = new();
	public static List<DropTable> GetList=>s_records;
	public string DebugDisplayName => m_rarity;

	public static bool PostImportARecord(DropTable _what)
	{
		if (_what.m_rarity.IsNullOrWhiteSpace())
		{
			Debug.LogError($"DropTable item has no rarity");
			return false;
		}
		ResearchLabRewardsController.AddToRewardDropTable(_what.m_rarity, _what.m_dropChance, _what.m_pityTimer);
		ResearchLabRewardsController.AddToFitnessTable(_what.m_rarity, _what.m_maxFitnessBonus, _what.m_minFitnessBonus, _what.m_partLifetime, _what.m_duplicateAgeRecovery, _what.m_localizationKey);
		return true;
	}
	public static List<DropTable> LoadInfo()
	{
		//This will download (or read from cache) all records on Knack NB can be called without the PostImportAReacord.
		s_records = NGKnack.ImportKnackInto<DropTable>(PostImportARecord);
		return s_records;
	}
}
