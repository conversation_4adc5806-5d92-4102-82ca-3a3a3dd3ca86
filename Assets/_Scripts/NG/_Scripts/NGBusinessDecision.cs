using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;
using System.Linq;
using System.Reflection;
using SevenZip.CommandLineParser;
using Object = System.Object;

[System.Serializable]
public class NGBusinessDecision : ICloneable
{
    public class MADecision : Attribute { }

    public const string None = "None";
    public const string ValueString = "[Value]";
    public const string TargetValueString = "[TargetValue]";
    public const string PowerString = "[Power]";
    public const string TimeString = "[Time]";
    public const string Param1String = "[Param1]";
    public const string Param2String = "[Param2]";

    public string id;
    public bool m_debugChanged;
    public string m_name;
    public string m_explainText;
    public string m_type;
    public float m_targetValue;
    public float m_initialValue;
    public string m_giftTitle;
    public float m_repeatCount;
    public string m_power;
    public string m_param1 = "";
    public string m_param2 = "";
    public string m_execute;
    public string m_clickedText;
    [ScanField] public string m_advisor;
    public bool m_activated = false;
    public static List<NGBusinessDecision> s_decisions = new();
    public static List<NGBusinessDecision> GetList=>s_decisions;

    public string DebugDisplayName => m_name;
    private long m_spentSinceStart;
    private long m_initialSpent;
    public string m_currentData = "";
    public MethodInfo m_methordInfoActivate = null;
    public MethodInfo m_methordInfoDisplay = null;
    public MethodInfo m_methordInfoValue = null;

    private MACharacterBase m_rangeCheckCharacter;

    public float TargetValue
    {
        get => m_targetValue;
        set => m_targetValue = value;
    }

    public List<NGBusinessGift> m_gifts = new List<NGBusinessGift>();

    public bool IsTriggered => GetValue() <= 0f  || m_type.Equals("None", StringComparison.OrdinalIgnoreCase);

    public void ResolveCalls()
    {
        var t = m_type.Trim();
        m_methordInfoActivate = GetType().GetMethod(t + "Activate");
        m_methordInfoDisplay = GetType().GetMethod(t + "Display");
        m_methordInfoValue = GetType().GetMethod(t + "Value");
    }

    public static bool PostImport(NGBusinessDecision _what)
    {
        if (_what.m_type.IsNullOrWhiteSpace() == false)
        {
            _what.ResolveCalls();
        }
        return true;
    }

    public static List<NGBusinessDecision> LoadInfo()
    {
        if (NGKnack.PDMFlowDecisionInfo)
            s_decisions = NGKnack.ImportKnackInto<NGBusinessDecision>(PostImport, "NGBusinessDecision-PDM");
        else
            s_decisions = NGKnack.ImportKnackInto<NGBusinessDecision>(PostImport);
        return s_decisions;
    }

    public static NGBusinessDecision GetInfo(string _name) => s_decisions.Find(o => o.m_name == _name);

    public string GetGiftTitle()
    {
        var text = m_giftTitle;
        if (text.Contains("<Value>"))
        {
            text = text.Replace("<Value>", (NGBusinessDecisionManager.Me.m_currentLevel + 1).ToString());
        }
        return text;
    }

    public void Save(ref SaveContainers.SaveCountryside _s)
    {
        _s.m_saveBusinessDecision.m_currentInitialValue = m_initialValue;
        _s.m_saveBusinessDecision.m_currentTargetValue = m_targetValue;
        _s.m_saveBusinessDecision.m_currentData = m_currentData;
    }

    public void SetupFromLoad(SaveContainers.SaveCountryside _l)
    {
        m_initialValue = _l.m_saveBusinessDecision.m_currentInitialValue;
        m_targetValue = _l.m_saveBusinessDecision.m_currentTargetValue;
    }

    public void Activate()
    {
        if (m_execute.IsNullOrWhiteSpace() == false)
        {
            if (MAParserSupport.TryParse(m_execute, out var triggered, $"Decision[{m_name}]") == false)
            {
                Debug.LogError(MAParserSupport.DebugColor($"[{m_execute}] Not Implemented Flow Trigger[{m_name}"));
                return;
            }
        }

        if (m_methordInfoActivate == null)
        {
//            Debug.LogError($"{m_name} Has No Activate function for {m_type}");
            return;
        }

        m_methordInfoActivate.Invoke(this, new object[] { });
        m_activated = true;
    }
    
    public string GetCurrentExplainText()
    {
        if (m_methordInfoDisplay != null)
        {
            return m_methordInfoDisplay.Invoke(this, new object[] { }) as string;
        }
        return GetDefaultExplainText();
    }

    public float GetValue()
    {
        if (m_methordInfoValue == null)
        {
            Debug.LogError($"{m_name} Has no Value function for {m_type}");
            return 0f;
        }

        var value = m_methordInfoValue.Invoke(this, new object[] { });
        return (float) value;
    }
    
    public string GetDefaultExplainText()
    {
        var ret = m_explainText.Replace(ValueString, Mathf.Max(0f, GetValue()).ToString("F0"));
        ret = ret.Replace(TargetValueString, Mathf.Max(0f, m_targetValue).ToString("F0"));
        ret = ret.Replace(Param1String, m_param1);
        ret = ret.Replace(Param2String, m_param2);
        return ret;
    }

    public bool GetPosFromPower(out Vector3 _pos)
    {
        _pos = Vector3.zero;
        if(m_power.StartsWith("Pos"))
            _pos = (Vector3)MAParserSupport.ConvertPos(m_power.Split('[', ']'));
        else if (m_power.StartsWith("Building"))
        {

            var building = (MABuilding)MAParserSupport.ConvertBuilding(m_power.Split('[', ']'));
            if(building == null)
            {
                MAParser.ParserError($"No such building as {m_power} in {m_name}");
                return false;
            }
            _pos = building.transform.position;
        }
        else if (m_power.StartsWith("Character"))
        {
            var character = (MACharacter)MAParserSupport.ConvertCharacter(m_power.Split('[', ']'));
            if(character == null)
            {
                MAParser.ParserError($"No such character as {m_power} in {m_name}");
                return false;
            }
            _pos = character.transform.position;
        }
        else
        {
            MAParser.ParserError($"illegal value  in m_power {m_power} in {m_name}");
            return false;
        }
        return true;
    }
    #region TypeFunctions
    
    [MADecision] public float OpenArcadiumValue() => MAResearchManagerUI.m_isActive ? 0 : 1;
    [MADecision] public float SpeedupToNightValue() => MAParser.WaitForNight() ? 0 : 1;
    
    public void DestroyBouldersActivate()
    {
        var pSplit = m_power.Split('[', ']');
        var building = MABuilding.FindBuilding(pSplit[1]);
        if (building)
        {
            m_initialValue = (building.IsLocked) ? 1f : 0f;
        }
        m_targetValue = 0;
    }

    [MADecision] public float DestroyBouldersValue()
    {
        var pSplit = m_power.Split('[', ']');
        var building = MABuilding.FindBuilding(pSplit[1]);
        if (building)
        {
            m_initialValue = (building.IsLocked) ? 1f : 0f;
        }
        return (int) (m_initialValue - m_targetValue);
    }
    //******* UnlockResearchItem
    public string UnlockResearchItemDisplay()
    {
        var text = GetDefaultExplainText();
        var rinfo = MAResearchInfo.GetInfo(m_power.Trim());
        if (rinfo == null)
        {
            MAParser.ParserError($"No such research as {m_power}");
            return text;
        }
        text = text.Replace("[Title]", rinfo.m_title);
        var cost = rinfo.m_dollarCost - NGPlayer.Me.m_cash.Balance;
        if (cost > 0)
        {
            text = text.Replace("[Money]", $"<sprite=0>{cost:F0}");
        }
        else
        {
            text = text.Replace("[Money]", "");
        }
        var favours = rinfo.m_factionCost - NGPlayer.Me.GetFavors(rinfo.m_faction);
        if(favours > 0)
        {
            if (cost > 0) text = text.Replace("[Favours]", " and [Favours]");
            text = text.Replace("[Favours]", $"{MAFactionInfo.GetFactionSprite(rinfo.m_faction)}{favours}");
        }
        else
        {
            text = text.Replace("[Favours]", "");
        }
        if(cost <= 0 && favours <= 0)
        {
            text = text.Replace("You Need .", "Unlock Now!");
            text = text.Replace("You need .", "Unlock Now!");
            text = text.Replace("You need", "Unlock Now!");
            text = text.Replace("You Need", "Unlock Now!");
        }
        return text;     
    }

    public float UnlockResearchItemValue()
    {
        var researchInfo = MAResearchInfo.GetInfo(m_power.Trim());
        if (researchInfo == null)
        {
            MAParser.ParserError($"No such research item field as {m_power}");
            return 1f;
        }
        return researchInfo.IsAcquired ? 0f : 1f;
    }
    //******* UnlockResearch
    public string UnlockResearchDisplay()
    {
        var text = GetDefaultExplainText();
        var powerSplit = m_power.Split('=');
        var rinfo = MAResearchInfo.GetInfoByUnlock(powerSplit[0].Trim());
        if (rinfo == null)
        {
            MAParser.ParserError($"No such research as {m_power}");
            return text;
        }
        text = text.Replace("[Title]", rinfo.m_title);
        var cost = rinfo.m_dollarCost - NGPlayer.Me.m_cash.Balance;
        if (cost > 0)
        {
            text = text.Replace("[Money]", $"<sprite=0>{cost:F0}");
        }
        else
        {
            text = text.Replace("[Money]", "");
        }
        var favours = rinfo.m_factionCost - NGPlayer.Me.GetFavors(rinfo.m_faction);
        if(favours > 0)
        {
            if (cost > 0) text = text.Replace("[Favours]", " and [Favours]");
            text = text.Replace("[Favours]", $"{MAFactionInfo.GetFactionSprite(rinfo.m_faction)}{favours}");
        }
        else
        {
            text = text.Replace("[Favours]", "");
        }
        if(cost <= 0 && favours <= 0)
        {
            text = text.Replace("You Need .", "Unlock Now!");
            text = text.Replace("You need .", "Unlock Now!");
            text = text.Replace("You need", "Unlock Now!");
            text = text.Replace("You Need", "Unlock Now!");
        }
        return text; 
    }
    [MADecision] public float UnlockResearchValue()
    {
        var powerSplit = m_power.Split('=');
        var unlockField = MAUnlocks.Me.GetValue(powerSplit[0].Trim());
        if (unlockField == null)
        {
            MAParser.ParserError($"No such field as {m_power}");
            return 1f;
        }
        var currentBool = unlockField as bool?;

        if (powerSplit.Length > 1)
        {
            var unlockValue = unlockField as float?;
            if (float.TryParse(powerSplit[1].Trim(), out var result) == false ||unlockValue == null)
            {
                MAParser.ParserError($"illegal value in {m_power}");
                return 1f;
            }
            return (unlockValue >= result) ? 0f : 1f;
        }
        return currentBool.Value ? 0f : 1f;
    }
    //*******BuilddWall
    public string BuildWallDisplay() => m_explainText.Replace(ValueString, Mathf.Max(0f, GetValue()*100).ToString("F0"));
    public void BuildWallActivate()
    {
    //was Pos[-105;30]
        var pos = (Vector3)MAParserSupport.ConvertPos(m_power.Split('[', ']'));
        var path = RoadManager.Me.m_pathSet.GetPlayerPathNearPoint(pos);
        if (path == null)
        {
            MAParser.ParserError($"No wall found at [{m_power}]{pos}");
            return;
        }
        m_initialValue = path.GetCompletedPercent();
        //m_initialValue = path.TotalLength() * path.GetCompletedPercent();
    }
    public float BuildWallValue()
    {
        var pos = (Vector3)MAParserSupport.ConvertPos(m_power.Split('[', ']'));
        var path = RoadManager.Me.m_pathSet.GetPlayerPathNearPoint(pos);
        if (path == null)
        {
            MAParser.ParserError($"No wall found at [{m_power}]{pos}");
            return 99;
        }

        var completed = Mathf.Clamp(path.GetCompletedPercent(), 0.001f, 1f);
        if (completed >= 1f)
            return 0f;
        return completed;        
    }
    public float BuildWallValueOld()
    {
        var pos = (Vector3)MAParserSupport.ConvertPos(m_power.Split('[', ']'));
        var path = RoadManager.Me.m_pathSet.GetPlayerPathNearPoint(pos);
        if (path == null)
        {
            MAParser.ParserError($"No wall found at [{m_power}]{pos}");
            return 99;
        }
        
        var completed =  path.GetCompletedPercent();
        var length = path.TotalLength()*completed;
        var seekLength = (length-m_initialValue)+0.001f;

        if (seekLength >= TargetValue) return 0;
        return seekLength / TargetValue;
        return completed >= TargetValue ? 0 : completed+0.01f;
    }
    //*******GrimShawsInspection
    public float GrimshawInspectionValue()
    {
        if (MAParserManager.Me.IsSectionActive("TakeInspection"))
            return 1f;
        return 0f;
    }
    //*******WaitForParser
    public float WaitForParserValue()
    {
        if (MAParserManager.m_updatingMaParserSection == null)
            return 1;

        MAParserSupport.TryParse(m_power.Trim(), out var result, $"Decision[{m_name}]");
        if (result == true) return 0;
        return 1;
    }
    //*******DesigndWall
    public void DesignWallActivate()
    {
        // Check if we have a remembered wall length from a RememberWallLength() call
        var rememberedWallLength =MAParser.Memory("InitialWallLength");
        if (rememberedWallLength.IsNullOrWhiteSpace() == false)
        {
            m_initialValue = float.Parse(rememberedWallLength);
            MAParser.Forget("InitialWallLength");
            return;
        }
        // Else use the wall length at the time of activation
        var pos = (Vector3)MAParserSupport.ConvertPos(m_power.Split('[', ']'));
        var path = RoadManager.Me.m_pathSet.GetPlayerPathNearPoint(pos);
        if (path == null)
        {
            MAParser.ParserError($"No wall found at [{m_power}]{pos}");
            return;
        }
        m_initialValue = path.TotalLength();
    }
    public float DesignWallValue()
    {
        var pos = (Vector3)MAParserSupport.ConvertPos(m_power.Split('[', ']'));
        var path = RoadManager.Me.m_pathSet.GetPlayerPathNearPoint(pos);
        if (path == null)
        {
            MAParser.ParserError($"No wall found at [{m_power}]{pos}");
            return 99;
        }

        //var value = m_targetValue - (path.TotalLength() - m_initialValue);
       // if(Mathf.Approximately(value, 0.001f)) value = 0;
        var length = path.TotalLength()- m_initialValue;
        if(length >= TargetValue) return 0;
        if(length < 0) length = 0;
        var remaining = TargetValue - length;
        if (remaining < 1) return 0.001f;
        return remaining;
    }
    //*******DesignProduct
    public void DesignProductActivate() => m_initialValue = 0;

    [MADecision]
    public float DesignProductValue()
    {
        var factoryBuildings = MABuilding.GetBuildingsWithComponent<BCFactory>();
        foreach (var building in factoryBuildings)
        {
            if(building.Order.IsValid && building.Order.HasPlayerDesigned)
            {
                if(m_power.IsNullOrWhiteSpace() == false && building.Order.OrderInfo != MAOrderInfo.GetInfo(m_power))
                    return 1f;
                return 0f;
            }
        }
        return 1f;
    }
    //*******GiftRecived
    public string GiftRecivedDisplay()
    {
        var text = m_explainText;
        if (m_power.IsNullOrWhiteSpace() == false)
        {
            var gift = NGBusinessGift.GetInfo(m_power.Trim());
            if (gift == null)
            {
                MAParser.ParserError($"No gift found {m_power} for {m_name}");
                return $"No gift found {m_power} for {m_name}";
            }
            text = text.Replace("[Value]", gift.m_giftTitle);
        }
        else
        {
            text = text.Replace("[Value]", "[No Gift error]");
        }
        return text;
    }

    public float GiftRecivedValue()
    {
        var gift = NGBusinessGift.GetInfo(m_power.Trim());
        if (gift == null)
        {
            MAParser.ParserError($"No gift found {m_power} for {m_name}");
            return 1f;
        }
        var ret = NGBusinessGiftsPanel.ContainsBusinessGift(m_power.Trim());
        return ret ? 1f : 0f;
    }
    //*******HireHero
    public void HireHeroActivate() => m_initialValue = NGManager.Me.m_MAHeroList.Count;
    public float HireHeroValue() => (int) m_targetValue - (NGManager.Me.m_MAHeroList.Count - m_initialValue);
    //*******KillCharacter
    public float KillCharacterValue()
    {
        var character = MAFlowCharacter.FindCharacter(m_power.Trim());
        if (character == null)
        {
            MAParser.ParserError($"No such character as {m_power} in Decision[{m_name}]");
            return 0f;
        }
        return character.m_state == NGMovingObject.STATE.MA_DEAD ? 0f : 1f;
    }

    //*******TrackHeroKills
    public string TrackHeroKillsDisplay()
    {
        var text = GetDefaultExplainText();    
        var ret = text.Replace(TimeString, $"{DayNight.Me.CurrentTimeString}");
        return ret;
    } 
    

    public void TrackHeroKillsActivate()
    {
        m_initialValue = 0;
        foreach (var hero in NGManager.Me.m_MAHeroList)
        {
            m_initialValue += hero.CharacterGameState.m_kills;
        }
    }
    public float TrackHeroKillsValue()
    {
        var total = 0;
        foreach (var hero in NGManager.Me.m_MAHeroList)
        {
            total += hero.CharacterGameState.m_kills;
        }

        return m_targetValue - (total - m_initialValue);
    }
    
    //*******EarnMoney
    public void EarnMoneyActivate() => m_initialValue = NGPlayer.Me.m_cash.Balance;
    [MADecision] public float EarnMoneyValue() => (int) (TargetValue - (NGPlayer.Me.m_cash.Balance - m_initialValue));

    //*******QuestComplete
    [MADecision]
    public float QuestCompleteValue()
    {
        var quest = MAQuestManager.Me.GetQuest(m_power);
        if (quest)
        {
            var completed = quest.CheckQuestComplete();
            return (completed) ? 0f : 1f;
        }

        return 1f;
    }

    [MADecision]
    public float QuestAcceptValue()
    {
        var quest = MAQuestManager.Me.GetQuest(m_power);
        if (quest)
        {
            var accepted = quest.CheckQuestAccepted();
            return (accepted) ? 0f : 1f;
        }
        return 1f;
    }
    
    [MADecision] public float QuestIntroValue()
    {
        var quest = MAQuestManager.Me.GetQuest(m_power);
        if (quest)
        {
            var accepted = quest.CheckQuestIntro();
            return (accepted) ? 0f : 1f;
        }
        return 1f;
    }

    //*******EarnMoneyFromSales
    public void EarnMoneyFromSalesActivate() => m_initialValue = NGPlayer.Me.m_cash.Earned;
    [MADecision] public float EarnMoneyFromSalesValue() => (int) (TargetValue - (NGPlayer.Me.m_cash.Earned - m_initialValue));
    
    //*******SellProducts
    public void SellProductsActivate() => m_initialValue = NGBusinessInterface.GetAllSales();
    [MADecision] public float SellProductsValue() => TargetValue - (NGBusinessInterface.GetAllSales() - m_initialValue);

    //*******DeliverResources
    public float GetCurrentDeliverResources()
    {
        var split = m_power.Split(';', '|', ':', '\n');

        MABuilding building = MABuilding.FindBuilding(split[0], true);

        if (building != null)
        {
            return building.GetStockCount(NGCarriableResource.GetInfo(split[1]));
        }

        return 0.0f;
    }
    public void DeliverResourcesActivate() => m_initialValue = GetCurrentDeliverResources();
    [MADecision] public float DeliverResourcesValue() => TargetValue - (GetCurrentDeliverResources() - m_initialValue);

    //*******WorkerMoved
    public void WorkerMovedActivate() => m_initialValue = NGBusinessInterface.GetAllFactoryWorkerDeliveries("NGProduction");
    [MADecision] public float WorkerMovedValue() => TargetValue - (NGBusinessInterface.GetAllFactoryWorkerDeliveries("NGProduction") - m_initialValue);

    //*******EmployWorkers
    public void EmployWorkersActivate() => m_initialValue = MAWorker.GetWorkerCount(m_power);

    [MADecision] public float EmployWorkersValue()
    {
        // Ensure the count doesn't ever exceed the origional target (e.g. when workers get killed)
        var workerCount = MAWorker.GetWorkerCount(m_power);
        if(workerCount < m_initialValue) m_initialValue = workerCount;
        return TargetValue - (workerCount - m_initialValue);
    }
    
    //*******TotalWorkers
    public string TotalWorkersDisplay()
    {
        if(m_power.Contains('[') == false)
        {
            var workersRequired = TotalWorkersValue();
            var freeBedrooms = 0;
            var freeWorkerSlots = 0;
            bool districtRestricted = m_power.IsNullOrWhiteSpace() == false;
            foreach(var building in NGManager.Me.m_maBuildings)
            {
                if(districtRestricted && building.DistrictID.Equals(m_power) == false)
                {
                    // Building cannot be considered
                    continue;
                }
                freeBedrooms += building.GetFreeWorkerBedrooms();
                freeWorkerSlots += building.GetFreeWorkerSlots();
            }
            
            m_param1 = Mathf.Max(0, workersRequired-freeBedrooms).ToString();
            m_param2 = Mathf.Max(0, workersRequired-freeWorkerSlots).ToString();
        }   
        
        return GetDefaultExplainText();
     }
    public void TotalWorkersActivate() => m_initialValue = TotalWorkersValue();
    [MADecision] public float TotalWorkersValue() => Mathf.Max(0, TargetValue - MAWorker.GetWorkerCount(m_power));
    
    //*******Build
    public string BuildDisplay()
    {
        var info = MAComponentInfo.GetInfo(m_power);
        var buildingName = $"No Such building component as {m_power}";
        if (info == null)
            Debug.LogError(buildingName + " Not Found");
        else
            buildingName = info.m_title;
        var text = m_explainText.Replace(ValueString, $"{Mathf.Max(0, GetValue()).ToString("F0")}");
        return text.Replace(PowerString, buildingName);
    }

    public void BuildActivate() => m_initialValue = NGBusinessInterface.GetBuildingCountWithComponent(m_power);
    [MADecision] public float BuildValue() => (NGBusinessInterface.GetBuildingCountWithComponent(m_power) - m_initialValue > 0) ? 0 : 1;
    
    //*******UnlockRegion
    public void UnlockRegionActivate() => m_initialValue = (DistrictManager.Me.IsDistrictUnlocked(m_power)) ? 1 : 0;
    [MADecision] public float UnlockRegionValue() => (DistrictManager.Me.IsDistrictUnlocked(m_power)) ? 0 : 1;
    
    //*******TapHereToBegin
    public void TapHereToBeginActivate() => m_initialValue = 1;
    [MADecision] public float TapHereToBeginValue() => m_initialValue;

    //*******ClickOnRoyalOrder
    public void ClickOnOrderActivate() => m_initialValue = 1;
    [MADecision] public float ClickOnOrderValue() => MAGUIBase.Find(m_power)==null? 1f : 0f;

    #region Chapter_1
    //*******CompleteOrder
    public float CompleteOrderValue()
    {
        var order = MAOrder.GetCurrentOrHistoricOrder(m_power.Trim());
        if (order == null)
        {
            MAParser.ParserError($"No such order as {m_power}");
            return 0;
        }

        return order.RemainingQuantity;
    }

    //*******ActivateBeacon
    public void ActivateBeaconActivate()
    {
        var building = MABuilding.FindBuilding(m_power, true);
        if(building == null)
        {
            MAParser.ParserError($"No such building as {m_power}");
            return;
        }
        if (NGManager.Me.m_maBuildings.Contains(building) == false)
        {
            MAParser.CreateGodBeam(building, m_power, "Silver");
        }
    }

    public float ActivateBeaconValue()
    {
        var building = MABuilding.FindBuilding(m_power.Trim(), true);
        if (building == null)
        {
            MAParser.ParserError($"No such building as {m_power}");
            return 1;
        }
        var beacon = building.GetComponentInChildren<BCBeacon>();
        if (beacon == null)
        {
            MAParser.ParserError($"Can't find BCBeacon in {m_power}");
            return 1;
        }

        if (beacon.HasBeenActivated() == false)
            return 1;
        MAParser.DestroyGodBeam(m_power);
        return 0;
    }
    
    public void CompleteBeaconActivate()
    {
    }

    // Returns 0.0f (true) when a beacon (defined in m_power) is completed and charged with manna
    public float CompleteBeaconValue()
    {
        var building = MABuilding.FindBuilding(m_power, true);
        if(building == null)
        {
            MAParser.ParserError($"No such building as {m_power}");
            return 1.0f;
        }
        
        BCBeacon beaconChild = building.GetComponentInChildren<BCBeacon>();
        if (beaconChild != null)
        {
            return beaconChild.IsComplete() ? 0.0f : 1.0f;
        }
        return 1.0f;
    }

    //*******PossessHero
    public void PossessHeroActivate() => m_initialValue = 1;
    public float PossessHeroValue()
    {
        var hero = NGManager.Me.m_MAHeroList.Find(o => o.GetTypeInfo() == m_power);
        if(hero && GameManager.Me.PossessedCharacter == hero) return 0f;
        return 1f;
    }
    //*******PossessAnimal
    public void PossessAnimalActivate() => m_initialValue = 1;
    public float PossessAnimalValue()
    {
        var animal = NGManager.Me.m_MAAnimalList.Find(o => o.GetTypeInfo() == m_power);
        if (animal && GameManager.Me.PossessedCharacter == animal) return 0f;
        return 1f;
    }

    //*******PossessCharacter
    public void PossessCharacterActivate() => m_initialValue = 1;
    public float PossessCharacterValue()
    {
        if (GameManager.Me.PossessedCharacter != null) return 0f;
        return 1f;
    }

    //*******PossessedInRange
    public void PossessedInRangeActivate()
    {
                var possessed = GameManager.Me.PossessedObject;

        if(possessed != null)
        {
            var pSplit = m_power.Split('[', ']');
            var distance = 0f;
            Vector3 pos;
            switch (pSplit[0].Trim())
            {
                case "Pos":
                    if (pSplit.Length < 2)
                    {
                        MAParser.ParserError($"No position given in {m_power}");
                        return;
                    }

                    pos = (Vector3) MAParserSupport.ConvertPos(pSplit);
                    break;
                case "FlowCharacter":
                    if (pSplit.Length < 2)
                    {
                        MAParser.ParserError($"No flow character given in {m_power}");
                        return;
                    }

                    var character = MAFlowCharacter.FindCharacter(pSplit[1]);
                    if (character == null)
                    {
                        MAParser.ParserError($"Can't find character in {m_power}");
                        return;
                    }

                    pos = character.transform.position;
                    break;
                default:
                    MAParser.ParserError($"Unknown PossessedInRange type {pSplit[0]} in {m_power}");
                    return;
            }
            m_initialValue = (possessed.gameObject.transform.position - pos).magnitude;
        }
        else
        {
            m_initialValue = TargetValue * 10.0f;
        }
    }
    public float PossessedInRangeValue()
    {
        var possessed = GameManager.Me.PossessedObject;

        if (possessed != null)
        {
            var pSplit = m_power.Split('[', ']');
            var distance = 0f;
            switch (pSplit[0].Trim())
            {
                case "Pos":
                    if (pSplit.Length < 2)
                    {
                        MAParser.ParserError($"No position given in {m_power}");
                        return 1f;
                    }
                    var pos = (Vector3)MAParserSupport.ConvertPos(pSplit);
                    distance = (possessed.gameObject.transform.position - pos).magnitude;
                    break;
                case "FlowCharacter":
                    if (pSplit.Length < 2)
                    {
                        MAParser.ParserError($"No flow character given in {m_power}");
                        return 1f;
                    }
                    var character = MAFlowCharacter.FindCharacter(pSplit[1]);
                    if (character == null)
                    {
                        MAParser.ParserError($"Can't find character in {m_power}");
                        return 1f;
                    }
                    distance = (character.transform.position - possessed.transform.position).magnitude;
                    break;
                default:
                    MAParser.ParserError($"Unknown PossessedInRange type {pSplit[0]} in {m_power}");
                    return 1f;
            }
            return distance <= TargetValue ? 0.0f : distance - TargetValue;
        }

        return m_initialValue;
    }
    
    //*******PossessedInRange
    public void PossessedInCharacterRangeActivate()
    {
        var possessed = GameManager.Me.PossessedObject;

        if(possessed != null)
        {
            m_rangeCheckCharacter = NGManager.Me.m_MACharacterList.Find(x => x.GetTypeInfo() == m_power.Trim());
            if (m_rangeCheckCharacter != null)
            {
                var pos = m_rangeCheckCharacter.transform.position;
                m_initialValue  = (possessed.gameObject.transform.position - pos).magnitude;
            }
        }
        else
        {
            m_initialValue = TargetValue * 10.0f;
        }
    }
    public float PossessedInCharacterRangeValue()
    {
        var possessed = GameManager.Me.PossessedObject;
        if (possessed != null)
        {
            if (m_rangeCheckCharacter == null)
                m_rangeCheckCharacter = NGManager.Me.m_MACharacterList.Find(x => x.GetTypeInfo() == m_power.Trim());
            
            if (m_rangeCheckCharacter != null)
            {
                var pos = m_rangeCheckCharacter.transform.position;
                var distance = (possessed.gameObject.transform.position - pos).magnitude;
                return distance <= TargetValue ? 0.0f : distance - TargetValue;
            }
        }

        return m_initialValue;
    }
    
    //*******CamFocusInRange
    public void CamFocusInRangeActivate()
    {
        var possessed = GameManager.Me.PossessedObject;
        m_initialValue = TargetValue * 10.0f;
        
        if(possessed == null)
        {
            var hit = GameManager.Me.RaycastTerrain(.5f, .5f);
            if (hit.HasValue)
            {
                var pos = (Vector3)MAParserSupport.ConvertPos(m_power.Split('[', ']'));
                m_initialValue = ((Vector3)hit - pos).magnitude;
            }
        }
    }
    public float CamFocusInRangeValue()
    {
        var possessed = GameManager.Me.PossessedObject;

        if(possessed == null)
        {
            var hit = GameManager.Me.RaycastTerrain(.5f, .5f);
            if (hit.HasValue)
            {
                if(GetPosFromPower(out var pos) == false) return m_initialValue;
                var distance = ((Vector3)hit - pos).magnitude;
                return distance <= (TargetValue*2f) ? 0.0f : distance - (TargetValue*2f);
            }
        }
        return m_initialValue;
    }
    
    //*******CamOrPossessedInRange
    public void CamOrPossessedInRangeActivate()
    {
        m_initialValue = TargetValue * 10.0f;
        m_power = m_power.Trim();
        if(GetPosFromPower(out Vector3 pos) == false) return;
        var possessed = GameManager.Me.PossessedObject;
        if(possessed != null)
        {
            m_initialValue = (possessed.gameObject.transform.position - pos).magnitude;
        }
        else
        {
            var hit = GameManager.Me.RaycastTerrain(.5f, .5f);
            if (hit.HasValue)
            {
                m_initialValue = ((Vector3)hit - pos).magnitude;
            }
        }
    }
    public float CamOrPossessedInRangeValue()
    {
        var pir = PossessedInRangeValue();
        var cir = CamFocusInRangeValue();
        var ret = Mathf.Min(pir, cir);
        return ret;
    }

    //*******QuestObjective
    public void QuestObjectiveActivate()
    {
        var split = m_power.Split(';', '|', ':', '\n');

        var quest = MAQuestManager.Me.GetQuest(split[0]);

        if (quest)
        {
            quest.QuestObjectiveActivate(split[1]);
        }
    }
    public float QuestObjectiveValue()
    {
        var split = m_power.Split(';', '|', ':', '\n');

        var quest = MAQuestManager.Me.GetQuest(split[0]);

        if (quest)
        {
            return quest.QuestObjectiveValue(split[1]);
        }

        return 1.0f;
    }

    //*******CotterAddWorkerBlocks
    public string CotterAddWorkerBlocksDisplay()
    {
        var text = $"<b>{GetDefaultExplainText()}</b>\n"; 
        var buildingsAndCount = GetCotterAddWorkerBlocksBuildingsAndCount(m_currentData);
        foreach(var bc in buildingsAndCount)
        {
            var count = bc.building.GetBuildingComponentCount(typeof(BCWorkers), true);
            if(count < bc.count) 
            {
                text+=$"{bc.building.Name} x Workers Blocks {count}/{bc.count}  \n";
            }
            else
            {
                text+=$"<color=grey>{bc.building.Name} x Worker Blocks {count}/{bc.count}</color>   <sprite=6>\n";
            }
        }
        text = text.TrimEnd('\n');
        
        return text;
    }
    public void CotterAddWorkerBlocksActivate()
    {
        var buildingsAndCount = GetCotterAddWorkerBlocksBuildingsAndCount(m_power);
        m_currentData = "";
        foreach(var bc in buildingsAndCount)
        {
            var count = bc.building.GetBuildingComponentCount(typeof(BCWorkers), true);
            if(count < bc.count) 
            {
                MAParser.ParserError($"Not enough workers in {bc.building.m_title}");
                return;
            }
            m_currentData += $"{bc.building.Name}+{count+bc.count};";
        }
        m_currentData = m_currentData.TrimEnd(';');
    }

    public float CotterAddWorkerBlocksValue()
    {
        if(m_currentData.IsNullOrWhiteSpace()) return 1;
        var buildingsAndCount = GetCotterAddWorkerBlocksBuildingsAndCount(m_currentData);
        foreach(var bc in buildingsAndCount)
        {
            var count = bc.building.GetBuildingComponentCount(typeof(BCWorkers), true);
            if(count < bc.count) 
            {
                return 1;
            }
        }
        return 0;
    }

    List<(MABuilding building, int count)> GetCotterAddWorkerBlocksBuildingsAndCount(string _from)
    {
        List<(MABuilding building, int count)> ret = new ();
        var split = _from.Split(';', '|', ':');
        foreach (var cName in split)
        {
            var pSplit = cName.Split('+');
            if (pSplit.Length != 2)
            {
                MAParser.ParserError($"No such building as {cName}");
                continue;
            }

            var building = MABuilding.FindBuilding(pSplit[0].Trim());
            if (building == null)
            {
                MAParser.ParserError($"No such building as {pSplit[0]}");
                continue;
            }

            var count = int.Parse(pSplit[1]);
            ret.Add(new ValueTuple<MABuilding, int>(building, count));
        }

        return ret;
    }
    //RoadBuildMode
    public float RoadBuildModeValue()
    {
        return RoadManager.Me.RoadBuildMode ? 0 : 1;
    }

    //MAUnlock
    public void CompleteCurrentOrderActivate() => m_initialValue = 1;
    public float CompleteCurrentOrderValue()
    {
        var building = MABuilding.FindBuilding(m_power.Trim());

        if (building == null)
        {
            MAParser.ParserError($"No such building as {m_power}");
            return 1;
        }

        var orders = building.GetOrdersDisplayed();
        if(orders.Count == 0) return 0;
        var order = orders[0];
        TargetValue = order.m_orderQuantity;
        return order.RemainingQuantity;
    }
    
    //MAUnlock
    public void MAUnlockActivate() => m_initialValue = 1;

    [MADecision] public float MAUnlockValue()
    {
        var field = typeof(MAUnlocks).GetField(m_power);
        if (field == null)
        {
            MAParser.ParserError($"No such field as {m_power}");
            return 1f;
        }
        var r = field.GetValue(MAUnlocks.Me);
        if(r == null) return 1f;
        if (field.FieldType.Name == "Boolean")
        {
            return (bool) r ? 0f : 1f;
        }

        return 1;
    }
    
    //*******Wait For Building to be clean
    public void WaitForBuildingToBeCleanActivate() => m_initialValue = 1;
    public float WaitForBuildingToBeCleanValue()
    {
        var findBuilding = m_power;
        var buildingSplit = m_power.Split('[', ']');
        if (buildingSplit.Length > 1)
        {
            findBuilding = buildingSplit[1];
        }
        var building = MABuilding.FindBuilding(findBuilding);
        if (building == null) return 1f;
        return building.IsDisabledByPlants ? 1f : 0f;
    }
    //*******RebuildBuildingComponents
    public string RebuildBuildingComponentsDisplay()
    {
        var text = $"<b>{GetDefaultExplainText()}</b>\n";
        var state = GetRebuildState(m_power);
        if(state != null)
        {
            foreach(var item in state)
            {
                var quantity = item.Value.quantity;
                var requirement = item.Value.required;
                var name = item.Value.name;
                
                if(quantity == 0) 
                    text+=$"{name} x 0/{quantity}\n";
                else if(requirement > quantity) 
                    text+=$"{name} x {quantity}/{requirement}\n";
                else
                    text+=$"<color=grey>{name} x {quantity}/{requirement}</color> <sprite=6>\n";
            }
        }

        text = text.TrimEnd('\n');
        return text;
    }
    public void RebuildBuildingComponentsActivate() => m_initialValue = 1;
    [MADecision] public float RebuildBuildingComponentsValue()
    {
        var state = GetRebuildState(m_power);
        if(state != null)
        {
            foreach(var requirement in state)
            {
                if(requirement.Value.quantity < requirement.Value.required)
                    return 1f; 
            }
        }
        return 0f;
    }
    
    //*******RebuildBuilding
    public string RebuildBuildingDisplay()
    {
        var text = $"<b>{GetDefaultExplainText()}</b>\n";
        var state = GetRebuildState(m_power);
        if(state != null)
        {
            foreach(var item in state)
            {
                var quantity = item.Value.quantity;
                var requirement = item.Value.required;
                var name = item.Value.name;
                
                if(quantity == 0) 
                    text+=$"{name} x 0/{requirement}\n";
                else if(requirement > quantity) 
                    text+=$"{name} x {quantity}/{requirement}\n";
                else
                    text+=$"<color=grey>{name} x {quantity}/{requirement}</color> <sprite=6>\n";
            }
        }

        text = text.TrimEnd('\n');
        return text;
    }
    public void RebuildBlocksActivate() => m_initialValue = 1;
    [MADecision] public float RebuildBuildingValue()
    {
        var state = GetRebuildState(m_power);
        if(state != null)
        {
            foreach(var requirement in state)
            {
                if(requirement.Value.quantity < requirement.Value.required)
                    return 1f; 
            }
        }
        return 0f;
    }
    //*******RebuildBuilding
    public string RebuildBuildingV2Display()
    {
        var text = $"<b>{GetDefaultExplainText()}</b>\n<size=80%>";
        var state = GetRebuildStateV2();
        if(state != null)
        {
            foreach(var item in state)
            {
                var count = item.Value.count;
                var requirement = item.Value.required;
                var name = item.Key.m_displayName;
                
                if(count == 0) 
                    text+=$"{name} x 0/{requirement}\n";
                else if(requirement > count) 
                    text+=$"{name} x {count}/{requirement}\n";
                else
                    text+=$"<color=grey>{name} x {count}/{requirement}</color> <sprite=6>\n";
            }
        }

        text = text.TrimEnd('\n');
        return text;
    }
    public void RebuildBlocksV2Activate() => m_initialValue = 1;
    [MADecision] public float RebuildBuildingV2Value()
    {
        var state = GetRebuildStateV2();
        if(state != null)
        {
            foreach(var requirement in state)
            {
                if(requirement.Value.count < requirement.Value.required)
                    return 1f; 
            }
        }
        return 0f;
    }
    #endregion Chapter_1
    
    public class RebuildItemInfo
    { 
        public string name; public int required; public int quantity;
        public RebuildItemInfo(string _name, int _required) { name = _name; required = _required; }
    }
    public DecisionMakeAHeroSword m_makeAHeroSwordDecision;
    public void MakeAHeroSwordActivate()
    {
        m_makeAHeroSwordDecision = new DecisionMakeAHeroSword(this);
    }

    public string MakeAHeroSwordDisplay()
    {
        return m_makeAHeroSwordDecision.GetDisplayText();
    }
    public float MakeAHeroSwordValue()
    {
        var result = m_makeAHeroSwordDecision.Update();
        return result;
    }

    public void MakeAHeroSwordExplain()
    {
        Debug.Log("Her we go");
    }
    //*******None
    Dictionary<Object, RebuildItemInfo> GetRebuildState(string _power)
    {
        var powers = m_power.Split('\n');
        MABuilding building = null;
        Dictionary<Object, RebuildItemInfo> blockTypesAndCount = new ();
        NGBlockInfo blockInfo = null;
        MAComponentInfo componentInfo = null;
        
        foreach (var v in powers)
        {
            if(v.IsNullOrWhiteSpace()) continue;
            if (v.StartsWith("Building"))
            {
                var b = v.Split('[', ']');
                if(b.Length != 3) return null;
                building = MABuilding.FindBuilding(b[1]);
                if (building == null) return null;
            }
            else if((blockInfo = NGBlockInfo.GetInfo(v)) != null)
            {
                if(blockTypesAndCount.TryGetValue(blockInfo, out var value) == false)
                    blockTypesAndCount.Add(blockInfo, new RebuildItemInfo(blockInfo.m_displayName, 1));
                else
                    value.required++;
            }
            else if((componentInfo = MAComponentInfo.GetInfo(v)) != null)
            {
                if(blockTypesAndCount.TryGetValue(componentInfo, out var value) == false)
                    blockTypesAndCount.Add(componentInfo, new RebuildItemInfo(componentInfo.m_title, 1));
                else
                    value.required++;
            }
            else if(v.IsNullOrWhiteSpace() == false)
            {
                Debug.LogError(MAParserSupport.DebugColor($"No such component or block type as {v}"));
            }
        }
        
        var blocks = building.GetComponentsInChildren<Block>();
        foreach(var item in blockTypesAndCount)
        {
            if(item.Key as MAComponentInfo != null)
            {
                item.Value.quantity = building.GetBuildingComponentCount(item.Key as MAComponentInfo);
            }
            else if(item.Key as NGBlockInfo != null)
            {
                foreach(var block in blocks)
                {
                    if(block.BlockInfo == item.Key as NGBlockInfo) item.Value.quantity++;
                }
            }
        }
        
        return blockTypesAndCount;
    }
    Dictionary<NGBlockInfo, (int required, int count)> GetRebuildStateV2()
    {
        var powers = m_power.Split('\n');
        MABuilding building = null;
        Dictionary<NGBlockInfo, (int required, int count)> blockTypesAndCount = new ();
        NGBlockInfo blockInfo = null;
        
        foreach (var v in powers)
        {
            if(v.IsNullOrWhiteSpace()) continue;
            if (v.StartsWith("Building["))
            {
                var b = v.Split('[', ']');
                if(b.Length != 3) return null;
                building = MABuilding.FindBuilding(b[1]);
                if (building == null) return null;
                continue;
            }
            if((blockInfo = NGBlockInfo.GetInfo(v.Trim())) != null)
            {
                if(blockTypesAndCount.TryGetValue(blockInfo, out var t) == false)
                    blockTypesAndCount.Add(blockInfo, (1,0));
                else
                    blockTypesAndCount[blockInfo] = (t.required+1, t.count );
            }
            else
            {
                MAParser.ParserError($"No such block type as {v}");
            }
        }
        
        var blocks = building.GetComponentsInChildren<Block>();
        foreach(var block in blocks)
        {
            blockInfo= block.BlockInfo;
            if(blockTypesAndCount.TryGetValue(blockInfo, out var t))
                blockTypesAndCount[blockInfo] = (t.required, t.count+1 );
        }
        
        return blockTypesAndCount;
    }
    
    public string NoneDisplay() => "None";
    public void NoneActivate() => m_initialValue = 0;
    [MADecision] public float NoneValue() => 0f;

    #endregion

    public object Clone()
    {
        return this.MemberwiseClone();
    }
}
/*
    //*******
    public string Display() => GetDefaultExplainText();
    public void Activate() => m_initialValue = ;
    public float Value() => ;
*/