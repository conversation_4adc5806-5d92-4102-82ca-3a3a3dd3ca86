using System.Collections;
using System.Collections.Generic;
using System;
using UnityEngine;

public class NGBusinessInterface : MonoSingleton<NGBusinessInterface>
{
	///////////////////////
	// FACTORY INTERFACE //
	///////////////////////

	// Factory Pipeline class
	public class FactoryPipeline {
		public bool m_isProducerAuto = false;
		public List<NGCommanderBase> m_smelters = new List<NGCommanderBase>();
		public List<NGCommanderBase> m_mines = new List<NGCommanderBase>();
		public List<NGCommanderBase> m_automatedSmelters = new List<NGCommanderBase>();
		public List<NGCommanderBase> m_automatedMines = new List<NGCommanderBase>();
	}

	// Get all factories by type
   /* public static List<NGCommanderBase> GetAllFactoriesByType(String _factoryType) {
        var allFactories = NGManager.Me.FactoryList;
        if (allFactories.Count == 0) return new();
		var factories = new List<NGCommanderBase>();
		switch (_factoryType) {

			case "NGProduction":
				if (factories != null)
				{
					factories.AddRange(GetAllFactoriesByType("NGProductFactory"));
					factories.AddRange(GetAllFactoriesByType("NGSales"));
					factories.AddRange(GetAllFactoriesByType("NGSmelters"));
					factories.AddRange(GetAllFactoriesByType("NGMines"));
				}

				break;

			// Get all factories that product products
			case "NGProductFactory":
				foreach (var f in allFactories) {
					if (f.IsProductProducer ) {
						factories.Add(f);
					}
				}
			break;

			// Get all shops that "sell" products
			case "NGSales":
				foreach (var f in allFactories) {
					if (f.IsShop) {
						factories.Add(f);
					}
				}
			break;

			// Get all smelters
			case "NGSmelters":
				factories = allFactories.FindAll(f => f.OutputIs != null && f.OutputIs.m_name.Contains("RawMaterial"));
				break;

			// Get all mines
			case "NGMines":
				factories = allFactories.FindAll(f => f.OutputIs != null && f.OutputIs.m_name.Contains("RawResource"));
			break;

			// Use default get all factories function in NGManager
			default:
				factories = allFactories;
			break;

		}

        return factories;
    }*/

	// Get all factories inputs
    public static float GetAllFactoryResources(String _factoryType) {
        var total = 0f;
        return total;
    }

    // Get all factories outputs
    public static float GetAllFactoryProducts(String _factoryType) {
        var total = 0f;
        return total;
    }

	
	// Get first pipeline automated state as total
	public static float GetFirstFactoryAutomatedStateAsTotal() {
		var total = 0f;
		var pipeline = GetAllFactoryPipelines()[0];

		if (pipeline.m_isProducerAuto) {
			total++;
		}
		if (pipeline.m_automatedSmelters.Count > 0) {
			total++;
		}
		if (pipeline.m_automatedMines.Count > 0) {
			total++;
		}

		return total;
	}

	// Get all factory pipelines
	public static List<FactoryPipeline> GetAllFactoryPipelines() {
		List<FactoryPipeline> factoryPipelines = new List<FactoryPipeline>();
		//foreach (var f in GetAllFactoriesByType("NGProductFactory")) {
		//	factoryPipelines.Add(GetFactoryPipeline(f));
		//}
		return factoryPipelines;
	}

	// Get factory automated state
	public static FactoryPipeline GetFactoryPipeline(NGCommanderBase _factory) {

		FactoryPipeline factoryPipeline = new FactoryPipeline();

		// Check if factory is producer
		if (!_factory.IsProductProducer) {
			return factoryPipeline;
		} else {
			factoryPipeline.m_isProducerAuto = _factory.NumWorkers > 0;
		}

		// Set factory smelters + automated
		factoryPipeline.m_smelters = GetAllFactorySuppliers(_factory);
		foreach (var s in factoryPipeline.m_smelters) {
			if (s.NumWorkers > 0) {
				factoryPipeline.m_automatedSmelters.Add(s);
			}
		}

		// Set factory mines and automated
		if (factoryPipeline.m_smelters.Count > 0) {
			factoryPipeline.m_mines = GetAllFactorySuppliers(factoryPipeline.m_smelters[0]);
		}

		if (factoryPipeline.m_mines.Count > 0) {
			foreach (var m in factoryPipeline.m_mines) {
				if (m.NumWorkers > 0) {
					factoryPipeline.m_automatedMines.Add(m);
				}
			}
		}

		return factoryPipeline;
	}

	// Find Factory Suppliers
	public static List<NGCommanderBase> GetAllFactorySuppliers(NGCommanderBase _factory) {
		List<NGCommanderBase> factorySuppliers = new List<NGCommanderBase>();
		// Check input requirements for factory
		//NGCarriableResource factoryInput = _factory.InputsAre.GetLowestStockItem();
		// Go through all factories and find partners
		/*foreach (var f in GetAllFactoriesByType("NGProduction")) {
			if (f.OutputIs.Name == factoryInput.Name) {
				factorySuppliers.Add(f);
			}
		}*/
		return factorySuppliers;
	}

	// Get all factory inputs delivered by workers
	public static float GetAllFactoryWorkerDeliveries(String _factoryType) {
		var total = 0f;
		return total;
	}

	// Get all workers
	public static float GetAllWorkers()
	{
		var total = 0f;
		return total;
	}

	// Get all factories workers
	public static float GetAllFactoryWorkers(String _factoryType)
	{
		var total = 0f;
		return total;
	}

	//////////////////////
	// WORKER INTERFACE //
	//////////////////////

	// Get all houses
	public static List<NGCommanderBase> GetAllHouses() {
		var houses = NGManager.Me.HouseList;
		return houses;
	}

	// Check if everyone is employed
	public static bool ReachedMaxNumWorkersPossible() {
		return (GetAllWorkers() == GetAllHouses().Count);
	}

	////////////////////////////
	// SATISFACTION INTERFACE //
	////////////////////////////

	// Get town satisfaction
	public static float GetTownSatisfactionTotal()
	{
		return 0f;
		//return NGTownSatisfactionManager.Me.Total;
	}


	// Get number of buildings above X quality
	public static float GetNumBuildingsQualityAbove<T>(List<T> _buildingList, float _quality) where T : NGCommanderBase
	{
		var count = 0;
		foreach(var building in NGManager.Me.m_maBuildings)
		{
			var di = NGDesignInterface.Get(building.Design);
			if(di.TotalScore >= _quality)
				count++;	
		}
		return count;
	}

	// Get number of buildings left to build above X quality
	public static float GetNumBuildingsQualityAboveLeftToConstruct<T>(List<T> _buildingList, float _targetValue, float _initialValue) where T : NGCommanderBase {
		float numBuildingsToConstruct = _initialValue + 1; // GL - +1 to make sure we have to build at least one
		float numBuildingsQualityAbove = GetNumBuildingsQualityAbove<T>(_buildingList, _targetValue);
		float numLeftToConstruct = numBuildingsToConstruct - numBuildingsQualityAbove;
		return numLeftToConstruct;
	}


	////////////////////////////
	// CONSTRUCTION INTERFACE //
	////////////////////////////

	// Get number of building type being constructed
	public static float GetNumBuildingsBeingConstructed<T>(List<T> _buildingList) where T : NGCommanderBase {
		return _buildingList.FindAll(b => b.IsBeingConstructed).Count;
	}

	// Get number of building type left to construct
	public static float GetNumBuildingsLeftToConstruct<T>(List<T> _buildingList, float _targetValue, float _initialValue) where T : NGCommanderBase {
		float numLeftToConstruct = 0f;
		float numConstructSites = GetNumBuildingsBeingConstructed<T>(_buildingList);
		if (numConstructSites == 0) { numLeftToConstruct = 0f; } else {
			if (numConstructSites > _initialValue) { _initialValue = numConstructSites; }
			numLeftToConstruct = _targetValue - (_initialValue - numConstructSites);
		}
		return numLeftToConstruct;
	}

	//////////////////////////////
	// MARKET / SALES INTERFACE //
	/////////////////////////////

	// Get total shop money
	public static float GetAllShopMoney() {
        var total = 0f;
        return total;
    }

	public static float GetAllSales()
	{
		return NGManager.NumSales;
	}

    // Get total shop sales
    public static float GetAllShopSales() {
        var total = 0f;
        return total;
    }

	// Check market remaining
	public static float GetFirstFactoryMarketRemaining() {
		var total = 0f;
		//var productFactory = GetAllFactoriesByType("NGProductFactory")[0];
		//total = productFactory.ProductMade.MarketRemaining;
		return total;
	}

	public static int GetNGBuildingCount(string _what)
	{
		var buildings = NGManager.Me.m_NGCommanderList.FindAll(o => o.IsBeingConstructed == false && o.Name.Equals(_what));
		return buildings.Count;
	}
	public static int GetBuildingCountWithComponent(string _componentName)
	{
		var info = MAComponentInfo.GetInfo(_componentName);
		int count = 0;
		foreach (var b in NGManager.Me.m_maBuildings)
		{
			count+= b.GetBuildingComponentCount(info);
		}
		return count;
	}
	
	public  void TestIt()
	{
		Debug.Log("Test it");
	}
	
	public  static void STestIt()
	{
		Debug.Log("STest it");
	}
	//******************************************************************************************************************
	// Exit Execute Functions
	//******************************************************************************************************************
	#region EnitExecute
	
	public void FinishedAlpha()
	{
		GameManager.Me.m_state.m_haveFinishedGame = true;
		GameManager.Me.ForceSave();
	}
	
	// public void TriggerMobileTutorialPopup()
	// {
	// 	MobileTutorial.Create();
	// }

	public void TakeScreenshot(int _index)
    {
		StartCoroutine(Screenshot(_index));
    }
    public IEnumerator Screenshot(int _index)
    {
        yield return null;
        Utility.ScreenGrab(1, _index);
        yield return null;
    }
    public void LaunchBoardroom()
	{
		//NGTutorialInterface.Me.LoadVCOffice();
	}

	public void SetBusinessStage(int _stage)
	{
		NGPlayer.Me.m_businessStage = _stage;
	}

	public void SetRunInBackground()
	{
		NGPlayer.Me.m_runInBackground = true;
		Application.runInBackground = true;
	}

	public void LoopFlow(int _index, float _multiplier, int _loopFlowCount)
	{
#if OldBusinessFlow
		if (NGBusinessFlow.m_inLoopFlow)
		{
			if (NGBusinessFlow.m_loopFlowCount > 0)
			{
				NGBusinessFlow.m_loopFlowCount--;
				if (NGBusinessFlow.m_loopFlowCount == 0)
				{
					NGBusinessFlow.m_inLoopFlow = false;
					NGBusinessFlow.m_loopFlowMultiplier = 1;
					NGBusinessFlow.m_loopFlowCount = -1;
					return;
				}
			}
		}
		else
		{
			NGBusinessFlow.m_loopFlowCount = _loopFlowCount;
		}

		NGBusinessFlow.m_inLoopFlow = true;
		NGBusinessFlow.m_loopFlowToIndex = _index;
		NGBusinessFlow.m_loopFlowMultiplier *= _multiplier;
#endif

	}
	#endregion EnitExecute
}