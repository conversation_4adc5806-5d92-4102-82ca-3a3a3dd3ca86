using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class NGBusinessObjectiveProgressTick : MonoBehaviour
{
    public enum State
    {
        Completed,
        Current,
        ToDo,
        Hidden,
        Last,
    }

    public Image m_tickImage;
    public Image m_currentImage;
    public Image m_background;
    public TextMeshProUGUI m_hiddenNumber;
    public Color[] m_colors = new Color[(int)State.Last];
    virtual public void Toggle(bool _flag)
    {
        if(m_tickImage != null)
            m_tickImage.gameObject.SetActive(_flag);
    }

    void Activate()
    {
        if (m_hiddenNumber != null)
        {
            m_hiddenNumber.gameObject.SetActive(false);
        }
    }

    void SetActive(GameObject _obj, bool _active)
    {
        if(_obj.activeSelf != _active)
            _obj.SetActive(_active);
    }
    
    public void Activate(State _state)
    {
        bool active = true;
        SetActive(m_hiddenNumber.gameObject, false);
        m_background.color = m_colors[(int) _state];
        switch (_state)
        {
            case State.Completed:
                SetActive(m_tickImage.gameObject,true);
                break;
            case State.Hidden:
                active = false;
                break;
            default:
                SetActive(m_tickImage.gameObject,false);
                break;                
        }
        SetActive(gameObject, active);
    }
    void Activate(bool _showTick, string _showText, bool _isCurrent)
    {
        Toggle(_showTick);
        m_currentImage?.gameObject.SetActive(_isCurrent);
        if (!m_hiddenNumber)
            return;
        m_hiddenNumber.enabled = _showText != "";
        m_hiddenNumber.text = "+" + _showText;
    }

    public static NGBusinessObjectiveProgressTick Create(NGBusinessObjectiveProgressTick _prefab, Transform _holder, bool _showTick, string _showText, bool _isCurrent)
    {
        var go = Instantiate(_prefab.gameObject, _holder);
        go.transform.SetAsFirstSibling();
        var bopt =  go.GetComponent<NGBusinessObjectiveProgressTick>();
        bopt.Activate(_showTick, _showText, _isCurrent);
        return bopt;
    }
    public static NGBusinessObjectiveProgressTick Create(NGBusinessObjectiveProgressTick _prefab, Transform _holder)
    {
        var go = Instantiate(_prefab.gameObject, _holder);
        var bopt =  go.GetComponent<NGBusinessObjectiveProgressTick>();
        bopt.Activate();
        return bopt;
    }

}
