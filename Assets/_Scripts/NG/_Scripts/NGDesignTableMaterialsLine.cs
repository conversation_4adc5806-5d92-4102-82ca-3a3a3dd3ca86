using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class NGDesignTableMaterialsLine : MonoBehaviour
{
    public Image m_matarialImage;
    public TMP_Text m_materialType;
    public TMP_Text m_materialCount;
    public TMP_Text m_animText;
    public Animator m_anim;

    public float m_numMaterials = 0;
    public string m_resource = "";
    public bool m_active = false;
    
    public void DeleteMe()
    {
        Destroy(gameObject);
    }

    /*public void SetValue(string _resource, float _numMaterials)
    {
        m_resource = _resource;
        m_materialType.text = _resource;
        m_materialCount.text = _numMaterials.ToString();
        var value = (_numMaterials - m_numMaterials);
        if(NGDesignTableInfoSheet.SetAnimText(m_animText, value, "F1"))
            m_anim.SetTrigger("FloatUp");
        var material = GetCarryFromMaterial(_resource);
        m_matarialImage.sprite = material.SpriteImage();
        m_numMaterials = _numMaterials;
        m_resource = _resource;
        m_active = true;
    }*/
    public void SetError(string _error)
    {
        m_matarialImage.gameObject.SetActive(false);
        m_materialCount.gameObject.SetActive(false);
        m_materialType.text = _error;
        GetComponent<HorizontalLayoutGroup>().childControlWidth = true;
        m_active = true;
    }



    void Activate()
    {
        
    }

    public static NGDesignTableMaterialsLine Create(NGDesignTableMaterialsLine _prefab, Transform _holder)
    {
        var go = Instantiate(_prefab.gameObject, _holder);
        var activate = go.GetComponent<NGDesignTableMaterialsLine>();
        activate.Activate();
        return activate;
    }
}
