using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
//----------------------------------------------------------------------------------------
//----------------------------------------------------------------------------------------    
[CustomPropertyDrawer(typeof(IntRange))]
class IntRangeDrawer : PropertyDrawer
{    
    // Draw the property inside the given rect
    public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
    {
        var min = property.FindPropertyRelative("m_min");
        var max = property.FindPropertyRelative("m_max");

        Rect contentPosition = EditorGUI.PrefixLabel(position, new GUIContent(property.displayName));

        //Check if there is enough space to put the name on the same line (to save space)
        if (position.height > 16f)
        {
            position.height = 16f;
            EditorGUI.indentLevel += 1;
            contentPosition = EditorGUI.IndentedRect(position);
            contentPosition.y += 18f;
        }

        float half = contentPosition.width / 2;
        GUI.skin.label.padding = new RectOffset(3, 3, 6, 6);

        //show the X and Y from the point
        EditorGUIUtility.labelWidth = 30f;
        contentPosition.width *= 0.5f;
        EditorGUI.indentLevel = 0;

        // Begin/end property & change check make each field
        // behave correctly when multi-object editing.
        EditorGUI.BeginProperty(contentPosition, label, min);
        {
            EditorGUI.BeginChangeCheck();
            var newVal = EditorGUI.IntField(contentPosition, new GUIContent("Min"), min.intValue);
            if (EditorGUI.EndChangeCheck()) min.intValue = newVal;
        }
        EditorGUI.EndProperty();

        contentPosition.x += half;

        EditorGUI.BeginProperty(contentPosition, label, max);
        {
            EditorGUI.BeginChangeCheck();
            var newVal = EditorGUI.IntField(contentPosition, new GUIContent("Max"), max.intValue);
            if (EditorGUI.EndChangeCheck()) max.intValue = newVal;
        }
        EditorGUI.EndProperty();
    }
}
#endif

//----------------------------------------------------------------------------------------    
//----------------------------------------------------------------------------------------    
[System.Serializable]
public class IntRange
{
    public int m_min;
    public int m_max;

    //----------------------------------------------------------------------------------------        
    public int GetRandom()
    {
        return Random.Range(m_min, m_max);
    }
}