using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;
using TMPro;
using static CharacterPickupBehaviour;
using System.Linq.Expressions;
using System.Text.RegularExpressions;
using Unity.Mathematics;
using System.Reflection;
using UnityEngine.UI;

#if UNITY_EDITOR
using UnityEditor.Recorder;
using LMiniJSON;
using UnityEditor;
#endif

// stand-ins for CustomInputModule interfaces
public interface IStandardClickHandler : UnityEngine.EventSystems.IEventSystemHandler { }
public interface IBeginClickHoldHandler : UnityEngine.EventSystems.IEventSystemHandler {}
public interface IEndClickHoldHandler : UnityEngine.EventSystems.IEventSystemHandler {}
public interface IBeginDragHandler : UnityEngine.EventSystems.IEventSystemHandler {
	void OnBeginDrag(PointerEventData _eventData);
}
public interface IEndDragHandler : UnityEngine.EventSystems.IEventSystemHandler {
	void OnEndDrag(PointerEventData _eventData);
}
public interface IDragHandler : UnityEngine.EventSystems.IEventSystemHandler {
	void OnDrag(PointerEventData _eventData);
}

public class DummyBehaviour : MonoBehaviour {}
public static class Utility {
#if UNITY_EDITOR
	static Utility() {
		UnityEditor.AssemblyReloadEvents.beforeAssemblyReload -= OnBeforeAssemblyReload;
		UnityEditor.AssemblyReloadEvents.beforeAssemblyReload += OnBeforeAssemblyReload;
	}
	public static void OnBeforeAssemblyReload() {
		//Debug.LogError($"OnBeforeAssemblyReload");
		if (TerrainManager.Me != null) TerrainManager.Me.ClearAllTerrainBlocks();
		if (GlobalData.Me != null) GlobalData.Me.DisposeAll();
	}
	[UnityEditor.Callbacks.DidReloadScripts(-1)]
	private static void OnScriptsReloaded() {
		//Debug.LogError($"ScriptsReload");
	}

	static long s_lastProfileTime;
	public static void Profile(string _label) {
		long t = DateTime.Now.Ticks;
		if (_label != null) {
			long dt = t - s_lastProfileTime;
			Debug.LogError($"Profile: {_label} took {dt/10000.0f:n1}");
		}
		s_lastProfileTime = t;
	}
	const string c_guidPrefix = "guid: ";
	static Dictionary<string, string> EnumerateGUIDs(string _root) {
		if (System.IO.Directory.Exists(_root) == false && _root.EndsWith("/Assets")) _root = _root[..^6];
		var metas = System.IO.Directory.EnumerateFiles(_root, "*.meta", System.IO.SearchOption.AllDirectories);
		var guids = new Dictionary<string, string>();
		foreach (var m in metas) {
			foreach (var l in System.IO.File.ReadAllLines(m)) {
				if (l.StartsWith(c_guidPrefix)) {
					var guid = l.Substring(c_guidPrefix.Length);
					guids[guid] = m;
					break;
				}
			}
		}
		return guids;
	}

	[MenuItem("22Cans/Art/Replace Shaders")]
	static void replaceShaders()
	{
		var oldShader = Shader.Find("Universal Render Pipeline/Lit");
		var oldShader2 = Shader.Find("MOA/Lit");
		var oldShader3 = Shader.Find("_Standard/StandardTinted");
		var newShader = Shader.Find("Shader Graphs/MOA_SG_Tint");
		if (Selection.activeObject != null && Selection.activeObject is Material)
		{
			var path = UnityEditor.AssetDatabase.GetAssetPath(UnityEditor.Selection.activeObject);
			replaceShader(path, oldShader, oldShader2, oldShader3, newShader);
			return;
		}
		var assets = AssetDatabase.FindAssets("t:material");
		foreach (var matID in assets)
		{
			replaceShader(AssetDatabase.GUIDToAssetPath(matID), oldShader, oldShader2, oldShader3, newShader);
		}

		/*foreach (var asset in AssetDatabase.GetAllAssetPaths())
		{
			if (asset.EndsWith(".prefab"))
			{
				var obj = PrefabUtility.LoadPrefabContents(asset);
				bool changed = false;
				foreach (var rnd in obj.GetComponentsInChildren<Renderer>())
				{
					foreach (var mat in rnd.materials)
					{
						changed |= replaceShader(mat, oldShader, oldShader2, newShader);
					}
				}
				if (changed) PrefabUtility.SaveAsPrefabAsset(obj, asset);
				PrefabUtility.UnloadPrefabContents(obj);
			}
		}*/
		Resources.UnloadUnusedAssets();
	}

	static void replaceShader(string path, Shader oldShader, Shader oldShader2, Shader oldShader3, Shader newShader)
	{
		if (path.StartsWith("Packages/")) return;
		var mat = AssetDatabase.LoadAssetAtPath<Material>(path);
		replaceShader(mat, oldShader, oldShader2, oldShader3, newShader);
	}

	static bool replaceShader(Material mat, Shader oldShader, Shader oldShader2, Shader oldShader3, Shader newShader)
	{
		if (mat == null) return false;
		bool res = false;
		if (mat.shader == oldShader || mat.shader == oldShader2 || mat.shader == oldShader3)
		{
			Debug.LogError($"Upgrading shader on {mat.name}");
			var albedoTex = mat.GetTexture("_BaseMap") ?? mat.GetTexture("_Color") ?? mat.GetTexture("_MainTex");
			var normalTex = mat.GetTexture("_BumpMap");
			var metallicTex = mat.GetTexture("_MetallicGlossMap");
			var occlusionTex = mat.GetTexture("_OcclusionMap");
			var heightTex = mat.GetTexture("_ParallaxMap");
			float metallic = mat.GetFloat("_Metalness");
			if (mat.HasProperty("_Metallic")) metallic = mat.GetFloat("_Metallic");
			float smoothness = mat.GetFloat("_Smothness");
			if (mat.HasProperty("_Glossiness")) smoothness = mat.GetFloat("_Glossiness");
			mat.shader = newShader;
			AssetDatabase.SaveAssetIfDirty(mat);
			mat.SetTexture("_BaseMap", albedoTex);
			mat.SetTexture("_BumpMap", normalTex);
			mat.SetTexture("_MaskMap", metallicTex ?? occlusionTex);
			mat.SetFloat("_Metalness", metallic);
			mat.SetFloat("_Smothness", smoothness);
			AssetDatabase.SaveAssetIfDirty(mat);
			res = true;
		}
		else if (mat.shader.name.ToLower().Contains("moa_"))
		{
			if (mat.GetTexture("_BaseMap") == null)
			{
				mat.SetTexture("_BaseMap", mat.GetTexture("_BaseColor") ?? mat.GetTexture("_Color"));
				res = true;
			}
			if (mat.GetTexture("_BumpMap") == null)
			{
				mat.SetTexture("_BumpMap", mat.GetTexture("_Normal"));
				res = true;
			}
			if (mat.GetTexture("_MaskMap") == null)
			{
				mat.SetTexture("_MaskMap", mat.GetTexture("_MetallicGlossMap") ?? mat.GetTexture("_OcclusionMap"));
				res = true;
			}
			AssetDatabase.SaveAssetIfDirty(mat);
		}
		return res;
	}


	[Serializable]
	private class Dump
	{
		[SerializeField]
		public string ClientSave;
	}
	[UnityEditor.MenuItem("22Cans/Misc/Extract Save From Dump")]
	static void ExtractSaveFromDump()
	{
		var jsonFile = EditorUtility.OpenFilePanelWithFilters("Choose a dump file", Application.persistentDataPath, new string[] { "Json files", "json", "All files", "*" });
		var json = System.IO.File.ReadAllText(jsonFile);
		var dump = (LMiniJson.JsonDecode(json) as Dictionary<string, object>);
		if (dump != null && dump.TryGetValue("ClientSave", out var save))
		{
			var bytes = System.Convert.FromBase64String(save as string);
			var outputPath = System.IO.Path.ChangeExtension(jsonFile, ".dat");
			System.IO.File.WriteAllBytes(outputPath, bytes);
			try
			{
				var finalSave = PrepareSaveFromDownload(bytes);
				Debug.LogError($"Extracted and verified dump to {outputPath}");
			}
			catch (Exception _e)
			{
				Debug.LogError($"Extracted but verification failed for {jsonFile}");
			}
		}
		else
			Debug.LogError($"Couldn't extract ClientSave from {jsonFile}");
	}
	
	[UnityEditor.MenuItem("22Cans/CI/Reimport all shaders")]
	static void ReimportAllShaders()
	{
		string[] guids = AssetDatabase.FindAssets("t:shader");
		try
		{
			AssetDatabase.StartAssetEditing();
			foreach (string guid in guids)
			{
				var path = AssetDatabase.GUIDToAssetPath(guid);
				//Debug.LogError($"Reimport shader {path}");
				AssetDatabase.ImportAsset(path);
			}
		}
		finally
		{
			AssetDatabase.StopAssetEditing();
		}
		const bool moveBrokenShaders = false;
		if (moveBrokenShaders)
		{
			foreach (string guid in guids)
			{
				var path = AssetDatabase.GUIDToAssetPath(guid);
				var shader = AssetDatabase.LoadAssetAtPath<Shader>(path);
				var info = ShaderUtil.GetShaderInfo(shader);
				if (info.hasErrors)
				{
					Debug.LogError($"shader {path} {shader.name} has errors");
					var toPath = path.Replace("Assets/", "OldShaders/");
					System.IO.Directory.CreateDirectory(System.IO.Path.GetDirectoryName(toPath));
					System.IO.File.Move(path, toPath);
					path += ".meta";
					toPath += ".meta";
					System.IO.File.Move(path, path.Replace("Assets/", "OldShaders/"));
				}
			}
		}


		//AssetDatabase.ImportAsset("Assets/_Shaders/", ImportAssetOptions.ForceUpdate | ImportAssetOptions.ImportRecursive);
		//AssetDatabase.ImportAsset("Assets/_Art/TA/Shaders/", ImportAssetOptions.ForceUpdate | ImportAssetOptions.ImportRecursive);
		//AssetDatabase.ImportAsset("Library/PackageCache/com.unity.render-pipelines.universal@14.0.4/Shaders", ImportAssetOptions.ForceUpdate | ImportAssetOptions.ImportRecursive);
		Fix2022ShonkyShaders();
	}
	

	const string c_shaderPath = "Assets/_Shaders/StandardTinted.shader";
	[UnityEditor.MenuItem("22Cans/Fix 2022 Shonky Shaders %g")]
	static void Fix2022ShonkyShaders()
	{
		var str = System.IO.File.ReadAllText(c_shaderPath);
		var strBroken = str + "_";
		System.IO.File.WriteAllText(c_shaderPath, strBroken);
		AssetDatabase.SaveAssets();
		AssetDatabase.Refresh();
		System.IO.File.WriteAllText(c_shaderPath, str);
		AssetDatabase.SaveAssets();
		AssetDatabase.Refresh();
	}

	static Dictionary<string, string> s_guids = null;
	static Dictionary<string, string> s_oldGuids = null;
	[UnityEditor.MenuItem("22Cans/Misc/Clear References Cache")]
	static void ClearReferencesCache() {
		s_guids = null;
		s_oldGuids = null;
	}
	[UnityEditor.MenuItem("22Cans/Misc/Check References")]
	static void CheckReferences() {
		const string c_oldProjectLocationFile = "../oldProjectLocation.txt";
		if (!System.IO.File.Exists(c_oldProjectLocationFile)) {
			Debug.LogError($"Please add the relative location of the old Legacy project to the text file {c_oldProjectLocationFile}");
			System.IO.File.WriteAllText(c_oldProjectLocationFile, "../Legacy\nOn the line above please give the location of the old Legacy project either relative to this project (starting with ..) or as an absolute path");
			System.Diagnostics.Process.Start(c_oldProjectLocationFile);
			return;
		}
		var oldProjectLocation = System.IO.File.ReadAllLines(c_oldProjectLocationFile)[0];
		if (s_guids == null) s_guids = EnumerateGUIDs("Assets");
		if (s_oldGuids == null) s_oldGuids = EnumerateGUIDs($"{oldProjectLocation}/Assets");
		var guids = s_guids;
		var oldGuids = s_oldGuids;
		var o = UnityEditor.Selection.objects[0];
		var path = UnityEditor.AssetDatabase.GetAssetPath(o);
		Debug.LogError($"Checking references for <color=#80ff80>{path}</color> against old project path <color=#8080ff>{oldProjectLocation}</color>");
		List<string> copies = new List<string>();
		HashSet<string> copied = new HashSet<string>();
		foreach (var l in System.IO.File.ReadAllLines(path)) {
			int guidIndex = l.IndexOf(c_guidPrefix);
			if (guidIndex == -1) continue;
			string guid = "";
			guidIndex += c_guidPrefix.Length;
			while (char.IsLetterOrDigit(l[guidIndex])) guid += l[guidIndex++];
			if (!guids.ContainsKey(guid)) {
				if (oldGuids.TryGetValue(guid, out string oldPath)) {
					if (!copied.Contains(guid)) {
						copied.Add(guid);
						copies.Add($"cp {oldPath.Replace(".meta", "")}* {System.IO.Path.GetDirectoryName(oldPath).Replace($"{oldProjectLocation}/", "")}");
						Debug.LogError($"<color=#ff0000>{path} references asset with GUID {guid} - found at {oldPath}</color>");
					}
				} else {
					Debug.LogWarning($"<color=#ffff00>{path} references asset with GUID {guid} - not found in old project</color>");
				}
			}
		}
		if (copies.Count > 0) {
			const string c_cpScript = "cpAssets";
			System.IO.File.WriteAllLines(c_cpScript, copies);
		}
	}
	
	[UnityEditor.MenuItem("22Cans/Save/Open Save Folder")]
	static void OpenSaveFolder()
	{
		System.Diagnostics.Process.Start(Application.persistentDataPath);
	}
	
	[MenuItem("22Cans/Screen Grab/Screen Grab %F2")]
	static public void ScreenGrabNormalRes()
	{
		ScreenGrab(1);
	}

	[MenuItem("22Cans/Screen Grab/Screen Grab Hi Res")]
	static void ScreenGrabHiRes()
	{
		ScreenGrab(2);
	}

	[MenuItem("22Cans/Screen Grab/Screen Grab Extra Hi Res")]
	static void ScreenGrabExtraHiRes()
	{
		ScreenGrab(4);
	}

	[MenuItem("22Cans/Screen Grab/Screen Grab Mega Hi Res #%F2")]
	static void ScreenGrabMegaHiRes()
	{
		ScreenGrab(8);
	}

	[MenuItem("22Cans/Screen Grab/Open Grab Folder")]
	static void OpenGrabFolder()
	{
		System.Diagnostics.Process.Start(System.Environment.GetFolderPath(System.Environment.SpecialFolder.MyDocuments) + "/Legacy/Grabs");
	}

#if UNITY_EDITOR
	[UnityEditor.MenuItem("22Cans/Art/Check Texture Export Settings")]
	static void CheckTextureExportSettings()
	{
		int numAutoCompressed = 0, numAutoCompressedHQ = 0, numAutoCompressedLQ = 0;
		int totalTextureCount = 0;
		foreach (var tId in AssetDatabase.FindAssets("t:Texture2D", null))
		{
			var path = AssetDatabase.GUIDToAssetPath(tId);
			if (path.StartsWith("Packages/")) continue;
			TextureImporter tImporter = AssetImporter.GetAtPath(path) as TextureImporter;
			if (tImporter != null)
			{
				++totalTextureCount;
				if (tImporter.maxTextureSize > 1024 || tImporter.maxTextureSize == 0)
				{
					int w, h;
					tImporter.GetSourceTextureWidthAndHeight(out w, out h);
					if (w > 1024 && h > 1024)
						Debug.LogError($"Max size for <color=white>{path.Replace("Assets/", "")}</color> <color=yellow>[{w}x{h}]</color> set to <color=#ff4040>{tImporter.maxTextureSize}</color>");
				}
				/*var iOSSettings = tImporter.GetPlatformTextureSettings("iOS"); // TODO - fix now that AutomaticCompressed is deprecated
				if ((int) iOSSettings.format < (int) TextureImporterFormat.ASTC_4x4 || (int) iOSSettings.format > (int) TextureImporterFormat.ASTC_12x12)
				{
					if (iOSSettings.format == TextureImporterFormat.AutomaticCompressed)
					{
						if (tImporter.textureCompression == TextureImporterCompression.CompressedHQ)
							++numAutoCompressedHQ;
						else if (tImporter.textureCompression == TextureImporterCompression.CompressedLQ)
							++numAutoCompressedLQ;
						else
							++numAutoCompressed;
					}
					else
						Debug.LogError($"Non-ASTC iOS format {iOSSettings.format} for <color=white>{path.Replace("Assets/", "")}</color>");
				}*/
			}
		}
		if (numAutoCompressed > 0)
			Debug.LogError($"{numAutoCompressedHQ}:{numAutoCompressed}:{numAutoCompressedLQ}/{totalTextureCount} iOS textures set to Automatic Compressed (High/Normal/Low)");
	}
	[UnityEditor.MenuItem("22Cans/Art/Clamp Texture Export Sizes")]
	static void ClampTextureExportSizes()
	{
		if (UnityEditor.Selection.count == 0)
		{
			Debug.LogError($"Select a folder to clamp");
			return;
		}
		var searchPath = AssetDatabase.GetAssetPath(UnityEditor.Selection.objects[0]);
		foreach (var tId in AssetDatabase.FindAssets("t:Texture2D", null))
		{
			var path = AssetDatabase.GUIDToAssetPath(tId);
			if (path.StartsWith(searchPath))
			{
				TextureImporter tImporter = AssetImporter.GetAtPath(path) as TextureImporter;
				if (tImporter != null)
				{
					if (tImporter.maxTextureSize > 1024 || tImporter.maxTextureSize == 0)
					{
						int w, h;
						tImporter.GetSourceTextureWidthAndHeight(out w, out h);
						if (w > 1024 && h > 1024)
						{
							Debug.LogError($"Setting max size for <color=white>{path.Replace("Assets/", "")}</color> <color=yellow>[{w}x{h}]</color> set to 1024 from <color=#ff4040>{tImporter.maxTextureSize}</color>");
							tImporter.maxTextureSize = 1024;
							//AssetDatabase.ImportAsset(path, ImportAssetOptions.ForceUpdate);
						}
					}
				}
			}
		}
		AssetDatabase.ImportAsset(searchPath, ImportAssetOptions.ImportRecursive);
	}
#endif
	
	[UnityEditor.MenuItem("22Cans/Art/UnRaycastTarget")]
	static void UnRaycastTarget()
	{
		var o = Selection.activeObject as GameObject;
		if (o == null)
		{
			Debug.LogError($"Select an object to make non-raycast target");
			return;
		}

		foreach (var tmp in o.GetComponentsInChildren<TextMeshProUGUI>(true))
			tmp.raycastTarget = false;

		foreach (var img in o.GetComponentsInChildren<UnityEngine.UI.Image>(true))
			img.raycastTarget = false;
	}

	[MenuItem("22Cans/Art/FindLightingIssues")]
	static void FindLightingIssues()
	{
		int count = 0;
		var selection = AssetDatabase.GetAssetPath(Selection.activeObject);
		foreach (var guid in AssetDatabase.FindAssets("t:Prefab", new string[] { selection }))
		{
			var path = AssetDatabase.GUIDToAssetPath(guid);
			GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
			foreach (var renderer in prefab.GetComponentsInChildren<Renderer>())
			{
				if (renderer.renderingLayerMask != 1)
				{
					Debug.LogError($"Prefab at path {path} has a renderer on part {renderer.gameObject.name} which does not have a layer mask set to only layer 0.", prefab);
					count++;
				}
			}
		}
		Debug.LogError($"Found {count} renderers with wrong layer mask");
	}

	[MenuItem("22Cans/Art/FixLightingIssues")]
	static void FixLightingIssues()
	{
		int count = 0;
		var selection = AssetDatabase.GetAssetPath(Selection.activeObject);
		foreach (var guid in AssetDatabase.FindAssets("t:Prefab", new string[] { selection }))
		{
			var path = AssetDatabase.GUIDToAssetPath(guid);
			GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
			foreach (var renderer in prefab.GetComponentsInChildren<Renderer>())
			{
				if (renderer.renderingLayerMask != 1)
				{
					renderer.renderingLayerMask = 1;
					Debug.LogError($"Setting layer mask of part {renderer.gameObject.name} on prefab at path {path} to only layer 0.", prefab);
					count++;
				}
			}
		}
		Debug.LogError($"Fixed {count} renderers with wrong layer mask");
	}
	
	[MenuItem("22Cans/Testing/Enable Combat Testing")]
	public static void EnableCombatTesting()
	{
		PlayerSettings.GetScriptingDefineSymbols(UnityEditor.Build.NamedBuildTarget.Standalone, out string[] defines);
		List<string> definedSymbols = new List<string>(defines);
		definedSymbols.Add("COMBAT_TESTING_ENABLED");
		PlayerSettings.SetScriptingDefineSymbols(UnityEditor.Build.NamedBuildTarget.Standalone, definedSymbols.ToArray());
	}
	[MenuItem("22Cans/Testing/Enable Combat Testing", true)]
    public static bool EnableCombatTestingValidate()
    {
        return !IsCombatTestingEnabled();
    }
	[MenuItem("22Cans/Testing/Disable Combat Testing")]
	public static void DisableCombatTesting()
	{
		PlayerSettings.GetScriptingDefineSymbols(UnityEditor.Build.NamedBuildTarget.Standalone, out string[] defines);
		List<string> definedSymbols = new List<string>(defines);
		definedSymbols.Remove("COMBAT_TESTING_ENABLED");
		PlayerSettings.SetScriptingDefineSymbols(UnityEditor.Build.NamedBuildTarget.Standalone, definedSymbols.ToArray());
	}
	[MenuItem("22Cans/Testing/Disable Combat Testing", true)]
    public static bool DisableCombatTestingValidate()
    {
        return IsCombatTestingEnabled();
    }
    private static bool IsCombatTestingEnabled()
    {
		PlayerSettings.GetScriptingDefineSymbols(UnityEditor.Build.NamedBuildTarget.Standalone, out string[] defines);
		List<string> definedSymbols = new List<string>(defines);

        return definedSymbols.Contains("COMBAT_TESTING_ENABLED");
    }

#endif

	public static int GlobalTextureLimit
	{
		get
		{
#if UNITY_2022_1_OR_NEWER
			return QualitySettings.globalTextureMipmapLimit;
#else
                return QualitySettings.masterTextureLimit;
#endif
		}
		set
		{
#if UNITY_2022_1_OR_NEWER
			QualitySettings.globalTextureMipmapLimit = value;
#else
                QualitySettings.masterTextureLimit = value;
#endif
		}
	}
	
	private static HashSet<string> s_shownErrors = new();
	public static void ShowErrorOnce(string _message, GameObject _obj = null)
	{
		if (s_shownErrors.Contains(_message)) return;
		s_shownErrors.Add(_message);
		Debug.LogError(_message, _obj);
	}

	static int m_nextGrab = 0;
	public static string LastGrabFileName(int _offset)
	{
        return string.Format("grab{0:0000}.png", _offset);
    }
    static public void ScreenGrab(int _superSize, int _slot = -1)
	{
		//string path = Application.persistentDataPath;
#if (UNITY_IOS || UNITY_ANDROID) && !UNITY_EDITOR
        string path = Application.persistentDataPath + "/Grabs";
#else
		string path = System.Environment.GetFolderPath(System.Environment.SpecialFolder.MyDocuments) + "/Legacy/Grabs";
#endif
		System.IO.Directory.CreateDirectory(path);
		
		
		string full_path = "";
		if (_slot == -1)
		{
			string name = "";
			while (true)
			{
				name = string.Format ("grab{0:0000}.png", ++m_nextGrab);
				full_path = string.Format("{0}/{1}", path, name);
				if (!System.IO.File.Exists (full_path))
					break;
			}
		}
		else
		{
			full_path = $"{path}/grab{_slot:0000}.png";
		}
		Debug.LogErrorFormat("Grab screen shot to {0}", full_path);
		ScreenCapture.CaptureScreenshot(full_path, _superSize);	
	}
	
	public static float LastClickLength { get; set; }
	public static string[] SplitSpanClassStrings(string _data)
	{
		string[] items = _data.Split("<span");
		int c = 0;
		foreach (string s in items)
		{
			if (s == "") continue;
			var cutPoint = s.IndexOf('>') + 1;
			var cutEnd = s.IndexOf('<');
			items[c] = s.Substring(cutPoint, cutEnd - cutPoint);
			c++;
		}

		string[] newData = new string[c];
		for (int i = 0; i < c; i++)
			newData[i] = items[i];

		return newData;
	}

	public static Color HexToColor(string _hexString)
	{
		if(_hexString.IsNullOrWhiteSpace())
			return Color.white;
		if (_hexString.IndexOf('#') != -1)
			_hexString = _hexString.Replace("#", "");
		byte r,g,b = 0;
		byte a = 255;
		
		r = byte.Parse(_hexString.Substring(0, 2));
		g = byte.Parse(_hexString.Substring(2, 2));
		b = byte.Parse(_hexString.Substring(4, 2));

		if (_hexString.Length > 6)
			a = byte.Parse(_hexString.Substring(6, 2));
		return new Color32(r, g, b, a);
	}

	private static float s_diagonal = 0;
	private static bool s_isTablet = false;
	public static bool IsTablet
	{
		get
		{
			if (s_diagonal == 0)
			{
				var dpi = Screen.dpi;
				if (dpi == 0) dpi = 96;
				s_diagonal = Mathf.Sqrt(Screen.width * Screen.width + Screen.height * Screen.height) / dpi;
				s_isTablet = s_diagonal > 6.9f;
				Debug.Log($"Device diagonal {s_diagonal}\" tablet:{s_isTablet}");
			}
			return s_isTablet;
		}
	}
	
	static DebugConsole.Command s_dialog = new DebugConsole.Command("dialog", _s =>
	{
		var bits = _s.Split("&");
		ShowDialog(bits[0], bits[1], true, bits.Length > 2 ? bits[2] : "Ok", bits.Length > 3 ? bits[3] : null, _n => { });
	});
	
	static bool s_latchMouseWheel = false;
	static float s_wheelSmoothing = 0.9f;
	static float s_rawMouseWheel = 0;
	static float s_unsmoothedMouseWheel = 0;
	static float s_smoothedMouseWheel = 0;
	const int c_mouseWheelSmoothingWindow = 2;//16;
	static float[] s_lastMouseWheels = new float[c_mouseWheelSmoothingWindow];
	static int s_lastMouseWheelsIndex = 0;
	static bool s_ignoreMouseWheel = false;

	public class ShakeData
	{
		public Vector3 m_mousePosition = Vector3.zero;
		public bool m_moveDirection = false;
		public float m_moveDirectionChangeTime = 0;
		public int m_directionChanges = 0;
		public bool m_isShaking = false;
		public Vector3 m_directionChangePosition;

		public void Reset(int _id)
		{
			m_directionChangePosition = GameManager.InputPosition(_id);
			m_moveDirectionChangeTime = 0;
			m_directionChanges = 0;
			m_isShaking = false;
		}
	}
	static ShakeData[] s_shakeData = null;
	const int m_mouseShakeDirectionChanges = 3;
	const float m_mouseShakeMaxTimeBetween = 0.15f;
	const float c_mouseShakeMinInchesPerSecond = 5;

	public static bool IsMouseShaking(int _id) { return s_shakeData[_id].m_isShaking; }

	public static void ResetMouseShake(int _id)
	{
		s_shakeData[_id].Reset(_id);
	}

	public static void UpdateMouseShake()
	{
		if (s_shakeData == null)
		{
			s_shakeData = new ShakeData[16];
			for (int i = 0; i < s_shakeData.Length; ++i)
				s_shakeData[i] = new ShakeData();
		}
		int maxInputsToCheck = TouchManager.TouchInputActive ? 16 : 1;
		for (int i = 0; i < maxInputsToCheck; ++i)
		{
			if (GameManager.GetMouseButton(i) == false)
			{
				ResetMouseShake(i);
				continue;
			}

			var mousePos = GameManager.InputPosition(i);
			s_shakeData[i].m_mousePosition = mousePos;

			var xSpeed = (mousePos - s_shakeData[i].m_directionChangePosition).x / Time.deltaTime;
			var mouseDeltaDirection = xSpeed > 0;

			s_shakeData[i].m_moveDirectionChangeTime += Time.deltaTime;
			if (s_shakeData[i].m_moveDirectionChangeTime >= m_mouseShakeMaxTimeBetween)
			{
				s_shakeData[i].m_directionChanges = 0;
				s_shakeData[i].m_moveDirection = !mouseDeltaDirection;
			}

			float pixelsPerSecond = TouchManager.InchesToPixels(c_mouseShakeMinInchesPerSecond);
			if (mouseDeltaDirection != s_shakeData[i].m_moveDirection && Mathf.Abs(xSpeed) > pixelsPerSecond)
			{
				s_shakeData[i].m_directionChangePosition = mousePos;
				s_shakeData[i].m_directionChanges++;
				s_shakeData[i].m_moveDirection = mouseDeltaDirection;
				s_shakeData[i].m_moveDirectionChangeTime = 0f;
			}

			if (s_shakeData[i].m_directionChanges > m_mouseShakeDirectionChanges)
				s_shakeData[i].m_isShaking = true;
		}
	}

	public static float GameTime = 0;
	public static (bool, bool) UpdateMouseWheel() {
		GameTime += Time.deltaTime;
		
		var ui = GetUIObjectAt(ClampedMousePosition);
		var uiRaw = ui;
		if (ui != null)
		{
			bool isValid = false;
			if (ui.GetComponentInParent<PassDragToScrollView>() != null) isValid = true;
			else if (ui.GetComponentInParent<UnityEngine.UI.ScrollRect>() != null) isValid = true;
			else isValid |= ui.GetComponentInParent<MAGUIBase>()?.m_isMouseWheelPriority ?? false;
			if (isValid == false) ui = null;
		}

		s_rawMouseWheel = (IsMouseInWindow && !s_ignoreMouseWheel) ? Input.GetAxis("Mouse ScrollWheel") : 0;
		s_unsmoothedMouseWheel = Sign(s_rawMouseWheel);
		if (ui != null) s_unsmoothedMouseWheel = 0;
		if (s_latchMouseWheel)
		{
			if (s_unsmoothedMouseWheel * s_unsmoothedMouseWheel > 0)
				s_unsmoothedMouseWheel = 0;
			else
				s_latchMouseWheel = false;
		}

		float wheelSmoothCandidate = s_unsmoothedMouseWheel;
		//-- smooth by considering the max of the last n wheel values
		s_lastMouseWheels[(s_lastMouseWheelsIndex++) & (c_mouseWheelSmoothingWindow-1)] = s_unsmoothedMouseWheel;
		float absMax = 0;
		for (int i = 0; i < c_mouseWheelSmoothingWindow; i ++) {
			if (s_lastMouseWheels[i] * s_unsmoothedMouseWheel < 0)
				s_lastMouseWheels[i] = 0;
			if (Mathf.Abs(s_lastMouseWheels[i]) > Mathf.Abs(absMax))
				absMax = s_lastMouseWheels[i];
		}
		wheelSmoothCandidate = absMax;

		float smoothing = s_wheelSmoothing;
		s_smoothedMouseWheel = s_smoothedMouseWheel * smoothing + wheelSmoothCandidate * (1-smoothing);
		if (Mathf.Abs(s_smoothedMouseWheel) < 0.01f)
			s_smoothedMouseWheel = 0;
		
		return (ui != null, uiRaw != null);
	}
	public static bool IgnoreMouseWheel
    {
        get { return s_ignoreMouseWheel; }
        set { s_ignoreMouseWheel = value; }
    }
	
	public static bool IsMouseInWindow {
		get {
			var mousePos = Input.mousePosition;
			return mousePos.x >= 0 && mousePos.x < Screen.width && mousePos.y >= 0 && mousePos.y < Screen.height;
		}
	}
	
	public static float Sign(float _value)
	{
		if (_value > 0.0001f)
			return 1f;
		else if (_value < -0.0001f)
			return -1f;
		else
			return 0;
	}

	static public String PathSimplify(String path)
	{
		while (true)
		{
			String newPath = new Regex(@"[^\\/]+(?<!\.\.)[\\/]\.\.[\\/]").Replace(path, "" );
			if (newPath == path) break;
			path = newPath;
		}
		return path;
	}
	
	static	public	List<string>	GetNextLine(string text, ref int offset)
	{
		List<string>	lines = new List<string>();
		bool			in_quote_flag = false;
		int				length = text.Length;
		int				start_line = offset;
		for(; offset < length; offset++)
		{
			char	c = text[offset];
			switch (c)
			{
				case	'"':
					if(in_quote_flag == false)
					{
						start_line++;
						in_quote_flag = true;
					}
					else
						in_quote_flag = false;
					break;
				case	',':
					if(in_quote_flag == false)
					{
						int 	copy_length = offset - start_line;
						if(copy_length > 0 && text[offset-1] == '"')
							copy_length--;
						lines.Add(text.Substring(start_line, copy_length).Trim()); start_line = offset+1;			
					}	
					break;
				case	'\n':
					if(in_quote_flag == false)
					{
						lines.Add(text.Substring(start_line,offset-start_line).Trim()); start_line = offset+1;					
						offset++;
						return	lines;
					}
					break;
			}
		}
		if(offset == start_line)
		{
			if (lines.Count == 0)
				return	null;
		}
		else
			lines.Add(text.Substring(start_line,offset-start_line).Trim());					
		offset++;
		return	lines;
	}
	public static bool TryParseRect(string _what, out Rect _value)
	{
		_value = new Rect();
		string[] bits = _what.Split(',');
		if(bits.Length != 4)
			return	false;
		if(floatinv.TryParse(bits[0], out var sx) == false)
			return	false;
		if(floatinv.TryParse(bits[1], out var sy) == false)
			return	false;
		if(floatinv.TryParse(bits[2], out var width) == false)
			return	false;
		if(floatinv.TryParse(bits[3], out var height) == false)
			return	false;
		_value = new Rect(sx, sy, width, height);
		return	true;
	}
	public static bool TryParsePercentage(string _what, out float _value) {
		_what = _what.Trim();
		if(_what.EndsWith("%"))
		{
			_what = _what.TrimEnd('%');
			if(floatinv.TryParse(_what, out _value))
			{
				_value/=100f;
				return true;
			}
			return false;
		}
		
		return floatinv.TryParse(_what, out _value);
	}
	
	public static bool IsNullOrEmpty(this MAOrder _this)
	{
		return (_this == null || _this == MAOrder.EmptyOrder);
	}

	public static bool IsQuitting = false;
	public static bool IsShuttingDown => IsQuitting || GameManager.Me == null || GlobalData.Me == null || GlobalData.Me.Heights.Length == 0;

    public static bool IsInputFieldActive()
    {
        if (EventSystem.current.currentSelectedGameObject != null)
        {
			UnityEngine.UI.InputField IF = EventSystem.current.currentSelectedGameObject.GetComponent<UnityEngine.UI.InputField>();
			if (IF != null)
			{
				return true;
			}
			TMP_InputField TIF = EventSystem.current.currentSelectedGameObject.GetComponent<TMP_InputField>();
			if (TIF != null)
			{
				return true;
			}
		}
		return false;
    }

    public static string GetPural(string _what, int _count)
    {
	    Dictionary<string, string> purals = new Dictionary<string, string>()
	    {
		    {"worker", "workers"},
		    {"Worker", "Workers"},
		    {"bedroom", "bedrooms"},
		    {"Bedroom", "Bedrooms"},
		    {"component", "components"},
		    {"Component", "Components"},
	    };
	    if (_count > 1)
	    {
		    if (purals.TryGetValue(_what, out var pural))
			    return pural;
		    return _what + "s";
	    }
	    else
		    return _what;
    }
	    

    public static string SecondsToString(float _seconds) {
		if (_seconds < 60f) {
			return _seconds.ToString("n0") + "s";
		}
		float minutes = Mathf.Floor(_seconds / 60f);
		_seconds -= minutes * 60f;
		if (minutes < 60f) {
			return minutes.ToString("n0") + "m " + _seconds.ToString("n0") + "s";
		}
		float hours = Mathf.Floor(minutes / 60f);
		minutes -= hours * 60f;
		return hours.ToString("n0") + "h " + minutes.ToString("n0") + "m";
	}

	public static int CountBits(ulong _n) {
		int count = 0;
		while (_n != 0) { _n &= _n-1; count ++; }
		return count;
	}
	public static int CountBits(uint _n) { return CountBits((ulong)_n); }
	public static int CountBits(int _n) { return CountBits((ulong)(uint)_n); }

	public static uint XorShift(ref uint _seed)
	{
		_seed ^= _seed << 13;
		_seed ^= _seed >> 17;
		_seed ^= _seed << 5;
		return _seed;
	}
	
	public static float XorShiftRange(ref uint _seed, float _min, float _max)
	{
		return Mathf.Lerp(_min, _max, XorShift01(ref _seed));
	}
	
	public static int XorShiftRange(ref uint _seed, int _min, int _max)
	{
		var r = (int)XorShift(ref _seed) & 0xFFFF;
		return _min + (((1 + _max - _min) * r) >> 16);
	}
	
	public static float XorShift01(ref uint _seed)
	{
		var res = XorShift(ref _seed);
		return (float) (res & 0xFFFFFF) / (float) (0xFFFFFF + 1);
	}
	public static float XorShift01(int _seed) // one-off random
	{
		uint finalSeed = (uint)_seed;
		XorShift(ref finalSeed);
		XorShift(ref finalSeed);
		return XorShift01(ref finalSeed);
	}
	
	static uint HashInto(uint _hash, byte[] _b) {
		for (int i = 0; i < _b.Length; i ++) {
			_hash += _b[i];
			_hash += _hash << 10;
			_hash ^= _hash >> 6;
		}
		return _hash;
	}
	static uint HashFinal(uint _hash) {
		uint final = _hash;
		final += (final << 3);
		final ^= (final >> 11);
		final += (final << 15);
		return final;
	}
	static int Rand64kFromFloat2(float _f1, float _f2, int _seed) {
		uint hash = HashInto(0, System.BitConverter.GetBytes(_f1));
		hash = HashInto(hash, System.BitConverter.GetBytes(_f2));
		hash = HashInto(hash, System.BitConverter.GetBytes(_seed));
		return (int)(HashFinal(hash) & 0xFFFF);
	}
	/*static int _FloatToInt(float _v) { return (int)(_v * 12345.678f); }
	static int Rand64kFromFloat2(float _f1, float _f2, int _seed) {
		int v = _seed;
		v *= 71381;
		v = ((v << 27) | (v >> 5)) + 98143;
		v ^= _FloatToInt(_f1);
		v *= 13331;
		v = ((v << 27) | (v >> 5)) + 98143;
		v ^= _FloatToInt(_f2);
		v *= 89751;
		v = ((v << 27) | (v >> 5)) + 98143;
		return v & 65535;
	}*/
	public static int RandIntFromPos(Vector3 _pos, int _seed, int _minInc, int _maxExc) {
		var r64 = Rand64kFromFloat2(_pos.x, _pos.z, _seed);
		return _minInc + (((_maxExc - _minInc) * r64) >> 16);
	}
	public static int RandIntFromPos(float _x, float _z, int _seed, int _minInc, int _maxExc) {
		var r64 = Rand64kFromFloat2(_x, _z, _seed);
		return _minInc + (((_maxExc - _minInc) * r64) >> 16);
	}
	public static float RandFloatFromPos(Vector3 _pos, int _seed, float _min, float _max) {
		var r64 = (float)Rand64kFromFloat2(_pos.x, _pos.z, _seed) * (1f/65536f);
		return _min + (_max - _min) * r64;
	}
	public static float RandFloatFromPos(float _x, float _z, int _seed, float _min, float _max) {
		var r64 = (float)Rand64kFromFloat2(_x, _z, _seed) * (1f/65536f);
		return _min + (_max - _min) * r64;
	}

	public static int RandInt(this Vector3 _this, int _seed, int _min, int _max) {
		return RandIntFromPos(_this, _seed, _min, _max);
	}
	//==
	static int s_baseSeed = 0x19283746;
	public static int IntRand(int bottomInc, int topExc) {
		return SimpleRndRange(bottomInc, topExc, ref s_baseSeed);
	}
	//==
	public static int SimpleRndNext(int _prev) {
		_prev = _prev * 7317421 + 12388343;
		_prev = (_prev >> 19) | (_prev << 13);
		_prev ^= _prev * 153467;
		return _prev;
	}
	public static int SimpleRndRange(int _minInc, int _maxExc, ref int _seed) {
		_seed = SimpleRndNext(_seed);
		return _minInc + ((_seed & 0xFFFF) * (_maxExc - _minInc) >> 16);
	}
	//==
	public static float xzMagnitude(this Vector3 _this) {
		return Mathf.Sqrt(_this.x * _this.x + _this.z * _this.z);
	}
	public static float xzSqrMagnitude(this Vector3 _this) {
		return _this.x * _this.x + _this.z * _this.z;
	}
	public static Vector3 NewX(this Vector3 _this, float _newX) {
		_this.x = _newX;
		return _this;
	}
	public static Vector3 NewY(this Vector3 _this, float _newY) {
		_this.y = _newY;
		return _this;
	}
	public static Vector3 NewZ(this Vector3 _this, float _newZ) {
		_this.z = _newZ;
		return _this;
	}
	
	public static Vector3 GetX(this Vector3 v) {
		Vector3 temp = v;
		temp.y = temp.z = 0;
		return temp;
	}
	public static Vector3 GetY(this Vector3 v) {
		Vector3 temp = v;
		temp.x = temp.z = 0;
		return temp;
	}
	public static Vector3 GetZ(this Vector3 v) {
		Vector3 temp = v;
		temp.x = temp.y = 0;
		return temp;
	}
	
	public static Vector3 V3XZ(this Vector2 _this) {
		return new Vector3(_this.x, 0, _this.y);
	}

	public static bool AlmostEquals(this Vector3 _this, Vector3 _other, float _epsilon = .001f)
	{
		var dx = _this.x - _other.x;
		var dy = _this.y - _other.y;
		var dz = _this.z - _other.z;
		return dx * dx + dy * dy + dz * dz < _epsilon * _epsilon;
	}

	public static Vector3 AboveGround(this Vector3 _this, float _howFarAbove)
	{
		var groundY = GlobalData.Me.GetRealHeight(_this);
		if (_this.y < groundY + _howFarAbove)
			_this.y = groundY + _howFarAbove;
		return _this;
	}

	public static float HeightAboveGround(this Vector3 _this)
	{
		float groundHeight = GlobalData.Me.GetRealHeight(_this);
		return _this.y - groundHeight;
	}
	
	public static Vector3 GroundPosition(this Vector3 _this, float _raise = 0, bool _originalHeight = false) {
		_this.y = GlobalData.Me.GetRealHeight(_this, _originalHeight) + _raise;
		return _this;
	}

	public static Vector3 GroundPositionCamera(this Vector3 _this, float _raise = 0)
	{
		_this.y = GlobalData.Me.GetCameraBaseHeight(_this) + _raise;
		return _this;
	}	

	public static Vector3 GroundPositionFast(this Vector3 _this, float _raise = 0)
	{
		_this.y = GlobalData.Me.GetRawHeight(_this) + _raise;
		return _this;
	}

	public static float3 GroundPosition(this float3 _this, float _raise = 0)
	{
		return ((Vector3)_this).GroundPosition(_raise);
	}
	
	public static bool IsUnderGround(this Vector3 _this, float _offset = 0)
	{
		var groundPos = _this.GroundPosition(_offset);
		return _this.y < groundPos.y;
	}

	public static Vector3 ToSeaLevel(this Vector3 _this, float _raise = 0) {
		_this.y = GlobalData.c_seaLevel + _raise;
		return _this;
	}

	static RaycastHit[] s_raycastHits = new RaycastHit[200];
	public static Vector3 PhysicsPosition(this Vector3 _this, float _raise = 0, GameObject _exclude = null, bool _excludeRigidbodies = false)
	{
		var ray = new Ray(_this + Vector3.up * 100, Vector3.down);
		var highestHit = -1e23f;
		int numHits = Physics.RaycastNonAlloc(ray, s_raycastHits, 1000, -1, QueryTriggerInteraction.Ignore);
		var excludeTransform = _exclude?.transform;
		for (int i = 0; i < numHits; ++i)
		{
			var hit = s_raycastHits[i];
			if (hit.point.y <= highestHit) continue;
			if (hit.collider.transform.IsChildOf(excludeTransform)) continue;
			if(_excludeRigidbodies && hit.rigidbody != null) continue;
			
			highestHit = hit.point.y;
		}
		if (highestHit > -1e22f)
			_this.y = highestHit + _raise;
		return _this;
	}

	public enum ReseatType
	{
		GroundPosition,
		PhysicsPosition,
		PhysicsPositionExcludeRidigbodies,
	}
	public static void Reseat(this Transform _this, float _raise, ReseatType _type)
	{
		switch(_type)
		{
		case ReseatType.GroundPosition:
			_this.position = _this.position.GroundPosition(_raise);
			break;
		case ReseatType.PhysicsPosition:
			_this.position = _this.position.PhysicsPosition(_raise, _this.gameObject);
        			break;
		case ReseatType.PhysicsPositionExcludeRidigbodies:
			_this.position = _this.position.PhysicsPosition(_raise, _this.gameObject, true);
			break;
		}
	}

	public static float SampleHeight(Vector3 _pos) { return GlobalData.Me.GetRealHeight(_pos); }

	
	public static void Deconstruct(this int2 i2, out int x, out int y) { x = i2.x; y = i2.y; }


	public static bool IsOn(string _s) {
		_s = _s.ToLower();
		return _s != "0" && _s != "off" && !_s.StartsWith("f");
	}
	public static void SetOrToggle(ref bool _b, string _s) {
		_b = SetOrToggle(_b, _s);
	}
	public static bool SetOrToggle(bool _b, string _s) {
		if (string.IsNullOrEmpty(_s))
			_b = !_b;
		else
			_b = IsOn(_s);
		return _b;
	}
	public static void SetOrToggle(ref int _n, string _s, int _d = 1) {
		_n = SetOrToggle(_n, _s, _d);
	}
	public static int SetOrToggle(int _n, string _s, int _d = 1) {
		if (string.IsNullOrEmpty(_s))
			_n = (_n == 0) ? _d : 0;
		else
			int.TryParse(_s, out _n);
		return _n;
	}
	public static int GetIntFromString(string _s, int _default = 0) {
		int r;
		if (!int.TryParse(_s, out r)) r=  _default;
		return r;
	}
	
	public static bool Nearly(this float _this, float _f, float _epsilon = .0001f) {
		var d = _f - _this;
		return d * d < _epsilon * _epsilon;
	}

	public static Vector3 EulerY(this int _this)
	{
		var rad = _this * Mathf.Deg2Rad;
		return new Vector3(Mathf.Sin(rad), 0, Mathf.Cos(rad));
	}
	public static Vector3 EulerY(this float _this)
	{
		_this *= Mathf.Deg2Rad;
		return new Vector3(Mathf.Sin(_this), 0, Mathf.Cos(_this));
	}

	public static bool IsChildOf(this Transform _t, Transform _find) {
		if (_t == _find) return true;
		if (_t.parent == null) return false;
		return _t.parent.IsChildOf(_find);
	}

	public static void RunNextFrame(this Transform _this, System.Action _action) {
		var cmp = _this.gameObject.AddComponent<DummyBehaviour>(); // a bit long-winded but ensures that if the object is destroyed the coroutine goes with it
		cmp.StartCoroutine(_this.Co_RunNextFrame(_action, cmp));
	}
	public static IEnumerator Co_RunNextFrame(this Transform _this, System.Action _action, DummyBehaviour _tmpCmp) {
		yield return null;
		_action();
		//Debug.LogErrorFormat("RunNextFrame");
		UnityEngine.Object.Destroy(_tmpCmp);
	}

	static int s_bitsPerTopologicalLayer = 6;
	static int s_maxTopologicalLayers = 30 / s_bitsPerTopologicalLayer;
	static int s_usesSwitchObjectsBit = 1 << 30;
	static int s_layerMask = (1 << s_bitsPerTopologicalLayer) - 1;
	public static int GetTopologicalIDFromTransform(this Transform _this, Transform _root) {
		// search up the hierarchy until you find either _root or an object referenced in an immediate parent SwitchObject instance
		int id = 0;
		var t = _this;
		for (int i = 0; i < s_maxTopologicalLayers; i++) {
			id <<= s_bitsPerTopologicalLayer;
			id |= (t.GetSiblingIndex() + 1);
			t = t.parent;
			if (t == _root || t == null) break;
			if (t.parent != null) {
				var so = t.parent.GetComponent<SwitchObjects>();
				if (so != null) {
					if (so.Switches(t.gameObject)) {
						id |= s_usesSwitchObjectsBit;
						break;
					}
				}
			}
		}
		return -id;
	}

	// Extensions to store and restore a transform relative to its parents
	// Includes support for SwitchObject groups so we can find the equivalent object in an alternate switch
	public static Transform GetTransformFromTopologicalID(this Transform _root, int _id) {
		if (_id >= 0) {
			if (_id < _root.childCount) return _root.GetChild(_id);
			return null;
		}
		_id = -_id;

		Transform current = _root;
		if ((_id & s_usesSwitchObjectsBit) != 0) {
			_id &= ~s_usesSwitchObjectsBit;
			var so = current.GetComponentInChildren<SwitchObjects>();
			if (so != null) {
				current = so.GetCurrentSetObject().transform;
			} else {
				return null; // error - flagged to use SwitchObject but no SwitchObject found
			}
		}
		while (_id != 0) {
			int index = (_id & s_layerMask) - 1;
			if (index == -1)
				return null; // error - got a zero id between non-zero ids
			_id >>= s_bitsPerTopologicalLayer;
			if (index >= current.childCount)
				return null; // error - unexpected child index
			current = current.GetChild(index);
		}
		return current;
	}

	public static float GetTInSegment(Vector3 _position, Vector3 _a, Vector3 _b)
	{
		var ab = _b - _a; var t = Vector3.Dot(_position - _a, ab) / Vector3.Dot(ab, ab);
		return Mathf.Clamp01(t);
	}

	public static Vector3 constrainToSegment(Vector3 position, Vector3 a, Vector3 b) {
		return Vector3.Lerp(a, b, GetTInSegment(position, a, b));
	}
	public static float LineSegmentMinimumDistanceSquared(Vector3 _a1, Vector3 _a2, Vector3 _b1, Vector3 _b2, out Vector3 _closestOnA, out Vector3 _closestOnB) {
		var b12 = _b2-_b1; float lineDirSqrMag = Vector3.Dot(b12, b12);
		var inPlaneA = _a1-((Vector3.Dot(_a1-_b1, b12)/lineDirSqrMag)*b12);
		var inPlaneB = _a2-((Vector3.Dot(_a2-_b1, b12)/lineDirSqrMag)*b12);
		var inPlaneBA = inPlaneB-inPlaneA;
		var t = Vector3.Dot(_b1-inPlaneA, inPlaneBA)/Vector3.Dot(inPlaneBA, inPlaneBA);
		t = (inPlaneA != inPlaneB) ? t : 0f; // Zero's t if parallel
		var aToLineB = Vector3.Lerp(_a1, _a2, Mathf.Clamp01(t));
		var aToB = constrainToSegment(aToLineB, _b1, _b2);
		var bToA = constrainToSegment(aToB, _a1, _a2);
		_closestOnA = aToB; _closestOnB = bToA;
		return (aToB - bToA).sqrMagnitude;
	}

	public static float CrossXZ(Vector3 _v, Vector3 _w)
	{
		return _v.x * _w.z - _v.z * _w.x;
	}
	public static void LineSegmentIntersectXZ(Vector3 _p, Vector3 _p2, Vector3 _q, Vector3 _q2, out float _t, out float _u, bool _unclamped = false)
	{
		Vector3 r = _p2 - _p, s = _q2 - _q;
		if (true)
		{
			var rp = new Vector3(r.z, 0, -r.x);
			var sp = new Vector3(s.z, 0, -s.x);
			var rDotSp = Vector3.Dot(r, sp);
			if (rDotSp * rDotSp < .01f * .01f)
			{
				_t = _u = 0;
			}
			else
			{
				_t = Vector3.Dot(_q - _p, sp) / rDotSp;
				_u = Vector3.Dot(_q - _p, rp) / rDotSp;
				if (_unclamped == false)
				{
					_t = Mathf.Clamp01(_t);
					_u = Mathf.Clamp01(_u);
				}
			}
			return;
		}

		var rCrossS = CrossXZ(r, s);
		if (rCrossS * rCrossS < .01f * .01f)
		{
			_t = _u = 0;
		}
		else
		{
			_t = CrossXZ(_q - _p, s) / rCrossS;
			_u = CrossXZ(_q - _p, r) / rCrossS;
			if (_unclamped == false)
			{
				_t = Mathf.Clamp01(_t);
				_u = Mathf.Clamp01(_u);
			}
		}
	}

	public static void LineSegmentIntersectXZ(float3 _p, float3 _p2, float3 _q, float3 _q2, out float _t, out float _u, bool _unclamped = false)
	{
		float3 r = _p2 - _p, s = _q2 - _q;
		if (true)
		{
			var rp = new float3(r.z, 0, -r.x);
			var sp = new float3(s.z, 0, -s.x);
			var rDotSp = math.dot(r, sp);
			if (rDotSp * rDotSp < .01f * .01f)
			{
				_t = _u = 0;
			}
			else
			{
				_t = math.dot(_q - _p, sp) / rDotSp;
				_u = math.dot(_q - _p, rp) / rDotSp;
				if (_unclamped == false)
				{
					_t = math.saturate(_t);
					_u = math.saturate(_u);
				}
			}
		}
	}

	public static float DistSqrdToRay(Vector3 _point, Ray _ray)
	{
		var oToP = _point - _ray.origin;
		var along = Vector3.Dot(oToP, _ray.direction);
		var nearest = _ray.origin + _ray.direction * along;
		return (nearest - _point).sqrMagnitude;
	}

	public static float ClampAngleBetween(float angle, float min, float max)
	{
		if (AngleBetween(angle, min, max))
			return angle;
		return ClosestAngle(angle, min, max);
	}

	public static bool AngleBetween(float a, float low, float high)
	{
		a = Mathf.Repeat(a, 360f);
		low = Mathf.Repeat(low, 360f);
		high = Mathf.Repeat(high, 360f);
		if (low <= high) //No wrapping in this case
			return a >= low && a <= high;
		//There is wrapping so we need to check that a is between low and 360, or 0 and high.
		//But we know it's not less than 0 or greater than 360 thanks to the Repeat at the start
		return (a >= low || a <= high); 
	}
	
	public static float ClosestAngle(float x, float a, float b)
	{
		float aDist = Mathf.Repeat(a - x, 360f);
		if (aDist > 180f)
			aDist = 360f - aDist;
		float bDist = Mathf.Repeat(b - x, 360f);
		if (bDist > 180f)
			bDist = 360f - bDist;
		return (aDist < bDist) ? a : b;
	}

	public static float ClampAngle(float _t) {
		if (_t > 180f) _t -= 360f;
		else if (_t < -180f) _t += 360f;
		return _t;
	}

	public static Vector3 SlerpFixedY(Vector3 a, Vector3 b, float t)
	{
		return SlerpAboutAxis(a, b, Vector3.up, t);
	}

	public static Vector3 SlerpAboutAxis(Vector3 a, Vector3 b, Vector3 axis, float t)
	{
		//Instead of taking the shortest path over the sphere, this function slerps over the path that changes the least in the direction of axis.
		if (a.sqrMagnitude.IsZero())
			return b * t;
		if (b.sqrMagnitude.IsZero())
			return a * (1 - t);

		//First we want to find the plane a and b both lie on with the largest angle to axis
		Vector3 tangent = Vector3.Cross(a - b, axis); //Get a second line tangent to the plane
		Vector3 n = Vector3.Cross(a - b, tangent).normalized; //Find the normal of the plane
		float d = Vector3.Dot(a, n); //From the general formula of a plane: r dot n = d
		Vector3 newO = n * d; //The origin projected onto the plane
		Vector3 newA = a - newO; //Calculate a and b relative to the projected origin
		Vector3 newB = b - newO; //We need to do this because the origin is not guaranteed to be in the plane
		var theta = Vector3.Angle(newA, newB);
		theta *= t;
		var slerped = Vector3.RotateTowards(newA, newB, Mathf.Deg2Rad * theta, float.PositiveInfinity);
		return slerped + newO; //Unproject the result
	}

	public static Ray ClampedScreenPointToRay(this Camera _cam, Vector3 _pos)
	{
		return _cam.ScreenPointToRay(ClampToScreen(_pos));
	}
	
	public static Ray ScreenPointToRay(this Camera _cam, Vector3 _screenPoint, Vector3 _cameraPositionReplace, Quaternion _cameraRotationReplace)
	{
		if (_cameraPositionReplace.sqrMagnitude < .001f * .001f) return _cam.ScreenPointToRay(_screenPoint);
		var ndcX = (_screenPoint.x / _cam.pixelWidth) * 2f - 1f;
		var ndcY = (_screenPoint.y / _cam.pixelHeight) * 2f - 1f;
		var tanHalfFOV = Mathf.Tan(_cam.fieldOfView * 0.5f * Mathf.Deg2Rad);
		var camSpacePoint = new Vector3(ndcX * _cam.aspect * tanHalfFOV * _cam.nearClipPlane, ndcY * tanHalfFOV * _cam.nearClipPlane, _cam.nearClipPlane);
		var worldSpaceOffset = _cameraRotationReplace *camSpacePoint;
		var worldOrigin = _cameraPositionReplace + worldSpaceOffset;
		var worldDirection = worldSpaceOffset; // will be normalized by the Ray constructor
		return new Ray(worldOrigin, worldDirection);
	}

	public static Ray RayThrough(this Camera _cam, Vector3 _pos, float _push = 1f)
	{
		var fwd = (_pos - _cam.transform.position).normalized;
		return new Ray(_pos + fwd * _push, fwd);
	}
	
	public static Vector3 RaycastThrough(this Camera _cam, Vector3 _pos, float _push = 1f) {
		RaycastHit hit;
		if (Physics.Raycast(_cam.RayThrough(_pos, _push), out hit, 1000, -1)) {
			return hit.point;
		}
		return _pos;
	}
	
#if UNITY_EDITOR
	private static RecorderWindow s_window;
	private static bool s_simRecording = false;
	private static DebugConsole.Command s_simRecordingCmd = new ("simrecording", (_s) => SetOrToggle(ref s_simRecording, _s));
	public static bool IsVideoRecording
	{
		get {
			return false;
			if (s_simRecording) return true;
			if (s_window == null)
				s_window = EditorWindow.GetWindow<RecorderWindow>(false, null, false); 
			return s_window.IsRecording();
		}
	}
	public static void StartVideoRecording()
	{
		var window = EditorWindow.GetWindow<UnityEditor.Recorder.RecorderWindow>(false, null, false);
		window.StartRecording();
	}
	public static void StopVideoRecording()
	{
		var window = EditorWindow.GetWindow<UnityEditor.Recorder.RecorderWindow>(false, null, false);
		window.StopRecording();
	}
#else
	public static bool IsVideoRecording => false;
	public static void StartVideoRecording() {}
	public static void StopVideoRecording() {}
#endif

	public static Vector3 ClampToScreen(Vector3 _pos)
	{
		_pos.x = Mathf.Clamp(_pos.x, 0, Screen.width);
		_pos.y = Mathf.Clamp(_pos.y, 0, Screen.height);
		return _pos;
	}

	private static KeyCode s_inhibitedKey = KeyCode.None;
	public static void InhibitKey(KeyCode _key) => s_inhibitedKey = _key;

#if UNITY_EDITOR 
	private static bool s_inputOverride = false;
	private static Vector3 s_mouseOverridePosition = Vector3.zero;
	private static float s_mouseOverrideWheel = 0, s_mouseOverrideXAxis = 0;
	private static uint s_mouseOverrideLastButtons = 0;
	private static uint s_mouseOverrideButtons = 0;
	private static uint[] s_keyEmpty = new uint[32];
	private static uint[] s_keyOverrideLast = s_keyEmpty;
	private static uint[] s_keyOverride = s_keyEmpty;

	public static void OverrideInputs(bool _override = true)
	{
		s_inputOverride = _override;
	}
	public static void OverrideInputsMousePosition(Vector2 _mouse, float _wheel, float _xAxis)
	{
		s_mouseOverridePosition = new Vector3(_mouse.x * Screen.width, _mouse.y * Screen.height, 0);
		s_mouseOverrideWheel = _wheel;
		s_mouseOverrideXAxis = _xAxis;
	}
	public static void OverrideInputsButtons(uint _buttonStates, uint[] _keyStates)
	{
		s_mouseOverrideButtons = _buttonStates;
		s_keyOverride = _keyStates;
	}

	public static void UpdateOverrideInputs()
	{
		s_mouseOverrideLastButtons = s_mouseOverrideButtons;
		s_keyOverrideLast = s_keyOverride;
	}
	
	private static Vector3 s_mouseSmoothPos; public static Vector3 mousePosition => s_inputOverride ? s_mouseOverridePosition : s_mouseSmoothPos;
	public static Vector3 clampedMousePosition => ClampToScreen(mousePosition);
	public static bool GetMouseButton(int _index)
	{
		if (s_inputOverride) return (s_mouseOverrideButtons & (1u << _index)) != 0;
		return Input.GetMouseButton(_index);
	}

	public static bool GetMouseButtonDown(int _index)
	{
		if (s_inputOverride) return (s_mouseOverrideButtons & (1u << _index)) != 0 && (s_mouseOverrideLastButtons & (1u << _index)) == 0;
		return Input.GetMouseButtonDown(_index);
	}

	public static bool GetMouseButtonUp(int _index)
	{
		if (s_inputOverride) return (s_mouseOverrideButtons & (1u << _index)) == 0 && (s_mouseOverrideLastButtons & (1u << _index)) != 0;
		return Input.GetMouseButtonUp(_index);
	}
	public static bool GetKey(KeyCode _key)
	{
		if (_key == KeyCode.None) return false;
		if (s_inputOverride) return (s_keyOverride[(int)_key >> 5] & (1u << ((int)_key & 31))) != 0;
		return Input.GetKey(_key);
	}
	public static bool GetKeyDown(KeyCode _key, bool _ignoreInhibited = false)
	{
		if (_key == KeyCode.None) return false;
		if (_ignoreInhibited == false && _key == s_inhibitedKey) return false; // if this key is inhibited, don't respond to it
		if (s_inputOverride) return ((s_keyOverride[(int) _key >> 5] & (1u << ((int) _key & 31))) != 0) && ((s_keyOverrideLast[(int) _key >> 5] & (1u << ((int) _key & 31))) == 0);
		return Input.GetKeyDown(_key);
	}
	public static bool GetKeyUp(KeyCode _key)
	{
		if (_key == KeyCode.None) return false;
		if (s_inputOverride) return ((s_keyOverride[(int) _key >> 5] & (1u << ((int) _key & 31))) == 0) && ((s_keyOverrideLast[(int) _key >> 5] & (1u << ((int) _key & 31))) != 0);
		return Input.GetKeyUp(_key);
	}
	public static float MouseXAxis => s_inputOverride ? s_mouseOverrideXAxis : Input.GetAxis("Mouse X");
	public static float SmoothedMouseWheel => s_inputOverride ? s_mouseOverrideWheel : s_smoothedMouseWheel;
	public static float UnsmoothedMouseWheel => s_inputOverride ? s_mouseOverrideWheel : s_rawMouseWheel;
#else
	private static Vector3 s_mouseSmoothPos; public static Vector3 mousePosition => s_mouseSmoothPos;
	public static Vector3 clampedMousePosition => ClampToScreen(mousePosition);
	public static bool GetMouseButton(int _index) => Input.GetMouseButton(_index);
	public static bool GetMouseButtonDown(int _index) => Input.GetMouseButtonDown(_index);
	public static bool GetMouseButtonUp(int _index) => Input.GetMouseButtonUp(_index);
	public static void OverrideInputs(bool _override = true) { }
	public static void OverrideInputsMousePosition(Vector2 _mouse, float _wheel, float _xAxis) { }
	public static void OverrideInputsButtons(uint _buttonStates, uint[] _keyStates) { }
	public static void UpdateOverrideInputs() { }
	public static bool GetKey(KeyCode _key) => Input.GetKey(_key);
	public static bool GetKeyDown(KeyCode _key, bool _ignoreInhibited = false)
	{
		if (_ignoreInhibited == false && _key == s_inhibitedKey) return false; // if this key is inhibited, don't respond to it
		return Input.GetKeyDown(_key);
	}
	public static bool GetKeyUp(KeyCode _key) => Input.GetKeyUp(_key);
	public static float MouseXAxis => Input.GetAxis("Mouse X");
	public static float SmoothedMouseWheel => s_smoothedMouseWheel;
	public static float UnsmoothedMouseWheel => s_rawMouseWheel;
#endif
	public static void ClearMouseWheel() 
	{
		for (int i = 0; i < s_lastMouseWheels.Length; ++i)
			s_lastMouseWheels[i] = 0;
		s_smoothedMouseWheel = 0;
		s_unsmoothedMouseWheel = 0;
		s_latchMouseWheel = true;
	}
	
	private static float[] s_timeForLastFrames = new float[60];
	private static int s_lastFrameIndex = 0;
	public static void UpdateSmoothedMouse()
	{
		s_timeForLastFrames[s_lastFrameIndex % 60] = Time.unscaledTime;
		float timeForLast60 = 0;
		if (s_lastFrameIndex >= 60)
			timeForLast60 = s_timeForLastFrames[s_lastFrameIndex % 60] - s_timeForLastFrames[(s_lastFrameIndex + 1) % 60];
		++ s_lastFrameIndex;
		//if (s_lastFrameIndex % 60 == 0) Debug.LogError($"Real frame time last 60 is {timeForLast60}");
		
		var isRecording = IsVideoRecording;
		if (isRecording == false || timeForLast60 < 5) s_mouseSmoothPos = Input.mousePosition; // if not recording or recording at 30fps or more don't slow mouse cursor
		else s_mouseSmoothPos = Vector3.Lerp(s_mouseSmoothPos, Input.mousePosition, 0.05f);
	}

#if UNITY_EDITOR || DEVELOPMENT_BUILD
	private static int s_overrideMouseUntilFrame = 0;
	private static Vector3 s_overrideMousePos = Vector3.zero; public static bool IsMouseOverriding => s_overrideMouseUntilFrame > Time.frameCount; 
	public static Vector3 InputPos => IsMouseOverriding ? s_overrideMousePos : mousePosition;

	public static void SetInputPos(Vector3 _v, bool _add = false)
	{
		s_overrideMouseUntilFrame = 0x7FFFFFFF;
		if (_add) s_overrideMousePos += _v; else s_overrideMousePos = _v;
		s_overrideMousePos.x = Mathf.Clamp(s_overrideMousePos.x, 1, Screen.width-2);
		s_overrideMousePos.y = Mathf.Clamp(s_overrideMousePos.y, 1, Screen.height-2);
	}
	public static void ClearInputPos() { s_overrideMouseUntilFrame = Time.frameCount+2; }
	public static bool GetMouseButtonOverride(int _button) { return s_overrideMouseUntilFrame > Time.frameCount+1; }
#else
	public static Vector3 InputPos => mousePosition;
	public static bool GetMouseButtonOverride(int _button) { return false; }
#endif
	public static Vector3 ClampedMousePosition => new Vector3(Mathf.Clamp(InputPos.x, 0, Screen.width-1), Mathf.Clamp(InputPos.y, 0, Screen.height-1), InputPos.z); 
	public static PointerEventData MouseEventData => FakeEventData(ClampedMousePosition);
	public static PointerEventData PositionEventData(Vector3 _pos) => FakeEventData(_pos);
	private static PointerEventData s_fakeEventData = new(null);
	public static PointerEventData FakeEventData(Vector3 _pos)
	{
		s_fakeEventData.position = _pos;
		return s_fakeEventData;
	}

	public static bool IsMouseOver(this MonoBehaviour _this) {
		return _this.gameObject.IsMouseOver();
	}

	public static void DumpMouseOver()
	{
		List<UnityEngine.EventSystems.RaycastResult> hits = new List<UnityEngine.EventSystems.RaycastResult>();
		UnityEngine.EventSystems.EventSystem.current.RaycastAll(MouseEventData, hits);
		string s = "Mouse Over: \n";
		foreach (var h in hits)
			s += $"{h.gameObject.transform.Path()} - {h.module} - {(h.module is UnityEngine.UI.GraphicRaycaster)}\n";
		Debug.LogError(s);
	}
	
	public static Vector3 GetRaycastCameraPosition() 
	{
		// Get main camera
		Camera camera = Camera.main;

		// Create a ray from the camera's center towards forward direction
		Ray ray = new Ray(camera.transform.position, camera.transform.forward);

		// Perform a raycast with specified distance and layer mask
		RaycastHit hit;
		if (Physics.Raycast(ray, out hit, 1000, -1))
		{
			return hit.point; // Return hit point if collision occurs
		}
		else
		{
			// Handle no collision case
			return camera.transform.position + camera.transform.forward * 1000;  // Fallback position
		}
		if (Physics.Raycast(Camera.main.ScreenPointToRay(Camera.main.transform.position), out var hiat, 1000, -1)) {
			return hiat.point;
		}
		return Vector3.zero;
	}
	
	public static bool IsMouseOverChild(this GameObject _this) {
		foreach(Transform child in _this.transform)
		{
			if(IsMouseOver(child.gameObject)) return true;
		}
		return false;
	}
	
	public static bool IsMouseOver(this GameObject _this) {
		var caster = _this.GetComponentInParent<UnityEngine.UI.GraphicRaycaster>();
		if (caster == null) return false;
		var res = new List<UnityEngine.EventSystems.RaycastResult>();
		caster.Raycast(MouseEventData, res);
		foreach (var r in res) {
			if (r.gameObject == _this) return true;
		}
		return false;
	}

	public static bool IsOverUI(Vector3 _pos)
	{
		return GetUIObjectAt(_pos) != null;
	}
	public static bool IsOverPISS(Vector3 _pos)
	{
		return false;//GetUIObjectAt(_pos)?.name == "PISSUI";
	}

	public static void ClickObject(Vector3 _pos, bool _isDown, int _mouseButton)
	{
		List<RaycastResult> hits = new List<RaycastResult>();
		var ped = PositionEventData(_pos);
		ped.button = (UnityEngine.EventSystems.PointerEventData.InputButton)_mouseButton;
		EventSystem.current.RaycastAll(ped, hits);
		for (int i = 0; i < hits.Count; ++i)
		{
			var go = hits[i].gameObject;
			if (_isDown)
			{
				var ipdh = go.GetComponentInParent<IPointerDownHandler>();
				if (ipdh == null) ipdh = go.GetComponentInChildren<IPointerDownHandler>();
				if (ipdh != null)
				{
					ipdh.OnPointerDown(ped);
					break;
				}
			}
			else
			{
				var ipch = go.GetComponentInParent<IPointerClickHandler>();
				if (ipch == null) ipch = go.GetComponentInChildren<IPointerClickHandler>();
				if (ipch != null)
				{
					ipch.OnPointerClick(ped);
					break;
				}
			}
		}
	}

	public static bool IsOverDraggable(Vector3 _pos)
	{
		var hitObj = GetPhysicsObjectAt(_pos);
		if (hitObj == null) return false;
		var cmd = hitObj.GetComponentInParent<NGCommanderBase>();
		if (cmd != null) return cmd.HasDragContent();
		var obj = hitObj.GetComponentInParent<NGMovingObject>();
		if (obj != null) return true;
		return false;
	}

	public static bool IsOverObject(Vector3 _pos, GameObject _obj)
	{
		var hitObj = GetPhysicsObjectAt(_pos);
		var xform = _obj.transform;
		var hit = hitObj.transform;
		while (hit != null)
		{
			if (hit == xform) return true;
			hit = hit.parent;
		}
		return false;
	}

	public static GameObject GetPhysicsObjectAt(Vector3 _pos)
	{
		List<UnityEngine.EventSystems.RaycastResult> hits = new List<UnityEngine.EventSystems.RaycastResult>();
		UnityEngine.EventSystems.EventSystem.current.RaycastAll(PositionEventData(_pos), hits);
		if (hits.Count == 0) return null;
		if (hits[0].module is PhysicsRaycaster)
			return hits[0].gameObject;
		return null;
	}

	public static GameObject GetUIObjectAt(Vector3 _pos) => GetUIObjectAt(_pos, out _, out _);

	// Decompiled from Base Raycaster
	private static int RaycastComparer(RaycastResult lhs, RaycastResult rhs)
	{
		if (lhs.module != rhs.module)
		{
			var lhsEventCamera = lhs.module.eventCamera;
			var rhsEventCamera = rhs.module.eventCamera;
			if (lhsEventCamera != null && rhsEventCamera != null && lhsEventCamera.depth != rhsEventCamera.depth)
			{
				// need to reverse the standard compareTo
				if (lhsEventCamera.depth < rhsEventCamera.depth)
					return 1;
				if (lhsEventCamera.depth == rhsEventCamera.depth)
					return 0;

				return -1;
			}
			if (lhs.module.sortOrderPriority != rhs.module.sortOrderPriority)
				return rhs.module.sortOrderPriority.CompareTo(lhs.module.sortOrderPriority);
			if (lhs.module.renderOrderPriority != rhs.module.renderOrderPriority)
				return rhs.module.renderOrderPriority.CompareTo(lhs.module.renderOrderPriority);
		}
		// Renderer sorting
		if (lhs.sortingLayer != rhs.sortingLayer)
		{
			// Uses the layer value to properly compare the relative order of the layers.
			var rid = SortingLayer.GetLayerValueFromID(rhs.sortingLayer);
			var lid = SortingLayer.GetLayerValueFromID(lhs.sortingLayer);
			return rid.CompareTo(lid);
		}
		if (lhs.sortingOrder != rhs.sortingOrder)
			return rhs.sortingOrder.CompareTo(lhs.sortingOrder);
		// comparing depth only makes sense if the two raycast results have the same root canvas (case 912396)
		if (lhs.depth != rhs.depth && lhs.module.rootRaycaster == rhs.module.rootRaycaster)
			return rhs.depth.CompareTo(lhs.depth);
		if (lhs.distance != rhs.distance)
			return lhs.distance.CompareTo(rhs.distance);
		return lhs.index.CompareTo(rhs.index);
	}

	private static DebugConsole.Command s_dumpRaycastsCmd = new("dumpraycasts", (_s) => SetOrToggle(ref s_dumpRaycasts, _s));
	private static bool s_dumpRaycasts = false;
	private static Dictionary<GraphicRaycaster, bool> s_disabledRaycasters = new();
	private static Dictionary<Canvas, bool> s_disabledCanvases = new();
	private static int s_disabledRaycastersFrame = -1;
	private static List<Canvas> s_canvasList = new();
	public static void RaycastAllUI(PointerEventData eventData, List<RaycastResult> hits, bool _useCaches = false) // _useCaches no longer needed
	{
		// Decompiled from EventSystem.RaycastAll with PhysicsRaycaster removed and some additional checks and optimisations
		//UnityEngine.EventSystems.EventSystem.current.RaycastAll(PositionEventData(_pos), hits);
		if (s_disabledRaycastersFrame != Time.frameCount)
		{
			s_disabledRaycasters.Clear();
			s_disabledCanvases.Clear();
			s_disabledRaycastersFrame = Time.frameCount;
		}
		var modules = RaycasterManager.GetRaycasters();
		var modulesCount = modules.Count;
		var childCanvases = s_canvasList;
		childCanvases.Clear();
		for (int i = 0; i < modulesCount; ++i)
		{
			var gr = modules[i] as GraphicRaycaster;
			if (gr == null || !gr.IsActive())
				continue;
			var isDisabled = false;
			if (_useCaches && s_disabledRaycasters.TryGetValue(gr, out isDisabled) == false)
			{
				isDisabled = false;
				childCanvases.Clear();
				var t = gr.transform;
				while (t != null)
				{
					var canvas = t.GetComponent<Canvas>();
					if (canvas != null)
					{
						if (s_disabledCanvases.TryGetValue(canvas, out var isCanvasDisabled))
						{
							isDisabled = isCanvasDisabled;
							break;
						}
						childCanvases.Add(canvas);
						if (canvas.isActiveAndEnabled == false)
						{
							isDisabled = true;
							break;
						}
					}
					t = t.parent;
				}
				foreach (var childCanvas in childCanvases)
					s_disabledCanvases[childCanvas] = isDisabled;
				s_disabledRaycasters[gr] = isDisabled;
			}
			if (isDisabled == false)
			{
				if (s_dumpRaycasts) Debug.LogError($"Raycasting {gr.transform.Path(100)} {GraphicRegistry.GetRaycastableGraphicsForCanvas(gr.GetComponent<Canvas>())?.Count ?? 0} graphics");
				gr.Raycast(eventData, hits);
			}
		}
		s_dumpRaycasts = false;
		hits.Sort(RaycastComparer);
	}
	private static List<RaycastResult> s_raycastResults = new();
	public static GameObject GetUIObjectAt(Vector3 _pos, out Vector3 _hitPos, out Vector3 _hitNrm)
	{
		List<RaycastResult> hits = s_raycastResults;
		hits.Clear();
		RaycastAllUI(PositionEventData(_pos), hits);
		foreach (var h in hits)
		{
			if(h.gameObject != null && h.gameObject.GetComponent<OffClickEvent>() != null)
				continue;
			_hitPos = h.worldPosition;
			_hitNrm = h.worldNormal;
			return h.gameObject;
		}
		_hitPos = Vector3.zero;
		_hitNrm = Vector3.zero;
		return null;
	}	
	public static bool IsMouseOverUI() {
		return IsOverUI(ClampedMousePosition);
	}

	public static void DoNextFrame (System.Action action) {
#if UNITY_EDITOR
		if (GlobalData.Me == null) {
			action();
			return;
		}
#endif
		GlobalData.Me.StartCoroutine (Co_DoNextFrame (action));
	}

	public static void SetTintWindowColour(GameObject _go, int _window, Color _colour, bool _includeSMRs = false,
		bool _applyUVOffsets = false, bool _enableUVOffsets = false, Transform _ignore = null, bool _ignoreIfAlreadyTinted = false) {
		Extensions.SetTintWindowColour(_go, _window, _colour, _includeSMRs, _applyUVOffsets, _enableUVOffsets, _ignore, _ignoreIfAlreadyTinted);
	}

	public static IEnumerator Co_DoNextFrame(System.Action action) {
		yield return null;
		action ();
	}

	public static IEnumerator Co_DoAfter(float _delay, System.Action action)
	{
		yield return new WaitForSeconds(_delay);
		action();
	}

	public static IEnumerator Co_Do(System.Func<bool> action)
	{
		while (action()) yield return null;
	}

	public static IEnumerator Co_DoFor(float _duration, Func<float, bool> _perUpdateAction, Action _finalAction)
	{
		float t = 0;
		while (t < _duration)
		{
			if ((_perUpdateAction?.Invoke(t) ?? false) == false)
				break;
			t += Time.deltaTime;
			yield return null; 
		}
		_finalAction?.Invoke();
	}

	public static IEnumerator Co_DoPostLoad(System.Action action)
	{
		while (GameManager.Me == null || GameManager.Me.LoadComplete == false) yield return null;
		action();
	}

	public static Coroutine After(float _time, System.Action _do) {
		return GameManager.Me.StartCoroutine(Co_After(_time, _do));
	}
	public static IEnumerator Co_After(float _t, System.Action _do) {
		yield return new WaitForSeconds(_t);
		_do();
	}

	private static Dictionary<string, System.Text.StringBuilder> s_debugStrings = new Dictionary<string, System.Text.StringBuilder>();
	[System.Diagnostics.Conditional("UNITY_EDITOR"), System.Diagnostics.Conditional("DEVELOPMENT_BUILD")]
	public static void DebugClear(string _label)
	{
		if (s_debugStrings.TryGetValue(_label, out var sb))
			sb.Clear();
		else
			s_debugStrings[_label] = new System.Text.StringBuilder();
	}

	[System.Diagnostics.Conditional("UNITY_EDITOR"), System.Diagnostics.Conditional("DEVELOPMENT_BUILD")]
	public static void DebugAdd(string _label, string _msg)
	{
		if (s_debugStrings.TryGetValue(_label, out var sb))
			sb.AppendLine(_msg);
		else
			s_debugStrings[_label] = new System.Text.StringBuilder($"{_msg}\n");
	}

	public static string DebugGet(string _label)
	{
		if (s_debugStrings.TryGetValue(_label, out var sb))
			return sb.ToString();
		return "";
	}


	public static void SetCollisionModeRecursive(Transform _this, CollisionDetectionMode _mode) {
		var rb = _this.GetComponent<Rigidbody>();
		if (rb != null) rb.collisionDetectionMode = _mode;
		foreach (Transform c in _this) SetCollisionModeRecursive(c, _mode);
	}

	public static IEnumerator Co_ThrowToPoint(Transform _me, Vector3 _target, float _xzSpeed, System.Action _onComplete)
	{
		var initialPosition = _me.position;
		var (velocity, time) = _me.GetVelocityToMoveWithXZSpeed(_target, _xzSpeed);
		var t = 0f;
		while (t < time)
		{
			t += Time.deltaTime;
			_me.position = initialPosition + velocity.ApplyVelocityOverTime(t);
			yield return null;
		}
		_onComplete();
	}

	public static Vector3 ApplyVelocityOverTime(this Vector3 _this, float _time)
	{
		var gravity = Physics.gravity;
		return _this * _time + gravity * (.5f * _time * _time);
	}

	public static Vector3 GetVelocityToMoveOverTime(this Transform _this, Vector3 _to, float _time)
	{
		var velocity = (_to - _this.position) / _time;
		// s = ut + .5at^2
		var gravityY = Physics.gravity.y;
		velocity.y = (_to.y - _this.position.y - .5f * gravityY * _time * _time) / _time;
		return velocity;
	}

	public static (Vector3, float) GetVelocityToMoveWithXZSpeed(this Transform _this, Vector3 _to, float _xzSpeed)
	{
		float t = (_to - _this.position).xzMagnitude() / _xzSpeed;
		return (_this.GetVelocityToMoveOverTime(_to, t), t);
	}

	public static Vector3 SetVelocityToMoveOverTime(this Rigidbody _this, Vector3 _to, float _time, System.Action<Rigidbody> _onComplete = null) {
		var velocity = GetVelocityToMoveOverTime(_this.position, _to, _time);
		_this.linearVelocity = velocity;
		GameManager.Me.StartCoroutine(Co_After(_time, () => _onComplete?.Invoke(_this)));
		return velocity;
	}
	public static Vector3 SetVelocityToMoveWithSpeed(this Rigidbody _this, Vector3 _to, float _xzSpeed, System.Action<Rigidbody> _onComplete = null) {
		return _this.SetVelocityToMoveWithSpeed(_to, _xzSpeed, out _, _onComplete);
	}

	public static Vector3 SetVelocityToMoveWithSpeed(this Rigidbody _this, Vector3 _to, float _xzSpeed, out float _time, System.Action<Rigidbody> _onComplete = null)
	{
		float t = (_to - _this.position).xzMagnitude() / _xzSpeed;
		_time = t;
		return _this.SetVelocityToMoveOverTime(_to, t, _onComplete);
	}

	public static Vector3 GetVelocityToMoveWithSpeed(Transform _fromTransform, Vector3 _to, float _xzSpeed) {
		float t = (_to - _fromTransform.position).xzMagnitude() / _xzSpeed;
		return GetVelocityToMoveOverTime(_fromTransform.position, _to, t);
	}
	public static Vector3 GetVelocityToMoveOverTime(Vector3 _from, Vector3 _to, float _time) {
		var velocity = (_to - _from) / _time;
		// s = ut + .5at^2
		var gravityY = Physics.gravity.y;
		velocity.y = (_to.y - _from.y - .5f * gravityY * _time * _time) / _time;
		return velocity;
	}

	public static void SetWidthBasedFoV(this Camera _this, float _fov) {
		var aspect = Mathf.Min(2, _this.aspect); // if we're very wide then there's plenty of room on the sides
		_this.fieldOfView = 2 * Mathf.Atan(Mathf.Tan(_fov * Mathf.Deg2Rad * 0.5f) / aspect) * Mathf.Rad2Deg;
	}

	public static IEnumerator Co_LerpTransform(Transform _transform, Vector3 _targetPos, Quaternion _targetRot, float _time, System.Action _onComplete, float _parabolaHeight = 0.0f)
	{
		Vector3 startPos = _transform.position;
		Quaternion startRot = _transform.rotation;
		float lerpRate = 1.0f / _time;
		float t = 0.0f;

		while (t < 1.0f)
		{
			t += lerpRate * Time.deltaTime;
			t = Mathf.Clamp01(t);
			float smoothed = t * t * (3 - 2 * t);
			float yOffset = Mathf.Sin(t * Mathf.PI) * _parabolaHeight;
			_transform.position = Vector3.Lerp(startPos, _targetPos, smoothed) + new Vector3(0.0f, yOffset, 0.0f);
			_transform.rotation = Quaternion.Lerp(startRot, _targetRot, smoothed);
			
			yield return null;
		}

		_onComplete();
	}

	public static void LerpTransform(Transform _transform, Vector3 _targetPos, Quaternion _targetRot, float _time, System.Action _onComplete, float _parabolaHeight = 0.0f)
    {
		GameManager.Me.StartCoroutine(Co_LerpTransform(_transform, _targetPos, _targetRot, _time, _onComplete, _parabolaHeight));
	}

	public static IEnumerator Co_LerpRigidbody(Rigidbody _rigidbody, Vector3 _targetPos, Quaternion _targetRot, float _time, System.Action _onComplete)
	{
		Vector3 startPos = _rigidbody.position;
		Quaternion startRot = _rigidbody.rotation;
		float lerpRate = 1.0f / _time;
		float t = 0.0f;

		while (t < 1.0f)
		{
			t += lerpRate * Time.deltaTime;
			t = Mathf.Clamp01(t);
			float smoothed = t * t * (3 - 2 * t);
			_rigidbody.Move(Vector3.Lerp(startPos, _targetPos, smoothed), Quaternion.Lerp(startRot, _targetRot, smoothed));
			yield return null;
		}

		_onComplete();
	}

	public static void LerpRigidbody(Rigidbody _rigidbody, Vector3 _targetPos, Quaternion _targetRot, float _time, System.Action _onComplete)
	{
		GameManager.Me.StartCoroutine(Co_LerpRigidbody(_rigidbody, _targetPos, _targetRot, _time, _onComplete));
	}

	//=====

	//if more error codes need adding/using, it can be done so here.
	public static string TranslateErrorCode(string _error)
	{
		switch (_error)
		{
			case "UserCancelled":
				return "User Cancelled";
			default:
				return "An Error Has Occured, Please Try Again";
		}
	}

	//=====

	static HashSet<KeyCode> m_lockedKeys = new HashSet<KeyCode>();
	public static void InputUpdate() {
		var newLockedKeys = new HashSet<KeyCode>();
		foreach (var k in m_lockedKeys) {
			if (GetKey(k)) {
				newLockedKeys.Add(k);
			}
		}
		m_lockedKeys = newLockedKeys;
	}
	public static bool InputKey(KeyCode _k) {
		//if (Console.IsActive()) return false;
		if (m_lockedKeys.Contains(_k)) return false;
		return GetKey(_k);
	}
	public static bool InputKeyDown(KeyCode _k) {
		//if (Console.IsActive()) return false;
		if (m_lockedKeys.Contains(_k)) return false;
		return GetKeyDown(_k);
	}
	public static bool InputKeyDownAndLock(KeyCode _k) {
		var b = InputKeyDown(_k);
		if (b) m_lockedKeys.Add(_k);
		return b;
	}

	public static float QuaternionDifference(Quaternion _a, Quaternion _b) {
		var dot = _a.x * _b.x + _a.y * _b.y + _a.z * _b.z + _a.w * _b.w;
		return 1.0f - dot * dot;
	}

	public static bool AlmostEquals(this Quaternion _this, Quaternion _other) {
		return QuaternionDifference(_this, _other) < .001f;
	}


	//==========
	public static Vector3 PyramidPosition(int _index, float _radius, Vector3 _origin) {
		return PyramidPosition(_index, _radius, _origin, Vector3.right, Vector3.forward);
	}
	public static Vector3 PyramidPosition(int _index, float _radius, Vector3 _origin, Vector3 _axis1, Vector3 _axis2) {
		int row = 0, rowStart = 0, col = 0;
		for (int i = 0; i <= _index; ++i) {
			col = i - rowStart;
			if (col > row) {
				++row;
				col = 0;
				rowStart = i;
			}
		}
		// balance position in row from center to edges
		col = (row / 2) + ((col + 1) / 2) * ((col & 1) * 2 - 1);
		const float c_rootThree = 1.732f;
		float colOffset = -(float)row * _radius + (float)col * _radius * 2;
		float rowOffset = (float)row * c_rootThree * _radius;
		return _origin + rowOffset * _axis2 + colOffset * _axis1;
	}
	public static Vector3 ConcentricPosition(int _index, float _radius, Vector3 _origin) {
		return ConcentricPosition(_index, _radius, _origin, Vector3.right, Vector3.forward);
	}
	public static Vector3 ConcentricPosition(int _index, float _radius, Vector3 _origin, Vector3 _axis1, Vector3 _axis2) {
		int inRing = 1, ringStart = 0, ringIndex = 0, ringNumber = 0;
		for (int i = 0; i <= _index; ++i) {
			ringIndex = i - ringStart;
			if (ringIndex >= inRing) {
				++ ringNumber;
				inRing = ringNumber * 6;
				ringIndex = 0;
				ringStart = i;
			}
		}
		var angle = (float)ringIndex * Mathf.PI * 2f / (float)inRing;
		var ringSize = (float)ringNumber * _radius * 2f;
		return _origin + _axis1 * (Mathf.Sin(angle) * ringSize) + _axis2 * (Mathf.Cos(angle) * ringSize);
	}
	//==========

	private static void LZMACompressStringWorker(object _data)
	{
		var (_s, _cb, _dictSize) = _data as Tuple<string, System.Action<byte[]>, int>;
		var cmp = LZMACompressString(_s, _dictSize);
		_cb(cmp);
	}
	public static void LZMACompressStringThreaded(string _s, System.Action<byte[]> _cb, int _dictSize = 256 * 1024)
	{
		System.Threading.ThreadPool.QueueUserWorkItem(LZMACompressStringWorker, Tuple.Create(_s, _cb, _dictSize));
	}
	public static byte[] LZMACompressString(string _s, int _dictSize = 256 * 1024) {
		return LZMACompress(System.Text.Encoding.UTF8.GetBytes(_s), _dictSize);
	}
	public static string LZMADecompressString(byte[] _data) {
		return System.Text.Encoding.UTF8.GetString(LZMADecompress(_data));
	}
	public static byte[] LZMACompress(byte[] _bytes, int _dictSize) {
		System.IO.Stream input = new System.IO.MemoryStream(_bytes);
		System.IO.MemoryStream output = new System.IO.MemoryStream();
		SevenZip.Compression.LZMA.Encoder coder = new SevenZip.Compression.LZMA.Encoder();
		coder.SetCoderProperties (new SevenZip.CoderPropID[] {SevenZip.CoderPropID.DictionarySize}, new object[] {_dictSize});
		coder.WriteCoderProperties(output);
		output.Write(System.BitConverter.GetBytes(input.Length), 0, 4);
		coder.Code(input, output, input.Length, -1, null);
		output.Flush();
		output.Close();
		return output.ToArray();
	}
	public static byte[] LZMADecompress(byte[] _bytes) {
		System.IO.Stream input = new System.IO.MemoryStream(_bytes);
		System.IO.MemoryStream output = new System.IO.MemoryStream();
		SevenZip.Compression.LZMA.Decoder coder = new SevenZip.Compression.LZMA.Decoder();
		byte[] properties = new byte[5]; // 5 comes from kPropSize (LzmaEncoder.cs)
		input.Read(properties, 0, 5);
		byte [] fileLengthBytes = new byte[4];
		input.Read(fileLengthBytes, 0, 4);
		int fileLength = System.BitConverter.ToInt32(fileLengthBytes, 0);
		coder.SetDecoderProperties(properties);
		coder.Code(input, output, input.Length, fileLength, null);
		output.Flush();
		output.Close();
		return output.ToArray();
	}

	public static byte[] SimpleXor(byte[] _data, int _initial = 0x73) {
		byte xor = (byte)_initial;
		for (int i = 0; i < _data.Length; ++i) {
			_data[i] ^= xor;
			xor = (byte)(xor * 77 + 33);
		}
		return _data;
	}

	private static void RFH(byte[] _data)
	{
		if (_data == null || _data.Length < 2) return;
		int mid = (_data.Length / 2) & (~1);
		for (int i = 0; i < mid / 2; ++i) (_data[i], _data[mid - i - 1]) = (_data[mid - i - 1], _data[i]);
	}
	
	public static byte[] SimpleXorWithHeader(byte[] _data, int _header, int _initial = 0x73)
	{
		var result = new byte[_data.Length + 3];
		result[0] = (byte)(_header & 0xFF);
		result[1] = (byte)((_header >> 8) & 0xFF);
		result[2] = (byte)((_header >> 16) & 0xFF);
		byte xor = (byte) _initial;
		RFH(_data);
		for (int i = 0; i < _data.Length; ++i)
		{
			result[i + 3] = (byte)(_data[i] ^ xor);
			xor = (byte) (xor * 57 + 37);
		}
		return result;
	}
	public static byte[] SimpleXorRemovingHeader(byte[] _data, int _header, int _initial = 0x73)
	{
		if (_data == null) return null;
		if (_data.Length < 3) return null;
		if (_data[0] != (_header & 0xFF)) return null;
		if (_data[1] != ((_header >> 8) & 0xFF)) return null;
		if (_data[2] != ((_header >> 16) & 0xFF)) return null;
		var result = new byte[_data.Length - 3];
		byte xor = (byte) _initial;
		for (int i = 3; i < _data.Length; ++i)
		{
			result[i - 3] = (byte)(_data[i] ^ xor);
			xor = (byte) (xor * 57 + 37);
		}
		RFH(result);
		return result;
	}

	public static byte[] PrepareSaveForUpload(string _save) {
		var cmp = LZMACompressString(_save);
		SimpleXor(cmp);
		return cmp;
	}

	public static void PrepareSaveForUploadThreaded(string _save, System.Action<byte[]> _cb)
	{
		LZMACompressStringThreaded(_save, _bytes =>
		{
			SimpleXor(_bytes);
			_cb(_bytes);
		});
	}

	public static string PrepareSaveFromDownload(byte[] _data) {
		SimpleXor(_data);
		return LZMADecompressString(_data);
	}

	//==========

	private static Func<UnityEngine.Object, UnityEngine.Object> MakeCastDelegate(Type from, Type to)
	{
		var p = Expression.Parameter(typeof(UnityEngine.Object));
		try
		{
			return Expression.Lambda<Func<UnityEngine.Object, UnityEngine.Object>>(
				Expression.Convert(Expression.ConvertChecked(Expression.Convert(p, from), to), typeof(UnityEngine.Object)),
				p).Compile();
		}
		catch
		{
			return null;
		}
	}

	private static readonly Dictionary<Tuple<Type, Type>, Func<UnityEngine.Object, UnityEngine.Object>> CastCache
	= new Dictionary<Tuple<Type, Type>, Func<UnityEngine.Object, UnityEngine.Object>>();
	private static Func<UnityEngine.Object, UnityEngine.Object> GetCastDelegate(Type from, Type to)
	{
		lock (CastCache)
		{
			var key = new Tuple<Type, Type>(from, to);
			Func<UnityEngine.Object, UnityEngine.Object> cast_delegate;
			if (!CastCache.TryGetValue(key, out cast_delegate))
			{
				cast_delegate = MakeCastDelegate(from, to);
				CastCache.Add(key, cast_delegate);
			}
			return cast_delegate;
		}
	}
	public static UnityEngine.Object Cast(Type t, UnityEngine.Object o)
	{
		try
		{
			return GetCastDelegate(o.GetType(), t).Invoke(o);
		}
		catch
		{
			return null;
		}
	}
	public static bool KeyboardInUse => false;
	public static Dictionary<int, string> s_debugKeyCheck = new Dictionary<int, string>();
	public static HashSet<int> s_debugKeyCheckErrorShown = new HashSet<int>();
	public static HashSet<int> s_debugKeyUnmodifiedErrorShown = new HashSet<int>();
	// Debug key access
	// 1) Protected from input fields
	// 2) Easy check for Ctrl/Alt/Shift including negative (e.g. Control but not Alt)
	// 3) Shows an error if the same key binding is queried in different contexts
	// 4) Easy to switch off centrally for release builds
	public static bool ModifiedKey(KeyCode _key, bool _shift, bool _control, bool _alt, string _context, bool _showWarnings = true)
	{
		if (KeyboardInUse) return false;
		int finalId = (int) _key + (_shift ? 0x10000 : 0) + (_control ? 0x20000 : 0) + (_alt ? 0x40000 : 0);
		if (_showWarnings)
		{
			if (!_shift && !_control && !_alt && !s_debugKeyUnmodifiedErrorShown.Contains(finalId))
			{
				s_debugKeyUnmodifiedErrorShown.Add(finalId);
				Debug.LogError($"Unmodified debug keys are discouraged ({_key})");
			}
			string existing;
			if (s_debugKeyCheck.TryGetValue(finalId, out existing))
			{
				if (existing != _context && !s_debugKeyCheckErrorShown.Contains(finalId))
				{
					s_debugKeyCheckErrorShown.Add(finalId);
					Debug.LogError($"Debug key {(_control ? "Ctrl+" : "")}{(_alt ? "Alt+" : "")}{(_shift ? "Shift+" : "")}{_key} used in multiple contexts ({_context} and {existing})");
				}
			}
			else
			{
				s_debugKeyCheck[finalId] = _context;
			}
		}
		if ((GetKey(KeyCode.LeftShift) || GetKey(KeyCode.RightShift)) != _shift) return false;
		if ((GetKey(KeyCode.LeftControl) || GetKey(KeyCode.RightControl) || GetKey(KeyCode.LeftCommand) || GetKey(KeyCode.RightCommand)) != _control) return false;
		if ((GetKey(KeyCode.LeftAlt) || GetKey(KeyCode.RightAlt)) != _alt) return false;
		return InputKeyDownAndLock(_key);
	}

	public static void RemoveAtSwap<T>(this List<T> _this, int _index) {
		if (_index < _this.Count-1) _this[_index] = _this[_this.Count-1];
		_this.RemoveAt(_index);
	}
	public static void Shuffle<T>(this List<T> _this, int _seed = 0x1234) {
		for (int i = 1; i < _this.Count; i ++) {
			int r = SimpleRndRange(0, i, ref _seed);
			var copy = _this[r];
			_this[r] = _this[i];
			_this[i] = copy;
		}
	}
	public static int[] GetShuffleList(int _count) {
		int[] rnd = new int[_count];
		for (int i = 0; i < _count; i ++) { rnd[i] = i; }
		for (int i = 0; i < _count; i ++) {
			int r = UnityEngine.Random.Range(i, _count);
			int t = rnd[r]; rnd[r] = rnd[i]; rnd[i] = t;
		}
		return rnd;
	}


	public static List<T> FindAll<T>(this T[] _array, Predicate<T> _match)
	{
		var matches = (List<T>)Activator.CreateInstance(typeof(List<>).MakeGenericType(typeof(T)));
		if (_array == null || _match == null)
			return matches;
		for (int i = 0; i < _array.Length; i++)
			if (_match(_array[i]))
				matches.Add(_array[i]);
		return matches;
	}

	public static int FindBestIndex<T>(this T[] _arr, Func<T, float> _valuation)
	{
		if (_arr.IsNullOrEmpty() || _valuation == null)
			return default;

		float bestValue = float.MinValue;
		int bestIdx = 0;
		
		for (int i = 0; i < _arr.Length; i++)
		{
			float value = _valuation(_arr[i]);
			if (value > bestValue)
			{
				bestValue = value;
				bestIdx = i;
			}
		}

		return bestIdx;
	}

	public static int FindIndex<T>(this T[] _array, Predicate<T> _match)
	{
		if (_array == null || _match == null)
			return -1;
		for (int i = 0; i < _array.Length; i++)
			if (_match(_array[i]))
				return i;
		return -1;
	}


	public static void SetExpanded(this RectTransform _rt, float _left = 0, float _top = 0, float _right = 0, float _bottom = 0)
	{
		_rt.anchorMin = Vector3.zero;
		_rt.anchorMax = Vector3.one;
		_rt.pivot = Vector3.one * .5f;
		_rt.anchoredPosition3D = new Vector3((_left - _right) * .5f, (_top - _bottom) * .5f, 0);
		_rt.sizeDelta = new Vector2(-(_right + _left), -(_bottom + _top));
	}

	public static float GetWidth(this RectTransform _rt)
	{
		return _rt.sizeDelta.x;
	}

	public static float GetHeight(this RectTransform _rt)
	{
		return _rt.sizeDelta.y;
	}

	public static void SetWidth(this RectTransform _rt, float _width)
	{
		_rt.sizeDelta = new Vector2(_width, _rt.sizeDelta.y);
	}
	
	public static void AddWidth(this RectTransform _rt, float _additionalWidth)
	{
		_rt.sizeDelta = new Vector2(_rt.sizeDelta.x + _additionalWidth, _rt.sizeDelta.y);
	}
	
	public static void SetHeight(this RectTransform _rt, float _height)
	{
		_rt.sizeDelta = new Vector2(_rt.sizeDelta.x, _height);
	}

	public static Dictionary<Renderer, Material[]> ReplaceAllMaterials(this GameObject _o, Material _with, Func<Renderer, bool> _shouldReplace = null) {
		var backup = new Dictionary<Renderer, Material[]>();
		_o.ReplaceAllMaterials(_with, backup, _shouldReplace);
		return backup;
	}
	public static void DeactivateCanvasUI(this GameObject _o)
	{
        var canvases = _o.GetComponentsInChildren<Canvas>(true);
		foreach (var c in canvases)
			c.gameObject.SetActive(false);
    }

	private static bool IsNonReplaceShader(Shader _s)
	{
		var name = _s.name;
		if (name.Contains("MOA_SG_")) return false;
		if (name.Contains("OneColour")) return false;
		return true;
		//if (name.Contains("Decal")) return true;
		//if (name.Contains("Legacy")) return true;
		//return false;
	}

	public static void ReplaceAllMaterials(this GameObject _o, Material _with, Dictionary<Renderer, Material[]> _backup, Func<Renderer, bool> _shouldReplace = null) {
		var renderers = _o.GetComponentsInChildren<Renderer>(true);
		foreach (var r in renderers) {
			if (_shouldReplace != null && !_shouldReplace(r))
				continue;
            _backup[r] = r.materials;
            var mats = new Material[r.materials.Length];
            int c = 0, numReplaced = 0;
            foreach (var m in r.materials)
            {
	            //Debug.LogError($"Replacing mat {r.materials[c].name} shader {r.materials[c].shader.name} in renderer {r.name}");
	            var mat = _with;
	            if (IsNonReplaceShader(r.materials[c].shader)) mat = r.materials[c];
	            else ++numReplaced;
                mats[c] = mat;
                c++;
            }
            if (numReplaced > 0)
				r.materials = mats;
		}
	}
	public static void RestoreAllMaterials(this GameObject _o, Dictionary<Renderer, Material[]> _backup) {
		var renderers = _o.GetComponentsInChildren<Renderer>(true);
		foreach (var r in renderers) {
			if (_backup.TryGetValue(r, out var mat)) r.materials = mat;
		}
	}
	public static Dictionary<Renderer, Material[]> ReplaceAllMaterials(this List<GameObject> _objs, Material _with) {
		var backup = new Dictionary<Renderer, Material[]>();
		foreach (var o in _objs) o.ReplaceAllMaterials(_with, backup);
		return backup;
	}
	public static void RestoreAllMaterials(this List<GameObject> _objs, Dictionary<Renderer, Material[]> _backup) {
		foreach (var o in _objs) o.RestoreAllMaterials(_backup);
	}
	
	public static void SetAllMaterials(GameObject _o, string _label, float _value) {
		var ms = _o.GetComponentsInChildren<MeshRenderer>(true);
		foreach (var m in ms)
			foreach (var mat in m.materials)
				mat.SetFloat(_label, _value);
		var smrs = _o.GetComponentsInChildren<SkinnedMeshRenderer>(true);
		foreach (var m in smrs)
			foreach (var mat in m.materials)
				mat.SetFloat(_label, _value);
	}
	public static void SetGreyable(GameObject _o, bool _greyable) {
		SetAllMaterials(_o, "_IgnoreDistrictFilter", _greyable ? 0f : 1f);
	}

	/*public static Locator GetLocator(this GameObject _this, LocatorLookup.LocType _s, int _index = 0, bool _sub = false) {
		var lookup = _this.GetComponent<LocatorLookup>();
		if (lookup == null) return null;
		return lookup.Get(_s, _index, _sub);
	}*/

	const float c_brightnessNormal = .8f;
	const float c_brightnessHighlight = 4f;//10f;
	public static void HighlightRenderers(GameObject _o, bool _highlight, bool _includeSMRs = false)
	{
		float brightness = _highlight ? c_brightnessHighlight : c_brightnessNormal;
		SetRendererBrightness(_o, brightness, _includeSMRs);
	}

	public static void ColourHighlightRenderers(GameObject _o, bool _highlight, Color _colour, bool _includeSMRs = false)
	{
		var finalColour = _highlight ? _colour : Color.black;
		SetRendererEmissive(_o, finalColour, _includeSMRs);
	}
	public static void ColourHighlightRenderers(GameObject _o, bool _highlight, Vector4 _colour, bool _includeSMRs = false)
	{
		var finalColour = _highlight ? _colour : Vector4.zero;
		SetRendererEmissive(_o, finalColour, _includeSMRs);
	}
	
	public static void HighlightRenderersForTime(GameObject _o, float _time, float _fadeDuration)
	{
		GlobalData.Me.DoFor(_time, (t) => {
				var brightness = c_brightnessHighlight;
				if (t < _fadeDuration) brightness = Mathf.Lerp(c_brightnessNormal, c_brightnessHighlight, t / _fadeDuration);
				else if (t > _time - _fadeDuration) brightness = Mathf.Lerp(c_brightnessHighlight, c_brightnessNormal, (t - (_time - _fadeDuration)) / _fadeDuration);
				SetRendererBrightness(_o, brightness);
				return true;
			},
			() => SetRendererBrightness(_o, c_brightnessNormal)
		);
	}

	public static void SetRendererBrightness(GameObject _o, float brightness, bool _includeSMRs = false)
	{
		if(_o == null) return;

		void ApplyToMaterial(Material[] _mats)
		{
			foreach (var mat in _mats)
				if (mat.shader.name.Contains("MOA_SG_Tint"))
					mat.SetFloat("_Brightness", brightness);
		}
		
		foreach (var rnd in _o.GetComponentsInChildren<MeshRenderer>())
			ApplyToMaterial(rnd.materials);
		if (_includeSMRs)
			foreach (var smr in _o.GetComponentsInChildren<SkinnedMeshRenderer>())
				ApplyToMaterial(smr.materials);
	}

	public static void SetRendererEmissive(GameObject _o, Color _colour, bool _includeSMRs = false)
	{
		if (_o == null) return;

		void ApplyToMaterial(Material[] _mats)
		{
			foreach (var mat in _mats)
				if (mat.shader.name.Contains("MOA_SG_Tint"))
					mat.SetColor("_EmisivColor", _colour);
		}
		
		foreach (var rnd in _o.GetComponentsInChildren<MeshRenderer>())
			ApplyToMaterial(rnd.materials);
		if (_includeSMRs)
			foreach (var smr in _o.GetComponentsInChildren<SkinnedMeshRenderer>())
				ApplyToMaterial(smr.materials);
	}

	public static void SetRendererEmissive(GameObject _o, Vector4 _colour, bool _includeSMRs = false)
	{
		if (_o == null) return;

		void ApplyToMaterial(Material[] _mats)
		{
			foreach (var mat in _mats)
				if (mat.shader.name.Contains("MOA_SG_Tint"))
					mat.SetVector("_EmisivColor", _colour);
		}
		
		foreach (var rnd in _o.GetComponentsInChildren<MeshRenderer>()) 
			ApplyToMaterial(rnd.materials);
		if (_includeSMRs)
			foreach (var smr in _o.GetComponentsInChildren<SkinnedMeshRenderer>())
				ApplyToMaterial(smr.materials);
	}
	
	private static GameObject s_tooltip;
	public static GameObject ShowTooltip3D(string _message, Vector3 _pos3D, Vector3 _offset2D = default)
	{
		var pos = Camera.main.WorldToScreenPoint(_pos3D) + _offset2D;
		return ShowTooltip(_message, pos);
	}

	public static GameObject ShowTooltip(string _message, Vector3 _pos2D)
	{
		if (s_tooltip == null)
			s_tooltip = GameObject.Instantiate(Resources.Load<GameObject>("_Prefabs/Dialogs/ObjectMessage"), GameManager.Me.CurrentCanvas);
		s_tooltip.GetComponentInChildren<TextMeshProUGUI>().text = _message;
		var pos = _pos2D;
		pos.x = Mathf.Clamp(pos.x, Screen.width * .1f, Screen.width * .9f);
		pos.y = Mathf.Clamp(pos.y, Screen.height * .05f, Screen.height * .95f);
		s_tooltip.transform.position = pos;
		return s_tooltip;
	}

	public static void ClearTooltip()
	{
		if (s_tooltip != null)
		{
			GameObject.Destroy(s_tooltip);
			s_tooltip = null;
		}
	}

	static int s_clearObjectMessagesBefore;
	public static void ClearObjectMessages() => s_clearObjectMessagesBefore = Time.frameCount;
	public static void ShowObjectMessage(string _message, Func<Vector3> _screenPosition, GameObject _obj, float _duration, System.Action _onFinish = null, bool _highlightObject = false, bool _clearOthers = true, float _objectHighlightDuration = -1)
	{
		if (_clearOthers) ClearObjectMessages();
		var inst = GameObject.Instantiate(Resources.Load<GameObject>("_Prefabs/Dialogs/ObjectMessage"), GameManager.Me.CurrentCanvas);
		inst.GetComponentInChildren<TextMeshProUGUI>().text = _message;
		const float c_fadeDuration = .25f;
		if (_highlightObject) HighlightRenderersForTime(_obj, _objectHighlightDuration > 0 ? _objectHighlightDuration : _duration, c_fadeDuration);
		var canvasGroup = inst.GetComponent<CanvasGroup>();
		canvasGroup.alpha = 0;
		int startedOnFrame = Time.frameCount;
		GlobalData.Me.DoFor(_duration - c_fadeDuration, (t) => { // - c_fadeDuration because fade out happens afterwards
				canvasGroup.alpha = Mathf.Clamp01(t / c_fadeDuration);
				if(_obj != null)
				{
					inst.transform.position = _screenPosition();
				}
				return startedOnFrame >= s_clearObjectMessagesBefore;;
			},
			() => {
				GlobalData.Me.DoFor(c_fadeDuration, (t) => {
					canvasGroup.alpha = Mathf.Clamp01(1 - t / c_fadeDuration);
					return true;
				}, () =>
				{
					_onFinish?.Invoke();
					GameObject.Destroy(inst);
				});
			}
		);
	}
	
	public static void ShowObjectMessagePosition(string _message, Vector3 _worldPos, GameObject _obj, float _duration, Action _onFinish = null, bool _highlightObject = false, bool _clearOthers = true, bool _clampToScreen = false, float _objectHighlightDuration = 1)
	{
		Vector3 GetPosition()
		{
			var pos = Camera.main.WorldToScreenPoint(_worldPos);
			if(_clampToScreen)
			{
				pos.x = Mathf.Clamp(pos.x, Screen.width * .15f, Screen.width * .85f);
				pos.y = Mathf.Clamp(pos.y, Screen.height * .1f, Screen.height * .9f);
			}
			return pos;
		}
		
		ShowObjectMessage(_message, GetPosition, _obj, _duration, _onFinish, _highlightObject, _clearOthers, _objectHighlightDuration);
	}
	
	public static void ShowObjectMessage(string _message, Vector3 _offset, GameObject _obj, float _duration, Action _onFinish = null, bool _highlightObject = false, bool _clearOthers = true, bool _clampToScreen = false, float _objectHighlightDuration = 1)
	{
		_offset.x *= Screen.width;
		_offset.y *= Screen.height;
		
		Vector3 GetPosition()
		{
			var pos = Camera.main.WorldToScreenPoint(_obj.transform.position) + _offset;
			if(_clampToScreen)
			{
				pos.x = Mathf.Clamp(pos.x, Screen.width * .15f, Screen.width * .85f);
				pos.y = Mathf.Clamp(pos.y, Screen.height * .1f, Screen.height * .9f);
			}
			return pos;
		}
		
		ShowObjectMessage(_message, GetPosition, _obj, _duration, _onFinish, _highlightObject, _clearOthers, _objectHighlightDuration);
	}


	private static NotificationUIController s_currentDialog = null;
	public static bool IsShowingDialog =>  s_currentDialog != null;
	public static NotificationUIController ShowDialog(string _title, string _message, bool _showClose, string _button1, string _button2, System.Action<int> _cb, int _autoCloseTimeout = -1, Transform _parent = null, string _overridePrefab = null)
	{
		const string c_dialogEscapeBackLabel = "Dialog";
		Vector3 pos = Vector3.zero;
		NotificationUIController uiController = InfoPlaqueManager.Me.LoadUI<NotificationUIController>(true, _overridePrefab);
		uiController.titleText.text = _title;
		uiController.descriptionText.text = _message;
		if (_button1 != null) uiController.AddButton(_button1, _ped => { if (s_currentDialog == null) return; s_currentDialog = null; uiController.Close(); if (_cb != null) _cb(0); HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);});
		if (_button2 != null) uiController.AddButton(_button2, _ped => { if (s_currentDialog == null) return; s_currentDialog = null; uiController.Close(); if (_cb != null) _cb(1); HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);});
		uiController.onClose += () => {
			GameManager.Me.PopEscapeBackFunction(c_dialogEscapeBackLabel);
			s_currentDialog = null;
			if (_cb != null) _cb(-1);
		};
		uiController.ToggleCloseButtonX(_showClose);
		uiController.Show();
		s_currentDialog = uiController;
		if (_autoCloseTimeout > 0) GlobalData.Me.StartCoroutine(Co_AutoCloseDialog(_autoCloseTimeout));
		if (_parent != null) uiController.transform.parent = _parent;
		GameManager.Me.PushEscapeBackFunction(c_dialogEscapeBackLabel, () => uiController.Close());
		return uiController;
	}

	public static IEnumerator Co_AutoCloseDialog(int _timeout)
	{
		yield return new WaitForSeconds(_timeout);
		if (IsShowingDialog)
		{
			s_currentDialog.Close();
			s_currentDialog = null;
		}
	}


	public static void SetGameViewSize(int _width, int _height)
	{
#if UNITY_EDITOR
		GameViewUtils.AddAndSelectCustomSize(GameViewUtils.GameViewSizeType.FixedResolution, GameViewSizeGroupType.Standalone, _width, _height, $"{_width}x{_height}");
#else
		SettingsUIController.ChangeResolution(_width, _height);
#endif
	}
	
	private static Dictionary<(Type, BindingFlags), MethodInfo[]> s_getMethodsCache = new ();
	public static MethodInfo[] GetMethodsCached(this Type _type, BindingFlags _flags = BindingFlags.Instance | BindingFlags.Static | BindingFlags.Public)
	{
		if (s_getMethodsCache.TryGetValue((_type, _flags), out var methods))
			return methods;
		methods = _type.GetMethods(_flags);
		s_getMethodsCache[(_type, _flags)] = methods;
		return methods;
	}
	
	// Function that takes a path string and returns the final Transform
	public static Transform FindTransformByPath(string path)
	{
		return GameObject.Find(path.StartsWith("/") ? path : $"/{path}")?.transform;
		
		// Split the path string by '/'
		string[] pathElements = path.Split('/');

		// Start by finding the root object using the first part of the path
		GameObject currentObject = GameObject.Find(pathElements[0]);

		// If the root object isn't found, return null
		if (currentObject == null)
		{
			return null;
		}

		// Traverse the hierarchy using the remaining path elements
		Transform currentTransform = currentObject.transform;
		for (int i = 1; i < pathElements.Length; i++)
		{
			// Search for the child with the name in pathElements[i]
			currentTransform = currentTransform.Find(pathElements[i]);

			// If any part of the path isn't found, return null
			if (currentTransform == null)
			{
				return null;
			}
		}

		// Return the final Transform found
		return currentTransform;
	}



#if UNITY_EDITOR
	//[MenuItem("22Cans/TestSS")]
	static void TestSS()
	{
		var go = new GameObject();
		var thing = go.AddComponent<BCActionArmourer>();
		thing.GetStock().Items.Add(new NGStock.NGStockItem(new NGCarriableResource() {m_name = "Things"}, 15, 12));
		thing.GetStock().Items.Add(new NGStock.NGStockItem(new NGCarriableResource() {m_name = "Others"}, 4, 6));
		var s = SSerializer.Serialize(thing);
		System.Diagnostics.Stopwatch sw = new System.Diagnostics.Stopwatch();
		sw.Start();
		for (int i = 0; i < 10000; ++i)
			SSerializer.Serialize(thing);
		sw.Stop();
		s = $"{s}\n\n{sw.ElapsedMilliseconds}ms\n";
		System.IO.File.WriteAllText($"SS_{DateTime.UtcNow.ToShortDateString().Replace("/", "_")}_{DateTime.UtcNow.ToLongTimeString().Replace(":", "_")}.txt", s);
		UnityEngine.Object.DestroyImmediate(go);
	}
#endif
}

public static class FLog
{
	public static void Log(string _msg)
	{
		//Debug.Log($"FLog: {_msg}");
	}
}

public static class TMPExtension {
	public static void FitTextBox(this TextMeshProUGUI _tmp, int _maxWidth) {
		var rt = _tmp.transform as RectTransform;
		rt.sizeDelta = new Vector2(_maxWidth, 20000);
		_tmp.ForceMeshUpdate();
		float w = Mathf.Ceil(_tmp.textBounds.size.x), h = Mathf.Ceil(_tmp.textBounds.size.y);
		rt.sizeDelta = new Vector2(w, h);
	}
	public static void SetTextAndFit(this TextMeshProUGUI _tmp, string _text, int _maxWidth) {
		_tmp.text = _text;
		_tmp.FitTextBox(_maxWidth);
	}
}

#if UNITY_EDITOR
public static class AssetUtils {
	public static string GetResourcePath(UnityEngine.Object _obj) {
		var fullPath = AssetDatabase.GetAssetPath(_obj);
		string output = ResourcePathHandling(fullPath, _obj);
		return output;
	}

	private static string ResourcePathHandling(string fullPath, UnityEngine.Object _obj)
	{
		// Debug.Log($"GetResourcePath {fullPath}", _obj);
		const string c_resStr = "/Resources/";
		var resIndex = fullPath.LastIndexOf(c_resStr);
		if (resIndex != -1)
		{
			var resPath = fullPath.Substring(resIndex + c_resStr.Length);
			var extIndex = resPath.LastIndexOf('.');
			return resPath.Substring(0, extIndex);
		}
		// Disabling because it's spamming the animation window. Sorry!
		if (!string.IsNullOrEmpty(fullPath))
			Debug.LogError($"Object path not in a Resource folder - {fullPath}", _obj);
		return null;
	}
}
#endif



public class InputUtilities 
{
	public static MABuilding GetClosestFreeHome(NGMovingObject _worker)
	{
		MABuilding bestBuilding = null;
		float shortestDistance = float.MaxValue;
		
		foreach (var b in NGManager.Me.m_maBuildings)
		{
			var finalDist = DropCommanderFinalScore(_worker.transform.position, _worker, b, out var action, SpecialHandlingAction.AssignHome, true);
			if (finalDist >= 0 && finalDist < shortestDistance)
			{
				shortestDistance = finalDist;
				bestBuilding = b;
			}
		}
		return bestBuilding;
	}
	
	public static MABuilding GetClosestJob(MAWorker _worker)
	{
		MABuilding bestBuilding = null;
		float shortestDistance = float.MaxValue;
		foreach (var b in NGManager.Me.m_maBuildings)
		{
			var finalDist = DropCommanderFinalScore(_worker.transform.position, _worker, b, out var action, SpecialHandlingAction.AssignJob, true);
			if (finalDist >= 0 && finalDist < shortestDistance)
			{
				shortestDistance = finalDist;
				bestBuilding = b;
			}
		}
		return bestBuilding;
	}
	
	public static bool GetObjectFromLocationString(string _location, out object _outObject, bool _verboseLogs = false)
	{
		_outObject = null;

		if (_location.IsNullOrWhiteSpace()) return false;

		var bits = _location.Split(new char[] {'[', ']'}, StringSplitOptions.RemoveEmptyEntries);
		if (bits.Length < 2) return false;
		
		string locationPart1 = bits[0].Trim();
		string locationPart2 = bits[1].Trim();
		if (locationPart1.Trim() == "Pos")
		{
			var re = MAParserSupport.ConvertPos(new string[] { "", locationPart2 });
			if (re != null)
			{
				_outObject = re;
				return true;
			}
			if (_verboseLogs) Debug.LogError($"{nameof(MAParserSupport)} - {nameof(MAParserSupport.ConvertPos)} - Could not parse Position (null). bits: '{locationPart2}]'");
			return false;
		}
		object obj;
		if (locationPart1 == "Building")
		{
			obj = MAParserSupport.ConvertBuilding(new string[] {"", locationPart2});
			if (obj != null)
			{
				_outObject = obj;
				return true;
			}
			if (_verboseLogs) Debug.LogError($"{nameof(MAParserSupport)} - {nameof(MAParserSupport.ConvertBuilding)} - Could not find Building (null). bits: '{locationPart2}]'");
		}
		if (locationPart1 == "Character")
		{
			obj = MAParserSupport.ConvertCharacter(new string[] {"", locationPart2});
			if (obj != null)
			{
				_outObject = obj;
				return true;
			}
			if (_verboseLogs) Debug.LogError($"{nameof(MAParserSupport)} - {nameof(MAParserSupport.ConvertComponentBuilding)} - Could not find Components (null). bits: '{locationPart2}]'");
		}
		if (locationPart1 == "Decoration")
		{
			obj = MAParserSupport.ConvertDecoration(new string[] {"", locationPart2});
			if (obj != null)
			{
				_outObject = obj;
				return true;
			}
			if (_verboseLogs) Debug.LogError($"{nameof(MAParserSupport)} - {nameof(MAParserSupport.ConvertDecoration)} - Could not find Decoration (null). bits: '{locationPart2}]'");
		}
		if (locationPart1 == "ComponentBuilding")
		{
			obj = MAParserSupport.ConvertComponentBuilding(new string[] {"", locationPart2});
			if (obj != null)
			{
				_outObject = obj;
				return true;
			}
			if (_verboseLogs) Debug.LogError($"{nameof(MAParserSupport)} - {nameof(MAParserSupport.ConvertComponentBuilding)} - Could not find Components (null). bits: '{locationPart2}]'");
		}
		if (locationPart1 == "Components")
		{
			obj = MAParserSupport.ConvertComponents(new string[] {"", locationPart2});
			if (obj != null)
			{
				_outObject = obj;
				return true;
			}
			if (_verboseLogs) Debug.LogError($"{nameof(MAParserSupport)} - {nameof(MAParserSupport.ConvertComponents)} - Could not find Components (null). bits: '{locationPart2}]'");
		}
		if (locationPart1 == "NamedPoint")
		{
			obj = MAParserSupport.ConvertNamedPoint(new string[] {"", locationPart2});
			if (obj != null)
			{
				_outObject = obj;
				return true;
			}
			if (_verboseLogs) Debug.LogError($"{nameof(MAParserSupport)} - {nameof(MAParserSupport.ConvertNamedPoint)} - Could not find NamedPoint (null). bits: '{locationPart2}]'");
		}
		return false;
	}
	
	public static bool GetPositionFromObjectString(string _location, out Vector3 outPos, bool _verboseLogs = false)
	{
		outPos = Vector3.zero;

		if (GetObjectFromLocationString(_location, out object outObject, _verboseLogs))
		{
			var v = outObject as Vector3?;
			if (v != null)
			{
				outPos = v.Value;
				return true;
			}

			var b = outObject as MABuilding;
			if (b != null)
			{
				outPos = b.transform.position;
				return true;
			}		
			
			var ch = outObject as MACharacterBase;
			if (ch != null)
			{
				outPos = ch.transform.position;
				return true;
			}
			
			var d = outObject as NGDecoration;
			if (d != null)
			{
				outPos = d.transform.position;
				return true;
			}

			var cb = outObject as HashSet<MABuilding>;
			if (cb != null)
			{
				foreach (var bl in cb)
				{
					outPos = bl.transform.position;
					return true;
				}
			}
			
			var c = outObject as List<BCBase>;
			if (c != null)
			{
				foreach (var comp in c)
				{
					if(comp.Building == null) continue;
					outPos = comp.Building.transform.position;
					return true;
				}
			}

			var n = outObject as NamedPoint;
			if (n != null)
			{
				outPos = n.transform.position;
				return true;
			}
		}
		return false;
	}
	
	public static float DropCommanderFinalScore(Vector3 hitPoint, NGMovingObject obj, NGCommanderBase target, out SpecialHandlingAction _action, SpecialHandlingAction _restrictedAction, bool _ignoreDistance = false)
	{
		_action = null;
		if (!target.IsWithinOpenDistrict())
			return -1f;
			
		var maBuilding = target.GetComponent<MABuilding>();
		
		if (maBuilding == null) 
			return -1f;
		
		var assignPower = maBuilding.GetDropScore(obj, out _action, _restrictedAction);
		if (assignPower <= 0f) 
			return -1f;
		
		float maxDist = NGManager.Me.m_bezierLineDefaultRadius;
		
		var bezierDistOverride = obj.GetBezierDistanceOverride(_restrictedAction);
		if(bezierDistOverride > -1)
			maxDist = bezierDistOverride;
			
		bool isNonePickup = obj.GetComponent<ReactPickup>()?.Contents?.IsNone ?? false;	
		var sqrMag = (target.DoorPosInner - hitPoint).xzSqrMagnitude();
		var distanceSq = maxDist * maxDist;
		bool hitValid = _ignoreDistance || isNonePickup || sqrMag < distanceSq;
		if(hitValid == false)
			return -1;
			
		return Mathf.Sqrt(sqrMag) / assignPower;
	}

	public static float DropDecorationActionFinalScore(Vector3 hitPoint, NGMovingObject obj, MADecorationActionBase target, out SpecialHandlingAction _action, SpecialHandlingAction _restrictedAction, bool _ignoreDistance = false)
	{
		_action = null;
		if (!target.IsWithinOpenDistrict())
			return -1f;

		var assignPower = target.GetDropScore(obj, out _action, _restrictedAction);
		if (assignPower <= 0f)
			return -1f;

		float maxDist = NGManager.Me.m_bezierLineDefaultRadius;

		var bezierDistOverride = obj.GetBezierDistanceOverride(_restrictedAction);
		if (bezierDistOverride > -1)
			maxDist = bezierDistOverride;

		bool isNonePickup = obj.GetComponent<ReactPickup>()?.Contents?.IsNone ?? false;

		Vector3 targetPos = target.transform.position;

		var anchor = target.GetComponentInChildren<BezierAnchor>();

		if (anchor != null)
		{
			targetPos = anchor.gameObject.transform.position;
		}

		var sqrMag = (targetPos - hitPoint).xzSqrMagnitude();
		var distanceSq = maxDist * maxDist;
		bool hitValid = _ignoreDistance || isNonePickup || sqrMag < distanceSq;
		if (hitValid == false)
			return -1;

		return Mathf.Sqrt(sqrMag) / assignPower;
	}

	public static GameObject NGFindBestCollider(int _inputId, NGMovingObject _object, out SpecialHandlingAction _action, SpecialHandlingAction _restrictedAction = null, bool _useObjectPosition = false)
	{
		GameObject bestCommander = null;
		_action = null;
		Vector3 hitPoint;

		if(_useObjectPosition)
        {
			hitPoint = _object.transform.position;
		}
		else if(NGDirectionCardBase.DraggingCard != null && NGDirectionCardBase.DraggingCard.IsIn3D)
		{
			hitPoint = GetCardToTerrainPos(NGDirectionCardBase.DraggingCard);
		}
		else
		{
			hitPoint = GetCursorToTerrainPos(_inputId);
		}
		
		if (hitPoint.sqrMagnitude > 1e22f)
			return null;
			
		float shortestDistance = float.MaxValue;

		foreach(var c in NGManager.Me.m_maBuildings)
		{
			if(c == _object.DraggedFrom) continue;

			var finalDist = DropCommanderFinalScore(hitPoint, _object, c, out var action, _restrictedAction);
			if (finalDist >= 0 && finalDist < shortestDistance)
            {
				shortestDistance = finalDist;
				_action = action;
				bestCommander = c.gameObject;
            }
		}

#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		// check for interactable decorations
		foreach (var dab in NGManager.Me.m_decorationHolder.GetComponentsInChildren<MADecorationActionBase>())
		{
			var finalDist = DropDecorationActionFinalScore(hitPoint, _object, dab, out var action, _restrictedAction);
			if (finalDist >= 0 && finalDist < shortestDistance)
			{
				shortestDistance = finalDist;
				_action = action;
				bestCommander = dab.gameObject;
			}
		}
#endif

		if (_object.DraggedFrom != null && (_object.CanReturn || bestCommander == null))
		{
			float dist = DropCommanderFinalScore(hitPoint, _object, _object.DraggedFrom, out var action, _restrictedAction);
			if (dist >= 0 && dist < shortestDistance)
			{
				shortestDistance = dist;
				bestCommander = _object.DraggedFrom.gameObject;
				_action = action;
			}
		}

		// Check return card to slot
		var card = (_object as NGPickupUpgradeCard)?.m_card;
		if(card == null && NGDirectionCardBase.DraggingCard != null)
			card = NGDirectionCardBase.DraggingCard;
		
		if (card != null && card.FromCardSlot != null && 
		    (card.CanReturnToHolder || bestCommander == null))
		{
			var dist = (hitPoint - card.FromCardSlot.transform.position).xzMagnitude();
			if (dist < shortestDistance)
			{
				shortestDistance = dist;
				bestCommander = card.FromCardSlot.gameObject;
				_action = SpecialHandlingAction.ReturnCard;
			}
		}

		_object.ShowInHandDetails(bestCommander, _object);
		return bestCommander;
	}	
	/*public static NGCommanderBase NGFindRandomCollider(NGMovingObject _object, PickupAction _assignType = PickupAction.None)
	{
		if (_object == null) return null;
		var results = new List<NGCommanderBase>();
		foreach(var c in NGManager.Me.m_NGCommanderList)
		{
			if (!c.IsWithinOpenDistrict())
				continue;
			if (!IsValidCommander(_object, c, _assignType))
				continue;
			var finalDist = DropCommanderFinalScore(c.transform.position, _object, c);
			if (finalDist >= 0) results.Add(c);
		}
		
		if (results.Count == 0) return null;
		return results.PickRandom();
	}*/
	
	public static Vector3 GetCardToTerrainPos(NGDirectionCardBase _card)
	{
		var ray = Camera.main.RayThrough(_card.transform.position);// Camera.main.RayAtMouse(_inputId); 
		RaycastHit[] hits = Physics.RaycastAll(ray, 1000f, -1);//TODO Me.m_terrainLayerMask);
		if (hits == null || hits.Length == 0) return Vector3.one * 1e23f;
		return hits[0].point;
	}
	
	public static Vector3 GetCursorToTerrainPos(int _inputId)
	{
		var ray = Camera.main.RayAtMouse(_inputId); 
		RaycastHit[] hits = Physics.RaycastAll(ray, 1000f, -1);//TODO Me.m_terrainLayerMask);
		if (hits == null || hits.Length == 0) return Vector3.one * 1e23f;
		return hits[0].point;
	}

	public static MAHeroBase GetCurrentDraggingHero()
	{
		if (PickupManager.Me.m_heldObjects.Count > 0 && PickupManager.Me.m_heldObjects[0].m_object != null)
			return PickupManager.Me.m_heldObjects[0].m_object as MAHeroBase;
		return null;
	}
	public static MAWorker GetCurrentDraggingWorker()
	{
		if (PickupManager.Me.m_heldObjects.Count > 0 && PickupManager.Me.m_heldObjects[0].m_object != null)
			return PickupManager.Me.m_heldObjects[0].m_object as MAWorker;
		return null;
	}
	public static GameObject GetCurrentDragObject()
	{
		if (PickupManager.Me.m_heldObjects.Count > 0 && PickupManager.Me.m_heldObjects[0].m_object != null)
			return PickupManager.Me.m_heldObjects[0].m_object.gameObject;
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		if (NGDecorationInputHandler.s_draggingDecoration != null)
			return NGDecorationInputHandler.s_draggingDecoration.gameObject;
		if (MAKey.s_currentDrag != null)
			return MAKey.s_currentDrag.gameObject;
#endif
		return null;
	}

	[System.Serializable]
	public class HeldObject
	{
		public HeldObject(NGMovingObject _object)
		{
			m_object = _object;
			m_orginalPosition = _object.transform.position;
		}
		public NGMovingObject m_object;
		public Vector3 m_orginalPosition;
	}
}

public class NGStandardClick : MonoBehaviour, IOnClick {
	public void OnClick(int _inputId, bool _longClick) {
//		if (TutorialVideoHandler.Me.VideoPlaying) return;
		if (GameManager.IsVisiting && !_longClick) return;
		if (GameManager.SubSceneActive) return;
		var rcs = GetComponent<NGCommanderBase>();
		var ped = new PointerEventData(null);
		var pcr = new RaycastResult(); pcr.gameObject = gameObject;
		ped.pointerCurrentRaycast = pcr;
		ped.button = (UnityEngine.EventSystems.PointerEventData.InputButton)_inputId;
//		if (PISSManager.UsePISS) _longClick = PISSManager.PISSActive;
		if (_longClick) rcs?.OnBeginClickHold(ped);
		else rcs?.OnStandardClick(ped);
	}
}

public class NukeAnyHandler : MonoBehaviour, IPointerDownHandler
{
	public void OnPointerDown(PointerEventData eventData)
	{
#if UNITY_EDITOR
		if (Utility.GetKey(KeyCode.N))
		{
			BuildingPlacementManager.Me.ShowConfirmationDialog(transform.position, _b =>
			{
				if (!_b) return;
				DesignTableManager.ConvertFromWildBlock(gameObject);
				Destroy(gameObject);
			}, 0, "Destroy this object?");
		}
#endif
	}
}



[System.Serializable]
public class DemandPrefab {
#if UNITY_EDITOR
	[SerializeField] public string m_path;
	public void OnValidate() {
		if (m_prefab == null)
			m_prefab = AssetDatabase.LoadAssetAtPath<GameObject>($"Assets/Resources/{m_path}.prefab");
	}
#endif
	[SerializeField] public string m_type;
	[SerializeField] public GameObject m_prefab;
	public static implicit operator GameObject(DemandPrefab dp) { return dp.m_prefab; }
}
public class DemandObject : MonoBehaviour {
	public DemandPrefab m_prefab;
}


/*[AttributeUsage(AttributeTargets.All, AllowMultiple = false, Inherited = true)]
[System.Diagnostics.Conditional("UNITY_EDITOR")]
public sealed class ReadOnlyAttribute : Attribute {
	public ReadOnlyAttribute() {}
}*/
[AttributeUsage(AttributeTargets.All, AllowMultiple = false, Inherited = false)]
[System.Diagnostics.Conditional("UNITY_EDITOR")]
//[MeansImplicitUse]
public class ShowInInspectorAttribute : Attribute {
	public ShowInInspectorAttribute() {}
}
public enum TitleAlignments {
	Left = 0,
	Centered = 1,
	Right = 2,
	Split = 3
}
[AttributeUsage(AttributeTargets.All, AllowMultiple = true, Inherited = true)]
[System.Diagnostics.Conditional("UNITY_EDITOR")]
//[DontApplyToListElements]
public class TitleAttribute : Attribute {
	public string Title;
	public string Subtitle;
	public bool Bold;
	public bool HorizontalLine;
	public TitleAlignments TitleAlignment;
	public TitleAttribute(string title, string subtitle = null, TitleAlignments titleAlignment = TitleAlignments.Left, bool horizontalLine = true, bool bold = true) {}
}
public enum ButtonSizes {
	Small = 0,
	Medium = 22,
	Large = 31,
	Gigantic = 62
}
public enum ButtonStyle {
	CompactBox = 0,
	FoldoutButton = 1,
	Box = 2
}
[AttributeUsage(AttributeTargets.Method, AllowMultiple = false, Inherited = false)]
[System.Diagnostics.Conditional("UNITY_EDITOR")]
public class ButtonAttribute : ShowInInspectorAttribute {
	public int ButtonHeight;
	public string Name;
	public ButtonStyle Style;
	public bool Expanded;
	public bool DisplayParameters;
	public ButtonAttribute() {}
	public ButtonAttribute(ButtonSizes size) {}
	public ButtonAttribute(int buttonSize) {}
	public ButtonAttribute(string name) {}
	public ButtonAttribute(ButtonStyle parameterBtnStyle) {}
	public ButtonAttribute(string name, ButtonStyle parameterBtnStyle) {}
	public ButtonAttribute(ButtonSizes size, ButtonStyle parameterBtnStyle) {}
	public ButtonAttribute(string name, ButtonSizes buttonSize) {}
	public ButtonAttribute(string name, int buttonSize) {}
	public ButtonAttribute(int buttonSize, ButtonStyle parameterBtnStyle) {}
	public ButtonAttribute(string name, ButtonSizes buttonSize, ButtonStyle parameterBtnStyle) {}
	public ButtonAttribute(string name, int buttonSize, ButtonStyle parameterBtnStyle) {}
	public bool DrawResult { get; set; }
	public bool DrawResultIsSet { get; }
}

[System.AttributeUsage(System.AttributeTargets.Method)]
public class EditorDebug : System.Attribute{}
[System.AttributeUsage(System.AttributeTargets.Property)]
public class ExposeInInspector : System.Attribute {
	public string m_prefix;
	public ExposeInInspector() { m_prefix = string.Empty; }
	public ExposeInInspector(string _prefix) {m_prefix = _prefix;}
}

public class MonoEditorDebug : MonoBehaviour {
#if UNITY_EDITOR
	public class MonoBehaviourEditor : Editor {
		public override void OnInspectorGUI() {
			base.OnInspectorGUI();
		}
	}
#endif
}

public static class floatinv
{
	public static bool TryParse(string _s, out float _f) => _s.TryFloatInv(out _f);
	public static float Parse(string _s) => _s.ToFloatInv();
}

public class SurfaceTypeTracker
{
	private GameObject m_lastOnSurface = null;
	private string m_lastOnSurfaceType = "";
	private GameObject m_nextOnSurface = null;
	private string m_nextOnSurfaceType = "";
	private bool m_onSurfaceRemains = false;
	private int m_lastCheckFrame;

	public string DebugInfo => $"{m_lastOnSurfaceType} - {(m_lastOnSurface != null ? m_lastOnSurface.name : "none")}";

	public void CheckSurface(GameObject _on, GameObject _me)
	{
		var mePos = _me.transform.position;
		if (m_lastCheckFrame != Time.frameCount)
		{
			string surfaceToSet = null;
			if (m_onSurfaceRemains == false && m_nextOnSurfaceType != null && m_nextOnSurfaceType != m_lastOnSurfaceType)
			{
				surfaceToSet = m_nextOnSurfaceType;
				m_lastOnSurface = m_nextOnSurface;
			}
			m_onSurfaceRemains = false;
			m_nextOnSurfaceType = null;
			if (m_lastOnSurface != null && m_lastOnSurface.GetComponent<Terrain>() != null)
				surfaceToSet = GlobalData.GetTerrainSurfaceAtPoint(mePos);
			if (surfaceToSet != null && surfaceToSet != m_lastOnSurfaceType)
			{
				m_lastOnSurfaceType = surfaceToSet;
				var applyTo = _me.GetComponentInChildren<WwiseAnimationEvent>()?.gameObject ?? _me;
				AudioClipManager.Me.SetSurfaceType(m_lastOnSurfaceType, applyTo, false);
			}
			m_lastCheckFrame = Time.frameCount;
		}

		var surface = GlobalData.GetSurfaceTypeFromObject(_on, mePos);
		if (surface != null)
		{
			if (surface == m_lastOnSurfaceType)
			{
				m_onSurfaceRemains = true; // still touching same surface as last frame, do nothing
			}
			else
			{
				m_nextOnSurfaceType = surface; // we may have a new surface type, only if not touching the old one
				m_nextOnSurface = _on;
			}
		}
	}
}


public static class GameViewUtils
{
#if UNITY_EDITOR
    static object gameViewSizesInstance;
    static MethodInfo getGroup;

    static GameViewUtils()
    {
        // gameViewSizesInstance  = ScriptableSingleton<GameViewSizes>.instance;
        var sizesType = typeof(Editor).Assembly.GetType("UnityEditor.GameViewSizes");
        var singleType = typeof(ScriptableSingleton<>).MakeGenericType(sizesType);
        var instanceProp = singleType.GetProperty("instance");
        getGroup = sizesType.GetMethod("GetGroup");
        gameViewSizesInstance = instanceProp.GetValue(null, null);
    }

    public enum GameViewSizeType
    {
        AspectRatio, FixedResolution
    }

    public static void SetSize(int index)
    {
        var gvWndType = typeof(Editor).Assembly.GetType("UnityEditor.GameView");
        var selectedSizeIndexProp = gvWndType.GetProperty("selectedSizeIndex",
                BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic);
        var gvWnd = EditorWindow.GetWindow(gvWndType);
        selectedSizeIndexProp.SetValue(gvWnd, index, null);
    }
    
    public static void AddAndSelectCustomSize(GameViewSizeType viewSizeType, GameViewSizeGroupType sizeGroupType, int width, int height, string text)
    {
        AddCustomSize(viewSizeType, sizeGroupType, width, height, text);
        int idx = GameViewUtils.FindSize(GameViewSizeGroupType.Standalone, width, height);
        GameViewUtils.SetSize(idx);
    }

    public static void AddCustomSize(GameViewSizeType viewSizeType, GameViewSizeGroupType sizeGroupType, int width, int height, string text)
    {
        var group = GetGroup(sizeGroupType);
        var addCustomSize = getGroup.ReturnType.GetMethod("AddCustomSize"); // or group.GetType().
        var gvsType = typeof(Editor).Assembly.GetType("UnityEditor.GameViewSize");
        string assemblyName = "UnityEditor.dll";
        Assembly assembly = Assembly.Load(assemblyName);
        Type gameViewSize = assembly.GetType("UnityEditor.GameViewSize");
        Type gameViewSizeType = assembly.GetType("UnityEditor.GameViewSizeType");
        ConstructorInfo ctor = gameViewSize.GetConstructor(new Type[]
            {
                 gameViewSizeType,
                 typeof(int),
                 typeof(int),
                 typeof(string)
            });
        var newSize = ctor.Invoke(new object[] { (int)viewSizeType, width, height, text });
        addCustomSize.Invoke(group, new object[] { newSize });
    }

    public static bool SizeExists(GameViewSizeGroupType sizeGroupType, string text)
    {
        return FindSize(sizeGroupType, text) != -1;
    }

    public static int FindSize(GameViewSizeGroupType sizeGroupType, string text)
    {
        // GameViewSizes group = gameViewSizesInstance.GetGroup(sizeGroupType);
        // string[] texts = group.GetDisplayTexts();
        // for loop...

        var group = GetGroup(sizeGroupType);
        var getDisplayTexts = group.GetType().GetMethod("GetDisplayTexts");
        var displayTexts = getDisplayTexts.Invoke(group, null) as string[];
        for (int i = 0; i < displayTexts.Length; i++)
        {
            string display = displayTexts[i];
            // the text we get is "Name (W:H)" if the size has a name, or just "W:H" e.g. 16:9
            // so if we're querying a custom size text we substring to only get the name
            // You could see the outputs by just logging
            // Debug.Log(display);
            int pren = display.IndexOf('(');
            if (pren != -1)
                display = display.Substring(0, pren - 1); // -1 to remove the space that's before the prens. This is very implementation-depdenent
            if (display == text)
                return i;
        }
        return -1;
    }

    public static bool SizeExists(GameViewSizeGroupType sizeGroupType, int width, int height)
    {
        return FindSize(sizeGroupType, width, height) != -1;
    }

    public static int FindSize(GameViewSizeGroupType sizeGroupType, int width, int height)
    {
        // goal:
        // GameViewSizes group = gameViewSizesInstance.GetGroup(sizeGroupType);
        // int sizesCount = group.GetBuiltinCount() + group.GetCustomCount();
        // iterate through the sizes via group.GetGameViewSize(int index)

        var group = GetGroup(sizeGroupType);
        var groupType = group.GetType();
        var getBuiltinCount = groupType.GetMethod("GetBuiltinCount");
        var getCustomCount = groupType.GetMethod("GetCustomCount");
        int sizesCount = (int)getBuiltinCount.Invoke(group, null) + (int)getCustomCount.Invoke(group, null);
        var getGameViewSize = groupType.GetMethod("GetGameViewSize");
        var gvsType = getGameViewSize.ReturnType;
        var widthProp = gvsType.GetProperty("width");
        var heightProp = gvsType.GetProperty("height");
        var indexValue = new object[1];
        for (int i = 0; i < sizesCount; i++)
        {
            indexValue[0] = i;
            var size = getGameViewSize.Invoke(group, indexValue);
            int sizeWidth = (int)widthProp.GetValue(size, null);
            int sizeHeight = (int)heightProp.GetValue(size, null);
            if (sizeWidth == width && sizeHeight == height)
                return i;
        }
        return -1;
    }

    static object GetGroup(GameViewSizeGroupType type)
    {
        return getGroup.Invoke(gameViewSizesInstance, new object[] { (int)type });
    }

    public static GameViewSizeGroupType GetCurrentGroupType()
    {
        var getCurrentGroupTypeProp = gameViewSizesInstance.GetType().GetProperty("currentGroupType");
        return (GameViewSizeGroupType)(int)getCurrentGroupTypeProp.GetValue(gameViewSizesInstance, null);
    }
#endif
}


public static class MPlayerPrefs
{
	private static Dictionary<string, string> s_prefs = null;

	private static string s_prefsPath => Application.persistentDataPath + "/prefs.txt";
	private static void CheckAndLoad()
	{
		if (s_prefs == null)
		{
			s_prefs = new();
			if (System.IO.File.Exists(s_prefsPath) == false) return;
			var lines = System.IO.File.ReadAllLines(s_prefsPath);
			foreach (var l in lines)
			{
				var equalsIndex = l.IndexOf('=');
				if (equalsIndex == -1) continue;
				var key = l.Substring(0, equalsIndex);
				var value = l.Substring(equalsIndex + 1);
				value = value.Replace("\\n", "\n");
				s_prefs[key] = value;
			}
		}
	}

	private static void SaveAll()
	{
		System.Text.StringBuilder prefs = new();
		foreach (var kvp in s_prefs)
			prefs.AppendLine($"{kvp.Key}={kvp.Value.Replace("\n", "\\n")}");
		System.IO.File.WriteAllText(s_prefsPath, prefs.ToString()); 
	}

	public static void Save() {} // does nothing, we always save after set

	public static bool HasKey(string _key)
	{
		CheckAndLoad();
		return s_prefs.ContainsKey(_key);
	}
	public static string GetString(string _key, string _default = "")
	{
		CheckAndLoad();
		if (s_prefs.TryGetValue(_key, out var value))
			return value;
		return _default;
	}
	public static void SetString(string _key, string _value)
	{
		CheckAndLoad();
		s_prefs[_key] = _value;
		SaveAll();
	}
	public static int GetInt(string _key, int _default = 0)
	{
		var s = GetString(_key, null);
		if (s != null && int.TryParse(s, out var i))
			return i;
		return _default;
	}
	public static float GetFloat(string _key, float _default = 0)
	{
		var s = GetString(_key, null);
		if (s != null && floatinv.TryParse(s, out var i))
			return i;
		return _default;
	}
	public static void SetInt(string _key, int _value) => SetString(_key, _value.ToString());
	public static void SetFloat(string _key, float _value) => SetString(_key, _value.ToStringInv());

	public static void DeleteKey(string _key)
	{
		CheckAndLoad();
		s_prefs.Remove(_key);
		SaveAll();
	}

	public static void DeleteAll()
	{
		s_prefs.Clear();
		SaveAll();
	}
}

public static class PauseManager
{
	private static float s_previousTimeScale = 1f;
	private static int s_isPaused = 0;
	private static bool s_isInPausedState = false;

	public static bool IsPaused => CheckPaused();

	private static bool CheckPaused()
	{
		var nowPaused = s_isPaused > 0;
		if (s_isInPausedState != nowPaused)
		{
			s_isInPausedState = nowPaused;
			if (nowPaused)
			{
				s_previousTimeScale = Time.timeScale;
				Time.timeScale = 0f;
				AudioClipManager.Me.PlaySound("PlaySound_Pause_ENTER", GameManager.Me.gameObject);
				AudioListener.pause = true;
				GameManager.Me.PumpSteam();
			}
			else
			{
				Time.timeScale = s_previousTimeScale;
				AudioClipManager.Me.PlaySound("PlaySound_Pause_EXIT", GameManager.Me.gameObject);
				AudioListener.pause = false;
			}
		}
		return nowPaused;
	}

	public static void Pause()
	{
		++s_isPaused;
		CheckPaused();
	}

	public static void Resume()
	{
		--s_isPaused;
		CheckPaused();
	}
}

public class OptimisationCheck
{
	private System.Diagnostics.Stopwatch m_sw = System.Diagnostics.Stopwatch.StartNew();
	private string m_name;
	private long m_totalTimeOld = 0, m_totalTimeNew = 0;
	private int m_totalErrors = 0, m_totalTests = 0;
	private float m_lastDumpTime = 0;
	private long m_testOld = 0, m_testNew = 0;

	public OptimisationCheck(string _name)
	{
		m_name = _name;
	}

	public void StartCheckOld()
	{
		m_testOld = m_sw.ElapsedTicks;
	}

	public void EndCheckOld()
	{
		m_totalTimeOld += m_sw.ElapsedTicks - m_testOld;
	}

	public void StartCheckNew()
	{
		m_testNew = m_sw.ElapsedTicks;
	}

	public void EndCheckNew()
	{
		m_totalTimeNew += m_sw.ElapsedTicks - m_testNew;
	}

	public void AddTest(bool _passed)
	{
		++m_totalTests;
		if (_passed == false) ++m_totalErrors;
		const float c_dumpPeriod = 2;
		var t = Time.time;
		if (t > m_lastDumpTime + c_dumpPeriod)
		{
			m_lastDumpTime = t;
			Debug.LogError($"OptTest {m_name}: {m_totalTests} tests, {m_totalErrors} errors, old: {m_totalTimeOld / 1e6f:n2}ms, new: {m_totalTimeNew / 1e6f:n2}ms, ratio: {(m_totalTimeOld / 1e3f) / (m_totalTimeNew / 1e3f):n2}");
		}
	}
}
