using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
using UnityEditorInternal;
#endif

[System.Serializable]
public class AnimationClipDataManager : MonoSingleton<AnimationClipDataManager>
{
    [System.Serializable]
    public class AnimationClipHolder
    {
        // Data
        public string Name;
        public List<AnimationClipHolder> Folders;
        public List<AnimationClipData> Items;
		
        // Display params
        public bool FoldoutOpen;
        public bool RenameInProgress;
        
        public AnimationClipHolder(string _name)
        {
            Name = _name;
            Folders = new List<AnimationClipHolder>();
            Items = new List<AnimationClipData>();
        }
        
        public void DoActionOnAllChildren(System.Action<AnimationClipHolder> _action)
        {
            if(_action != null)
                _action(this);
            
            for(int i = 0; i < Folders.Count; i++)
                Folders[i].DoActionOnAllChildren(_action);
        }
    }
    
    // =========================================================================

    [SerializeField] AnimationClipHolder m_animationClipLibraryData;
    
    Dictionary<string, AnimationClipData> m_nameToClipData;
    public List<string> m_animationNames;

    // =========================================================================

    protected override void Awake()
    {
        base.Awake();
        //DontDestroyOnLoad(gameObject);
        Initialise();
    }

    // -------------------------------------------------------------------------

    void Initialise()
    {
	    GetAnimationDictionaryFromLibrary();
	    PetersDebug();
    }
	void PetersDebug()
	{
		return;
		m_animationNames = new ();
        var writeString = "Animations:\n";
        foreach(var d in m_nameToClipData)
		{
			if(d.Value.Clip)
			{
				m_animationNames.Add(d.Key);
				writeString += d.Key + "\n";
			}
		}
        File.WriteAllText("Assets/PetersTests/_Animation/AnimationList.xls", writeString);

		
    }

	// -------------------------------------------------------------------------

	private static DebugConsole.Command s_findContaining = new DebugConsole.Command("findanim", _s =>
	{
		var res = $"Animations containing {_s}:\n";
		_s = _s.ToLower();
		var bits = _s.Split(' ');
		foreach (var kvp in Me.m_nameToClipData)
		{
			bool containsAll = true;
			var key = kvp.Key.ToLower();
			foreach (var b in bits)
				if (!key.Contains(b))
					containsAll = false;
			if (containsAll)
				res += $"{kvp.Key} - {kvp.Value.Name ?? "<none>"} - {kvp.Value.Clip?.name ?? "<none>"}\n";
		}
		Debug.LogError(res);
	});
	
    public AnimationClipData GetAnimationClipDataByName(string _name)
    {
        AnimationClipData data = null;
        if(_name != null) m_nameToClipData.TryGetValue(_name, out data);
        return data;
    }
    
    public string GetAnimationNameCaseInsensitive(string _name)
    {
	    _name = _name.ToLower();
	    foreach (var kvp in m_nameToClipData)
	    {
		    if (kvp.Key.ToLower() == _name)
		    {
			    return kvp.Key;
			}
		}
	    return _name;
	}

	// -------------------------------------------------------------------------

	public AnimationClip GetAnimationClipByName(string _name)
	{
        return m_nameToClipData[_name].Clip;
	}
    
    // -------------------------------------------------------------------------
    
    public AnimationClipHolder GetAnimationLibraryDisplayData()
    {
        if(m_animationClipLibraryData == null) m_animationClipLibraryData = new AnimationClipHolder("Root");
        return m_animationClipLibraryData;
    }
    
    // -------------------------------------------------------------------------
    
    public void GetAnimationDictionaryFromLibrary(bool _clearLibary = true)
    {
        // Convert library to dictionary
        m_nameToClipData = new Dictionary<string, AnimationClipData>();
        AddFolderToDictionary(m_animationClipLibraryData, m_nameToClipData);
        
		if(_clearLibary) {
			// Clear some memory
        	m_animationClipLibraryData = null;
		}
    }

    // -------------------------------------------------------------------------

	public static void AutoPopulateTags(AnimationClipHolder _folder, string _tag, string _postfix, string _postfixReplace) {
		for (int i = 0; i < _folder.Items.Count; i ++) {
			var item = _folder.Items[i];
			if (item.ClipPath.EndsWith(_postfix) && (item.ClipPath.Contains("/" + _postfix + "/") || item.ClipPath.Contains("\\" + _postfix + "\\"))) {
				AnimationTagData tagData = null;
				for (int j = 0; j < item.TagData.Count; j ++) {
					if (item.TagData[j].Tag == _tag) {
						tagData = item.TagData[j];
						break;
					}
				}
				bool addToList = false;
				if (tagData == null) {
					tagData = new AnimationTagData();
					tagData.Tag = _tag;
					addToList = true;
				}
				
				if (tagData.Clip == null) {
					var tagPath = item.ClipPath;
					if (tagPath.Contains("/" + _postfix + "/"))
						tagPath = tagPath.Replace("/" + _postfix + "/", "/" + _postfixReplace + "/");
					else
						tagPath = tagPath.Replace("\\" + _postfix + "\\", "\\" + _postfixReplace + "\\");
					tagPath = tagPath.Substring(0, tagPath.Length - _postfix.Length) + _postfixReplace;
					tagData.ClipPath = tagPath;

					var clip = ResManager.Load<AnimationClip>(tagPath);
					if (clip == null) addToList = false;

					if (addToList)
						item.TagData.Add(tagData);
				}
			}
		}
		if (_folder.Folders != null) {
			for (int i = 0; i < _folder.Folders.Count; i ++) {
				AutoPopulateTags(_folder.Folders[i], _tag, _postfix, _postfixReplace);
			}
		}
	}

    // -------------------------------------------------------------------------

	public static void UpgradeRecursive(AnimationClipHolder _folder)
	{
        for (int i = 0; i < _folder.Items.Count; i++)
		{
            var data = _folder.Items[i];
            data.Upgrade();
		}

        if(_folder.Folders != null)
        {
            for(int i = 0; i < _folder.Folders.Count; i++)
            {
                UpgradeRecursive(_folder.Folders[i]);
            }
        }
	}

	// -------------------------------------------------------------------------

	public static void Clean(AnimationClipHolder _folder)
	{
		for (int i = 0; i < _folder.Items.Count; ++i)
		{
			var data = _folder.Items[i];
			if (data.Clip == null)
			{
				_folder.Items.RemoveAt(i);
				--i;
			}
		}
		for (int i = 0; i < _folder.Folders.Count; ++i)
		{
			var folder = _folder.Folders[i];
			Clean(folder);
			if (folder.Items.Count == 0 && folder.Folders.Count == 0)
			{
				_folder.Folders.RemoveAt(i);
				--i;
			}
		}
	}

	// -------------------------------------------------------------------------

    void AddFolderToDictionary(AnimationClipHolder _folder, Dictionary<string, AnimationClipData> _dictionary)
    {
        for(int i = 0; i < _folder.Items.Count; i++)
        {
            var data = _folder.Items[i];
            _dictionary[data.Name] = data;
        }
        
        if(_folder.Folders != null)
        {
            for(int i = 0; i < _folder.Folders.Count; i++)
            {
                AddFolderToDictionary(_folder.Folders[i], _dictionary);
            }
        }
    }
    
    // -------------------------------------------------------------------------
}
