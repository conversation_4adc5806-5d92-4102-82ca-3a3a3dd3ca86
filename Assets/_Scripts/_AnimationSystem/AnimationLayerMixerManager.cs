using UnityEngine;
using UnityEngine.Animations;
using UnityEngine.Playables;
using System.Collections;
using System.Collections.Generic;
using System;

public class AnimationLayerMixerManager : MonoBehaviour
{
	Animator m_anim;
	AnimationPlayableOutput m_animOutput;
	PlayQueuePlayable m_playQueue;
	AnimationLayerMixerPlayable m_animLayerMixer;
	AnimationLayerUpdater m_layerUpdater;
	int m_activeAnimLayer = 0;
	PlayableGraph m_playableGraph;
	int m_maxLayers = 8;

	Coroutine m_fadeCoroutine = null;
	List<int> m_addedLayers = new List<int>();
	List<float> m_layerBlendTimes = new List<float>();

	bool m_isSetup = false;

	public PlayableGraph Graph()
	{
		return m_playableGraph;
	}

	void Awake()
	{
		m_anim = GetComponent<Animator>();

		//KW: add an AnimationOverrideController if it doesn't already have one before setting up the playable graph so that we can swap out animations at runtime
		var overrideController = m_anim.runtimeAnimatorController as AnimatorOverrideController;

		if (overrideController == null)
		{
			//KW: only do this for quest characters (IsQuestCharacter is not set until later)
			MACharacterBase character = m_anim.GetComponentInParent<MACharacterBase>();

			if (character != null && (character is MAFlowCharacter || character is MAQuestGiver))
			{
				overrideController = new AnimatorOverrideController(m_anim.runtimeAnimatorController);
				m_anim.runtimeAnimatorController = overrideController;
			}
		}

		m_playableGraph = PlayableGraph.Create();

		m_animLayerMixer = AnimationLayerMixerPlayable.Create(m_playableGraph, m_maxLayers);

		// The AnimationLayerUpdater is responsible for receiving the PrepareFrame calls to 
		// update the individual mixers. This is the ultimate step in the output chain 
		// and the AnimLayerMixer is just passed through this.
		var scriptableAnimLayerUpdater = ScriptPlayable<AnimationLayerUpdater>.Create(m_playableGraph);
		scriptableAnimLayerUpdater.SetInputCount(1);
		scriptableAnimLayerUpdater.SetInputWeight(0, 1);
		m_playableGraph.Connect(m_animLayerMixer, 0, scriptableAnimLayerUpdater, 0);

		m_animOutput = AnimationPlayableOutput.Create(m_playableGraph, "Animation", m_anim);
		m_animOutput.SetSourcePlayable(scriptableAnimLayerUpdater);

		m_layerUpdater = scriptableAnimLayerUpdater.GetBehaviour();
		m_layerUpdater.Setup(m_maxLayers);
		
		var controllerPlayable = AnimatorControllerPlayable.Create(m_playableGraph, m_anim.runtimeAnimatorController);
		m_playableGraph.Connect(controllerPlayable, 0, m_animLayerMixer, 0);
		// Setting the base layer visibility from the AC to be 1. Note this always has to stay 1. Seems like the way 
		// AnimationLayerMixablePlayer handles the weights is that the base stays as 1 and everything else gets blended.
		m_animLayerMixer.SetInputWeight(0, 1f);

		m_playableGraph.SetTimeUpdateMode(DirectorUpdateMode.GameTime);
		m_playableGraph.Play();

		m_isSetup = true;
	}

	// RW-10-SEP-25: If the object is disabled and has never been enabled, Awake will not have run and
	// the ALMM won't be in a valid state to use.
	public bool IsSetup()
	{
		return m_isSetup;
	}

	public bool HasFreeCapacity()
	{
		return m_layerUpdater.HasFreeSlots();
	}

	IEnumerator FadeLayerWeight(int _fromLayer, int _toLayer, float _duration, Action _callback=null)
	{
		var sptp = transform.FindChildRecursiveByName(GlobalData.c_avatarRootName)?.GetComponent<SendPosToParent>();
		sptp?.StopMotionExtraction();

		if (_duration > 0)
		{
			yield return null;
		}

		float time = 0f;

		int minBlendLayer = Math.Min(_fromLayer, _toLayer);
		while (time < _duration)
		{
			//string debugStr = string.Format("{0} t: {1}  active: {2} ", Time.frameCount, time, m_activeAnimLayer);
			time += Time.deltaTime;

			//debugStr += string.Format("L0W: {0} ", m_animLayerMixer.GetInputWeight(0));

			// RW-26-AUG-25: All our layers are in override mode, not additive. That means that if a layer's weight is 
			// 1, nothing below it gets blended in. This is why layer 0 (the AnimController) always has to keep weight 1, since
			// if it had less, we'd be blending in an invalid pose.
			// If we're blending from L2 to L3, for example, we keep L2 at weight 1 as L3's weight is increased from 0. This ensures that
			// L0 doesn't get blended in. Then once L3 is at 1, we can set L2 to 0, since it's not being used anymore.
			// The exception is if we're blending back to L0. Then we just reduce everything else gradually to 0 and let L0 take over.
			for (int i=1; i<m_maxLayers; i++)
			{
				float w = m_animLayerMixer.GetInputWeight(i);
				if (i == minBlendLayer)
				{
					m_animLayerMixer.SetInputWeight(i, 1);
				}
				else if (i == m_activeAnimLayer)
				{
					m_animLayerMixer.SetInputWeight(i, Mathf.Clamp01(w+Time.deltaTime/_duration));
				}
				else// if (m_activeAnimLayer == 0)
				{
					m_animLayerMixer.SetInputWeight(i, Mathf.Clamp01(w-Time.deltaTime/_duration));
				}

				// Debug
				//w = m_animLayerMixer.GetInputWeight(i);
				//debugStr += string.Format("L{0}W: {1} ", i, w);
			}
			//Debug.LogErrorFormat(debugStr);
			yield return null;
		}
		
		for (int i=1; i<m_maxLayers; i++)
		{
			if (i == m_activeAnimLayer)
			{
				m_animLayerMixer.SetInputWeight(i, 1);
			}
			else
			{
				m_animLayerMixer.SetInputWeight(i, 0);
			}
		}
		if (_callback != null)
		{
			_callback();
		}

		CleanupUnusedLayers(m_activeAnimLayer);
		
		sptp?.StartMotionExtraction(false);

		/*string endDebugStr = string.Format("{0} ", Time.frameCount);
		for (int i=0; i<m_maxLayers; i++)
		{
			float w = m_animLayerMixer.GetInputWeight(i);
			endDebugStr += string.Format("L{0}W: {1} ", i, w);
		}
		Debug.LogErrorFormat(endDebugStr);*/
	}

	public int HandleLayerAdded(PlayQueuePlayable _pqp, float _blendTime)
	{
		int newLayerIdx = m_layerUpdater.AddManagedLayer(_pqp);
		//Debug.LogErrorFormat("{0}: Adding layer {1}", Time.frameCount, newLayerIdx);
		m_addedLayers.Add(newLayerIdx);
		m_layerBlendTimes.Add(_blendTime);

		// Connect the new mixer into the AnimLayerMixer.
		m_playableGraph.Connect(_pqp.m_mixer, 0, m_animLayerMixer, newLayerIdx);
		m_animLayerMixer.SetInputWeight(newLayerIdx, 0f); // action layer hidden initially

		// Set the layer to override mode so it replaces base animation
		m_animLayerMixer.SetLayerAdditive((uint)newLayerIdx, false);


		if (m_activeAnimLayer == 0)
		{
			m_activeAnimLayer = newLayerIdx;
			
			StopFadeCoroutine();
			m_fadeCoroutine = GameManager.Me.StartCoroutine(FadeLayerWeight(0, m_activeAnimLayer, _blendTime));
		}
		return newLayerIdx;
	}

	public void HandleLayerFinished(int _layerIndex)
	{
		if (m_activeAnimLayer == _layerIndex)
		{
			// Move onto the next anim after this one, if we can. If there's nothing to move to,
			// we go back to the base from the AC.
			int currentActiveLayerIdx = -1;
			for (int i=0; i<m_addedLayers.Count; i++)
			{
				if (m_addedLayers[i] == m_activeAnimLayer)
				{
					currentActiveLayerIdx = i;
				}
			}

			int oldActiveLayer = m_activeAnimLayer;
			if (currentActiveLayerIdx+1 < m_addedLayers.Count)
			{
				m_activeAnimLayer = m_addedLayers[currentActiveLayerIdx+1];
				//Debug.LogErrorFormat("Set Active Layer: {0} ({1})", m_activeAnimLayer);
			}
			else
			{
				m_activeAnimLayer = 0;
			}
			
			StopFadeCoroutine();
			m_fadeCoroutine = GameManager.Me.StartCoroutine(FadeLayerWeight(oldActiveLayer, m_activeAnimLayer, m_activeAnimLayer == 0 ? 0.25f : m_layerBlendTimes[currentActiveLayerIdx+1], () => { /*Stop();*/ }));
			
			// This is done to allow the layers to be cleaned up if anims are being interrupted and thus the fade coroutine isn't completing.
			CleanupUnusedLayers(oldActiveLayer);
		}
		else
		{
			for (int i=m_addedLayers.Count-1; i>=0; i--)
			{
				if (m_addedLayers[i] == _layerIndex)
				{
					m_addedLayers.RemoveAt(i);
					m_layerBlendTimes.RemoveAt(i);
					break;
				}
			}
		}
	}

	void StopFadeCoroutine()
	{
		if (m_fadeCoroutine != null && GameManager.Me != null)
		{
			GameManager.Me.StopCoroutine(m_fadeCoroutine);
			m_fadeCoroutine = null;

			var sptp = transform.FindChildRecursiveByName(GlobalData.c_avatarRootName)?.GetComponent<SendPosToParent>();
			sptp?.StartMotionExtraction(false);
		}
	}

	void CleanupUnusedLayers(int _newestLayerToKeep)
	{
		// Cleanup everything which was added prior to the now-active layer.
		while (m_addedLayers.Count > 0 && m_addedLayers[0] != _newestLayerToKeep)
		{
			int layerToClean = m_addedLayers[0];
			//Debug.LogErrorFormat("{0}: Cleanup layer {1}", Time.frameCount, layerToClean);
			m_playableGraph.Disconnect(m_animLayerMixer, layerToClean);
			m_layerUpdater.ClearManagedLayer(layerToClean);
			m_addedLayers.RemoveAt(0);
			m_layerBlendTimes.RemoveAt(0);
		}
	}

	void CleanupAllLayers()
	{
		m_addedLayers.Clear();
		m_layerBlendTimes.Clear();
		m_layerUpdater.ClearAllManagedLayers();
		for (int i=1; i<m_maxLayers; i++)
		{
			m_playableGraph.Disconnect(m_animLayerMixer, i);
		}
	}

	void OnDestroy()
	{
		StopFadeCoroutine();

		if (m_animOutput.IsOutputValid())
			m_animOutput.SetTarget(null);

		if (m_playableGraph.IsValid())
		{
			m_playableGraph.Stop();
			m_playableGraph.Evaluate(0f);
			m_playableGraph.Destroy();
		}
	}

	// RW-28-AUG-25: Since the AnimationOverrides get cleaned up when disabled, I think we need to clear up this side of things, too?
	void OnDisable()
	{
		StopFadeCoroutine();

		m_activeAnimLayer = 0;

		CleanupAllLayers();
	}
}

// We need to hook a ScriptableBehaviour into the graph so PrepareFrame gets called and this 
// can be used to update the individual layers so they can blend weights between their anims.
// That's the purpose of this.
public class AnimationLayerUpdater : PlayableBehaviour
{
	List<PlayQueuePlayable> m_managedLayers = new List<PlayQueuePlayable>();

	public void Setup(int _almmMaxLayers)
	{
		// Layers at the ALMM-level start with 0, which is the base pose from the AC.
		// We setup one fewer because that layer isn't managed by this system.
		for (int i=0; i<_almmMaxLayers-1; i++)
		{
			m_managedLayers.Add(null);
		}
	}
	
	public bool HasFreeSlots()
	{
		for (int i=0; i<m_managedLayers.Count; i++)
		{
			if (m_managedLayers[i] == null)
			{
				return true;
			}
		}
		return false;
	}

	public int AddManagedLayer(PlayQueuePlayable _pqp)
	{
		for (int i=0; i<m_managedLayers.Count; i++)
		{
			if (m_managedLayers[i] == null)
			{
				m_managedLayers[i] = _pqp	;
				// Layers at the ALMM-level start with 0, which is the base pose from the AC.
				// That layer isn't managed by this system, so we offset to account for this.
				return i+1;
			}
		}
		
		return -1;
	}

	public void ClearAllManagedLayers()
	{
		for (int i=m_managedLayers.Count-1; i>0; i--)
		{
			ClearManagedLayer(i);
		}
	}

	public void ClearManagedLayer(int _layerIdx)
	{
		// Layers at the ALMM-level start with 0, which is the base pose from the AC.
		// That layer isn't managed by this system, so we offset to account for this.
		m_managedLayers[_layerIdx-1]?.Stop();
		m_managedLayers[_layerIdx-1] = null;
	}

	public override void PrepareFrame(Playable owner, FrameData info)
	{
		for (int i=0; i < m_managedLayers.Count; i++)
		{
			m_managedLayers[i]?.UpdateLayer();
		}
	}
}
