using System;
using System.Collections.Generic;
using UnityEngine;

public class PhysicsAnimationBlend : MonoBehaviour
{
    private float m_physicsWeight = 1.0f;
    private float m_physicsWeightTarget = 1.0f;
    Dictionary<Transform, Transform> m_toAnimation = new();
    Rigidbody m_rootBody;
    void Awake()
    {
        var bodies = GetComponentsInChildren<Rigidbody>();
        m_toAnimation.Clear();
        var boneRoot = transform.FindChildRecursiveByName(GlobalData.c_avatarRootName);
        m_rootBody = bodies[0];
        foreach (var body in bodies)
            m_toAnimation[body.transform] = boneRoot.FindChildRecursiveByName(body.name);
    }
    
    public void SetPhysicsWeight(float weight)
    {
        m_physicsWeightTarget = weight;
    }

    void LateUpdate()
    {
        m_physicsWeight = Mathf.Lerp(m_physicsWeight, m_physicsWeightTarget, .25f);
        foreach (var kvp in m_toAnimation)
        {
            var phys = kvp.Key;
            var anim = kvp.Value;
            var weight = phys == m_rootBody.transform ? 1 : m_physicsWeight;
            anim.position = Vector3.Lerp(anim.position, phys.position, weight);
            anim.rotation = Quaternion.Slerp(anim.rotation, phys.rotation, weight);
        }
    }
}
