using UnityEngine;

public class AfterTouch : MonoBehaviour
{
    public float m_afterTouchEndTime;
    private float m_afterTouchTime = 0;
    private Vector3 m_afterTouchStartPos;
    private Rigidbody m_afterTouchBody;

    void Start()
    {
        m_afterTouchStartPos = Utility.InputPos;
        m_afterTouchTime = 0;
        m_afterTouchBody = GetComponentInChildren<Rigidbody>();
    }

    void Update()
    {
        m_afterTouchTime += Time.deltaTime;
        if (m_afterTouchTime >= m_afterTouchEndTime)
        {
            Destroy(this);
            return;
        }
        float scale = 1 - m_afterTouchTime / m_afterTouchEndTime;
        var pos = Utility.InputPos;
        var delta = (pos - m_afterTouchStartPos) * (10f / Screen.height);
        m_afterTouchStartPos = pos;
        
        var camXform = Camera.main.transform;
        delta *= scale;
        delta.x *= -1;
        m_afterTouchBody.angularVelocity += camXform.right * delta.y + camXform.up * delta.x;
    }
    
    public static void AddAfterTouch(GameObject _obj, float _endTime)
    {
        var afterTouch = _obj.AddComponent<AfterTouch>();
        afterTouch.m_afterTouchEndTime = _endTime;
    }
}
