using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using System.Collections;
using UnityEngine.Serialization;

[AddComponentMenu("UI/Auto Repeat Button")]
public class AutoRepeatButton : Button
{
    [Tooltip("Initial delay (in seconds) before auto-repeat starts.")]
    [SerializeField] private float m_initialDelay = 0.5f;
    
    [Tooltip("Time (in seconds) between consecutive clicks during auto-repeat.")]
    [SerializeField] private float m_repeatInterval = 0.1f;

    [Tooltip("End the click if the mouse leaves the control")]
    [SerializeField] private bool m_endClickOnLeave = false;
    
    private Coroutine m_repeatCoroutine;
    private bool m_isHeld;

    public override void OnPointerDown(PointerEventData eventData)
    {
        base.OnPointerDown(eventData);
        m_isHeld = true;
        StartAutoRepeat();
    }

    public override void OnPointerUp(PointerEventData eventData)
    {
        base.OnPointerUp(eventData);
        ResetAutoRepeat();
    }

    public override void OnPointerExit(PointerEventData eventData)
    {
        base.OnPointerExit(eventData);
        if (m_endClickOnLeave)
            ResetAutoRepeat();
    }

    private void StartAutoRepeat()
    {
        if (m_repeatCoroutine != null) StopCoroutine(m_repeatCoroutine);
        m_repeatCoroutine = StartCoroutine(AutoRepeatRoutine());
    }

    private void ResetAutoRepeat()
    {
        m_isHeld = false;
        if (m_repeatCoroutine != null)
        {
            StopCoroutine(m_repeatCoroutine);
            m_repeatCoroutine = null;
        }
    }

    private IEnumerator AutoRepeatRoutine()
    {
        yield return new WaitForSecondsRealtime(m_initialDelay);
        while (m_isHeld)
        {
            onClick.Invoke();
            yield return new WaitForSecondsRealtime(m_repeatInterval);
        }
    }
}
