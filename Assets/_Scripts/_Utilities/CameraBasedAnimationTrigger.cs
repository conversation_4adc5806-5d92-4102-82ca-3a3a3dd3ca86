using UnityEngine;

public class CameraBasedAnimationTrigger : MonoBehaviour
{
    public enum ETriggerType
    {
        Trigger,
        Override,
    }
    public float m_distanceToTrigger = 50;
    [Range(0.0f, 1.0f)] public float m_directionSensitivity = 0;
    public ETriggerType m_triggerType;
    public string m_animationName;
    public bool m_headTrackCamera = false;
    private Animator m_animator;
    private bool m_wasClose, m_wasDirected;
    void Start()
    {
        m_animator = GetComponentInChildren<Animator>();
    }

    void Update()
    {
        var cam = Camera.main.transform;
        if (m_headTrackCamera)
            GetComponentInChildren<HeadTracker>()?.Override(cam);
        if (m_animator == null) return;
        var toCamera = cam.position - transform.position;
        var isDirected = -Vector3.Dot(toCamera.normalized, cam.forward) > m_directionSensitivity;
        var isClose = toCamera.sqrMagnitude < m_distanceToTrigger * m_distanceToTrigger;
        if (isClose && isDirected && (!m_wasClose || !m_wasDirected))
        {
            switch (m_triggerType)
            {
                case ETriggerType.Trigger:
                    m_animator.SetTrigger(m_animationName);
                    break;
                case ETriggerType.Override:
                    AnimationOverride.PlayClip(m_animator.gameObject, m_animationName, _b => { });
                    break;
            }
        }
        m_wasClose = isClose;
        m_wasDirected = isDirected;
    }
}
