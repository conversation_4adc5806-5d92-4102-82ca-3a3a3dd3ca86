using UnityEngine;
using UnityEngine.Events;
using UnityEngine.EventSystems;

public class ClickToInteract : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointer<PERSON>lick<PERSON><PERSON><PERSON>, ICharacterObjectInteract
{
    public UnityEvent m_onInteract;

    public void OnPointerClick(PointerEventData eventData) { if (eventData.button == PointerEventData.InputButton.Left) Click(); }

    public void Click()
    {
#if UNITY_EDITOR
        UnityEditor.Selection.activeGameObject = gameObject;
#endif
        m_onInteract?.Invoke();
    }

    private bool m_interactDisabled = false;

    public string InteractType => null;
    public bool CanInteract(NGMovingObject _chr) => m_interactDisabled == false && GameManager.Me.GlobalInteractCheck(_chr) &&
                                                    NGMovingObject.c_defaultInteractRange * NGMovingObject.c_defaultInteractRange >= transform.position.DistanceXZSq(_chr.transform.position);
    public string GetInteractLabel(NGMovingObject _chr) => "Interact";
    public void DoInteract(NGMovingObject _chr) => Click();
    public bool RunInteractSequence(System.Collections.Generic.List<FollowChild> _chr) => InteractionSequenceNode.AttemptInteraction(_chr, gameObject);
    public void EnableInteractionTriggers(bool _b) => m_interactDisabled = !_b;
    public float AutoInteractTime => 0;
}
