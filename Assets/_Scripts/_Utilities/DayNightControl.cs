using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.EventSystems;

#if UNITY_EDITOR
using UnityEditor;
[CustomEditor(typeof(DayNightControl), true)]
public class DayNightControlEditor : Editor
{
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();
        var dnc = target as DayNightControl;
        if (dnc == null) return;
        if (dnc is DayNightControlWithInteract)
        {
            if (GUILayout.But<PERSON>("Convert to DayNightControl"))
                dnc.ConvertToNonInteract();
        }
        else
        {
            if (GUILayout.But<PERSON>("Convert to DayNightControlWithInteract"))
                dnc.ConvertToInteract();
        }
    }
}
#endif


public class DayNightControl : MonoBehaviour, IBatchPartitioner, IPointerClickHandler
{
    public bool m_onDuringNight = true;
    public bool m_offAroundMidnight = false;
    public float m_onTimeRandomness = 0;
    public GameObject[] m_controlledObjects;
    public GameObject[] m_reverseControlledObjects;
    public Animator[] m_controlledAnimators;
    public Animator[] m_reverseControlledAnimators;
    public string m_animatorBoolName;
    public bool m_onlyOnIfInhabited = true;
    public bool m_clickToToggle = false;
    public AkEventHolder m_clickToToggleAudioEvent;
    public AkEventHolder m_toggleAudioEvent;
    private long m_onTimeRandomnessLastDay = -1L;
    private float m_dayStartRnd, m_dayEndRnd, m_midnightStartRnd, m_midnightEndRnd;
    private Transform m_transform;
    private bool m_hardDisabled = false;
    private EOverrideState m_overrideState = EOverrideState.Auto;
    
    public enum EOverrideState { Auto, On, Off };

    private MABuilding m_parentBuilding;
    
    protected void CopyFrom(DayNightControl _copyFrom)
    {
        m_onDuringNight = _copyFrom.m_onDuringNight;
        m_offAroundMidnight = _copyFrom.m_offAroundMidnight;
        m_onTimeRandomness = _copyFrom.m_onTimeRandomness;
        m_controlledObjects = _copyFrom.m_controlledObjects;
        m_reverseControlledObjects = _copyFrom.m_reverseControlledObjects;
        m_controlledAnimators = _copyFrom.m_controlledAnimators;
        m_reverseControlledAnimators = _copyFrom.m_reverseControlledAnimators;
        m_animatorBoolName = _copyFrom.m_animatorBoolName;
        m_onlyOnIfInhabited = _copyFrom.m_onlyOnIfInhabited;
        m_clickToToggle = _copyFrom.m_clickToToggle;
        m_clickToToggleAudioEvent = _copyFrom.m_clickToToggleAudioEvent;
        m_toggleAudioEvent = _copyFrom.m_toggleAudioEvent;
    }

    public void ConvertToInteract()
    {
        var go = gameObject;
        var withInteract = go.AddComponent<DayNightControlWithInteract>();
        withInteract.CopyFrom(this);
        DestroyImmediate(this, true);
        DirtyObject(go);
    }

    public void ConvertToNonInteract()
    {
        var go = gameObject;
        var nonInteract = go.AddComponent<DayNightControl>();
        nonInteract.CopyFrom(this);
        DestroyImmediate(this, true);
        DirtyObject(go);
    }

    void DirtyObject(GameObject _obj)
    {
#if UNITY_EDITOR
        if (Application.isPlaying)
            return;
        EditorUtility.SetDirty(_obj);
        UnityEditor.SceneManagement.EditorSceneManager.MarkSceneDirty(_obj.scene);
#endif
    }


    void Start()
    {
        m_parentBuilding = GetComponentInParent<MABuilding>();
        m_transform = transform;
        Update();
    }

    private bool m_toggleState = false, m_toggleUntil = false, m_toggleUntilValid = false;

    public void OnPointerClick(PointerEventData eventData)
    {
        if (m_clickToToggle == false) return;
        if (eventData.button == PointerEventData.InputButton.Left)
            Click();
    }
    
    protected void Click()
    {
        m_toggleState = true;
        if (m_clickToToggleAudioEvent != null)
            m_clickToToggleAudioEvent?.Play(gameObject);
    }

    public void SetOverride(EOverrideState _state)
    {
        m_overrideState = _state;
    }

    private static DebugConsole.Command s_overridednc = new ("overridednc", _s => {
        var bits = _s.Split(' ');
        var id = int.Parse(bits[0]);
        var state = (EOverrideState)System.Enum.Parse(typeof(EOverrideState), bits[1]);
        var building = NGManager.Me.FindBuildingByID(id);
        SetStateOverride(building.gameObject, state);
    });

    public static void SetStateOverride(GameObject _go, EOverrideState _state)
    {
        foreach (var dnc in _go.GetComponentsInChildren<DayNightControl>())
            dnc.SetOverride(_state);
    }

    public void SetHardDisabled(bool _hardDisable)
    {
        m_hardDisabled = _hardDisable;
    }

    void Update()
    {
        bool isOn = false;
        var cts = DayNight.Me.m_timeStage;
        if (m_onTimeRandomnessLastDay != DayNight.Me.m_day)
        {
            m_onTimeRandomnessLastDay = DayNight.Me.m_day;
            m_dayStartRnd = DayNight.c_timeStageDayStart - .2f + Random.Range(-.2f, .2f) * m_onTimeRandomness;
            m_dayEndRnd = DayNight.c_timeStageDayEnd + .2f + Random.Range(-.2f, .2f) * m_onTimeRandomness;
            m_midnightStartRnd = DayNight.c_timeStageNightMid - .25f + Random.Range(-.1f, .2f) * m_onTimeRandomness;
            m_midnightEndRnd = DayNight.c_timeStageNightMid + .25f + Random.Range(-.2f, .1f) * m_onTimeRandomness;
        }
        if (m_onDuringNight && m_offAroundMidnight)
            isOn = cts >= m_dayEndRnd || cts < m_midnightStartRnd || (cts > m_midnightEndRnd && cts < m_dayStartRnd); 
        else if (m_onDuringNight)
            isOn = cts >= m_dayEndRnd || cts < m_dayStartRnd;
        else
            isOn = cts >= m_dayStartRnd && cts < m_dayEndRnd;
        
        bool canBeOn = true;
        if (m_overrideState != EOverrideState.Auto)
        {
            isOn = canBeOn = m_overrideState == EOverrideState.On;
        }
        else if(isOn)
        {
            if (m_onlyOnIfInhabited)
                canBeOn = m_parentBuilding != null && m_parentBuilding.IsInhabited();
            canBeOn &= m_transform.IsChildOf(DesignHarness.Me.transform) == false;   
        }
        
        bool on = canBeOn && isOn;
        if (m_toggleState)
        {
            m_toggleState = false;
            if (m_toggleUntilValid)
                m_toggleUntilValid = false;
            else
            {
                m_toggleUntil = !on;
                m_toggleUntilValid = true;
            }
        }
        if (m_toggleUntilValid)
        {
            if (on == m_toggleUntil)
                m_toggleUntilValid = false;
            else
                on = !on;
        }
        bool normalOn = on && m_hardDisabled == false;
        bool reverseOn = on == false || m_hardDisabled == true;
        bool changed = false;
        if (m_controlledObjects != null)
            foreach (var go in m_controlledObjects)
                if (go != null && go.activeSelf != normalOn)
                {
                    go.SetActive(normalOn);
                    changed = true;
                }
        if (m_reverseControlledObjects != null)
            foreach (var go in m_reverseControlledObjects)
                if (go != null && go.activeSelf != reverseOn)
                {
                    go.SetActive(reverseOn);
                    changed = true;
                }
        if (m_controlledAnimators != null)
            foreach (var anim in m_controlledAnimators)
                if (anim != null && anim.GetBool(m_animatorBoolName) != normalOn)
                {
                    anim.SetBool(m_animatorBoolName, normalOn);
                    changed = true;
                }
        if (m_reverseControlledAnimators != null)
            foreach (var anim in m_reverseControlledAnimators)
                if (anim != null && anim.GetBool(m_animatorBoolName) != reverseOn)
                {
                    anim.SetBool(m_animatorBoolName, reverseOn);
                    changed = true;
                }
        if (changed && m_toggleAudioEvent != null)
            m_toggleAudioEvent?.Play(gameObject);
    }
    
    public bool IsReady => DayNight.Me.m_timeStage > 0;
    
    public Component Component() => this;
    public List<List<Transform>> GetExcludedTransforms()
    {
        var result = new List<List<Transform>>
        {
            new List<GameObject>(m_controlledObjects.FindAll(x => x != null)).ConvertAll(x => x.transform),
            new List<GameObject>(m_reverseControlledObjects.FindAll(x => x != null)).ConvertAll(x => x.transform)
        };
        return result;
    }

    public void OnBatch(Dictionary<MeshRenderer, MeshRenderer> _replaced)
    {
        HashSet<GameObject> newControlled = new HashSet<GameObject>();
        HashSet<GameObject> newReverseControlled = new HashSet<GameObject>();
        foreach (var (original, combined) in _replaced)
        {
            if (!m_controlledObjects.IsNullOrEmpty())
            {
                foreach (var go in m_controlledObjects)
                {
                    if (go == null)
                        continue;
                    newControlled.Add(go);
                    if (original.transform.IsChildOf(go.transform))
                    {
                        newControlled.Add(combined.gameObject);
                        break;
                    }
                }
            }
            
            if (!m_reverseControlledObjects.IsNullOrEmpty())
            {
                foreach (var go in m_reverseControlledObjects)
                {
                    if (go == null)
                        continue;
                    newReverseControlled.Add(go);
                    if (original.transform.IsChildOf(go.transform))
                    {
                        newReverseControlled.Add(combined.gameObject);
                        break;
                    }
                }
            }
        }
        m_controlledObjects = newControlled.ToArray();
        m_reverseControlledObjects = newReverseControlled.ToArray();
    }
}
