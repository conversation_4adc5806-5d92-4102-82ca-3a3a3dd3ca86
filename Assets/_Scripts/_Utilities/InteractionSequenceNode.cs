using System.Collections.Generic;
using UnityEngine;

public abstract class InteractionSequenceNode : MonoBehaviour
{
    protected bool m_isController;
    private List<InteractionSequenceNode> m_nodes;
    protected InteractionSequenceNode m_root;
    
    private int m_nodeIndex;
    private bool m_finish;
    
    protected List<MACharacterBase> m_characters;
    protected ICharacterObjectInteract m_interact;
    
    protected List<List<InteractionSequenceNode_Navigate>> m_queuedNavNodes = new();
    protected List<InteractionSequenceNode> m_pendingNodes = new();

    public void Activate(List<MACharacterBase> _characters, ICharacterObjectInteract _interact)
    {
        m_characters = _characters;
        m_interact = _interact;
        transform.parent.gameObject.SetActive(true);
    }
    
    void OnEnable()
    {
        m_finish = false;
        m_queuedNavNodes.Clear();
        m_pendingNodes.Clear();
        m_isController = transform.GetSiblingIndex() == 0;
        if (m_isController == false)
        {
            gameObject.SetActive(false);
            return;
        }
        m_interact.EnableInteractionTriggers(false);
        m_nodes = new();
        for (int i = 0; i < transform.parent.childCount; ++i)
        {
            var node = transform.parent.GetChild(i).GetComponent<InteractionSequenceNode>();
            if (node != null)
                m_nodes.Add(node);
        }
        for (int i = 0; i < m_nodes.Count; ++i) m_nodes[i].m_root = m_nodes[0];
        
        m_nodeIndex = 0;
        m_nodes[0].Begin();

        SetCharactersBeingControlled(true);
    }
    
    void SetCharactersBeingControlled(bool _controlled)
    {
        foreach (var chr in m_characters)
        {
            if (chr == null) continue;
            chr.SetFollowBeingControlled(_controlled);
        }
    }

    void OnDisable()
    {
        if (m_isController)
        {
            m_interact.EnableInteractionTriggers(true);
            SetCharactersBeingControlled(false);
        }
    }

    public void QueueNavNode(int _index, InteractionSequenceNode_Navigate _node)
    {
        while (m_queuedNavNodes.Count <= _index)
            m_queuedNavNodes.Add(new ());
        m_queuedNavNodes[_index].Add(_node);
    }
    
    public bool HasNavQueue(int _index)
    {
        if (_index < 0 || _index >= m_queuedNavNodes.Count) return false;
        return m_queuedNavNodes[_index].Count > 0;
    }
    
    public void AddPending(InteractionSequenceNode _node)
    {
        m_pendingNodes.Add(_node);
    }
    
    void Update()
    {
        if (m_finish)
        {
            if (m_pendingNodes.Count > 0)
            {
                if (m_pendingNodes[0].IsFinished) m_pendingNodes.RemoveAt(0);
            }
            else
                transform.parent.gameObject.SetActive(false);
            return;
        }
        var node = m_nodes[m_nodeIndex];
        if (node.Tick())
        {
            ++m_nodeIndex;
            if (m_nodeIndex >= m_nodes.Count)
                m_finish = true;
            else
                SetAndBegin();
        }
        for (int i = 0; i < m_queuedNavNodes.Count; ++i)
        {
            if (m_queuedNavNodes[i].Count == 0) continue;
            if (m_queuedNavNodes[i][0].HasStarted == false)
                m_queuedNavNodes[i][0].BeginFromQueue();
            else if (m_queuedNavNodes[i][0].HasEnded)
                m_queuedNavNodes[i].RemoveAt(0);
        }
    }

    void SetAndBegin()
    {
        m_nodes[m_nodeIndex].m_characters = m_characters;
        m_nodes[m_nodeIndex].m_interact = m_interact;
        m_nodes[m_nodeIndex].Begin();
    }

    protected abstract void Begin();
    
    protected virtual void BeginFromQueue() { }

    protected abstract bool Tick();
    
    protected virtual bool IsFinished => true; 
    
    protected abstract int CharacterIndex();
    protected abstract bool CharacterIndexRequired();
    
    public static (int, int) GetMaxChildIndex(InteractionSequenceNode _node)
    {
        if (_node == null) return (-1, -1);
        int maxIndex = -1, maxRequiredIndex = -1;
        var parent = _node.transform.parent;
        for (int i = 0; i < parent.childCount; ++i)
        {
            var child = parent.GetChild(i).GetComponent<InteractionSequenceNode>();
            if (child == null) continue;
            var chrIndex = child.CharacterIndex();
            maxIndex = Mathf.Max(maxIndex, chrIndex);
            if (child.CharacterIndexRequired()) maxRequiredIndex = Mathf.Max(maxRequiredIndex, chrIndex);
        }
        return (maxIndex, maxRequiredIndex);
    }
    public static InteractionSequenceNode PickRandomNode(List<FollowChild> _characters, GameObject _interact)
    {
        if (_characters == null || _characters.Count == 0) return null;
        var chrCount = _characters.Count;
        var nodes = _interact.GetComponentsInChildren<InteractionSequenceNode>(true);
        var validNodes = new List<InteractionSequenceNode>();
        var bestMaxIndex = -1;
        foreach (var node in nodes)
        {
            if (node.transform.GetSiblingIndex() != 0) continue;
            var (_, requiredIndex) = GetMaxChildIndex(node);
            if (requiredIndex + 1 > chrCount) continue;
            if (requiredIndex > bestMaxIndex)
            {
                bestMaxIndex = requiredIndex;
                validNodes.Clear();
            }
            validNodes.Add(node);
        }
        return validNodes.PickRandom();
    }
    public static bool AttemptInteraction(List<FollowChild> _characters, GameObject _interact)
    {
        var node = PickRandomNode(_characters, _interact);
        if (node == null) return false;
        var interact = _interact.GetComponent<ICharacterObjectInteract>();
        var (maxIndex, _) = GetMaxChildIndex(node);
        var chrCount = Mathf.Min(maxIndex + 1, _characters.Count);
        var chrs = new List<MACharacterBase>(chrCount);
        for (int i = 0; i < chrCount; ++i) chrs.Add(_characters[i].GetComponent<MACharacterBase>());
        node.Activate(chrs, interact);
        return true;
    }
}
