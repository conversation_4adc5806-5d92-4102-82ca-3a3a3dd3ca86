using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class ScanField : Attribute
{

}

public class InfoField : Attribute
{
    
}

public static class JsonHelper
{
    public static T[] FromJson<T>(string json, bool _gotRecords = false)
    {
        if(_gotRecords == false)
            json = $"{{ \"records\": {json} }}"; // 5/24/22 scast<PERSON> added

        try
        {
            Wrapper<T> wrapper = JsonUtility.FromJson<Wrapper<T>>(json);
            return wrapper.records;
        }
        catch (Exception ex)
        {
            Debug.LogErrorFormat("Exception ({0}) while parsing JSON from string: {1}", ex.Message, json);
            throw;
        }
    }

    public static string ToJson<T>(T[] array)
    {
        Wrapper<T> wrapper = new Wrapper<T>();
        wrapper.records = array;
        return JsonUtility.ToJson(wrapper);
    }

    public static string ToJson<T>(T[] array, bool prettyPrint)
    {
        Wrapper<T> wrapper = new Wrapper<T>();
        wrapper.records = array;
        return JsonUtility.ToJson(wrapper, prettyPrint);
    }

    public static T[] FromJsonTypeSafe<T>(string json)
    {
        try
        {
            var wrapper = JsonUtility.FromJson<SRWrapper<T>>(json);
            return wrapper.records;
        }
        catch (Exception ex)
        {
            Debug.LogErrorFormat("Exception ({0}) while parsing JSON from string: {1}", ex.Message, json);
            throw;
        }
    }

    public static string ToJsonTypeSafe<T>(T[] array, bool prettyPrint = false)
    {
        var wrapper = new SRWrapper<T>();
        wrapper.records = array;
        return JsonUtility.ToJson(wrapper, prettyPrint);
    }    

    [System.Serializable]
    private class Wrapper<T>
    {
        public T[] records;
    }
    
    [System.Serializable]
    private class SRWrapper<T>
    {
        [SerializeReference] public T[] records;
    }
}
