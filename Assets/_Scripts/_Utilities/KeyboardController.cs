using UnityEngine;

public enum EKeyboardFunction
{
    None,
    CAT_CameraControl,
    MoveCamera,
    RotateCamera,
    RotateCameraByMouse,
    DollyCamera,
    JumpToOakridge,
    ShowMap,
    CAT_Misc,
    Settings,
    Cancel,
    Skip,
    SkipDialogue,
    GestureMenu,
    HandPowerMenu,
    CAT_Design,
    DesignMode,
    LeaveFocus,
    RaiseLowerDesignCamera,
    CAT_Worker,
    AssignJob,
    AssignHome,
    CAT_Hero,
    SetPatrolZone,
    IncPatrolZone,
    DecPatrolZone,
    CAT_Possession,
    Possess,
    PossessRun,
    PossessInfo,
    UnfollowAll,
    PossessTransfer,
    ResetRotation,
    LevelUp,
};

public class KeyboardController : Mono<PERSON>ingleton<KeyboardController>
{
    public class KeyAssignEntry
    {
        public enum EInputType { None, Click, Hold, ClickOrHold };
        public enum EInputContext { General, Drag, Possess, Design };
        public string m_label;
        public KeyCode[] m_keys;
        public KeyCode[] m_defaults;
        public string[] m_keyLabels;
        public EKeyboardFunction[] m_canConflictWith;
        public bool m_canClearFirst = true;
        public EInputType m_inputType = EInputType.Hold;
        public EInputContext m_inputContext = EInputContext.General;

        public void SetKey(int _index, KeyCode _key)
        {
            m_keys[_index] = _key;
            MPlayerPrefs.SetInt(m_label + _index, (int) _key);
        }
        public void ClearKey(int _index) => SetKey(_index, KeyCode.None);
        public KeyCode Load(int _index) => (KeyCode)MPlayerPrefs.GetInt(m_label + _index, (int) m_defaults[_index]);

        public string GetString(int _index, bool _colourIfConflicted = false)
        {
            var str = KeyboardShortcutManager.Me.GetKeyString(m_keys[_index]);
            if (_colourIfConflicted && Me.IsConflicted(this, _index))
                str = $"<color=#c00000>{str}</color>";
            return str;
        }
    }

    public KeyAssignEntry[] m_keyAssignRows =
    {
        new(),
        new() {m_label = "Camera Control" },
        new() {m_label = "Move Camera", m_defaults = new[] {KeyCode.W, KeyCode.A, KeyCode.S, KeyCode.D}, m_keyLabels = new[] {"Up", "Left", "Down", "Right"}},
        new() {m_label = "Rotate Camera", m_defaults = new[] {KeyCode.Q, KeyCode.E}, m_keyLabels = new[] {"Left", "Right"}},
        new() {m_label = "Rotate Camera (mouse)", m_defaults = new[] {KeyCode.C, KeyCode.Mouse2}},
        new() {m_label = "Dolly Camera", m_defaults = new[] {KeyCode.Z, KeyCode.X}, m_keyLabels = new[] {"In", "Out"}},
        new() {m_label = "Jump to Oakridge", m_defaults = new[] {KeyCode.Space, KeyCode.Mouse2}, m_inputType = KeyAssignEntry.EInputType.Click},
        new() {m_label = "World Map", m_defaults = new[] {KeyCode.M, KeyCode.None}, m_inputType = KeyAssignEntry.EInputType.Click},
        new() {m_label = "Miscellaneous" },
        new() {m_label = "Settings", m_defaults = new[] {KeyCode.Escape}, m_canConflictWith = new[] {EKeyboardFunction.Cancel}, m_canClearFirst = false, m_inputType = KeyAssignEntry.EInputType.Click},
        new() {m_label = "Cancel", m_defaults = new[] {KeyCode.Escape}, m_canConflictWith = new[] {EKeyboardFunction.Settings}, m_inputType = KeyAssignEntry.EInputType.Click},
        new() {m_label = "Skip", m_defaults = new[] {KeyCode.Escape}, m_inputType = KeyAssignEntry.EInputType.Hold},
        new() {m_label = "Skip Dialogue", m_defaults = new[] {KeyCode.Tab}, m_inputType = KeyAssignEntry.EInputType.Click},
        new() {m_label = "Gesture Menu", m_defaults = new[] {KeyCode.G}, m_inputType = KeyAssignEntry.EInputType.ClickOrHold},
        new() {m_label = "Hand Power Menu", m_defaults = new[] {KeyCode.H}, m_inputType = KeyAssignEntry.EInputType.ClickOrHold},
        new() {m_label = "Design"},
        new() {m_label = "Design Mode", m_defaults = new[] {KeyCode.Period}, m_inputContext = KeyAssignEntry.EInputContext.Design},
        new() {m_label = "Leave Focus", m_defaults = new[] {KeyCode.Escape}, m_inputType = KeyAssignEntry.EInputType.Click, m_inputContext = KeyAssignEntry.EInputContext.Design},
        new() {m_label = "Raise Camera", m_defaults = new[] {KeyCode.R, KeyCode.F}, m_keyLabels = new[] {"Raise", "Lower"}, m_inputContext = KeyAssignEntry.EInputContext.Design},
        new() {m_label = "Held Worker"},
        new() {m_label = "Assign Job", m_defaults = new[] {KeyCode.J}, m_inputType = KeyAssignEntry.EInputType.Hold, m_inputContext = KeyAssignEntry.EInputContext.Drag},
        new() {m_label = "Assign Home", m_defaults = new[] {KeyCode.H}, m_inputType = KeyAssignEntry.EInputType.Hold, m_inputContext = KeyAssignEntry.EInputContext.Drag},
        new() {m_label = "Held Hero"},
        new() {m_label = "Set patrol zone", m_defaults = new[] {KeyCode.P}, m_inputType = KeyAssignEntry.EInputType.Click, m_inputContext = KeyAssignEntry.EInputContext.Drag},
        new() {m_label = "Increase patrol zone", m_defaults = new[] {KeyCode.Plus, KeyCode.KeypadPlus, KeyCode.Equals}, m_inputType = KeyAssignEntry.EInputType.Hold, m_inputContext = KeyAssignEntry.EInputContext.Drag},
        new() {m_label = "Decrease patrol zone", m_defaults = new[] {KeyCode.Minus, KeyCode.KeypadMinus}, m_inputType = KeyAssignEntry.EInputType.Hold, m_inputContext = KeyAssignEntry.EInputContext.Drag},
        new() {m_label = "Possession"},
        new() {m_label = "Possess", m_defaults = new[] {KeyCode.P}, m_inputType = KeyAssignEntry.EInputType.Click, m_inputContext = KeyAssignEntry.EInputContext.Possess},
        new() {m_label = "Run", m_defaults = new[] {KeyCode.LeftShift, KeyCode.RightShift}, m_inputType = KeyAssignEntry.EInputType.Hold, m_inputContext = KeyAssignEntry.EInputContext.Possess},
        new() {m_label = "Info", m_defaults = new[] {KeyCode.I}, m_inputType = KeyAssignEntry.EInputType.Click, m_inputContext = KeyAssignEntry.EInputContext.Possess},
        new() {m_label = "Untag All", m_defaults = new[] {KeyCode.U}, m_inputType = KeyAssignEntry.EInputType.Click, m_inputContext = KeyAssignEntry.EInputContext.Possess},
        new() {m_label = "Possess Other", m_defaults = new[] {KeyCode.T}, m_inputType = KeyAssignEntry.EInputType.Click, m_inputContext = KeyAssignEntry.EInputContext.Possess},
        new() {m_label = "Reset Rotation", m_defaults = new[] {KeyCode.Backspace}, m_inputType = KeyAssignEntry.EInputType.Click, m_inputContext = KeyAssignEntry.EInputContext.Possess},
        new() {m_label = "Level Up", m_defaults = new[] {KeyCode.L}, m_inputType = KeyAssignEntry.EInputType.Click, m_inputContext = KeyAssignEntry.EInputContext.Possess},
    };

    public KeyCode[] GetKeys(EKeyboardFunction _key) => m_keyAssignRows[(int) _key].m_keys;
    public KeyCode GetKey(EKeyboardFunction _key, int _subKey = 0) => GetKeys(_key)[_subKey];

    public static bool GetKeyDown(EKeyboardFunction _key, int _subKey = 0)
    {
#if UNITY_EDITOR
        var type = Me.m_keyAssignRows[(int) _key].m_inputType;
        if (type != KeyAssignEntry.EInputType.Hold && type != KeyAssignEntry.EInputType.ClickOrHold)
            Utility.ShowErrorOnce($"KeyboardController: GetKeyDown called on invalid key {_key} of type {type} - use AnyClicked() instead");
#endif
        return Utility.GetKeyDown(Me.GetKey(_key, _subKey));
    }

    public static bool GetKeyHeld(EKeyboardFunction _key, int _subKey = 0)
    {
#if UNITY_EDITOR
        var type = Me.m_keyAssignRows[(int) _key].m_inputType;
        if (type != KeyAssignEntry.EInputType.Hold && type != KeyAssignEntry.EInputType.ClickOrHold)
            Utility.ShowErrorOnce($"KeyboardController: GetKeyHeld called on invalid key {_key} of type {type} - use IsClicked() instead");
#endif
        return Utility.GetKey(Me.GetKey(_key, _subKey));
    }

    private System.Collections.Generic.Dictionary<KeyCode, Vector4> m_keyDownPositions = new();

    private KeyCode m_lastCompleteKeyClickCheck = KeyCode.None;
    private int m_lastCompleteKeyClickCheckFrame = 0;
    private bool TrackKeyClick(KeyCode _key)
    {
        if (Utility.GetKeyDown(_key))
        {
            var v = (Vector4)Input.mousePosition;
            v.z = Time.unscaledTime;
            v.w = Time.frameCount;
            m_keyDownPositions[_key] = v;
        }
        else if (Utility.GetKeyUp(_key))
        {
            if (m_keyDownPositions.TryGetValue(_key, out var downPos))
            {
                m_keyDownPositions.Remove(_key);
                var threshold = Screen.height * .02f;
                var res = (Time.frameCount < (int)downPos.w + 2 || downPos.z.Nearly(Time.unscaledTime, .3f)) && Vector2.Distance(downPos, Input.mousePosition) < threshold;
                if (res)
                {
                    // in case we have multiple functions checking for a click on the same key
                    m_lastCompleteKeyClickCheck = _key;
                    m_lastCompleteKeyClickCheckFrame = Time.frameCount;
                }
                return res;
            }
            if (m_lastCompleteKeyClickCheck == _key && m_lastCompleteKeyClickCheckFrame == Time.frameCount)
                return true;
        }
        return false;
    }

    public static bool GetKeyClicked(EKeyboardFunction _key, int _subKey = 0)
    {
#if UNITY_EDITOR
        var type = Me.m_keyAssignRows[(int)_key].m_inputType;
        if (type != KeyAssignEntry.EInputType.Click && type != KeyAssignEntry.EInputType.ClickOrHold)
            Utility.ShowErrorOnce($"KeyboardController: GetKeyClicked called on invalid key {_key} of type {type} - use Is/AnyDown/Held() instead");
#endif
        return Me.TrackKeyClick(Me.GetKey(_key, _subKey));
    }

    public static bool AnyKeyDown(EKeyboardFunction _key)
    {
#if UNITY_EDITOR
        var type = Me.m_keyAssignRows[(int) _key].m_inputType;
        if (type != KeyAssignEntry.EInputType.Hold && type != KeyAssignEntry.EInputType.ClickOrHold)
            Utility.ShowErrorOnce($"KeyboardController: AnyKeyDown called on invalid key {_key} of type {type} - use AnyClicked() instead");
#endif
        foreach (var thisKey in Me.GetKeys(_key))
            if (Utility.GetKeyDown(thisKey))
                return true;
        return false;
    }

    public static bool AnyKeyHeld(EKeyboardFunction _key)
    {
#if UNITY_EDITOR
        var type = Me.m_keyAssignRows[(int) _key].m_inputType;
        if (type != KeyAssignEntry.EInputType.Hold && type != KeyAssignEntry.EInputType.ClickOrHold)
            Utility.ShowErrorOnce($"KeyboardController: AnyKeyHeld called on invalid key {_key} of type {type} - use AnyClicked() instead");
#endif
        foreach (var thisKey in Me.GetKeys(_key))
            if (Utility.GetKey(thisKey))
               return true;
        return false;
    }
    
    public static bool AnyKeyClicked(EKeyboardFunction _key)
    {
        foreach (var thisKey in Me.GetKeys(_key))
            if (Me.TrackKeyClick(thisKey))
                return true;
        return false;
    }

    public static KeyCode GetKeyClicked(EKeyboardFunction _key)
    {
        foreach (var thisKey in Me.GetKeys(_key))
            if (Me.TrackKeyClick(thisKey))
                return thisKey;
        return KeyCode.None;
    }

    void Start()
    {
        Load();
    }

    void Load()
    {
        for (int i = 0; i < m_keyAssignRows.Length; ++i)
        {
            int numKeys = m_keyAssignRows[i].m_defaults?.Length ?? 0;
            m_keyAssignRows[i].m_keys = new KeyCode[numKeys];
            for (int j = 0; j < numKeys; ++j)
                m_keyAssignRows[i].m_keys[j] = m_keyAssignRows[i].Load(j);
        }
    }

    public void ResetToDefaults()
    {
        for (int i = 0; i < m_keyAssignRows.Length; ++i)
        {
            int numKeys = m_keyAssignRows[i].m_defaults?.Length ?? 0;
            for (int j = 0; j < numKeys; ++j)
                m_keyAssignRows[i].SetKey(j, m_keyAssignRows[i].m_defaults[j]);
        }
    }

    private static bool ConflictAllowed(KeyAssignEntry.EInputType _type1, KeyAssignEntry.EInputType _type2)
    {
        int t1 = (int)_type1, t2 = (int)_type2;
        if ((t1 & t2) == 0) return true; // no overlap of minor types
        return false;
    }
    private static bool ConflictAllowed(KeyAssignEntry.EInputContext _context1, KeyAssignEntry.EInputContext _context2) => _context1 != _context2;
    private static bool ConflictAllowed(KeyAssignEntry _entry1, KeyAssignEntry _entry2)
    {
        if (ConflictAllowed(_entry1.m_inputContext, _entry2.m_inputContext)) return true;
        if (ConflictAllowed(_entry1.m_inputType, _entry2.m_inputType)) return true;
        return false;
    }
    private bool ConflictAllowed(KeyAssignEntry _entry, EKeyboardFunction _function)
    {
        if (ConflictAllowed(_entry, m_keyAssignRows[(int)_function]))
            return true;
        if (_entry.m_canConflictWith != null && _entry.m_canConflictWith.Contains(_function))
            return true;
        return false;
    }
    public bool IsConflicted(KeyAssignEntry _entry, int _index)
    {
        for (int i = 0; i < m_keyAssignRows.Length; ++i)
        {
            var check = m_keyAssignRows[i];
            if (ConflictAllowed(_entry, (EKeyboardFunction)i)) continue;
            for (int j = 0; j < m_keyAssignRows[i].m_keys.Length; ++j)
            {
                if (check == _entry && _index == j) continue;
                if (_entry.m_keys[_index] == check.m_keys[j])
                    return true;
            }
        }
        return false;
    }
}
