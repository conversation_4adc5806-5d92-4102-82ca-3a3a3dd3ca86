using System.Collections;
using System.Collections.Generic;
using System.Numerics;
using UnityEngine;
using UnityEngine.UI;
using Vector3 = UnityEngine.Vector3;

public class OnBoardingManager : MonoSingleton<OnBoardingManager>
{
    public GameObject m_vcOffice;

    public GameObject m_stageLeft;
    public GameObject m_stageRight;

    public float m_pushZ = 6.0f;
    public float m_pushY = -2.5f;

    public bool m_isActive = true;
    public int CurrentTutorial() { return 0; }

    public void LoadVCOffice() 
    {
        if (GameManager.IsVisitingInProgress) return;

        if (!m_vcOffice.activeSelf)
        {
            m_vcOffice.SetActive(true);
            PlayAmbientSounds(true);
            Application.runInBackground = false;
        }
    }
    public void LoadVCOptions() { }
    public void CloseVCOffice() 
    {
        if (m_vcOffice.activeSelf)//if (m_vcOffice != null)
        {
            m_vcOffice.SetActive(false);//Destroy(m_vcOffice); m_vcOffice = null;
            Application.runInBackground = NGPlayer.Me.m_runInBackground;
            PlayAmbientSounds(false);
            Resources.UnloadUnusedAssets();
        }
    }
    
    void LateUpdate()
    {
	    float aspectRatioFree = (float)Screen.width / (float)Screen.height;
	    float aspectRatio = Mathf.Min(2f, aspectRatioFree);
        if (Camera.main != null)
        {
            var camPos = Camera.main.transform.position;
            var camFwd = Camera.main.transform.forward;
            var camUp = Camera.main.transform.up;

            var holder = transform;
            var holderPos = camPos + camFwd * (m_pushZ / aspectRatio) + camUp * (m_pushY / aspectRatio); // shrink with width rather than height
            holder.position = holderPos;
            holder.LookAt(holderPos + camFwd, camUp);
        }
    }

    private void PlayAmbientSounds(bool _play)
    {
    }
}
