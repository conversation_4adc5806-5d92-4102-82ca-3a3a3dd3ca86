using UnityEngine;
using UnityEngine.Profiling;
using Unity.Profiling;

public class RuntimeStatsCollector : MonoBehaviour
{
    // <PERSON><PERSON> counters
    private ProfilerRecorder m_gpuMS;
    private ProfilerRecorder m_drawCalls;
    private ProfilerRecorder m_batches;
    private ProfilerRecorder m_setPass;
    private ProfilerRecorder m_triangles;
    private ProfilerRecorder m_vertices;
    private ProfilerRecorder m_shadowDraws;
    private ProfilerRecorder m_animUpdate, m_animUpdate2, m_animUpdate3;
    private ProfilerRecorder m_physicsUpdate;
    private ProfilerRecorder m_scriptUpdate, m_scriptFixedUpdate, m_scriptLateUpdate;
    private ProfilerRecorder m_canvasUpdate;
    
    private string m_statsOutput = "";
    public string Stats => m_statsOutput;

    void OnEnable()
    {
        m_gpuMS = ProfilerRecorder.StartNew(ProfilerCategory.Render, "GPU Frame Time", 3);
        m_drawCalls = ProfilerRecorder.StartNew(ProfilerCategory.Render, "Draw Calls Count");
        m_batches   = ProfilerRecorder.StartNew(ProfilerCategory.Render, "Batches Count");
        m_setPass   = ProfilerRecorder.StartNew(ProfilerCategory.Render, "SetPass Calls Count");
        m_triangles = ProfilerRecorder.StartNew(ProfilerCategory.Render, "Triangles Count");
        m_vertices  = ProfilerRecorder.StartNew(ProfilerCategory.Render, "Vertices Count");
        m_shadowDraws = ProfilerRecorder.StartNew(ProfilerCategory.Render, "Shadow Casters Count");
        m_animUpdate = ProfilerRecorder.StartNew(ProfilerCategory.Animation, "PreLateUpdate.DirectorUpdateAnimationBegin");
        m_animUpdate2 = ProfilerRecorder.StartNew(ProfilerCategory.Animation, "Update.DirectorUpdate");
        m_animUpdate3 = ProfilerRecorder.StartNew(ProfilerCategory.Animation, "PreLateUpdate.DirectorUpdateAnimationEnd");
        m_physicsUpdate = ProfilerRecorder.StartNew(ProfilerCategory.Animation, "FixedUpdate.PhysicsFixedUpdate");
        m_scriptUpdate = ProfilerRecorder.StartNew(ProfilerCategory.Scripts, "Update.ScriptRunBehaviourUpdate");
        m_scriptFixedUpdate = ProfilerRecorder.StartNew(ProfilerCategory.Scripts, "FixedUpdate.ScriptRunBehaviourFixedUpdate");
        m_scriptLateUpdate = ProfilerRecorder.StartNew(ProfilerCategory.Scripts, "PreLateUpdate.ScriptRunBehaviourLateUpdate");
        m_canvasUpdate = ProfilerRecorder.StartNew(ProfilerCategory.Gui, "PostLateUpdate.PlayerUpdateCanvases");
    }

    void OnDisable()
    {
        m_gpuMS.Dispose();
        m_drawCalls.Dispose();
        m_batches.Dispose();
        m_setPass.Dispose();
        m_triangles.Dispose();
        m_vertices.Dispose();
        m_shadowDraws.Dispose();
        m_animUpdate.Dispose(); m_animUpdate2.Dispose(); m_animUpdate3.Dispose();
        m_physicsUpdate.Dispose();
        m_scriptUpdate.Dispose(); m_scriptFixedUpdate.Dispose(); m_scriptLateUpdate.Dispose();
        m_canvasUpdate.Dispose();
    }

    private Animator[] m_animators;
    private float m_timeToNextAnimatorsGrab = 0f;
    
    static bool IsAnimatorEffectivelyPlaying(Animator a, float layerWeightEps = 0.001f)
    {
        // Basic guards
        if (a == null || !a.isActiveAndEnabled || a.runtimeAnimatorController == null)
            return false;

        // Timescale pause
        if (a.updateMode == AnimatorUpdateMode.Normal && Time.timeScale == 0f)
            return false;

        // Global speed pause
        if (Mathf.Approximately(a.speed, 0f))
            return false;

        // At least one layer contributing, with positive effective speed and not finished
        int lc = a.layerCount;
        bool anyLayerContributing = false;
        for (int layer = 0; layer < lc && anyLayerContributing == false; layer++)
        {
            float lw = a.GetLayerWeight(layer);
            if (lw <= layerWeightEps) continue;

            // If transitioning, that’s “playing” (blend is advancing)
            if (a.IsInTransition(layer))
            {
                // Some projects prefer to also check a.speed > 0 which we already did
                anyLayerContributing = true;
            }

            var st = a.GetCurrentAnimatorStateInfo(layer);

            // Effective speed: animator.speed * state.speed (Unity exposes state speed)
            // If it’s ~0, it’s not progressing.
            // Note: state.speedMultiplier is already folded into state.speed.
            float effSpeed = a.speed * st.speed;
            if (Mathf.Abs(effSpeed) <= 1e-4f) continue;

            bool looping = st.loop;
            bool stillRunning = looping || (st.normalizedTime < 1f);
            if (stillRunning) anyLayerContributing = true;
        }
        //if (anyLayerContributing == false) return false;

        // Culling: when not AlwaysAnimate, skip if nothing visible (common Stats behavior)
        if (a.cullingMode != AnimatorCullingMode.AlwaysAnimate)
        {
            bool anyVisible = false;
            // Check renderers in the animated hierarchy
            // (cost is tiny if you cache these per animator once)
            if (s_animatorRendererCache.TryGetValue(a, out var rends) == false)
                s_animatorRendererCache[a] = rends = a.GetComponentsInChildren<Renderer>(true);
            for (int i = 0; i < rends.Length; i++)
            {
                if (rends[i] != null && rends[i].isVisible)
                {
                    anyVisible = true;
                    break;
                }
            }
            if (!anyVisible) return false;
        }

        return true;
    }
    static System.Collections.Generic.Dictionary<Animator, Renderer[]> s_animatorRendererCache = new(); 

    public int GetAnimatorsPlaying()
    {
        if (m_animators == null || Time.time >= m_timeToNextAnimatorsGrab)
        {
            m_animators = FindObjectsByType<Animator>(FindObjectsInactive.Exclude, FindObjectsSortMode.None);
            m_timeToNextAnimatorsGrab = Time.time + 1f;
        }
        int count = 0;
        foreach (var animator in m_animators)
            if (animator != null && animator.isActiveAndEnabled && animator.runtimeAnimatorController != null && animator.runtimeAnimatorController.animationClips.Length != 0)
                if (IsAnimatorEffectivelyPlaying(animator))
                    ++count;
        return count;
    }
    
    int m_physicsTotalRidigBodies, m_physicsKinematicRigidBodies, m_physicsSleepingRigidBodies;
    float m_physicsUpdateLastTime = 0;
    private void UpdatePhysicsStats()
    {
        if (Time.time < m_physicsUpdateLastTime + 1f) return;
        m_physicsUpdateLastTime = Time.time;
        var rigidbodies = FindObjectsByType<Rigidbody>(FindObjectsInactive.Exclude, FindObjectsSortMode.None);
        m_physicsTotalRidigBodies = rigidbodies.Length;
        m_physicsKinematicRigidBodies = 0;
        m_physicsSleepingRigidBodies = 0;
        foreach (var rb in rigidbodies)
        {
            if (rb.isKinematic)
                ++m_physicsKinematicRigidBodies;
            else if (rb.IsSleeping())
                ++m_physicsSleepingRigidBodies;
        }
    }

    static string ProfilerMsString(long _profilerValue)
    {
        return ((_profilerValue / 1000) * .001f).ToString("n1");
    }
    long m_gpuTime = 0;
    void Update()
    {
        UpdatePhysicsStats();
        
        var sb = new System.Text.StringBuilder(256);

        var gpuTime = GetRecorderValue(m_gpuMS);
        if (gpuTime != 0) m_gpuTime = gpuTime;
        sb.Append("GPU: ").Append(ProfilerMsString(m_gpuTime)).Append("ms\n");

        sb.Append("Alloc'd: ").Append((Profiler.GetTotalAllocatedMemoryLong() / (1024 * 1024)).ToString("n0")).Append("mb   ");
        sb.Append("Resrv'd: ").Append((Profiler.GetTotalReservedMemoryLong() / (1024 * 1024)).ToString("n0")).Append("mb   ");
        sb.Append("Mono: ").Append((Profiler.GetMonoUsedSizeLong() / (1024 * 1024)).ToString("n0")).Append("mb\n");

        sb.Append("Draw Calls: ").Append(GetRecorderValue(m_drawCalls).ToString("n0")).Append("   ");
        sb.Append("Batches: ").Append(GetRecorderValue(m_batches).ToString("n0")).Append('\n');
        sb.Append("SetPass Calls: ").Append(GetRecorderValue(m_setPass).ToString("n0")).Append("   ");
        sb.Append("Shadow Casters: ").Append(GetRecorderValue(m_shadowDraws).ToString("n0")).Append('\n');
        sb.Append("Triangles: ").Append((GetRecorderValue(m_triangles) / 1000000f).ToString("n1")).Append("m   ");
        sb.Append("Vertices: ").Append((GetRecorderValue(m_vertices) / 1000000f).ToString("n1")).Append("m\n");
        sb.Append("Active animators: ").Append(GetAnimatorsPlaying().ToString("n0")).Append("   ");
        sb.Append("Animation: ").Append(ProfilerMsString(GetRecorderValue(m_animUpdate) + GetRecorderValue(m_animUpdate2) + GetRecorderValue(m_animUpdate3))).Append("ms\n");
        sb.Append("Physics: ").Append(ProfilerMsString(GetRecorderValue(m_physicsUpdate))).Append("ms   ");
        sb.Append("Rigidbodies: ").Append(m_physicsTotalRidigBodies).Append("   Kinematic:").Append(m_physicsKinematicRigidBodies).Append("   Sleeping:").Append(m_physicsSleepingRigidBodies).Append("\n");
        sb.Append("Canvas: ").Append(ProfilerMsString(GetRecorderValue(m_canvasUpdate))).Append("ms\n");
        sb.Append("Update: ").Append(ProfilerMsString(GetRecorderValue(m_scriptUpdate))).Append("ms   ");
        sb.Append("Fixed: ").Append(ProfilerMsString(GetRecorderValue(m_scriptFixedUpdate))).Append("ms   ");
        sb.Append("Late: ").Append(ProfilerMsString(GetRecorderValue(m_scriptLateUpdate))).Append("ms\n");
        
        m_statsOutput = sb.ToString();
    }

    static long GetRecorderValue(ProfilerRecorder recorder)
    {
        if (!recorder.Valid) return -1;
        //return recorder.CurrentValue > recorder.LastValue ? recorder.CurrentValue : recorder.LastValue;
        if (recorder.Count > 1)
        {
            long min = long.MaxValue, max = 0;
            for (int i = 0; i < recorder.Count; ++i)
            {
                var value = recorder.GetSample(i).Value;
                if (value > 0 && value < min) min = value;
                if (value > max) max = value;
            }
            if (min == long.MaxValue) min = 0;
            return max;//min;
        }
        return recorder.LastValue;
    }
}
