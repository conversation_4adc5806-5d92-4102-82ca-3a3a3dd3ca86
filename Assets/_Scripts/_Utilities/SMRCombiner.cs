using System.Collections.Generic;
using UnityEngine;

public static class SMRCombiner
{
    public static void Combine(IList<SkinnedMeshRenderer> _parts, uint _vertexColourInhibit = 0)
    {
        if (_parts == null || _parts.Count == 0 || _parts[0] == null || _parts[0].sharedMesh == null) return;

        var target = _parts[0];
        var body = _parts[0];
        var bodyMesh = body.sharedMesh;
        if (bodyMesh == null)
            return;

        // use bones and bindPoses from first mesh directly 
        var finalBones = body.bones;
        var finalBindposes = (Matrix4x4[])bodyMesh.bindposes.Clone();
        var rootBone = body.rootBone;
        int rootIdx = IndexOfBone(finalBones, rootBone);
        if (rootIdx < 0) rootIdx = 0;

        // Bone name -> final index map
        var finalIndexByName = new Dictionary<string, int>(finalBones.Length);
        for (int i = 0; i < finalBones.Length; i++)
        {
            var t = finalBones[i];
            if (t != null && !finalIndexByName.ContainsKey(t.name)) finalIndexByName.Add(t.name, i);
        }

        // === Combined buffers ===
        var vertices = new List<Vector3>(bodyMesh.vertexCount + 2048);
        var normals = new List<Vector3>(bodyMesh.vertexCount + 2048);
        var tangents = new List<Vector4>(bodyMesh.vertexCount + 2048);
        var uvs = new List<Vector2>(bodyMesh.vertexCount + 2048);
        var colors = new List<Color32>(bodyMesh.vertexCount + 2048);
        var boneWeights = new List<BoneWeight>(bodyMesh.vertexCount + 2048);

        var finalTriangles = new List<List<int>>(bodyMesh.subMeshCount + 32);
        var finalMaterials = new List<Material>(body.sharedMaterials.Length + 32);

        // === Append first SMR ===
        bodyMesh.GetVertices(vertices);
        bodyMesh.GetNormals(normals);
        bodyMesh.GetTangents(tangents);
        bodyMesh.GetUVs(0, uvs);
        bodyMesh.GetColors(colors);
        boneWeights.AddRange(bodyMesh.boneWeights);

        for (int s = 0; s < bodyMesh.subMeshCount; s++)
        {
            var tris = new List<int>();
            bodyMesh.GetTriangles(tris, s, true);
            if (_vertexColourInhibit > 0) // apply colour clip if any
            {
                for (int i = tris.Count - 3; i >= 0; i -= 3)
                {
                    var clr = colors[tris[i]];
                    var r = (int) ((Mathf.Floor(clr.r * (3.9999f / 255f)) + 1) * .5f);
                    var g = (int) ((Mathf.Floor(clr.g * (3.9999f / 255f)) + 1) * .5f);
                    var b = (int) ((Mathf.Floor(clr.b * (3.9999f / 255f)) + 1) * .5f);
                    var index = r + g * 3 + b * 9;
                    if (((_vertexColourInhibit >> index) & 1) == 1) { tris.RemoveAt(i + 2); tris.RemoveAt(i + 1); tris.RemoveAt(i); }
                }
            }
            finalTriangles.Add(tris);
            finalMaterials.Add((s < body.sharedMaterials.Length) ? body.sharedMaterials[s] : null);
        }

        // Merge in all other parts
        var toBodyLocal = body.transform.worldToLocalMatrix; // constant for all parts
        for (int p = 1; p < _parts.Count; p++)
        {
            var part = _parts[p];
            if (part == null) continue;
            var mesh = part.sharedMesh;
            if (mesh == null) continue;

            // Object‑space delta = BodyLocal * PartWorld
            var delta = toBodyLocal * part.transform.localToWorldMatrix;
            var revDelta = delta.inverse.transpose; // for normals/tangents

            // Read source attributes
            var v= ListPoolVector3.Get(); mesh.GetVertices(v);
            var n= ListPoolVector3.Get(); mesh.GetNormals(n);
            var tt = ListPoolVector4.Get(); mesh.GetTangents(tt);
            var uv = ListPoolVector2.Get(); mesh.GetUVs(0, uv);
            var c = ListPoolColor32.Get(); mesh.GetColors(c);

            int vCount = v.Count;
            if (vCount == 0) { ReleaseTemps(); continue; }

            // Ensure channels present
            if (n.Count != vCount) AddRepeated(n, Vector3.up, vCount - n.Count);
            if (tt.Count != vCount) AddRepeated(tt, new Vector4(1,0,0,1), vCount - tt.Count);
            if (uv.Count != vCount) AddRepeated(uv, Vector2.zero, vCount - uv.Count);
            if (c.Count != vCount) AddRepeated(c, new Color32(255,255,255,255), vCount - c.Count);

            // Pre‑transform geometry from PART local to BODY local
            int vtxOffset = vertices.Count;
            for (int i = 0; i < vCount; i++)
            {
                var pos = delta.MultiplyPoint3x4(v[i]);
                var nor = revDelta.MultiplyVector(n[i]);
                var tanV = new Vector3(tt[i].x, tt[i].y, tt[i].z);
                var tan = revDelta.MultiplyVector(tanV);
                vertices.Add(pos);
                normals.Add(nor.sqrMagnitude > 0f ? nor.normalized : Vector3.up);
                var tn = tan.sqrMagnitude > 0f ? tan.normalized : new Vector3(1,0,0);
                tangents.Add(new Vector4(tn.x, tn.y, tn.z, tt[i].w));
            }

            // Remap weights to final bone indices (by name with ancestor fallback)
            var srcBones = part.bones;
            var weights= mesh.boneWeights;
            if (weights != null && weights.Length == vCount)
            {
                var map = new int[srcBones.Length];
                for (int i = 0; i < srcBones.Length; i++) map[i] = MapByAncestor(srcBones[i], finalIndexByName, rootIdx);

                for (int i = 0; i < vCount; i++)
                {
                    var bw = weights[i];
                    bw.boneIndex0 = SafeMap(bw.boneIndex0, map, rootIdx);
                    bw.boneIndex1 = SafeMap(bw.boneIndex1, map, rootIdx);
                    bw.boneIndex2 = SafeMap(bw.boneIndex2, map, rootIdx);
                    bw.boneIndex3 = SafeMap(bw.boneIndex3, map, rootIdx);
                    boneWeights.Add(bw);
                }
            }
            else
            {
                for (int i = 0; i < vCount; i++) boneWeights.Add(new BoneWeight { boneIndex0 = rootIdx, weight0 = 1f });
            }

            // Copy UVs/colors
            uvs.AddRange(uv);
            colors.AddRange(c);

            // Triangles & materials
            AppendSubmeshes(mesh, part.sharedMaterials, vtxOffset, finalTriangles, finalMaterials);

            ReleaseTemps();

            void ReleaseTemps()
            {
                ListPoolVector3.Release(v);
                ListPoolVector3.Release(n);
                ListPoolVector4.Release(tt);
                ListPoolVector2.Release(uv);
                ListPoolColor32.Release(c);
            }
        }

        // === Build combined mesh ===
        var combined = new Mesh
        {
            name = "Combined",
            indexFormat = (vertices.Count > 65535) ? UnityEngine.Rendering.IndexFormat.UInt32 : UnityEngine.Rendering.IndexFormat.UInt16
        };

        combined.SetVertices(vertices);
        if (normals.Count == vertices.Count) combined.SetNormals(normals);
        if (tangents.Count == vertices.Count) combined.SetTangents(tangents);
        if (uvs.Count == vertices.Count) combined.SetUVs(0, uvs);
        if (colors.Count == vertices.Count) combined.SetColors(colors);
        combined.boneWeights = boneWeights.ToArray();

        combined.subMeshCount = finalTriangles.Count;
        for (int i = 0; i < finalTriangles.Count; i++)
            combined.SetTriangles(finalTriangles[i], i, true);

        combined.bindposes = finalBindposes;
        combined.RecalculateBounds();

        // Assign SMR
        target.sharedMesh = combined;
        target.rootBone = rootBone;
        target.bones = finalBones;
        target.sharedMaterials = finalMaterials.ToArray();
        target.updateWhenOffscreen = body.updateWhenOffscreen;
    }

    // === helpers ===

    private static int IndexOfBone(Transform[] _bones, Transform _t)
    {
        if (_t == null) return -1;
        for (int i = 0; i < _bones.Length; i++) if (_bones[i] == _t) return i;
        return -1;
    }

    private static int MapByAncestor(Transform _src, Dictionary<string,int> _finalIndexByName, int _rootIdx)
    {
        var t = _src;
        while (t != null)
        {
            if (_finalIndexByName.TryGetValue(t.name, out int idx)) return idx;
            t = t.parent;
        }
        return _rootIdx;
    }

    private static int SafeMap(int _idx, int[] _map, int _fallback) => (_idx >= 0 && _idx < _map.Length) ? _map[_idx] : _fallback;

    private static void AddRepeated<T>(List<T> _list, T value, int _extra)
    {
        for (int i = 0; i < _extra; i++) _list.Add(value);
    }

    private static void AppendSubmeshes(Mesh _mesh, Material[] _mats, int _vtxOffset, List<List<int>> _finalTris, List<Material> _finalMats)
    {
        int sub = _mesh.subMeshCount;
        for (int s = 0; s < sub; s++)
        {
            var tris = new List<int>(_mesh.GetTriangles(s).Length);
            _mesh.GetTriangles(tris, s, true);
            for (int k = 0; k < tris.Count; k++) tris[k] += _vtxOffset;
            _finalTris.Add(tris);
            _finalMats.Add((s < _mats.Length) ? _mats[s] : null);
        }
    }
}

#region Simple List Pools
internal static class ListPoolVector2
{
    static readonly Stack<List<Vector2>> pool = new Stack<List<Vector2>>();
    public static List<Vector2> Get() => pool.Count > 0 ? pool.Pop() : new List<Vector2>(256);
    public static void Release(List<Vector2> _list) { _list.Clear(); pool.Push(_list); }
}
internal static class ListPoolVector3
{
    static readonly Stack<List<Vector3>> pool = new Stack<List<Vector3>>();
    public static List<Vector3> Get() => pool.Count > 0 ? pool.Pop() : new List<Vector3>(256);
    public static void Release(List<Vector3> _list) { _list.Clear(); pool.Push(_list); }
}
internal static class ListPoolVector4
{
    static readonly Stack<List<Vector4>> pool = new Stack<List<Vector4>>();
    public static List<Vector4> Get() => pool.Count > 0 ? pool.Pop() : new List<Vector4>(256);
    public static void Release(List<Vector4> _list) { _list.Clear(); pool.Push(_list); }
}
internal static class ListPoolColor32
{
    static readonly Stack<List<Color32>> pool = new Stack<List<Color32>>();
    public static List<Color32> Get() => pool.Count > 0 ? pool.Pop() : new List<Color32>(256);
    public static void Release(List<Color32> _list) { _list.Clear(); pool.Push(_list); }
}
#endregion