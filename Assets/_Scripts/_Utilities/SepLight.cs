using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public static class Ext
{
    public static void Add(this List<int> _this, int _a, int _b, int _c)
    {
        _this.Add(_a);
        _this.Add(_b);
        _this.Add(_c);
    }
}
public class SLReadOnlyAttribute : PropertyAttribute { }
#if UNITY_EDITOR
[UnityEditor.CustomPropertyDrawer(typeof(SLReadOnlyAttribute))]
public class SLReadOnlyDrawer : UnityEditor.PropertyDrawer
{
    public override float GetPropertyHeight(UnityEditor.SerializedProperty property, GUIContent label)
    {
        return UnityEditor.EditorGUI.GetPropertyHeight(property, label, true) + 24;
    }
 
    public override void OnGUI(Rect position, UnityEditor.SerializedProperty property, GUIContent label)
    {
        //GUI.enabled = false;
        //UnityEditor.EditorGUI.PropertyField(position, property, label, true);
        //GUI.enabled = true;
        UnityEditor.EditorGUI.LabelField(position, label.text, property.stringValue);
    }
}
#endif

[ExecuteInEditMode]
public class SepLight : MonoBehaviour
{
#if false
    [SLReadOnly]
    [SerializeField]
    private string m_ = "Size is point radius / spot length\nIntensity can be negative";
    
    public enum EShape
    {
        Point,
        Spot,
    }
    public EShape m_shape = EShape.Point;
    public bool m_useNormals = false;
    public Color m_colour = Color.white;
    public float m_size = 1;
    public float m_intensity = 1;
    [Range(1, 89)]
    public float m_spotAngle = 30;
    [Range(0,0.99f)]
    public float m_tightness = 0.5f;
    public Material m_coreMaterial;
    
    private MeshRenderer m_renderer;
    private MeshFilter m_filter;
    private Material m_material;
    private EShape m_meshShape = (EShape)(-1);
    void Start()
    {
        Update();
    }

    Mesh GenerateSphereMesh()
    {
        var r = (1 + Mathf.Sqrt(5)) * .5f;
        var verts = new List<Vector3>();

        verts.Add(new Vector3(-1,  r,  0));
        verts.Add(new Vector3( 1,  r,  0));
        verts.Add(new Vector3(-1, -r,  0));
        verts.Add(new Vector3( 1, -r,  0));

        verts.Add(new Vector3( 0, -1,  r));
        verts.Add(new Vector3( 0,  1,  r));
        verts.Add(new Vector3( 0, -1, -r));
        verts.Add(new Vector3( 0,  1, -r));

        verts.Add(new Vector3( r,  0, -1));
        verts.Add(new Vector3( r,  0,  1));
        verts.Add(new Vector3(-r,  0, -1));
        verts.Add(new Vector3(-r,  0,  1));
        
        var inds = new List<int>();
        inds.Add(0, 11, 5);
        inds.Add(0, 5, 1);
        inds.Add(0, 1, 7);
        inds.Add(0, 7, 10);
        inds.Add(0, 10, 11);

        inds.Add(1, 5, 9);
        inds.Add(5, 11, 4);
        inds.Add(11, 10, 2);
        inds.Add(10, 7, 6);
        inds.Add(7, 1, 8);

        inds.Add(3, 9, 4);
        inds.Add(3, 4, 2);
        inds.Add(3, 2, 6);
        inds.Add(3, 6, 8);
        inds.Add(3, 8, 9);

        inds.Add(4, 9, 5);
        inds.Add(2, 4, 11);
        inds.Add(6, 2, 10);
        inds.Add(8, 6, 7);
        inds.Add(9, 8, 1);
        
        var mesh = new Mesh();
        mesh.vertices = verts.ToArray();
        mesh.SetIndices(inds, MeshTopology.Triangles, 0, true);
        return mesh;
    }

    Mesh GenerateConeMesh()
    {
        var verts = new List<Vector3>();
        verts.Add(Vector3.zero);
        for (int i = 0; i < 8; ++i)
        {
            float theta = Mathf.PI * 2 * i / 8;
            float s = Mathf.Sin(theta), c = Mathf.Cos(theta);
            verts.Add(new Vector3(s, c, 1));
        }
        var inds = new List<int>();
        for (int i = 0; i < 8; ++i)
        {
            inds.Add(0);
            inds.Add(1+i);
            inds.Add(1+((i+1)&7));
        }
        var mesh = new Mesh();
        mesh.vertices = verts.ToArray();
        mesh.SetIndices(inds, MeshTopology.Triangles, 0, true);
        return mesh;
    }
    void GenerateShape()
    {
        if (m_renderer == null)
        {
            m_renderer = gameObject.GetComponent<MeshRenderer>();
            if (m_renderer == null) m_renderer = gameObject.AddComponent<MeshRenderer>();
            m_filter = gameObject.GetComponent<MeshFilter>();
            if (m_filter == null) m_filter = gameObject.AddComponent<MeshFilter>();
            m_renderer.material = m_coreMaterial;
            m_material = m_renderer.material;
        }
        m_meshShape = m_shape;
        Mesh mesh;
        if (m_shape == EShape.Point) mesh = GenerateSphereMesh();
        else mesh = GenerateConeMesh();
        m_filter.mesh = mesh;
    }
    const float c_pointMeshScale = 0.5f / 0.755761f; 
    const float c_spotMeshScale = 1.0f / 0.924f; // 1 / cos(22.5)
    void Update()
    {
        float cos = 1;
        if (m_shape == EShape.Point)
        {
            transform.localScale = Vector3.one * (m_size * c_pointMeshScale);
        }
        else
        {
            var tan = Mathf.Tan(m_spotAngle * Mathf.Deg2Rad) * c_spotMeshScale;
            cos = Mathf.Cos(m_spotAngle * Mathf.Deg2Rad);
            transform.localScale = new Vector3(tan, tan, 1) * m_size;
        }
        
        if (m_meshShape != m_shape)
            GenerateShape();
        m_material.SetColor("_Colour", m_colour);
        m_material.SetVector("_Details", new Vector4(m_size * m_size, cos * cos, m_tightness * m_tightness, m_intensity));
        if (m_shape == EShape.Point) m_material.DisableKeyword("_SPOT");
        else m_material.EnableKeyword("_SPOT");
        if (m_useNormals) m_material.EnableKeyword("_USE_NORMALS");
        else m_material.DisableKeyword("_USE_NORMALS");
    }

#if UNITY_EDITOR
    void OnDrawGizmos()
    {
        var cam = Camera.current;
        var isSelected = gameObject == UnityEditor.Selection.activeObject;
        
        var c = m_colour; c.a = .2f;
        UnityEditor.Handles.color = c;
        if (m_shape == EShape.Point)
        {
            if (isSelected)
                UnityEditor.Handles.DrawSolidDisc(transform.position, -cam.transform.forward, m_size);
            UnityEditor.Handles.color = Color.white;
            UnityEditor.Handles.DrawWireDisc(transform.position, -cam.transform.forward, m_size);
            UnityEditor.Handles.color = new Color(1,1, 1,.5f);
            UnityEditor.Handles.DrawWireDisc(transform.position, -cam.transform.forward, m_size * m_tightness);
        }
        else
        {
            var tan = Mathf.Tan(m_spotAngle * Mathf.Deg2Rad);
            var start = transform.position;
            var fwd = transform.forward;
            var end = start + fwd * m_size;
            var mid = (start + end) * .5f;
            var perp = Vector3.Cross(end - cam.transform.position, fwd).normalized;
            if (isSelected)
            {
                UnityEditor.Handles.DrawSolidDisc(end, fwd, tan * m_size);
                //UnityEditor.Handles.DrawAAConvexPolygon(start, end + perp * tan * m_size, end - perp * tan * m_size);
            }
            UnityEditor.Handles.color = Color.white;
            UnityEditor.Handles.DrawWireDisc(end, fwd, tan * m_size);
            UnityEditor.Handles.DrawLine(start, mid); 
            UnityEditor.Handles.DrawLine(mid, mid - (fwd + perp * .5f) * .2f);
            UnityEditor.Handles.DrawLine(mid, mid - (fwd - perp * .5f) * .2f);
            UnityEditor.Handles.DrawLine(start, end + perp * tan * m_size);
            UnityEditor.Handles.DrawLine(start, end - perp * tan * m_size);
            UnityEditor.Handles.color = new Color(1,1, 1,.5f);
            UnityEditor.Handles.DrawWireDisc(end, fwd, tan * m_size * m_tightness);
            UnityEditor.Handles.DrawLine(start, end + perp * tan * m_size * m_tightness);
            UnityEditor.Handles.DrawLine(start, end - perp * tan * m_size * m_tightness);
        }
    }
    [UnityEditor.MenuItem("GameObject/Light/Fake Light")]
    public static GameObject Create()
    {
        var cam = UnityEditor.SceneView.lastActiveSceneView.camera;
        var go = Instantiate(Resources.Load<GameObject>("SepLight"));
        go.name = "FakeLight";
        go.transform.position = cam.transform.position + cam.transform.forward * 5;
        UnityEditor.Selection.activeObject = go;
        return go;
    }
#endif
#endif
}
