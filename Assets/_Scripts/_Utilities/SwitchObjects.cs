using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class SwitchObjects : Mon<PERSON><PERSON><PERSON><PERSON><PERSON>, IBatchPartitioner {
	// GL - objectsToSwitch is a set of objects that are going to be enabled and disabled based on a value passed to SwitchTo 	
	public GameObject[] m_objectsToSwitch;
	// GL - default defines an object to be enabled if we don't find the specified index or if it is empty; this allows for partial sets
	public GameObject m_default;
	// GL - initialSet defines a set to be switched to on starting, or -1 to do nothing; if SwitchTo is called before Start this will be cancelled
	// This can be used to give the effect of having no switch sets at all if no action is taken, and switch sets only if a call to SwitchTo is made before Start() 
	public int m_initialSet = -1;
	public bool m_initialSetPermanent = true;
	
	public Animator m_animatorToSwitch;
	public string m_parameterToSwitch;
	private bool m_hasTimeParameter;
	private float m_currentSpeed = 0;
	private float m_currentTime = 0;
	public bool m_manualControl = false;
	private float m_targetTime = 0;
	
	int m_currentSet; public int CurrentSet => m_currentSet;
	
	void Awake() {
		m_currentSet = m_initialSet;
		
		if (m_initialSet != -1)
			SwitchTo(m_initialSet, m_initialSetPermanent);
		m_hasTimeParameter = m_animatorToSwitch != null && m_animatorToSwitch.HasParameter("Time");
	}

	public void SetEnabled(bool _enabled)
	{
		if (m_animatorToSwitch != null)
			m_animatorToSwitch.speed = _enabled ? 1 : 0;
	}

	public float GetCurrentClipLength()
	{
		if(m_animatorToSwitch == null) return 0f;
		var clips = m_animatorToSwitch.GetCurrentAnimatorClipInfo(0);
		if (clips.Length == 0) return 0f;
		return clips[0].clip.length;
	}
	
	private float m_overrideSpeed = -1;
	void Update()
	{
		if (m_animatorToSwitch != null)
		{
			float target = m_currentSet;
			m_currentSpeed = Mathf.Lerp(m_currentSpeed, target, .1f);
			if (m_overrideSpeed >= 0) m_currentSpeed = m_overrideSpeed;
			if (m_hasTimeParameter)
			{
				if(m_manualControl)
				{
					m_currentTime = Mathf.Lerp(m_currentTime, m_targetTime, 0.2f);
				}
				else
				{
					var duration = GetCurrentClipLength();
					var step = duration > .01f ? 1 / duration : 1;
					m_currentTime = Mathf.Repeat(m_currentTime + Time.deltaTime * step * m_currentSpeed, 1.0f);
				}
				m_animatorToSwitch.SetFloat("Time", m_currentTime);
			}
			else
				m_animatorToSwitch.speed = m_currentSpeed;
		}
	}
	
	public void Reset()
	{
		m_currentTime = 0;
		m_targetTime = 0;
		if (m_hasTimeParameter) m_animatorToSwitch.SetFloat("Time", m_currentTime);		
	}
	
	public bool HasReachedManualTarget()
	{
		return Mathf.Approximately(m_targetTime, m_currentTime);
	}
	
	public void ManualControl(float _t)
	{
		m_overrideSpeed = 0;
		m_manualControl = true;
		m_targetTime = _t;
		
		if(m_targetTime < m_currentTime) m_currentTime = m_targetTime;
	}
	
	public void Animate(float _speed)
	{
		m_manualControl = false;
		m_overrideSpeed = _speed;
	}


	// GL - switch to a set
	// This is complicated by the fact that the same game object might be in multiple sets - switch on needs to override a later switch off
	// The _permanent flag denotes that we are never going to switch again, and destroys the deactivated objects, saving memory
	public void SwitchTo(int _which, bool _permanent = false) {
		m_initialSet = -1; // if SwitchTo is called before Start() then stop Start() from changing to initialSet
		
		m_currentSet = _which;
		GameObject switchOn = m_default;
		var switchOff = new HashSet<GameObject>();

		// old scheme, uses parameters to control animation
		//if (m_animatorToSwitch != null && string.IsNullOrEmpty(m_parameterToSwitch) == false)
		//	m_animatorToSwitch.SetInteger(m_parameterToSwitch, _which);
		if (m_animatorToSwitch != null)
		{
			// new scheme - always animate, use switch value to control animation speed
			if (string.IsNullOrEmpty(m_parameterToSwitch) == false)
				m_animatorToSwitch.SetInteger(m_parameterToSwitch, 1);
		}

		for (int i = 0; i < m_objectsToSwitch.Length; i ++) {
			if (m_objectsToSwitch[i] == null) continue;
			if (i == _which) {
				switchOn = m_objectsToSwitch[i];
			} else {
				if (!switchOff.Contains(m_objectsToSwitch[i]))
					switchOff.Add(m_objectsToSwitch[i]);
			}
		}
		if (switchOn != null && !switchOn.activeSelf)
			switchOn.SetActive(true);
		foreach (var o in switchOff) {
			if (o != switchOn && o != null) {
				o.SetActive(false);
				if (_permanent) Destroy(o);
			}
		}
		if (_permanent) Destroy(this);
	}

	public void SwitchToDefault(bool _permanent = false)
	{
		SwitchTo(-1, _permanent);
	}
	
	public GameObject GetCurrentSetObject()
	{
		if (m_animatorToSwitch != null && m_objectsToSwitch.Length > 0) return m_objectsToSwitch[0];
		if(m_objectsToSwitch.Length == 0 || m_currentSet < 0 || m_currentSet > m_objectsToSwitch.Length - 1) return null;
		
		return m_objectsToSwitch[m_currentSet];
	}

	public bool Switches(GameObject _go) {
		for (int i = 0; i < m_objectsToSwitch.Length; i++)
			if (m_objectsToSwitch[i] == _go)
				return true;
		return false;
	}

	public Component Component()
	{
		return this;
	}
	
	public List<List<Transform>> GetExcludedTransforms()
	{
		var list = new List<List<Transform>>();
		if (m_default != null)
			list.Add(new List<Transform> { m_default.transform });
		foreach (var go in m_objectsToSwitch)
		{
			if (go != null)
				list.Add(new List<Transform> { go.transform });
		}
		return list;
	}
	
	public void OnBatch(Dictionary<MeshRenderer, MeshRenderer> _replaced) { }
}
