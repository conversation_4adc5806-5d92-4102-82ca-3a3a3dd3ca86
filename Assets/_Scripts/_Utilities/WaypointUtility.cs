using UnityEngine;
using UnityEditor;

public class WaypointUtility : MonoBehaviour
{
    [SerializeField]
    [Multiline(10)]
    private string waypoints = "";

    public void SerialiseWaypoints()
    {
        string path = "";
        foreach (var t in transform.GetComponentsInChildren<Transform>())
        {
            if (t == transform)
                continue;

            path += $"Pos[{t.position.x},{t.position.z}]\n";
        }
        Debug.LogError(path);
    }

    public void DeserialiseWaypoints()
    {
        var path = MASpawnByDayInfo.DecodeWaypoints(waypoints);
        int index = 0;
        foreach (var p in path)
        {
            var w = new GameObject($"{index}");
            var t = w.transform;
            t.SetParent(transform);
            t.position = new Vector3(p.x, 0f, p.z);
            t.position = t.position.GroundPosition();
            ++index;
        }
    }
}


#if UNITY_EDITOR
[CanEditMultipleObjects]
[CustomEditor(typeof(WaypointUtility))]
public class WaypointUtilityEditor : Editor
{
	public override void OnInspectorGUI()
	{
		base.OnInspectorGUI();

		WaypointUtility wu = (WaypointUtility)target;

		if (Application.isPlaying)
		{
            if (GUILayout.Button($"Serialise Waypoints"))
            {
                wu.SerialiseWaypoints();
            }
            if (GUILayout.Button($"Deserialise Waypoints"))
            {
                wu.DeserialiseWaypoints();
            }
		}
	}
}
#endif
